import React, { useEffect, useState } from "react";
// eslint-disable-next-line no-unused-vars
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate } from "react-router-dom";  // Added import for navigation
import GreenTick from '@assets/greenTick.svg';
import UserAdd from '@assets/icon/userAdd.svg';
import OnboardingIcon1 from '@assets/icon/onboarding1.svg';
import OnboardingIcon2 from '@assets/icon/onboarding2.svg';
import OnboardingIcon3 from '@assets/icon/onboarding3.svg';
import OnboardingIcon4 from '@assets/icon/onboarding4.svg';
import InstagramCircle from "@assets/icon/instagram-circle.svg";
import YoutubeCircle from "@assets/icon/youtube-circle.svg";

const steps = [
  "Scanning Audience Demographics & Insights",
  "Optimising Your Profile for Brand Deals",
  "Spotting What Makes You Stand Out",
  "Collecting Metrics Brands Care About",
];

const onboardingIcons = [
  OnboardingIcon1,
  OnboardingIcon2,
  OnboardingIcon3,
  OnboardingIcon4,
];

const CIRCLE_RADIUS = 12;
const CIRCLE_CIRCUMFERENCE = 2 * Math.PI * CIRCLE_RADIUS;

function ProfileOnboardingFlow({ socialProfiles = [] }) {
  const navigate = useNavigate();  // Added navigate hook
  const [activeStep, setActiveStep] = useState(0);
  const [lineStep, setLineStep] = useState(-1);

  useEffect(() => {
    if (activeStep < steps.length) {
      const animateLine = setTimeout(() => {
        setLineStep(activeStep);
      }, 1600);

      const timer = setTimeout(() => {
        setActiveStep((prev) => prev + 1);
      }, 2500);

      return () => {
        clearTimeout(timer);
        clearTimeout(animateLine);
      };
    } else {
      // When all steps are complete, navigate to the dashboard after a short delay
      const navigationTimer = setTimeout(() => {
        navigate('/influencer/dashboard');
      }, 1000); // 1 second delay to allow the user to see the completed state

      return () => clearTimeout(navigationTimer);
    }
  }, [activeStep, navigate]);


  const [index, setIndex] = useState(0);

  useEffect(() => {
    const timer = setInterval(() => {
      setIndex((prev) => (prev + 1) % socialProfiles.length);
    }, 3000); // change every 3 seconds
    return () => clearInterval(timer);
  }, []);

  const variants = {
    enter: {
      y: 100,
      opacity: 0,
    },
    center: {
      y: 0,
      opacity: 1,
      transition: { duration: 1 },
    },
    exit: {
      y: -100,
      opacity: 0,
      transition: { duration: 1 },
    },
  };

  const current = socialProfiles[index];

  return (
    <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} className=" w-[450px] flex flex-col items-center justify-center bg-primary text-white space-y-5">
      <div className="w-full">
        <h2 className="text-gray-300 text-20-medium mb-2">Hang tight</h2>
        <h1 className="text-gray-50 text-30-bold mb-6">
          We're setting up your&nbsp;
          <span className="text-violet-1">
            space
          </span>
          🚀
        </h1>
      </div>

      {/* Profile card */}
      {/* <div className="bg-gradient-to-r from-indigo-400 to-purple-500 p-3 rounded-2xl w-full  mb-8">
        <div className="flex items-center space-x-4 bg-white/20 rounded-xl px-3.5 py-3">
          <div className="w-17.5 h-17.5 rounded-full bg-gray-200 relative">
            <img src="https://randomuser.me/api/portraits/men/45.jpg" className="rounded-full" alt="profile-pic" />
            <div className="absolute bottom-0 right-0 bg-white rounded-full p-0">
              <img src={InstagramCircle} alt="social" className="w-5 h-5" />
            </div>
          </div>
          <div className="flex flex-col space-y-1.5">
            <p className="text-20-semibold text-gray-50">Aditya Jain</p>
            <p className="text-16-semibold text-gray-200">@aditya_jain | 9K Followers</p>
          </div>
        </div>
      </div> */}
      <div className="bg-gradient-to-r from-indigo-400 to-purple-500 p-4 rounded-2xl relative h-[120px] overflow-hidden w-full">
        <AnimatePresence mode="wait">
          <motion.div
            key={current.id}
            variants={variants}
            initial="enter"
            animate="center"
            exit="exit"
            className="absolute inset-0 w-full h-full p-4"
          >
            <div className="flex items-center space-x-4 bg-white/20 backdrop-blur-md rounded-xl px-4 py-3 h-full">
              {/* Avatar */}
              <div className="w-[70px] h-[70px] rounded-full bg-gray-200 relative flex-shrink-0">
                <img
                  src={current.avatar}
                  alt="profile-pic"
                  className="w-full h-full rounded-full object-cover"
                />
                <div className="absolute bottom-0 right-0 bg-white rounded-full p-0.5 shadow-md">
                  <img
                    src={current.channel === "instagram" ? InstagramCircle : YoutubeCircle}
                    alt="social"
                    className="w-5 h-5"
                  />
                </div>
              </div>

              {/* Text Info */}
              <div className="flex flex-col justify-center">
                <p className="text-lg font-semibold text-white">{current.name}</p>
                <p className="text-sm font-medium text-gray-200">
                  {current.profile} | {current.followers}{" "}
                  {current.channel === "instagram" ? "Followers" : "Subscribers"}
                </p>
              </div>
            </div>
          </motion.div>
        </AnimatePresence>
      </div>


      {/* Stepper */}
      <div className="w-full px-6.5">
        {steps.map((step, index) => (
          <div key={index} className="flex items-start space-x-4">
            <div className="relative flex flex-col items-center">
              <div className="relative w-10 h-10">
                <svg className="w-10 h-10 rotate-[-90deg]" viewBox="0 0 30 30">
                  {index >= activeStep ? (
                    <motion.circle
                      cx="15"
                      cy="15"
                      r={CIRCLE_RADIUS}
                      fill="transparent"
                      stroke="#12c9ac"
                      strokeWidth="2"
                      strokeDasharray={CIRCLE_CIRCUMFERENCE}
                      strokeDashoffset={CIRCLE_CIRCUMFERENCE}
                      animate={index === activeStep ? { strokeDashoffset: 0 } : {}}
                      transition={{ duration: 1.5, ease: "easeInOut" }}
                    />
                  ) : null}
                </svg>

                <div className="absolute inset-0 flex items-center justify-center">
                  {index < activeStep ? (
                    <img src={GreenTick} className="w-8 h-8" />
                  ) : (
                    <img src={onboardingIcons[index]} className="w-10 h-10  rounded-2xl" />
                  )}
                </div>
              </div>

              {index < steps.length - 1 && (
                <div className="h-6 w-[3px] overflow-hidden rounded-3xl">
                  <motion.div
                    className="w-[3px] h-full bg-green-2"
                    initial={{ height: 0 }}
                    animate={{ height: lineStep >= index ? "2.7rem" : 0 }}
                    transition={{ duration: 0.5, ease: "easeInOut" }}
                  />
                </div>
              )}
            </div>

            <motion.div className="text-16-regular mt-3 flex flex-wrap gap-x-1">
              {step.split(" ").map((word, wordIndex) => (
                <motion.span
                  key={wordIndex}
                  initial={{ color: "#6b7280" }}
                  animate={
                    index === activeStep
                      ? { color: "#ffffff" }
                      : index < activeStep
                        ? { color: "#ffffff" }
                        : { color: "#6b7280" }
                  }
                  transition={{
                    delay: index === activeStep ? wordIndex * 0.15 : 0,
                    duration: 0.5,
                    ease: "easeInOut",
                  }}
                >
                  {word}
                </motion.span>
              ))}
            </motion.div>
          </div>
        ))}
      </div>

      {/* Optional: Success message when all steps complete */}
      {/* {activeStep >= steps.length && (
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-green-400 text-xl font-semibold mt-4"
        >
          All set! Taking you to your dashboard...
        </motion.div>
      )} */}
    </motion.div>
  );
};

export default ProfileOnboardingFlow;
