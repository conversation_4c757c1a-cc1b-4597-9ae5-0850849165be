import React from "react";

// Task templates
const messageTemplates = {
  invitation: [
    " {brand} wants you in their {campaign} campaign! Time to shine.",
    " {brand} just dropped you an invite for {campaign}. Are you in?",
    " You’ve caught {brand}’s eye, they want you in their {campaign} campaign!",
    " {brand} is calling, hop into their {campaign} campaign",
    " {brand} has invited you for their {campaign} campaign"
  ],
  accepted: [
    " {brand} gave you the green light for {campaign}. Time to make magic!",
    " {brand} just welcomed you to the {campaign} squad. Let’s go!",
    " {brand} has accepted your application for the {campaign} campaign",
    " You’re in! {brand} accepted you for their {campaign} campaign!",
    " Accepted and ready — your spot in {brand}’s {campaign} campaign is locked."
  ],
  approval: [
    " {brand} has approved your content for campaign {campaign}",
    " {brand} is ready to roll with your {campaign} content",
    " {brand} gave the green light for {campaign} content",
    " You're set for {campaign} content with {brand}",
    " Heads up from {brand} for {campaign} content"
  ],
  rejection: [
    " {brand} has rejected your content for campaign {campaign}",
    "✏️ Needs work for {campaign} content, says {brand}",
    " {brand} wants a redo for {campaign} content.",
    " Feedback from {brand} says {campaign} content needs a fix",
    " Try again for {campaign} content with {brand}"
  ],
  draft_pending: [
    " Don’t forget to send your draft for {brand}’s {campaign} campaign",
    " {brand} is waiting on your {campaign} draft",
    " The draft for {campaign} is still not submitted",
    " Draft due for {brand}’s {campaign}"
  ],
  schedule_pending: [
    " Schedule your approved post for {campaign} of {brand}",
    " {campaign} content for {brand} is ready but not scheduled",
    " {campaign} for {brand} is approved, just needs a time slot",
    " Don’t forget to schedule your {campaign} post for {brand}",
    " {campaign} content for {brand} is greenlit and waiting to go live"
  ],
  reply_pending: [
    " Still waiting for your reply to {brand}",
    " Don’t leave {brand} hanging; respond to their offer",
    " Reply pending on {brand}’s last message",
    " {brand} is waiting for your response",
    " Time to respond to {brand}’s offer for {campaign}"
  ]
};

const getRandomMessage = (type, brand, campaign) => {
  const templates = messageTemplates[type] || [];
  const template = templates[Math.floor(Math.random() * templates.length)];
  // Replace {brand} and {campaign} with bold spans
  return template
    .replace(/{brand}/g, `<span class="font-semibold text-white">${brand}</span>`)
    .replace(/{campaign}/g, `<span class="font-semibold text-white">${campaign}</span>`);
};

const TaskCard = ({ brand, campaign, logoUrl, messageType }) => (
  <div className="flex justify-start items-center gap-3 py-2.5 ">
    <img src={logoUrl} alt={brand} className="w-8 h-8 rounded-full" />
    <div>
      <p
        className="text-white text-16-regular"
        dangerouslySetInnerHTML={{ __html: getRandomMessage(messageType, brand, campaign) }}
      />
    </div>
  </div>
);

const Section = ({ title, tasks, ctaText, borderColorClass = "border-l-purple-500", titleColorClass = "bg-light-1" }) => (
  <div className={`flex flex-col gap-3 bg-gray-500 rounded-lg mb-4 p-3 shadow-inner border-l-4 ${borderColorClass}`}>
    <div className="flex justify-between items-start">
      <span className={`text-14-medium px-2.5 py-1.25 rounded-md text-gray-500 ${titleColorClass}`}>
        {title}
      </span>
    </div>
    <div className="flex flex-col">
      {tasks.map((task, i) => (
        <TaskCard key={i} {...task} />
      ))}
    </div>
    {ctaText && (
      <div className="text-right flex justify-end py-2">
        <button className="text-brand-500 hover:underline text-14-medium cursor-pointer">{ctaText}</button>
      </div>
    )}
  </div>
);


const TaskBoard = ({ data }) => (
  <div className="bg-gray-600 p-5 rounded-xl w-full flex gap-4 flex-col">
    <div className="">
      <h2 className="text-20-semibold text-gray-50 mb-2">Your Next Moves ⏳</h2>
      <p className="text-12-regular text-gray-50 mb-4">Your tasks, briefs, and edits — all in one place. Let's get things done!</p>
    </div>
    {data.sections.length > 0 ? (
      <div>
        {data.sections.map((section, idx) => (
          <Section
            key={idx}
            title={section.title}
            tasks={section.tasks}
            ctaText={section.ctaText}
            borderColorClass={section.borderColorClass}
            titleColorClass={section.titleColorClass}
          />
        ))}
      </div>
    ) : (
      <div className="bg-gray-600 rounded-lg p-6 text-center">
        <h3 className="text-white text-lg font-medium mb-1">✅ You’re all caught up!</h3>
        <p className="text-gray-300 text-sm">New assignments from brands will appear here as they arrive.</p>
      </div>
    )}
  </div>
);

export default TaskBoard;
