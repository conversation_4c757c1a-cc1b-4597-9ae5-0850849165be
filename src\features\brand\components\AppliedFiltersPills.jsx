import React, { useEffect, useRef, useState } from 'react';
import { Tooltip, Dropdown } from 'antd';
import { FaTimes } from 'react-icons/fa';

const capitalizeWords = (input) => {
    return input
        .split(',')
        .map(part =>
            part
                .trim()
                .split('_')
                .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
                .join(' ')
        )
        .join(', ');
};

const isNumberRange = (val) =>
    typeof val === 'string' && /^-?\d+(\.\d+)?\s*-\s*-?\d+(\.\d+)?$/.test(val);

const getDisplayValue = (val) => {
    if (isNumberRange(val)) return formatRange(val);
    return capitalizeWords(val);
};

const formatRange = (value) => {
    const [start, end] = value.split('-');
    return `${formatNumber(start)}-${formatNumber(end)}`;
};

const formatNumber = (num) => {
    const number = parseFloat(num);
    if (number >= 1_000_000) {
        return (number / 1_000_000).toFixed(number % 1_000_000 === 0 ? 0 : 1) + 'M';
    } else if (number >= 1_000) {
        return (number / 1_000).toFixed(number % 1_000 === 0 ? 0 : 1) + 'K';
    }
    return number.toString();
};

const AppliedFiltersPills = ({ filters, removeFilter }) => {
    const containerRef = useRef(null);
    const [pillWidths, setPillWidths] = useState({});
    const [containerWidth, setContainerWidth] = useState(0);

    const grouped = filters?.reduce((acc, filter) => {
        const key = filter.filterFor || 'audience';
        acc[key] = acc[key] || [];
        acc[key].push(filter);
        return acc;
    }, {}) || {};

    const groupCount = Object.keys(grouped).length;

    useEffect(() => {
        const updateWidth = () => {
            if (containerRef.current) {
                setContainerWidth(containerRef.current.offsetWidth);
            }
        };

        updateWidth();
        const resizeObserver = new ResizeObserver(updateWidth);
        resizeObserver.observe(containerRef.current);

        return () => resizeObserver.disconnect();
    }, []);

    const pillRefs = useRef({});

    useEffect(() => {
        const widths = {};
        Object.keys(pillRefs.current).forEach(key => {
            if (pillRefs.current[key]) {
                widths[key] = pillRefs.current[key].offsetWidth + 20; // include gap
            }
        });
        setPillWidths(widths);
    }, [filters, containerWidth]);

    const getVisibleCount = (filtersList) => {
        let usedWidth = 0;
        let count = 0;

        for (let i = 0; i < filtersList.length; i++) {
            const filter = filtersList[i];
            const width = pillWidths[filter.index] || 160; // fallback width estimate
            if (usedWidth + width <= (containerWidth / groupCount)) {
                usedWidth += width;
                count++;
            } else {
                break;
            }
        }

        return count;
    };

    return (
        <div ref={containerRef} className="flex w-full items-start gap-4 flex-wrap md:flex-nowrap">
            {Object.entries(grouped).map(([groupKey, groupFilters]) => {
                const visibleCount = getVisibleCount(groupFilters);
                const visible = groupFilters.slice(0, visibleCount);
                const hidden = groupFilters.slice(visibleCount);

                console.log('Visible filters:', hidden);

                return (
                    <div
                        key={groupKey}
                        className={`${groupCount === 1 ? 'w-full' : 'max-w-1/2'} flex items-center gap-2 flex-nowrap overflow-hidden`}
                    >
                        <span className="flex items-center capitalize text-gray-300 text-14-medium whitespace-nowrap">
                            {groupKey}:
                        </span>

                        {visible.map((filter, idx) => (
                            <div
                                key={filter.index}
                                ref={el => pillRefs.current[filter.index] = el}
                                className="border-1 border-gray-800 text-gray-200 text-sm px-3 py-1 rounded-full flex items-center gap-1 max-w-[160px] truncate cursor-default"
                            >
                                <Tooltip
                                    title={`${capitalizeWords(filter.filter)}: ${Array.isArray(filter.value)
                                        ? filter.value.map(v => `${v.filter}: ${getDisplayValue(v.value)}`).join(', ')
                                        : getDisplayValue(filter.value)
                                        }`}
                                    color='var(--color-gray-600)'
                                >
                                    <div className='flex truncate'>
                                        <span className="capitalize font-semibold">
                                            {getDisplayValue(filter.filter).length > 8
                                                ? getDisplayValue(filter.filter).slice(0, 8) + '..'
                                                : getDisplayValue(filter.filter)}
                                            : &nbsp;
                                        </span>
                                        <div className="flex capitalize truncate line-clamp-1">
                                            {Array.isArray(filter.value)
                                                ? filter.value.map((sub, idx) => (
                                                    <span key={idx}>{sub.filter}: {getDisplayValue(sub.value)}</span>
                                                ))
                                                : getDisplayValue(filter.value)}
                                        </div>
                                    </div>
                                </Tooltip>
                                <span
                                    onClick={() => removeFilter(filter.index)}
                                    className="text-gray-400 hover:text-white cursor-pointer mt-0.5"
                                >
                                    <FaTimes />
                                </span>
                            </div>
                        ))}

                        {hidden.length > 0 && (
                            <div className="flex items-center gap-1 bg-gray-800 text-gray-300 text-sm px-2 py-1 rounded-full shrink-0">
                                +{hidden.length}
                                <Dropdown
                                    trigger={['click']}
                                    placement="bottomLeft"
                                    // overlayStyle={{ transform: 'translateX(-150px)' }}
                                    align={{ offset: [-175, 10] }} // shift left by 150px
                                    dropdownRender={() => (
                                        <div className="flex flex-col gap-1 p-2 bg-gray-900 rounded-lg w-[200px]">
                                            {hidden.map((filter, i) => (
                                                <div
                                                    key={i}
                                                    className="flex justify-between items-center px-3 py-1 rounded-md hover:bg-gray-500 text-sm text-white gap-1"
                                                >
                                                    <span className='text-gray-300 flex gap-1'>
                                                        <span className='text-gray-100'>{capitalizeWords(filter.filter)}:{' '}</span>
                                                        {Array.isArray(filter.value)
                                                            ? filter.value.map((sub, idx) => (
                                                                <div key={idx} className="flex gap-1 items-center">
                                                                    <span className="text-gray-200 text-sm capitalize">
                                                                        {sub.filter.length > 6 ? sub.filter.slice(0, 6) + '..' : sub.filter}:
                                                                    </span>

                                                                    {sub.value.length > 10 || isNumberRange(sub.value) ? (
                                                                        <div className='text-[12px] mt-0.50 whitespace-nowrap'>{getDisplayValue(sub.value).slice(0, 10) + '..'}</div>
                                                                    ) : (
                                                                        <span className="text-gray-300 text-[13px] capitalize line-clamp-1">
                                                                            {getDisplayValue(sub.value)}
                                                                        </span>
                                                                    )}
                                                                </div>
                                                            ))
                                                            :
                                                            <Tooltip title={getDisplayValue(filter.value)}>
                                                                <span className='line-clamp-1'>
                                                                    {getDisplayValue(filter.value)}
                                                                </span>
                                                            </Tooltip>
                                                        }
                                                    </span>
                                                    <span
                                                        onClick={() => removeFilter(filter.index)}
                                                        className="text-gray-400 hover:text-white cursor-pointer mt-0.5"
                                                    >
                                                        <FaTimes />
                                                    </span>
                                                </div>
                                            ))}
                                        </div>
                                    )}
                                >
                                    <div className="flex items-center justify-center cursor-pointer">
                                        <i className="fi fi-br-angle-small-down mt-1 hover:text-gray-100"></i>
                                    </div>
                                </Dropdown>
                            </div>
                        )}
                    </div>
                );
            })}
        </div>
    );
};

export default AppliedFiltersPills;
