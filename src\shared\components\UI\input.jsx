import { forwardRef, useState } from "react";
import { cn } from "../../../lib/utils";

const Input = forwardRef(
    (
        {
            label,
            icon,
            error,
            disabled,
            value,
            placeholder,
            className,
            ...props
        },
        ref
    ) => {
        const [touched, setTouched] = useState(false);
        const hasValue = !!value?.toString().trim();

        const showError = touched && error;

        return (
            <div className="flex flex-col gap-1.5 w-full">
                {label && (
                    <label className="text-14-medium  text-foreground">
                        {label}
                    </label>
                )}

                <div
                    className={cn(
                        "flex items-center h-[44px] w-full rounded-lg px-3 py-2 bg-background",
                        disabled ? "opacity-50 cursor-not-allowed" : "",
                        "focus-within:ring-[0.5px] focus-within:ring-offset-1 transition-all duration-200",
                        "hover:shadow-[0px_0px_0px_3px_rgba(0,200,255,0.14)]",
                        showError
                            ? "border-1 border-red-2 focus-within:ring-red-2 "
                            : "border  border-input focus-within:ring-ring",
                        className
                    )}
                >
                    {icon && (
                        <img
                            src={icon}
                            alt="icon"
                            className={cn(
                                "mr-2 h-4 w-4",
                                hasValue ? "opacity-100" : "opacity-70"
                            )}
                        />
                    )}
                    <input
                        ref={ref}
                        type="email"
                        disabled={disabled}
                        value={value}
                        placeholder={placeholder}
                        onFocus={() => setTouched(false)}
                        onBlur={() => setTouched(true)}
                        onChange={(e) => {
                            setTouched(true);
                            props.onChange?.(e);
                        }}
                        className="w-full bg-transparent text-sm outline-none placeholder:text-muted-foreground focus:bg-transparent"
                        {...props}
                    />
                </div>

                {showError && <p className="text-sm text-red-2">{error}</p>}
            </div>
        );
    }
);

Input.displayName = "Input";
export { Input };
