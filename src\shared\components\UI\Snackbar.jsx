import React, { useEffect, useState, useRef } from 'react';
// eslint-disable-next-line no-unused-vars
import { motion } from 'framer-motion';
import { FiX, FiCheckCircle, FiInfo, FiAlertTriangle } from 'react-icons/fi';

const typeStyles = {
  success: 'bg-green-2',
  error: 'bg-red-2',
  info: 'bg-brand-500',
  warning: 'bg-yellow-500',
};

const typeIcons = {
  success: <FiCheckCircle size={20} className="mt-0.5" />,
  error: <FiAlertTriangle size={20} className="mt-0.5" />,
  info: <FiInfo size={20} className="mt-0.5" />,
  warning: <FiAlertTriangle size={20} className="mt-0.5" />,
};

const Snackbar = ({ message, type = 'info', onClose, duration = 3000 }) => {
  const [progress, setProgress] = useState(100);
  const timerRef = useRef(null);
  const elapsedRef = useRef(0);
  const isHoveredRef = useRef(false);

  const handleMouseEnter = () => {
    isHoveredRef.current = true;
  };

  const handleMouseLeave = () => {
    isHoveredRef.current = false;
  };

  useEffect(() => {
    const interval = 50;

    const startTimer = () => {
      timerRef.current = setInterval(() => {
        if (!isHoveredRef.current) {
          elapsedRef.current += interval;
          const newProgress = 100 - (elapsedRef.current / duration) * 100;

          if (newProgress <= 0) {
            clearInterval(timerRef.current);
            onClose?.(); // Trigger close when done
          }

          setProgress(Math.max(0, newProgress));
        }
      }, interval);
    };

    startTimer();

    return () => clearInterval(timerRef.current);
  }, [duration, onClose]);

  return (
    <motion.div
      initial={{ opacity: 0, y: 40 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: 40, transition: { duration: 0.3 } }}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      className={`fixed bottom-5 left-1/2 transform -translate-x-1/2 px-4 py-3 rounded-md shadow-lg text-white text-sm z-50 flex items-center justify-between gap-4 min-w-[250px] max-w-[90%] ${typeStyles[type]}`}
      style={{ minWidth: '300px' }}
    >
      {/* Icon + Message */}
      <div className="flex items-center gap-2 flex-1 mb-0.5">
        {typeIcons[type]}
        <span>{message}</span>
      </div>

      {/* Close Button */}
      <button
        onClick={onClose}
        className="text-white hover:text-gray-200 transition cursor-pointer"
        aria-label="Close"
      >
        <FiX size={18} />
      </button>

      {/* Progress Bar */}
      <div className="absolute bottom-0 left-0 h-1 bg-white/30 w-full">
        <div
          className="h-full bg-white transition-all"
          style={{ width: `${progress}%` }}
        />
      </div>
    </motion.div>
  );
};

export default Snackbar;
