import React, { useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import InviteGif from '@assets/gif/rocket.mp4';

const LaunchScreen = () => {
    const navigate = useNavigate();
    const videoRef = useRef(null);

    const handleVideoEnd = () => {
        setTimeout(() => {
            navigate('/brand/dashboard'); 
        }, 1000); 
    };

    return (
        <div className='flex flex-col items-center justify-center h-screen bg-[#222222] text-white p-4'>
            <div className='flex flex-col items-center text-center text-gray-50 gap-5'>
                <h1 className='text-30-bold'>Your brand is ready for lift-off ✨</h1>
                <p className='text-20-bold mb-7.5'>
                    Explore creators, review content, and launch <br />
                    campaigns with ease.
                </p>
                <video
                    ref={videoRef}
                    src={InviteGif}
                    autoPlay
                    muted
                    playsInline
                    onEnded={handleVideoEnd}
                    className="w-55 h-55 bg-transparent"
                >
                    Your browser does not support the video tag.
                </video>
            </div>
        </div>
    );
};

export default LaunchScreen;
