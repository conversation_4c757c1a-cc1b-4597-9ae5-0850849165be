/**
 * Discovery & Analytics Service Alova Instance
 * 
 * This instance handles all discovery and analytics-related API calls including:
 * - Creator discovery and search
 * - Profile analytics
 * - Filter management
 * - Saved filter sets
 * - Dashboard KPIs
 */

import { createAlova } from 'alova';
import FetchAdapter from 'alova/fetch';
import ReactHook from 'alova/react';
import { apiConfig, SERVICE_TYPES } from '../config/apiConfig';

/**
 * Token management utilities (shared with auth service)
 */
const getAuthToken = () => localStorage.getItem('auth_token');
const getRefreshToken = () => localStorage.getItem('refresh_token');
const clearTokens = () => {
  localStorage.removeItem('auth_token');
  localStorage.removeItem('refresh_token');
  localStorage.removeItem('user');
  localStorage.removeItem('allocatedBrands');
  localStorage.removeItem('organizationBrands');
  localStorage.removeItem('organisationId');
};

/**
 * Redirect to appropriate login page
 */
const redirectToLogin = (requestUrl = '') => {
  const isBrand = requestUrl.includes('/brand');
  if (isBrand) {
    window.location.href = '/brand/signin';
  } else {
    window.location.href = '/influencer/login';
  }
};

/**
 * Cross-service token refresh using auth service
 */
const refreshTokenViaAuthService = async (method) => {
  const refreshToken = getRefreshToken();
  if (!refreshToken) {
    console.error('[DISCOVERY SERVICE] No refresh token available');
    throw new Error('No refresh token available');
  }

  try {
    // Call auth service to refresh token
    const refreshResponse = await fetch(`${apiConfig.authServiceURL}/auth/refresh-token`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...(apiConfig.environment === 'development' && {
          'ngrok-skip-browser-warning': 'true'
        })
      },
      body: JSON.stringify({ refresh_token: refreshToken }),
    });

    const refreshData = await refreshResponse.json();

    console.log('Refresh response:', refreshResponse);

    if (refreshResponse.ok && refreshData.data?.access_token) {
      const { access_token, refresh_token } = refreshData.data;
      
      // Update localStorage
      localStorage.setItem('auth_token', access_token);
      if (refresh_token) {
        localStorage.setItem('refresh_token', refresh_token);
      }
      
      // Update the original request with new token
      method.config.headers['Authorization'] = `Bearer ${access_token}`;
      
      // Retry the original request
      return await method.send();
    } else {
      throw new Error(refreshData.message || 'Token refresh failed');
    }
  } catch (error) {
    console.error('[DISCOVERY SERVICE] Token refresh error:', error);
    clearTokens();
    redirectToLogin(method.url);
    throw error;
  }
};

/**
 * Create Discovery & Analytics Service Alova Instance
 */
const discoveryInstance = createAlova({
  baseURL: apiConfig.discoveryServiceURL,
  requestAdapter: FetchAdapter(),
  statesHook: ReactHook,

  // Default request options
  defaultOptions: {
    headers: {
      ...apiConfig.common.headers,
      ...(apiConfig.environment === 'development' && {
        'ngrok-skip-browser-warning': 'true'
      })
    },
  },
  withCredentials: apiConfig.common.withCredentials,

  // Global response handling
  responded: {
    // Transform successful responses
    onSuccess: async (response, method) => {
      console.log(`[DISCOVERY SERVICE] API response received:`, {
        url: method.url,
        status: response.status,
        timestamp: new Date().toISOString()
      });

      const json = await response.json();

      // Handle 401 Unauthorized - attempt token refresh via auth service
      if (response.status === 401) {
        console.warn('[DISCOVERY SERVICE] Access token expired. Attempting token refresh via auth service...');
        
        try {
          return await refreshTokenViaAuthService(method);
        } catch (refreshError) {
          console.error('[DISCOVERY SERVICE] Token refresh failed:', refreshError);
          // Let the error fall through to be handled by the component
        }
      }

      return {
        data: json,
        status: response.status,
        service: SERVICE_TYPES.DISCOVERY
      };
    },

    // Handle request errors
    onError: (error, method) => {
      console.error(`[DISCOVERY SERVICE] API request failed:`, {
        url: method.url,
        error: error.message,
        timestamp: new Date().toISOString()
      });
      throw error;
    },
  },

  // Add authorization headers for all requests
  beforeRequest(method) {
    console.log(`[DISCOVERY SERVICE] Preparing request:`, {
      url: method.url,
      method: method.type,
      timestamp: new Date().toISOString()
    });

    // All discovery service endpoints require authentication
    const token = getAuthToken();
    if (token) {
      method.config.headers['Authorization'] = `Bearer ${token}`;
    } else {
      console.warn('[DISCOVERY SERVICE] No auth token available for request');
      // Don't throw error here, let the server respond with 401
    }

    // Add development headers
    if (apiConfig.environment === 'development') {
      method.config.headers['ngrok-skip-browser-warning'] = 'true';
    }
  }
});

/**
 * Discovery instance utility functions
 */
export const discoveryUtils = {
  getAuthToken,
  getRefreshToken,
  clearTokens,
  redirectToLogin,
  refreshTokenViaAuthService,
  
  // Service health check
  checkHealth: () => apiConfig.checkServiceHealth(SERVICE_TYPES.DISCOVERY),
  
  // Get service configuration
  getConfig: () => apiConfig.getServiceConfig(SERVICE_TYPES.DISCOVERY),
  
  // Cache management utilities
  clearCache: async () => {
    try {
      await discoveryInstance.Delete('/filters/cache').send();
      await discoveryInstance.Post('/discovery/cache/cleanup').send();
      console.log('[DISCOVERY SERVICE] Cache cleared successfully');
    } catch (error) {
      console.error('[DISCOVERY SERVICE] Cache clear failed:', error);
    }
  },
  
  // Get cache statistics
  getCacheStats: async (platform = null) => {
    try {
      const params = platform ? `?platform=${platform}` : '';
      const response = await discoveryInstance.Get(`/discovery/cache/stats${params}`).send();
      return response.data;
    } catch (error) {
      console.error('[DISCOVERY SERVICE] Failed to get cache stats:', error);
      return null;
    }
  }
};

export default discoveryInstance;
