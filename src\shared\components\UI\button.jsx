import React from "react";
import { cn } from "../../../lib/utils";

const VARIANTS = {
  default: "bg-white text-black border border-gray-300 hover:bg-gray-100",
  filled: "bg-gray-800 text-white hover:bg-gray-700",
  outlined: "border border-cyan-400 text-white hover:bg-gray-800",
  selected: "border border-cyan-400 text-white bg-gray-800",
};

const SocialButton = ({
  icon,
  label,
  variant = "default",
  selected = false,
  className = "",
  type = "button",
  ...props
}) => {
  const baseClasses =
    "relative grid place-items-center px-4 py-2 h-[44px] rounded-lg font-medium transition-all duration-200 cursor-pointer";

  return (
    <button
      type={type}
      className={cn(baseClasses, VARIANTS[variant], className)}
      {...props}
    >
      <span className=" flex items-center justify-center w-full h-full">
        {icon && <span className="h-6 w-6 flex items-center justify-center">{icon}</span>}
        {label && (
          <span className="ml-2">{label}</span>
        )}
        {!label && (
          <span className="sr-only">Label</span>
        )}
      </span>
      {variant === "selected" && selected && (
        <span className="absolute -bottom-2 left-1/2 transform -translate-x-1/2 bg-cyan-400 w-4 h-4 rounded-full flex items-center justify-center">
          <div className="bg-black w-2 h-2 rounded-full" />
        </span>
      )}
    </button>
  );
};

export default SocialButton;
