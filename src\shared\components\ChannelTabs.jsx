import React, { useRef, useLayoutEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { selectAllChannelsWithIcons } from '@/app/store/slices/systemSlice';
// eslint-disable-next-line no-unused-vars
import { motion } from 'framer-motion';

export default function ChannelTabs({ activeTab, setActiveTab }) {
    const channels = useSelector((state) => selectAllChannelsWithIcons(state));
    console.log('Channels:', channels);
    const containerRef = useRef(null);
    const [underlineProps, setUnderlineProps] = useState({ width: 0, left: 0 });

    useLayoutEffect(() => {
        const container = containerRef.current;
        if (!container) return;

        const activeEl = container.querySelector(`[data-id='${activeTab}']`);
        if (activeEl) {
            const { offsetLeft: left, offsetWidth: width } = activeEl;
            setUnderlineProps({ left, width });
        }
    }, [activeTab, channels]);

    return (
        <div className="relative w-fit">
            <div className="flex items-start h-17" ref={containerRef}>
                {channels.map((channel) => {
                    const Icon = channel.icon.component; // 👈 extract the component

                    return (
                        <div
                            key={channel.name}
                            data-id={channel.name.toLowerCase()}
                            onClick={() => setActiveTab(channel.name.toLowerCase())}
                            className={`flex h-full gap-2 items-center py-5 px-4 text-16-semibold cursor-pointer ${activeTab === channel.name.toLowerCase()
                                    ? 'text-brand-500'
                                    : 'text-gray-400'
                                }`}
                        >
                            {Icon && <Icon className=" h-6 w-7" />} {/* 👈 Render icon if exists */}
                            {channel.name}
                        </div>
                    );
                })}

            </div>

            {/* Animated Underline */}
            <motion.div
                className="absolute bottom-0 h-1 bg-brand-500 rounded-full"
                layout
                transition={{ type: 'spring', stiffness: 500, damping: 30 }}
                animate={{
                    width: underlineProps.width,
                    left: underlineProps.left
                }}
            />
        </div>
    );
}
