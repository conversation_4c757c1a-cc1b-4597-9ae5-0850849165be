import React, { useState, useEffect, useRef, useMemo } from 'react';
import PropTypes from 'prop-types';
import { cn } from '../../lib/utils';
import { FaTimes } from 'react-icons/fa';


/**
 * A versatile filter component supporting checkbox, radio button, and multilevel-checkbox types
 * @param {Object} props - Component props
 * @param {string} props.filter - The filter name (e.g., 'age', 'Follower Growth')
 * @param {Object} props.filterConfig - Configuration for the filter including type, options
 * @param {Array|Object} props.initialValue - Initial selected value(s)
 * @param {Object} props.savedFilter - A previously saved filter to load
 * @param {Function} props.onApplyFilter - Callback when Apply button is clicked
 * @param {string} props.className - Additional CSS classes
 */
const FilterComponent = ({
    onClose,
    filter,
    filterConfig = null,
    initialValue = null,
    savedFilter = null,
    onApplyFilter,
    className = "absolute w-70 h-100",
}) => {
    const filterpopupRef = useRef(null);

    // console.log("Filter ", filterConfig);
    // console.log("saved Filter ", savedFilter);

    // Get filter type from filterConfig or use default
    const filterType = filterConfig?.type || 'checkbox';

    // For radio, track a single value
    const [selectedValue, setSelectedValue] = useState(initialValue);

    // For checkbox, track an array of values
    const [selectedMultiValues, setSelectedMultiValues] = useState(
        initialValue && Array.isArray(initialValue) ? initialValue : []
    );

    // For multilevel-checkbox (like Follower Growth example), track an object with sub-filters
    const [multiSelection, setMultiSelection] = useState(
        initialValue && Array.isArray(initialValue)
            ? initialValue
            : []
    );

    // Get options from filterConfig based on filter type
    const options = useMemo(() => filterConfig?.options || [], [filterConfig]);

    // For search functionality
    const [searchTerm, setSearchTerm] = useState('');
    const [filteredOptions, setFilteredOptions] = useState(options);

    // Track collapsed state for each suboption in multilevel filters
    const [collapsedSubOptions, setCollapsedSubOptions] = useState(
        options.reduce((acc, option) => {
            if (option.subOptionName) {
                acc[option.subOptionName] = option.collapsed || false;
            }
            return acc;
        }, {})
    );

    // Track local state for draft changes
    const [draftValue, setDraftValue] = useState(selectedValue);
    const [draftMultiValues, setDraftMultiValues] = useState(selectedMultiValues);
    const [draftMultiSelection, setDraftMultiSelection] = useState(multiSelection);

    // For min-max range inputs
    const [minValue, setMinValue] = useState('');
    const [maxValue, setMaxValue] = useState('');

    // Close popup if clicked outside
    useEffect(() => {
        const handleClickOutside = (e) => {
            if (filterpopupRef.current && !filterpopupRef.current.contains(e.target)) {
                onClose();
            }
        };
        document.addEventListener('mousedown', handleClickOutside);
        return () => document.removeEventListener('mousedown', handleClickOutside);
    }, [onClose]);



    // Effect to update state when initialValue changes
    useEffect(() => {
        if (initialValue !== null) {
            if (filterType === 'multilevel-checkbox') {
                setMultiSelection(Array.isArray(initialValue) ? initialValue : []);
                setDraftMultiSelection(Array.isArray(initialValue) ? initialValue : []);
            } else if (filterType === 'checkbox') {
                const initialArray = Array.isArray(initialValue) ? initialValue : (initialValue ? [initialValue] : []);
                setSelectedMultiValues(initialArray);
                setDraftMultiValues(initialArray);
            } else {
                setSelectedValue(initialValue);
                setDraftValue(initialValue);
            }
        }
    }, [initialValue, filterType]);

    // Effect to initialize from savedFilter if present
    useEffect(() => {
        if (savedFilter && savedFilter.filter === filter) {
            if (filterType === 'multilevel-checkbox') {
                if (Array.isArray(savedFilter.value)) {
                    // Process the saved filter to ensure all values are arrays
                    const processedFilter = savedFilter.value.map(item => {
                        if (typeof item.value === 'string' && item.value.includes(',')) {
                            return {
                                ...item,
                                value: item.value.split(',').map(v => v.trim())
                            };
                        }
                        return item;
                    });

                    setMultiSelection(processedFilter);
                    setDraftMultiSelection(processedFilter);
                } else {
                    setMultiSelection([]);
                    setDraftMultiSelection([]);
                }
            } else if (filterType === 'checkbox') {
                // Handle comma-separated string
                if (typeof savedFilter.value === 'string') {
                    const values = savedFilter.value.split(',').map(v => v.trim()).filter(Boolean);
                    setSelectedMultiValues(values);
                    setDraftMultiValues(values);
                }
            } else if (filterType === 'radio-button') {
                setSelectedValue(savedFilter.value);
                setDraftValue(savedFilter.value);
            }

            // Handle min-max value if it's a range
            if (typeof savedFilter.value === 'string' && savedFilter.value.includes('-')) {
                const [min, max] = savedFilter.value.split('-');
                if (!isNaN(min) && !isNaN(max)) {
                    setMinValue(min);
                    setMaxValue(max);
                }
            }
        }
    }, [savedFilter, filter, filterType]);

    // Effect to filter options based on search term
    useEffect(() => {
        if (!searchTerm.trim()) {
            setFilteredOptions(options);
            return;
        }

        const searchLower = searchTerm.toLowerCase();

        if (filterType === 'multilevel-checkbox') {      // Filter multilevel options
            const filtered = options.map(group => {
                // Filter subOptions within each group
                const filteredSubOptions = group.subOptions.filter(option =>
                    option.label.toLowerCase().includes(searchLower) ||
                    (option.description && option.description.toLowerCase().includes(searchLower))
                );

                // Return a new group object with filtered subOptions
                return {
                    ...group,
                    subOptions: filteredSubOptions
                };
            }).filter(group => group.subOptions.length > 0); // Only keep groups that have matching options

            setFilteredOptions(filtered);
        } else {
            // Filter simple options
            const filtered = options.filter(option => {
                const optionLabel = typeof option === 'object' ? option.label : option;
                const optionDesc = typeof option === 'object' && option.description ? option.description : '';

                return optionLabel.toLowerCase().includes(searchLower) ||
                    optionDesc.toLowerCase().includes(searchLower);
            });
            setFilteredOptions(filtered);
        }
    }, [searchTerm, options, filterType]);

    const handleSingleFilterChange = (value) => {
        if (filterType === 'checkbox') {
            // Clear min-max inputs when checkboxes are clicked
            setMinValue('');
            setMaxValue('');

            // For checkbox, toggle multiple selection
            setDraftMultiValues(prev => {
                if (prev.includes(value)) {
                    // Remove value if already selected
                    return prev.filter(v => v !== value);
                } else {
                    // Add value if not selected
                    return [...prev, value];
                }
            });
        } else if (filterType === 'radio-button') {
            // For radio, set the new value
            setDraftValue(value);
        }
    };
    const handleMultiFilterChange = (subFilter, value, subFilterType) => {
        // Find if we already have this sub-filter in our selection
        const existingSubFilterIndex = draftMultiSelection.findIndex(item =>
            item.filter === subFilter
        );

        // console.log(`handleMultiFilterChange - ${subFilter} - ${value}:`, subFilterType, existingSubFilterIndex);

        let updatedSelection = [...draftMultiSelection];

        if (subFilterType === 'checkbox') {
            // Handle multi-select values like percentages
            if (existingSubFilterIndex >= 0) {
                // If subfilter exists, check if value exists
                const existingValues = updatedSelection[existingSubFilterIndex].value;

                // If it's a string, we need to convert to array first
                const valueArray = typeof existingValues === 'string'
                    ? existingValues.split(',')
                    : Array.isArray(existingValues)
                        ? existingValues
                        : [existingValues];
                if (valueArray.includes(value)) {
                    // If value exists, remove it
                    const newValues = valueArray.filter(v => v !== value);

                    // If no more values, remove the subfilter entirely
                    if (newValues.length === 0) {
                        updatedSelection = updatedSelection.filter((_, i) => i !== existingSubFilterIndex);
                    } else {
                        // Otherwise update with the remaining values
                        updatedSelection[existingSubFilterIndex] = {
                            ...updatedSelection[existingSubFilterIndex],
                            value: newValues.join(',')
                        };
                    }
                } else {
                    // If value doesn't exist, add it
                    const newValues = [...valueArray, value];
                    updatedSelection[existingSubFilterIndex] = {
                        ...updatedSelection[existingSubFilterIndex],
                        value: newValues.join(',')
                    };
                }
            } else {
                // If subfilter doesn't exist, add it with the value
                updatedSelection.push({
                    filter: subFilter,
                    value: value
                });
            }
        } else if (subFilterType === 'radio-button') {
            // Handle radio-type subfilter values like time intervals
            if (existingSubFilterIndex >= 0) {
                // Update existing subfilter
                updatedSelection[existingSubFilterIndex] = {
                    ...updatedSelection[existingSubFilterIndex],
                    value: value
                };
            } else {
                // Add new subfilter
                updatedSelection.push({
                    filter: subFilter,
                    value: value
                });
            }
        }

        setDraftMultiSelection(updatedSelection);
    };
    // Helper to check if a value is selected in multi-checkbox
    const isMultiValueSelected = (subFilter, value) => {
        const subFilterData = draftMultiSelection.find(item => item.filter === subFilter);
        if (!subFilterData) return false;

        const values = Array.isArray(subFilterData.value)
            ? subFilterData.value
            : typeof subFilterData.value === 'string'
                ? subFilterData.value.split(',').map(v => v.trim())
                : [subFilterData.value];

        // console.log(`Checking ${subFilter} - ${value}:`, values, values.includes(value));
        return values.includes(value);
    };// Render a basic checkbox option
    const renderCheckboxOption = (option) => {
        const { value, label } = typeof option === 'object' ? option : { value: option, label: option };
        const isChecked = draftMultiValues.includes(value);

        return (
            <div key={value} className="flex items-center justify-between mb-2">
                <div>
                    <input
                        type="checkbox"
                        id={`${filter}-${value}`}
                        className="mr-2 h-4 w-4"
                        checked={isChecked}
                        onChange={() => handleSingleFilterChange(value)}
                    />
                    <label htmlFor={`${filter}-${value}`} className="text-sm">
                        {label}
                    </label>
                </div>
                <span className='text-sm text-gray-500'>{option.description}</span>
            </div>
        );
    };
    // Render a radio button option
    const renderRadioOption = (option) => {
        const { value, label } = typeof option === 'object' ? option : { value: option, label: option };
        const isChecked = draftValue === value;

        return (
            <div key={value} className="flex items-center justify-between mb-2">
                <div>
                    <input
                        type="radio"
                        id={`${filter}-${value}`}
                        name={filter}
                        className="mr-2 h-4 w-4"
                        checked={isChecked}
                        onChange={() => handleSingleFilterChange(value)}
                    />
                    <label htmlFor={`${filter}-${value}`} className="text-sm">
                        {label}
                    </label>
                </div>
                <span className='text-sm text-gray-500'>{option.description}</span>
            </div>
        );
    };
    // Toggle the collapse state of a suboption section
    const toggleSubOptionCollapse = (subOptionName) => {
        setCollapsedSubOptions(prev => ({
            ...prev,
            [subOptionName]: !prev[subOptionName]
        }));
    };
    // Handle selecting/deselecting all options in a subgroup
    const handleSelectAllSubOptions = (subOptionName, subOptions, event) => {
        // Prevent the click from also toggling collapse state
        event.stopPropagation();            // Check if all or some options are currently selected
        const allValues = subOptions.map(opt => opt.value);
        const selectedValues = allValues.filter(value => isMultiValueSelected(subOptionName, value));
        const allSelected = selectedValues.length === allValues.length;

        // console.log(`handleSelectAllSubOptions - ${subOptionName}:`, {
        //     allValues,
        //     selectedValues,
        //     allSelected,
        //     checked: event.target.checked
        // });

        if (allSelected) {
            // If all are selected, deselect all
            const updatedSelection = draftMultiSelection.filter(item => item.filter !== subOptionName);
            setDraftMultiSelection(updatedSelection);
        } else {
            // Otherwise, select all
            const existingSubFilterIndex = draftMultiSelection.findIndex(item =>
                item.filter === subOptionName
            );

            let updatedSelection = [...draftMultiSelection];
            if (existingSubFilterIndex >= 0) {
                // Update existing entry
                updatedSelection[existingSubFilterIndex] = {
                    filter: subOptionName,
                    value: allValues.join(', ')
                };
            } else {
                // Create new entry
                updatedSelection.push({
                    filter: subOptionName,
                    value: allValues.join(', ')
                });
            }

            setDraftMultiSelection(updatedSelection);
        }
    };
    // Render a multilevel-checkbox group (like Follower Growth with Percentage and Time Interval)
    const renderMultiCheckboxGroup = () => {
        return (
            <div className="space-y-4 ">
                {filteredOptions.map((subOption) => (
                    <div key={subOption.subOptionName} className="pt-3">
                        <div
                            className="flex items-center justify-between cursor-pointer mb-2"
                            onClick={() => toggleSubOptionCollapse(subOption.subOptionName)}
                        >
                            <div className="flex items-center">
                                {subOption.checkboxEnabled && (
                                    <input
                                        type="checkbox"
                                        className="mr-2 h-4 w-4"
                                        id={`${filter}-${subOption.subOptionName}-main`} checked={subOption.subOptions.length > 0 &&
                                            subOption.subOptions.every(opt => isMultiValueSelected(subOption.subOptionName, opt.value))}
                                        onChange={(e) => handleSelectAllSubOptions(subOption.subOptionName, subOption.subOptions, e)}
                                        onClick={(e) => e.stopPropagation()}
                                    />
                                )}
                                <h4 className="font-medium">{subOption.subOptionName}</h4>
                            </div>
                            <svg
                                className={`h-5 w-5 text-gray-400 transform transition-transform ${collapsedSubOptions[subOption.subOptionName] ? '' : 'rotate-180'}`}
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke="currentColor"
                            >
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                            </svg>
                        </div>

                        {!collapsedSubOptions[subOption.subOptionName] && (
                            <div className="pl-2 overflow-auto">
                                {subOption.subOptionType === 'checkbox' ? (
                                    // Render checkboxes that can have multiple selections
                                    subOption.subOptions.map((opt) => {
                                        const isChecked = isMultiValueSelected(subOption.subOptionName, opt.value);

                                        return (
                                            <div key={opt.value} className="flex items-center justify-between mb-2">
                                                <div>
                                                    <input
                                                        type="checkbox"
                                                        id={`${filter}-${subOption.subOptionName}-${opt.value}`}
                                                        className="mr-2 h-4 w-4"
                                                        checked={isChecked}
                                                        onChange={() => handleMultiFilterChange(subOption.subOptionName, opt.value, 'checkbox')}
                                                    />
                                                    <label htmlFor={`${filter}-${subOption.subOptionName}-${opt.value}`} className="text-sm">
                                                        {opt.label} {opt.description && <span className="text-gray-500">({opt.description})</span>}
                                                    </label>
                                                </div>
                                                <span className='text-sm text-gray-500'>{subOption.description}</span>
                                            </div>
                                        );
                                    })
                                ) : subOption.subOptionType === 'radio-button' && (
                                    // Render radio buttons for exclusive selection
                                    subOption.subOptions.map((opt) => {
                                        const subFilterData = multiSelection.find(item => item.filter === subOption.subOptionName);
                                        const isChecked = subFilterData?.value === opt.value;

                                        return (
                                            <div key={opt.value} className="flex items-center justify-between mb-2">
                                                <div>
                                                    <input
                                                        type="radio"
                                                        id={`${filter}-${subOption.subOptionName}-${opt.value}`}
                                                        name={`${filter}-${subOption.subOptionName}`}
                                                        className="mr-2 h-4 w-4"
                                                        checked={isChecked}
                                                        onChange={() => handleMultiFilterChange(subOption.subOptionName, opt.value, 'radio-button')}
                                                    />
                                                    <label htmlFor={`${filter}-${subOption.subOptionName}-${opt.value}`} className="text-sm">
                                                        {opt.label} {opt.description && <span className="text-gray-500">({opt.description})</span>}
                                                    </label>
                                                </div>
                                                <span className='text-sm text-gray-500'>{subOption.description}</span>
                                            </div>
                                        );
                                    })
                                )}
                            </div>
                        )}
                    </div>
                ))}
            </div>
        );
    };  // Handle Apply button click
    const handleApplyFilter = () => {
        // Call the callback with the updated values
        if (onApplyFilter) {
            if (filterType === 'multilevel-checkbox') {
                if (draftMultiSelection.length > 0) {
                    onApplyFilter({
                        filter,
                        value: draftMultiSelection
                    });
                } else {
                    onApplyFilter(null);
                }
            } else if (filterType === 'checkbox') {
                // Handle min-max range inputs if both are filled and minmax is true
                if (filterConfig?.minmax && minValue !== '' && maxValue !== '') {
                    onApplyFilter({
                        filter,
                        value: `${minValue}-${maxValue}`,
                        isCustomRange: true
                    });
                } else if (draftMultiValues.length > 0) {
                    // Return values as comma-separated string instead of array
                    onApplyFilter({
                        filter,
                        value: draftMultiValues.join(',')
                    });
                } else {
                    onApplyFilter(null);
                }
            } else {
                if (draftValue !== null) {
                    onApplyFilter({
                        filter,
                        value: draftValue
                    });
                } else {
                    onApplyFilter(null);
                }
            }
        }
    };

    // Handle Clear button click
    const handleClearFilter = () => {
        setDraftValue(null);
        setDraftMultiValues([]);
        setDraftMultiSelection([]);
        setSearchTerm('');
        setMinValue('');
        setMaxValue('');
    };

    // Handle min-max input changes
    const handleMinValueChange = (e) => {
        const value = e.target.value;
        // Clear checkbox selections when min-max values are entered
        if (value !== '') {
            setDraftMultiValues([]);
        }
        setMinValue(value);
    };

    const handleMaxValueChange = (e) => {
        const value = e.target.value;
        // Clear checkbox selections when min-max values are entered
        if (value !== '') {
            setDraftMultiValues([]);
        }
        setMaxValue(value);
    };
    return (
        <div className={cn("absolute left-0 top-11.5 w-150 flex flex-col p-5 bg-gray-900 rounded-lg  z-50", className)}
            ref={filterpopupRef}
            onClick={(e) => {
                e.stopPropagation();
            }}>
            <div className="flex-shrink-0 flex justify-between items-center pb-3 mb-3 border-b border-gray-700">
                <h2 className="text-white text-lg font-medium capitalize">{filterConfig.name}</h2>
                <button
                    onClick={onClose}
                    className="text-gray-400 hover:text-white"
                >
                    <FaTimes />
                </button>
            </div>
            <div className='flex-1 overflow-y-auto pr-2 text-white'>
                {/* Add search box if configured */}
                {filterConfig?.searchBox && (
                    <div className="mb-1">
                        <input
                            type="text"
                            className="w-full px-3 py-2 border rounded text-sm"
                            placeholder={filterConfig.placeholder || "Search..."}
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                        />
                    </div>
                )}

                {/* Add min-max range inputs if configured */}
                {filterType === 'checkbox' && filterConfig?.minmax && (
                    <div className="flex items-center mb-4 space-x-2">
                        <input
                            type="number"
                            className="w-full px-3 py-2 border rounded text-sm"
                            placeholder="Min"
                            value={minValue}
                            onChange={handleMinValueChange}
                        />
                        <span className="text-gray-500">-</span>
                        <input
                            type="number"
                            className="w-full px-3 py-2 border rounded text-sm"
                            placeholder="Max"
                            value={maxValue}
                            onChange={handleMaxValueChange}
                        />
                    </div>
                )}

                {/* Render appropriate filter UI based on type */}
                {filterType === 'checkbox' && filteredOptions.map(renderCheckboxOption)}
                {filterType === 'radio-button' && filteredOptions.map(renderRadioOption)}
                {filterType === 'multilevel-checkbox' && renderMultiCheckboxGroup()}
            </div>


            {/* Action buttons */}
            <div className="flex justify-between mt-4">
                <button
                    onClick={handleClearFilter}
                    className="px-4 py-2 bg-transparent border border-gray-500 rounded-lg flex items-center gap-1.5 text-sm text-gray-50 hover:text-gray-300 transition-colors cursor-pointer"
                >
                    Clear
                </button>
                <button
                    onClick={handleApplyFilter}
                    className="px-4 py-2 text-sm bg-brand-500 hover:bg-brand-600 text-white hover:text-gray-100 rounded"
                >
                    Apply
                </button>
            </div>
        </div>
    );
};

FilterComponent.propTypes = {
    filter: PropTypes.string.isRequired,
    onClose: PropTypes.func,
    savedFilter: PropTypes.shape({
        filter: PropTypes.string,
        value: PropTypes.oneOfType([PropTypes.string, PropTypes.array])
    }),
    filterConfig: PropTypes.shape({
        name: PropTypes.string,
        type: PropTypes.oneOf(['checkbox', 'radio-button', 'multilevel-checkbox']),
        minmax: PropTypes.bool,
        searchBox: PropTypes.bool,
        enterValue: PropTypes.bool,
        placeholder: PropTypes.string,
        options: PropTypes.arrayOf(
            PropTypes.oneOfType([
                // For simple checkbox/radio options
                PropTypes.shape({
                    value: PropTypes.string.isRequired,
                    label: PropTypes.string.isRequired,
                    description: PropTypes.string
                }),
                // For multilevel options
                PropTypes.shape({
                    subOptionName: PropTypes.string.isRequired,
                    subOptionType: PropTypes.oneOf(['checkbox', 'radio-button']).isRequired,
                    checkboxEnabled: PropTypes.bool,
                    collapsed: PropTypes.bool,
                    subOptions: PropTypes.arrayOf(
                        PropTypes.shape({
                            value: PropTypes.string.isRequired,
                            label: PropTypes.string.isRequired,
                            description: PropTypes.string
                        })
                    )
                })
            ])
        )
    }),
    initialValue: PropTypes.any, onApplyFilter: PropTypes.func,
    className: PropTypes.string,
};

export default FilterComponent;
