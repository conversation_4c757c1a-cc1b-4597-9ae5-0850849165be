# Speech Recognition Setup Instructions

## Required Package Installation

To enable the speech-to-text functionality in the SearchWithSuggestions component, you need to install the `react-speech-recognition` package:

```bash
npm install react-speech-recognition
```

## Browser Compatibility

The speech recognition feature requires:
- Modern browsers that support the Web Speech API
- Microphone permissions from the user
- HTTPS connection (required for microphone access in production)

## Supported Browsers
- Chrome (recommended)
- Edge
- Safari (limited support)
- Firefox (limited support)

## Features Implemented

1. **Microphone Icon**: Added to the right side of the search input
2. **Proactive Permission Handling**: Requests microphone permissions before attempting speech recognition
3. **Visual Feedback**: Icon changes color and animates based on permission state and listening status
4. **Smart Error Handling**: Shows appropriate messages for unsupported browsers or permission issues
5. **Auto-search**: Automatically triggers search when speech ends
6. **Timeout Protection**: Auto-stops listening after 10 seconds
7. **Permission State Tracking**: Monitors permission changes and updates UI accordingly
8. **User Guidance**: Provides helpful tips for enabling microphone access

## Usage

1. Click the microphone icon to start voice search
2. Speak your search query clearly
3. The system will automatically convert speech to text and trigger the search
4. Click the microphone icon again to stop listening manually

## Troubleshooting

If speech recognition doesn't work:
1. Ensure you're using a supported browser
2. Check that microphone permissions are granted
3. Verify you're on HTTPS (required for production)
4. Try refreshing the page and granting permissions again
