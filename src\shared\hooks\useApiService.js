import { Toast } from '../components/UI/Toast';
import useApiRequest from './useApiRequest';

/**
 * Custom hook for handling API calls with automatic loading and toast notifications
 * @returns {object} API service methods
 */
const useApiService = () => {
  const { executeRequest } = useApiRequest();

  /**
   * Generic function to handle API calls with appropriate UI feedback
   */
  const handleApiCall = async (
    apiFunction,
    {
      loadingMessage = 'Processing...',
      successMessage,
      errorMessage = 'Operation failed',
      showLoadingToast = true,
      showSuccessToast = true,
      showErrorToast = true,
      onSuccess,
      onError,
      ...params
    } = {}
  ) => {
    try {
      const response = await executeRequest(
        () => apiFunction(params),
        {
          loadingMessage,
          successMessage,
          errorMessage,
          showLoadingToast,
          showSuccessToast,
          showErrorToast,
        }
      );

      if (onSuccess) {
        onSuccess(response);
      }

      return response;
    } catch (error) {
      if (onError) {
        onError(error);
      }
      throw error;
    }
  };

  /**
   * Example API methods for a user service
   */
  const userService = {
    getProfile: (params) =>
      handleApiCall(
        (p) => fetch(`/api/users/${p.userId}`).then((res) => res.json()),
        {
          loadingMessage: 'Loading profile...',
          successMessage: 'Profile loaded successfully',
          errorMessage: 'Failed to load profile',
          ...params
        }
      ),

    updateProfile: (params) =>
      handleApiCall(
        (p) => fetch(`/api/users/${p.userId}`, {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(p.userData)
        }).then((res) => res.json()),
        {
          loadingMessage: 'Updating profile...',
          successMessage: 'Profile updated successfully',
          errorMessage: 'Failed to update profile',
          ...params
        }
      ),
  };

  /**
   * Example API methods for an authentication service
   */
  const authService = {
    login: (params) =>
      handleApiCall(
        (p) => fetch('/api/auth/login', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(p.credentials)
        }).then((res) => res.json()),
        {
          loadingMessage: 'Logging in...',
          successMessage: 'Login successful',
          errorMessage: 'Login failed',
          ...params
        }
      ),

    logout: (params) =>
      handleApiCall(
        () => fetch('/api/auth/logout', { method: 'POST' }).then((res) => res.json()),
        {
          loadingMessage: 'Logging out...',
          successMessage: 'Logout successful',
          errorMessage: 'Logout failed',
          ...params
        }
      ),
  };

  return {
    userService,
    authService,
  };
};

export default useApiService;
