# Internationalization (i18n)

This document explains how internationalization is set up and used in the Creatorverse Frontend project, leveraging `i18next` and `react-i18next`.

## Configuration (`src/i18n/config.js`)

The `config.js` file initializes the `i18next` instance with the available languages and default settings.

```javascript
import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';

import en from './language/en/translation.json';
import fr from './language/fr/translation.json';
import th from './language/th/translation.json';

i18n
  .use(initReactI18next)
  .init({
    resources: {
      en: { translation: en },
      fr: { translation: fr },
      th: { translation: th },
    },
    lng: 'en', // default language
    fallbackLng: 'en', // Fallback to English if translations are missing
    interpolation: {
      escapeValue: false,
    },
  });

export default i18n;
```

### Key Configuration Details:

- **`resources`**: Defines the translation files for each language. Currently, English (`en`), French (`fr`), and Thai (`th`) are supported.
- **`lng`**: Sets the default language to `en` (English).
- **`fallbackLng`**: Specifies `en` as the fallback language. If a translation key is not found in the currently selected language, it will fall back to English.
- **`interpolation.escapeValue`**: Set to `false` because React already handles escaping, preventing XSS vulnerabilities.

## Language Files (`src/i18n/language/`)

Translation files are located in `src/i18n/language/` and are organized by language code. Each language directory contains a `translation.json` file.

- `src/i18n/language/en/translation.json`
- `src/i18n/language/fr/translation.json`
- `src/i18n/language/th/translation.json`

These JSON files contain key-value pairs where the key is the translation identifier and the value is the translated string.

## Usage in Components

To use internationalization in React components, you can use the `useTranslation` hook from `react-i18next`.

```javascript
import React from 'react';
import { useTranslation } from 'react-i18next';

function MyComponent() {
  const { t } = useTranslation();

  return (
    <div>
      <h1>{t('welcome_message')}</h1>
      <p>{t('description')}</p>
    </div>
  );
}

export default MyComponent;
```

- **`useTranslation()`**: Returns the `t` function, which is used to translate keys.
- **`t('your_key')`**: Looks up the `your_key` in the appropriate `translation.json` file for the currently active language.

## Changing Language

You can change the application's language programmatically using `i18n.changeLanguage()`:

```javascript
import i18n from '../i18n/config'; // Adjust path as needed

// To change to French
i18n.changeLanguage('fr');

// To change to Thai
i18n.changeLanguage('th');
```

