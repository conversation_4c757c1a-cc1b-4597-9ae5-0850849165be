import {
  FaFacebookF,
  <PERSON>a<PERSON>nstagram,
  <PERSON>a<PERSON><PERSON><PERSON>,
  FaLinkedinIn,
  FaYoutube,
  FaTiktok,
} from 'react-icons/fa';



import googleIcon from "@assets/icon/google-icon.svg";
import YoutubeCircle from '@assets/icon/youtube-circle.svg';
import InstagramCircle from '@assets/icon/instagram-circle.svg';
import TiktokCircle from '@assets/icon/instagram-circle.svg';


export const channelIconMap = {
  facebook: { url: InstagramCircle, component: FaFacebookF },
  instagram: { url: InstagramCircle, component: FaInstagram },
  twitter: { url: '/icons/twitter.png', component: FaTwitter },
  linkedin: { url: '/icons/linkedin.png', component: FaLinkedinIn },
  google: { url: googleIcon, component: FaFacebookF },
  youtube: { url: YoutubeCircle, component: FaYoutube },
  tiktok: { url: TiktokCircle, component: <PERSON>a<PERSON>ik<PERSON>  },
  pinterest: { url: '/icons/pinterest.png' }, 
};
