import { useSelector } from 'react-redux';
import { useMemo } from 'react';

/**
 * Custom hook for user type detection and related utilities
 * Provides easy access to user type information and related helper functions
 */
const useUserType = () => {
  const { isAuthenticated, user, allocatedBrands, organizationBrands, socialProfiles } = useSelector(state => state.auth);

  // Memoize user type detection to prevent unnecessary recalculations
  const userTypeInfo = useMemo(() => {
    if (!isAuthenticated) {
      return {
        userType: null,
        isBrand: false,
        isInfluencer: false
      };
    }

    const isBrand = (allocatedBrands && allocatedBrands.length > 0) ||
                   (organizationBrands && organizationBrands.length > 0);
    const isInfluencer = socialProfiles && socialProfiles.length > 0;

    let userType = null;
    if (isBrand) {
      userType = 'brand';
    } else if (isInfluencer) {
      userType = 'influencer';
    }

    return {
      userType,
      isBrand,
      isInfluencer
    };
  }, [isAuthenticated, allocatedBrands, organizationBrands, socialProfiles]);

  // Memoize helper functions
  const helperFunctions = useMemo(() => ({
    getDashboardPath: () => {
      if (userTypeInfo.isBrand) return '/brand/dashboard';
      if (userTypeInfo.isInfluencer) return '/influencer/dashboard';
      return '/influencer/dashboard'; // Default fallback
    },

    getLoginPath: (type) => {
      const targetType = type || userTypeInfo.userType;
      return targetType === 'brand' ? '/brand/signin' : '/influencer/login';
    },

    canAccessRoute: (routeType) => {
      if (!userTypeInfo.userType) return true; // Allow if undetermined
      return userTypeInfo.userType === routeType;
    },

    canAccessBrandRoutes: () => userTypeInfo.isBrand,
    canAccessInfluencerRoutes: () => userTypeInfo.isInfluencer,
  }), [userTypeInfo]);

  return {
    // User type information
    ...userTypeInfo,
    isAuthenticated,

    // User data
    user,
    allocatedBrands,
    organizationBrands,
    socialProfiles,

    // Helper functions
    ...helperFunctions,
  };
};

export default useUserType;
