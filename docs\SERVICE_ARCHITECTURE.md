# Service Architecture Documentation

## Overview

This document describes the complete service architecture implemented for the brand and influencer features, following the same patterns established by the auth feature. The architecture provides a consistent, scalable approach to state management and API integration across the application.

## Architecture Pattern

### Core Components

Each feature service consists of four main files:

1. **`{feature}Thunks.js`** - Async thunks for API operations
2. **`{feature}Slice.js`** - Redux slice with state management
3. **`{feature}Actions.js`** - Custom hook for actions
4. **`{feature}Selectors.js`** - Custom hook for selectors
5. **`index.js`** - Central export point

### Design Principles

- **Consistency**: All features follow the same architectural patterns
- **Separation of Concerns**: Clear separation between API calls, state management, and UI interactions
- **Type Safety**: Proper error handling and status tracking
- **Reusability**: Custom hooks provide clean interfaces for components
- **Scalability**: Easy to extend with new functionality

## Auth Service Architecture

### Enhanced Logout Functionality

The auth service provides comprehensive authentication management with enhanced logout capabilities:

#### Authentication Operations
- `loginThunk` - User login with credentials
- `registerThunk` - User registration
- `verifyOTPThunk` - OTP verification
- `logoutThunk` - Enhanced logout with complete state clearing

#### Logout Flow Features
- **API Integration**: Calls `POST /v1/auth/logout` with session_id parameter
- **Complete State Clearing**: Clears all Redux state across auth, brand, and influencer slices
- **Storage Management**: Clears all localStorage and sessionStorage data
- **Cross-Slice Coordination**: Dispatches clear actions to other feature slices
- **Error Handling**: Graceful degradation - clears local data even if API fails
- **Secure Redirection**: Redirects to appropriate login page based on user type

#### State Structure

```javascript
{
  // Authentication state
  isAuthenticated: false,
  user: null,
  token: null,

  // User data
  allocatedBrands: [],
  organizationBrands: [],
  socialProfiles: [],

  // Request tracking
  status: 'idle',
  error: null
}
```

#### Enhanced Logout Implementation

The logout thunk performs comprehensive cleanup:

```javascript
// Enhanced logout with complete state clearing
export const logoutThunk = createAsyncThunk(
  'auth/logout',
  async (_, { rejectWithValue, dispatch }) => {
    // 1. Call logout API with session_id
    // 2. Clear all localStorage/sessionStorage
    // 3. Dispatch clear actions to other slices
    // 4. Handle errors gracefully
  }
);
```

## Brand Service Architecture

### API Coverage

The brand service provides complete coverage of the discovery and brand management APIs:

#### Brand Management
- `getBrandInfoThunk` - Get user's brand information
- `createBrandThunk` - Create new brand
- `requestBrandAccessThunk` - Request access to existing brand

#### Creator Discovery
- `searchCreatorsThunk` - Search for creators with filters
- `getFilterMetadataThunk` - Get available filters for platforms
- `transformFiltersThunk` - Transform filters to provider format

#### Filter Management
- `getSavedFiltersThunk` - Get user's saved filter sets
- `getSavedFilterSetThunk` - Get specific filter set
- `createSavedFilterThunk` - Create new filter set
- `updateSavedFilterThunk` - Update existing filter set
- `deleteSavedFilterThunk` - Delete filter set
- `getGlobalFiltersThunk` - Get global filter sets

#### Cache Management
- `invalidateFilterCacheThunk` - Invalidate filter cache
- `getCacheStatsThunk` - Get cache statistics
- `cleanupCacheThunk` - Cleanup expired cache entries

### State Structure

```javascript
{
  // Brand management
  allocatedBrands: [],
  organizationBrands: [],
  organizationId: null,
  selectedBrand: null,
  
  // Creator discovery
  searchResults: { profiles: [], metadata: null, lastSearchParams: null },
  activeFilters: [],
  searchQuery: '',
  
  // Filter management
  filterMetadata: {},
  savedFilters: [],
  globalFilters: [],
  currentFilterSet: null,
  transformedFilters: null,
  
  // System monitoring
  cacheStats: null,

  // Status tracking
  status: 'idle',
  searchStatus: 'idle',
  filterStatus: 'idle',
  savedFilterStatus: 'idle',
  cacheStatus: 'idle',

  // Error handling
  error: null,
  searchError: null,
  filterError: null,
  savedFilterError: null,
  cacheError: null,
}
```

### Usage Example

```javascript
import useBrandActions from '../services/brandActions';
import useBrandSelectors from '../services/brandSelectors';

const BrandComponent = () => {
  const {
    getBrandInfo,
    searchCreators,
    createSavedFilter,
    clearAllErrors
  } = useBrandActions();
  
  const {
    allocatedBrands,
    searchResults,
    isLoading,
    hasError,
    error
  } = useBrandSelectors();
  
  // Component logic...
};
```

## Influencer Service Architecture

### API Coverage

The influencer service provides complete coverage of the analytics and profile management APIs:

#### Profile Management
- `getInfluencerInfoThunk` - Get influencer profile information
- `getYoutubeChannelsThunk` - Get available YouTube channels
- `selectYoutubeChannelsThunk` - Select YouTube channels
- `listProfilesThunk` - List analytics profiles

#### Profile Analytics
- `getBasicProfileThunk` - Get basic profile data
- `getDetailedProfileThunk` - Get detailed analytics
- `triggerAnalyticsFetchThunk` - Trigger analytics refresh
- `getAnalyticsStatusThunk` - Get analytics processing status

#### Dashboard Analytics
- `getDashboardKPIsThunk` - Get key performance indicators
- `getTopPostsThunk` - Get top performing posts
- `getMonthlyImpressionsThunk` - Get monthly impression data
- `getMonthlyEngagementThunk` - Get monthly engagement rates
- `getMonthlyLikesByTypeThunk` - Get likes by content type
- `getContentDistributionThunk` - Get content distribution data

### State Structure

```javascript
{
  // Profile management
  socialProfiles: [],
  youtubeChannels: [],
  selectedChannels: [],
  profiles: [],
  currentProfile: null,
  selectedProfileId: null,
  
  // Analytics data (keyed by profileId)
  basicProfileData: {},
  detailedProfileData: {},
  analyticsStatus: {},
  
  // Dashboard data (keyed by profileId)
  dashboardKPIs: {},
  topPosts: {},
  monthlyImpressions: {},
  monthlyEngagement: {},
  monthlyLikesByType: {},
  contentDistribution: {},
  
  // UI state
  dashboardView: 'overview',
  
  // Status tracking
  status: 'idle',
  youtubeStatus: 'idle',
  profileStatus: 'idle',
  analyticsStatus: 'idle',
  dashboardStatus: 'idle',
  
  // Error handling
  error: null,
  youtubeError: null,
  profileError: null,
  analyticsError: null,
  dashboardError: null
}
```

### Usage Example

```javascript
import useInfluencerActions from '../services/influencerActions';
import useInfluencerSelectors from '../services/influencerSelectors';

const InfluencerComponent = () => {
  const {
    getInfluencerInfo,
    loadCompleteProfile,
    loadDashboardData,
    setSelectedProfile
  } = useInfluencerActions();
  
  const {
    profiles,
    currentProfile,
    getDashboardKPIs,
    isLoading,
    hasError
  } = useInfluencerSelectors();
  
  // Component logic...
};
```

## Error Handling

### Consistent Error Structure

All thunks follow a consistent error handling pattern:

```javascript
try {
  const response = await api.method().send();
  if (response.status === 200 && response.data) {
    return {
      message: 'Operation successful',
      success: true,
      data: response.data.data
    };
  } else {
    return rejectWithValue({
      message: response.data?.message || 'Operation failed',
      success: false
    });
  }
} catch (error) {
  return rejectWithValue({
    message: error.response?.data?.message || 'Operation failed. Please try again.',
    success: false
  });
}
```

### Error State Management

- Each operation type has its own error state
- Granular error clearing methods
- Global error clearing capability
- Error existence checks in selectors

## Status Tracking

### Request Status Enum

```javascript
const RequestStatus = {
  IDLE: 'idle',
  LOADING: 'loading',
  SUCCEEDED: 'succeeded',
  FAILED: 'failed'
};
```

### Status Management

- Separate status tracking for different operation types
- Loading state helpers in selectors
- Combined loading state checks

## State Clearing and Logout Integration

### Cross-Slice State Management

Both brand and influencer services include comprehensive state clearing actions for secure logout functionality:

#### Brand State Clearing

```javascript
// src/features/brand/services/brandSlice.js
clearAllBrandState(state) {
  // Reset to initial state values
  state.allocatedBrands = [];
  state.organizationBrands = [];
  state.organizationId = null;
  state.searchResults = { profiles: [], metadata: null, lastSearchParams: null };
  state.filterMetadata = {};
  state.savedFilters = [];
  state.globalFilters = [];
  state.currentFilterSet = null;
  state.transformedFilters = null;
  state.cacheStats = null;
  state.selectedBrand = null;
  state.activeFilters = [];
  state.searchQuery = '';

  // Reset all status and error states
  // ... (status and error clearing)
}
```

#### Influencer State Clearing

```javascript
// src/features/influencer/services/influencerSlice.js
clearAllInfluencerState(state) {
  // Reset to initial state values
  state.socialProfiles = [];
  state.youtubeChannels = [];
  state.selectedChannels = [];
  state.profiles = [];
  state.currentProfile = null;
  state.basicProfileData = {};
  state.detailedProfileData = {};
  // ... (complete state reset)
}
```

### Usage in Logout Flow

These actions are automatically dispatched during the logout process to ensure complete application state clearing:

```javascript
// Called from enhanced logoutThunk
dispatch(clearAllBrandState());
dispatch(clearAllInfluencerState());
```

## Integration with Existing Architecture

### Redux Store Integration

Both services are integrated into the main Redux store:

```javascript
// src/app/store.js
const store = configureStore({
  reducer: {
    auth: authReducer,
    brand: brandReducer,
    influencer: influencerReducer,
    // ... other reducers
  },
});
```

### Multi-Tenant Backend Support

- Services work with existing multi-tenant architecture
- Proper endpoint routing through existing API services
- Consistent authentication and authorization handling

## Best Practices

### Component Integration

1. **Use Custom Hooks**: Always use the provided action and selector hooks
2. **Error Handling**: Check for errors and provide user feedback
3. **Loading States**: Show loading indicators during operations
4. **Cleanup**: Clear errors and reset state when appropriate

### Performance Considerations

1. **Selective Data Loading**: Only load data when needed
2. **Caching**: Leverage built-in caching mechanisms
3. **Batch Operations**: Use convenience methods for multiple operations
4. **State Normalization**: Data is normalized by ID for efficient access

### Extensibility

The architecture is designed to be easily extensible:

1. **New Thunks**: Add new API operations following existing patterns
2. **State Properties**: Extend state structure as needed
3. **Selectors**: Add computed values and derived state
4. **Actions**: Create convenience methods for complex operations

## Conclusion

This service architecture provides a robust, scalable foundation for feature development that maintains consistency with existing patterns while providing comprehensive API coverage and excellent developer experience.
