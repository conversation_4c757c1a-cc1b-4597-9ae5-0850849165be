// SnackbarContext.jsx
import React, { createContext, useContext, useState, useCallback } from 'react';
import { AnimatePresence } from 'framer-motion';
import Snackbar from './Snackbar';

const SnackbarContext = createContext();
export const useSnackbar = () => useContext(SnackbarContext);

export const SnackbarProvider = ({ children }) => {
  const [snackbar, setSnackbar] = useState(null);

  const showSnackbar = useCallback((message, type = 'info', duration = 3000) => {
    setSnackbar({ message, type, duration });
  }, []);

  const hideSnackbar = () => {
    setSnackbar(null); // Triggers exit animation
  };

  return (
    <SnackbarContext.Provider value={{ showSnackbar }}>
      {children}
      <AnimatePresence>
        {snackbar && (
          <Snackbar
            key={snackbar.message}
            message={snackbar.message}
            type={snackbar.type}
            duration={snackbar.duration}
            onClose={hideSnackbar}
          />
        )}
      </AnimatePresence>
    </SnackbarContext.Provider>
  );
};
