import React, { useEffect } from "react";
import { Link, useLocation, useNavigate } from "react-router-dom";
import SocialButton from "@shared/components/UI/button";
import instagramIcon from "@assets/icon/instagram-icon.png";
import youtubeIcon from "@assets/icon/youtube-icon.svg";
import ArrowLeft from "@assets/icon/arrow-left.svg";
import { useLoading } from '@shared/components/UI/LoadingContext';
import { useSnackbar } from '@shared/components/UI/SnackbarContext';
import { useDispatch } from 'react-redux';
import NotificationStrip from "@auth/component/NotificationStrip";
import SocialSignupLayout from "@shared/layout/SocialSignupLayout"
import useRoles from '@shared/hooks/useRoles';
import { IoChevronBack } from "react-icons/io5";
import PopupLayout from '@shared/components/UI/PopupLayout';




import { initiateOAuthThunk, getYoutubeChannelsThunk, selectYoutubeChannelsThunk } from '../service/authThunks';
import { useState } from "react";
import ProfileSelector from "../component/ProfileSelector";




const SocialSignupPage = () => {
  const { showSnackbar } = useSnackbar();
  const dispatch = useDispatch();
  const { getRoleIdByName } = useRoles();
  const influencerRoleId = getRoleIdByName('influencer');
  const { isLoading, setIsLoading } = useLoading();
  const navigate = useNavigate();
  const [isPopupOpen, setIsPopupOpen] = useState("");
  const [selectedProfiles, setSelectedProfiles] = useState([]);
  const [selectedChannel, setSelectedChannel] = useState("");

  const [profiles, setProfiles] = useState([]);
  const [channels, setChannels] = useState([]);

  const location = useLocation();

  useEffect(() => {
    const searchParams = new URLSearchParams(location.search);
    const errorParam = searchParams.get('error');

    if (errorParam) {
      setIsPopupOpen("error");

      // Remove query params from URL
      const newUrl = window.location.pathname;
      window.history.replaceState({}, document.title, newUrl);
    }
  }, [location.search]);

  useEffect(() => {
    const searchParams = new URLSearchParams(location.search);
    if (searchParams.size > 0) {
      const accessToken = searchParams.get('accessToken');
      const refreshToken = searchParams.get('refreshToken');

      const errorParam = searchParams.get('error');
      const platformParam = searchParams.get('platform');

      if (errorParam) {
        setIsPopupOpen("error");
      }

      if (platformParam) {
        console.log("Platform", platformParam);
        setSelectedChannel(platformParam);
      }

      if (accessToken && refreshToken) {
        // console.log('Access Token:', accessToken);
        // console.log('Refresh Token:', refreshToken);
        localStorage.setItem('auth_token', accessToken);
        localStorage.setItem('refresh_token', refreshToken);
      }
      if (accessToken) {
        getAllProfileChannels();
      }



    }
  }, [location.pathname, location.search]);

  useEffect(() => {
    console.log("Platform", selectedChannel);
  }, [selectedChannel]);

  const handleOAuthInitiate = async (provider) => {
    try {
      setIsLoading(true);

      // showSnackbar("Hi my name is ankit")

      // Map provider names to their OAuth provider values
      const providerMap = {
        google: 'google',
        instagram: 'instagram',
        youtube: 'youtube'
      };

      // Make sure we have a valid role ID
      if (!influencerRoleId) {
        showSnackbar('(R) Internal Server Error', 'error');
        setIsLoading(false);
        return;
      }

      // Call the OAuth initiate endpoint
      const response = await dispatch(initiateOAuthThunk({
        provider: providerMap[provider],
        role_uuid: influencerRoleId,
        error_path: `/socialsignup`,
        redirect_path: `/socialsignup`
      }));

      var responseData = response.payload.data;

      console.log("Email signup response:", responseData, isLoading);
      // showSnackbar("Hi my name is ankit")


      console.log(`${provider} OAuth initiation response:`, response);

      // Handle the OAuth response - typically redirects to the provider's auth page
      if (responseData?.auth_url) {
        window.location.href = responseData.auth_url;
      } else {
        showSnackbar(`Failed to initiate ${provider} authentication`, 'error');
        // Toast.error(`Failed to initiate ${provider} authentication`);
        setIsLoading(false);
      }
    } catch (error) {
      console.error(`${provider} OAuth initiation error:`, error);
      showSnackbar(`Failed to initiate ${provider} authentication: ${error.message || 'Unknown error'}`, 'error');
      setIsLoading(false);
    }
  };

  const handleProfileChange = (profiles) => {
    console.log("Selected profiles Ankit:", profiles);
    setSelectedProfiles(profiles);
  };

  const getAllProfileChannels = async () => {
    try {
      setIsLoading(true);

      // Call the OAuth initiate endpoint
      const response = await dispatch(getYoutubeChannelsThunk());

      var responseData = response.payload;

      // showSnackbar("Hi my name is ankit")
      if (responseData.success && responseData.data !== null) {
        setIsLoading(false);

        setChannels(responseData.data.channels);

        const transformed = responseData.data.channels.map((profile) => ({
          id: profile.channel_id,
          name: profile.title,
          profile: profile.custom_url,
          avatar: profile.thumbnail_url
        }));
        if (transformed.length > 0) {
          setProfiles(transformed);
          setIsPopupOpen("profileSelector");
        }
        else {
          showSnackbar("No channels found", 'info');
        }
      }
      else {
        showSnackbar(responseData.message, 'error');
        setIsLoading(false);
      }
    } catch (error) {
      console.error(`Failed to get youtube channels:`, error);
      showSnackbar(`Failed to get youtube channels: ${error.message || 'Unknown error'}`, 'error');
      setIsLoading(false);
    }
  };

  const handleAddProfiles = async (selectedProfiles, channel) => {

    if (selectedProfiles.length === 0) {
      showSnackbar("Please select at least one profile", 'error');
      return;
    }

    try {
      setIsLoading(true);
      const selectedProfileChannels = channels.filter(channel =>
        Array.isArray(selectedProfiles) &&
        selectedProfiles.some(selectedProfile => selectedProfile.id === channel.channel_id)
      );

      // Call the OAuth initiate endpoint
      if (selectedProfileChannels.length > 0) {
        const response = await dispatch(selectYoutubeChannelsThunk(selectedProfileChannels));

        var responseData = response.payload;
        console.log("Added Youtube Channel response:", response.payload);

        // showSnackbar("Hi my name is ankit")
        if (responseData.success && responseData.data !== null) {
          console.log("Selected Youtube Channel response:", responseData.data);
          showSnackbar("Profile added successfully!", 'success');
          setIsPopupOpen("");

          if (channel === "instagram") {
            navigate('/social-signup-followup/instagram');
          } else {
            navigate('/social-signup-followup/youtube');
          }
        }
        else {
          showSnackbar(responseData.message, 'error');
        }
      }

    } catch (error) {
      console.error(`Failed to add youtube channels:`, error);
      showSnackbar(`Failed to add youtube channels: ${error.message || 'Unknown error'}`, 'error');
      setIsLoading(false);
    } finally {
      setIsLoading(false);
      setSelectedChannel("");
    }
  };



  return (
    <SocialSignupLayout>
      {
        isPopupOpen === "error" && (
          <PopupLayout
            title="Invite Creators"
            className="bg-[#292929]"
            onClose={() => setIsPopupOpen("")}
            isAcceptButton={false}
            isCancelButton={false}
            acceptText="Done"
          >
            <div className="flex flex-col gap-7">
              <span className='text-24-semibold text-gray-50'>⚠️ Invalid Email</span>
              <div className='flex flex-col gap-12 mb-5'>
                <div className='flex flex-col gap-2'>
                  <span className='text-18-medium text-gray-300'>Please use personal email to connect your account with Creatorverse.</span>
                  {/* <span className='text-14-semibold text-gray-300'>We'll keep you posted when they start responding.</span> */}
                </div>
              </div>
            </div>
          </PopupLayout>
        )
      }
      {
        isPopupOpen === "profileSelector" && (
          <PopupLayout
            className="bg-[#292929]"
            onClose={() => setIsPopupOpen("")}
            isAcceptButton={false}
            isCancelButton={false}
            acceptText="Add"
            width='400px'
          >
            <div className="flex flex-col gap-2">
              <span className='text-24-semibold text-gray-50'>Select Profile to Add</span>
              <div className='flex flex-col gap-12 mb-5'>
                <ProfileSelector onChange={handleProfileChange} profiles={profiles} />
              </div>
              <div className="flex justify-end">
                <button
                  onClick={() => handleAddProfiles(selectedProfiles, selectedChannel)}
                  className="px-15 py-1.5 bg-brand-500 text-white rounded hover:bg-brand-600 transition"
                >
                  Add
                </button>
              </div>
            </div>
          </PopupLayout>
        )
      }
      <div className=" flex flex-col items-center justify-center bg-primary text-white px-4 gap-10">
        <div className="max-w-md w-full space-y-8 relative">

          {/* Back Button */}
          <Link
            to="/influencer/signup"
            className="flex justify-center items-center bg-gray-500 rounded-full w-10 h-10 text-gray-300 hover:text-gray-200 hover:bg-gray-400 transition-colors cursor-pointer"
          >
            <IoChevronBack />

          </Link>

          {/* Title */}
          <div className="space-y-2 mt-6">
            <h1 className="text-2xl md:text-3xl text-heading-36 text-left mr-30">
              Link Your Socials, <br />
              <span className="text-[#E8FD95]">Land the Deals</span>
            </h1>
            <p className="text-gray-300 text-16-regular text-left mr-30">
              Creators with linked socials are <strong>3x</strong> more likely to get brand deals
            </p>
          </div>

          {/* Subtext */}
          <p className="text-gray-300 text-18-semibold mt-2 text-center">
            Your next brand deal starts here
          </p>

          {/* Buttons */}
          <div className="space-y-4">
            <p className="text-18-semibold text-white text-center">Connect with</p>

            {/* Instagram Button */}
            <SocialButton
              icon={<img src={instagramIcon} alt="Instagram" className="w-5 h-5" />}
              label="Instagram"
              variant="default"
              className="w-full justify-center text-16-semibold text-gray-900 bg-[#f3efff]"
              onClick={() => {
                handleOAuthInitiate("instagram");
              }}
            />
            <p className="text-14-medium -mt-3 text-right w-full">
              <span className="text-[#E8FD95]">+23 creators</span> just unlocked new deals ✨
            </p>

            {/* YouTube Button */}
            <SocialButton
              icon={<img src={youtubeIcon} alt="YouTube" className="w-7 h-7" />}
              label="Youtube"
              variant="default"
              className="w-full justify-center text-16-semibold text-gray-900 bg-light-6"
              onClick={() => {
                handleOAuthInitiate("youtube");
              }}
            />
            <p className="text-14-medium -mt-3 text-right w-full">
              Tap into <span className="text-[#51DC8E]">100+ active campaigns</span> live right now!
            </p>

          </div>


        </div>
        <div className="mt-15 w-full ">
          {/* Notification Strip */}
          <NotificationStrip />
        </div>
      </div>
    </SocialSignupLayout >
  );
};

export default SocialSignupPage;
