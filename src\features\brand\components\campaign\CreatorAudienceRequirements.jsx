import React, { useState } from 'react'
import { useF<PERSON><PERSON><PERSON><PERSON><PERSON>, Controller } from 'react-hook-form';
import { FaRegCalendarAlt } from 'react-icons/fa';
import { DatePicker, Switch, Select } from 'antd';
import FilterComponent from '../../../../shared/components/FilterComponent';
// eslint-disable-next-line no-unused-vars
import { motion } from 'framer-motion';

import MapPinIcon from '@assets/icon/location.svg';

const CreatorAudienceRequirements = (steps) => {
  const { register, control, formState: { errors } } = useFormContext();
  const [openFilter, setOpenFilter] = useState(null); // 'followerCount', 'location', etc.

  const creatorFilterFields = [
    {
      name: 'creatorFollowerCount',
      label: 'Follower Count',
      filterConfig: {
        name: "follower count",
        type: "checkbox",
        minmax: true,
        icon: "follower-icon",
        enterValue: false,
        placeholder: "Select Follower Count", //only needed for enterValue
        options: [
          { label: "Nano", value: "1000-10000", description: "1k-10K" },
          { label: "Micro", value: "10000-50000", description: "10K-50K" },
          { label: "Mid", value: "50000-500000", description: "50K-500K" },
          { label: "Macro", value: "500000-1000000", description: "500K-1M" },
          { label: "Mega", value: "1000000-", description: "1M+" }
        ]
      }
    },
    {
      name: 'creatorEngagementRate',
      label: 'Engagement Rate',
      filterConfig: {
        name: "engagement rate",
        type: "checkbox",
        minmax: true,
        icon: "engagement-icon",
        enterValue: false,
        placeholder: "Select Engagement Rate", //only needed for enterValue
        options: [
          { label: "Very High", value: "10%+", description: "10%+" },
          { label: "High", value: "5%-10%", description: "5%-10%" },
          { label: "Medium", value: "2%-5%", description: "2%-5%" },
          { label: "Low", value: "<2%", description: "<2%" },
        ]
      }
    },
    {
      name: 'creatorLocation',
      label: 'Location',
      filterConfig: {
        name: "Location",
        type: "multilevel-checkbox",
        icon: "location-icon",
        searchBox: true,
        minmax: false,
        enterValue: false,
        placeholder: "Search Location",
        options: [
          {
            subOptionName: "Tier 1",
            subOptionType: "checkbox",
            collapsed: true,
            checkboxEnabled: true,
            subOptions: [
              { label: "Mumbai", value: "mumbai", description: "" },
              { label: "Delhi", value: "delhi", description: "" },
              { label: "Bangalore", value: "bangalore", description: "" },
              { label: "Hyderabad", value: "hyderabad", description: "" },
              { label: "Chennai", value: "chennai", description: "" },
              { label: "Kolkata", value: "kolkata", description: "" },
              { label: "Pune", value: "pune", description: "" },
              { label: "Ahmedabad", value: "ahmedabad", description: "" }
            ]
          },
          {
            subOptionName: "Tier 2",
            subOptionType: "checkbox",
            checkboxEnabled: true,
            collapsed: true,
            subOptions: [
              { label: "Surat", value: "surat", description: "" },
              { label: "Jaipur", value: "jaipur", description: "" },
              { label: "Lucknow", value: "lucknow", description: "" },
              { label: "Kanpur", value: "kanpur", description: "" },
              { label: "Nagpur", value: "nagpur", description: "" },
              { label: "Indore", value: "indore", description: "" },
              { label: "Bhopal", value: "bhopal", description: "" },
              { label: "Coimbatore", value: "coimbatore", description: "" },
              { label: "Visakhapatnam", value: "visakhapatnam", description: "" },
              { label: "Patna", value: "patna", description: "" },
              { label: "Vadodara", value: "vadodara", description: "" },
              { label: "Ludhiana", value: "ludhiana", description: "" },
              { label: "Agra", value: "agra", description: "" },
              { label: "Nashik", value: "nashik", description: "" },
              { label: "Faridabad", value: "faridabad", description: "" },
              { label: "Meerut", value: "meerut", description: "" },
              { label: "Rajkot", value: "rajkot", description: "" },
              { label: "Varanasi", value: "varanasi", description: "" },
              { label: "Amritsar", value: "amritsar", description: "" },
              { label: "Ranchi", value: "ranchi", description: "" },
              { label: "Raipur", value: "raipur", description: "" },
              { label: "Jodhpur", value: "jodhpur", description: "" },
              { label: "Madurai", value: "madurai", description: "" },
              { label: "Guwahati", value: "guwahati", description: "" },
              { label: "Chandigarh", value: "chandigarh", description: "" },
              { label: "Mysore", value: "mysore", description: "" },
              { label: "Hubli", value: "hubli", description: "" },
              { label: "Trivandrum", value: "trivandrum", description: "" }
            ]
          },
          {
            subOptionName: "Tier 3 or Rural",
            subOptionType: "checkbox",
            collapsed: true,
            checkboxEnabled: true,
            subOptions: [
              { label: "Aligarh", value: "aligarh", description: "" },
              { label: "Gorakhpur", value: "gorakhpur", description: "" },
              { label: "Gaya", value: "gaya", description: "" },
              { label: "Saharanpur", value: "saharanpur", description: "" },
              { label: "Bhavnagar", value: "bhavnagar", description: "" },
              { label: "Guntur", value: "guntur", description: "" },
              { label: "Bilaspur", value: "bilaspur", description: "" },
              { label: "Rewa", value: "rewa", description: "" },
              { label: "Hapur", value: "hapur", description: "" },
              { label: "Jamnagar", value: "jamnagar", description: "" },
              { label: "Jhansi", value: "jhansi", description: "" },
              { label: "Ujjain", value: "ujjain", description: "" },
              { label: "Tirunelveli", value: "tirunelveli", description: "" },
              { label: "Kakinada", value: "kakinada", description: "" },
              { label: "Nanded", value: "nanded", description: "" },
              { label: "Shimla", value: "shimla", description: "" },
              { label: "Muzaffarpur", value: "muzaffarpur", description: "" },
              { label: "Cuttack", value: "cuttack", description: "" },
              { label: "Jalandhar", value: "jalandhar", description: "" },
              { label: "Belgaum", value: "belgaum", description: "" },
              { label: "Kottayam", value: "kottayam", description: "" },
              { label: "Hoshiarpur", value: "hoshiarpur", description: "" },
              { label: "Rohtak", value: "rohtak", description: "" }
            ]
          }
        ]
      }
    },
    {
      name: 'creatorGender',
      label: 'Gender',
      filterConfig: {
        name: "gender",
        type: "radio-button",
        minmax: false,
        icon: "gender-icon",
        enterValue: false,
        placeholder: "Select Gender", //only needed for enterValue
        options: [
          { label: "Male", value: "male", description: "Male" },
          { label: "Female", value: "female", description: "Female" },
          { label: "Non-Binary", value: "non-binary", description: "Non-Binary" },
        ]
      }
    },
    {
      name: 'creatorAge',
      label: 'Age Range',
      filterConfig: {
        name: "age",
        type: "checkbox",
        minmax: true,
        icon: "age-icon",
        enterValue: false,
        placeholder: "Select Age", //only needed for enterValue
        options: [
          { label: "Teen", value: "13-19", description: "13-19" },
          { label: "Young Adult", value: "20-35", description: "20-35" },
          { label: "Adult", value: "36-55", description: "36-55" },
          { label: "Senior Citizen", value: "56+", description: "56+" },
        ]
      }
    },
    {
      name: 'creatorLanguage',
      label: 'Language',
      filterConfig: {
        name: "language",
        type: "checkbox",
        minmax: false,
        icon: "language-icon",
        enterValue: false,
        placeholder: "Select Language", //only needed for enterValue
        options: [
          { label: "English", value: "english", description: "English" },
          { label: "Hindi", value: "hindi", description: "Hindi" },
          { label: "Spanish", value: "spanish", description: "Spanish" },
          { label: "French", value: "french", description: "French" },
        ]
      }
    },
    {
      name: 'creatorCategory',
      label: 'Category',
      filterConfig: {
        name: "category",
        type: "checkbox",
        minmax: false,
        icon: "category-icon",
        enterValue: false,
        searchBox: true,
        placeholder: "Select Category", //only needed for enterValue
        options: [
          { label: "Camera & Photography", value: "camera_photography", description: "" },
          { label: "Friends, Family & Relationships", value: "friends_family_relationships", description: "" },
          { label: "Clothes, Handbags & Accessories", value: "clothes_shoes_handbags_accessories", description: "" },
          { label: "Travel, Tourism & Aviation", value: "travel_tourism_aviation", description: "" },
          { label: "Restaurants, Food & Grocery", value: "restaurants_food_grocery", description: "" },
          { label: "Art & Design", value: "art_design", description: "" },
          { label: "Television & Film", value: "television_film", description: "" },
          { label: "Cars & Motorbikes", value: "cars_motorbikes", description: "" },
          { label: "Sports", value: "sports", description: "" },
          { label: "Toys, Children & Baby", value: "toys_children_baby", description: "" },
          { label: "Beauty & Cosmetics", value: "beauty_cosmetics", description: "" },
          { label: "Wedding", value: "wedding", description: "" }
        ]
      }
    }
  ];

  const audienceFilterFields = [
    {
      name: 'audienceGender',
      label: 'Gender',
      filterConfig: {
        name: "gender",
        type: "radio-button",
        minmax: false,
        icon: "gender-icon",
        enterValue: false,
        placeholder: "Select Gender", //only needed for enterValue
        options: [
          { label: "Male", value: "male", description: "Male" },
          { label: "Female", value: "female", description: "Female" },
          { label: "Non-Binary", value: "non-binary", description: "Non-Binary" },
        ]
      }
    },
    {
      name: 'audienceAge',
      label: 'Age Range',
      filterConfig: {
        name: "age",
        type: "checkbox",
        minmax: true,
        icon: "age-icon",
        enterValue: false,
        placeholder: "Select Age", //only needed for enterValue
        options: [
          { label: "Teen", value: "13-19", description: "13-19" },
          { label: "Young Adult", value: "20-35", description: "20-35" },
          { label: "Adult", value: "36-55", description: "36-55" },
          { label: "Senior Citizen", value: "56+", description: "56+" },
        ]
      }
    },
    {
      name: 'audienceLocation',
      label: 'Location',
      filterConfig: {
        name: "Location",
        type: "multilevel-checkbox",
        icon: "location-icon",
        searchBox: true,
        minmax: false,
        enterValue: false,
        placeholder: "Search Location",
        options: [
          {
            subOptionName: "Tier 1",
            subOptionType: "checkbox",
            collapsed: true,
            checkboxEnabled: true,
            subOptions: [
              { label: "Mumbai", value: "mumbai", description: "" },
              { label: "Delhi", value: "delhi", description: "" },
              { label: "Bangalore", value: "bangalore", description: "" },
              { label: "Hyderabad", value: "hyderabad", description: "" },
              { label: "Chennai", value: "chennai", description: "" },
              { label: "Kolkata", value: "kolkata", description: "" },
              { label: "Pune", value: "pune", description: "" },
              { label: "Ahmedabad", value: "ahmedabad", description: "" }
            ]
          },
          {
            subOptionName: "Tier 2",
            subOptionType: "checkbox",
            checkboxEnabled: true,
            collapsed: true,
            subOptions: [
              { label: "Surat", value: "surat", description: "" },
              { label: "Jaipur", value: "jaipur", description: "" },
              { label: "Lucknow", value: "lucknow", description: "" },
              { label: "Kanpur", value: "kanpur", description: "" },
              { label: "Nagpur", value: "nagpur", description: "" },
              { label: "Indore", value: "indore", description: "" },
              { label: "Bhopal", value: "bhopal", description: "" },
              { label: "Coimbatore", value: "coimbatore", description: "" },
              { label: "Visakhapatnam", value: "visakhapatnam", description: "" },
              { label: "Patna", value: "patna", description: "" },
              { label: "Vadodara", value: "vadodara", description: "" },
              { label: "Ludhiana", value: "ludhiana", description: "" },
              { label: "Agra", value: "agra", description: "" },
              { label: "Nashik", value: "nashik", description: "" },
              { label: "Faridabad", value: "faridabad", description: "" },
              { label: "Meerut", value: "meerut", description: "" },
              { label: "Rajkot", value: "rajkot", description: "" },
              { label: "Varanasi", value: "varanasi", description: "" },
              { label: "Amritsar", value: "amritsar", description: "" },
              { label: "Ranchi", value: "ranchi", description: "" },
              { label: "Raipur", value: "raipur", description: "" },
              { label: "Jodhpur", value: "jodhpur", description: "" },
              { label: "Madurai", value: "madurai", description: "" },
              { label: "Guwahati", value: "guwahati", description: "" },
              { label: "Chandigarh", value: "chandigarh", description: "" },
              { label: "Mysore", value: "mysore", description: "" },
              { label: "Hubli", value: "hubli", description: "" },
              { label: "Trivandrum", value: "trivandrum", description: "" }
            ]
          },
          {
            subOptionName: "Tier 3 or Rural",
            subOptionType: "checkbox",
            collapsed: true,
            checkboxEnabled: true,
            subOptions: [
              { label: "Aligarh", value: "aligarh", description: "" },
              { label: "Gorakhpur", value: "gorakhpur", description: "" },
              { label: "Gaya", value: "gaya", description: "" },
              { label: "Saharanpur", value: "saharanpur", description: "" },
              { label: "Bhavnagar", value: "bhavnagar", description: "" },
              { label: "Guntur", value: "guntur", description: "" },
              { label: "Bilaspur", value: "bilaspur", description: "" },
              { label: "Rewa", value: "rewa", description: "" },
              { label: "Hapur", value: "hapur", description: "" },
              { label: "Jamnagar", value: "jamnagar", description: "" },
              { label: "Jhansi", value: "jhansi", description: "" },
              { label: "Ujjain", value: "ujjain", description: "" },
              { label: "Tirunelveli", value: "tirunelveli", description: "" },
              { label: "Kakinada", value: "kakinada", description: "" },
              { label: "Nanded", value: "nanded", description: "" },
              { label: "Shimla", value: "shimla", description: "" },
              { label: "Muzaffarpur", value: "muzaffarpur", description: "" },
              { label: "Cuttack", value: "cuttack", description: "" },
              { label: "Jalandhar", value: "jalandhar", description: "" },
              { label: "Belgaum", value: "belgaum", description: "" },
              { label: "Kottayam", value: "kottayam", description: "" },
              { label: "Hoshiarpur", value: "hoshiarpur", description: "" },
              { label: "Rohtak", value: "rohtak", description: "" }
            ]
          }
        ]
      }
    }
  ];

  const capitalizeWords = (input) => {
    return input
      .split(',') // Split by commas
      .map(part =>
        part
          .trim()
          .split('_') // Split by underscores
          .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
          .join(' ') // Join underscore parts with space
      )
      .join(', '); // Join comma parts with comma and space
  };

  const formatNumber = (num) => {
    if (num >= 1_000_000) {
      return (num / 1_000_000).toFixed(num % 1_000_000 === 0 ? 0 : 1) + 'M ';
    } else if (num >= 1_000) {
      return (num / 1_000).toFixed(num % 1_000 === 0 ? 0 : 1) + 'K ';
    }
    return num.toString();
  };

  const isNumberRange = (value) => /^\d+-\d+$/.test(value);

  const formatRange = (value) => {
    const [start, end] = value.split('-');
    return `${formatNumber(start)}-${formatNumber(end)}`;
  };

  const getDisplayValue = (val) => {
    return val
      .split(',') // Split by commas
      .map(part => {
        part = part.trim();
        if (isNumberRange(part)) {
          return formatRange(part);
        }
        return capitalizeWords(part);
      })
      .join(', ');
  };


  return (
    <div className='flex flex-col gap-5 p-7.5 rounded-3xl border-1 border-gray-300 bg-gray-900'>
      <div className='flex items-center gap-2'>
        <motion.div
          // whileHover={{ scale: 1.2, rotate: 3 }}
          // whileTap={{ scale: 0.95 }}
          className="text-xl p-2 text-amber-50 w-[36px] h-[36px] flex items-center justify-center"
          style={{
            borderRadius: '8px',
            background: 'var(--color-blue)',
            boxShadow: '0px 0px 8px 0px rgba(79, 152, 250, 0.50)',
            color: 'white',
          }}
        >
          {steps.stepConfig.icon}
        </motion.div>
        <div className="text-20-semibold text-center text-gray-50">{steps.stepConfig.title}</div>
      </div>
      {/* Form Step Content */}
      <div className="flex flex-col gap-7.5">
        <div className='flex gap-5'>
          <div className='flex flex-col w-1/2 p-5 gap-5 rounded-2xl border-1 border-gray-500'>
            <span>Creator Requirements</span>
            <div className='flex gap-5 w-full'>
              <div className="grid grid-cols-2 gap-6 w-full">
                {creatorFilterFields.map(({ name, label, filterConfig }, index) => {
                  const isLastOdd = creatorFilterFields.length % 2 === 1 && index === creatorFilterFields.length - 1;

                  return (
                    <div
                      key={name}
                      className={`flex flex-col gap-1 ${isLastOdd ? 'col-span-2' : ''}`}
                    >
                      <label className="text-white">{label}*</label>
                      <Controller
                        name={name}
                        control={control}
                        rules={{ required: `${label} is required` }}
                        render={({ field }) => (
                          <div className="relative w-full">
                            <button
                              type="button"
                              className={`relative w-full ${openFilter === name ? 'border-gray-300' : 'border-gray-500'} px-4 py-2.5 bg-transparent border-1 rounded-lg flex items-center justify-between gap-1.5 text-sm transition-colors cursor-pointer  text-gray-300 hover:text-gray-0`}
                              onClick={() => setOpenFilter(openFilter === name ? null : name)}
                            >
                              <div className={`line-clamp-1 ${openFilter === name ? 'text-gray-200' : ''}`}>
                                {Array.isArray(field.value)
                                  ? field.value.length > 0
                                    ? getDisplayValue(field.value.map(f => f.value).join(','))
                                    : 'Select'
                                  : field.value
                                    ? getDisplayValue(field?.value)
                                    : 'Select'}
                              </div>
                              <svg
                                className={`h-5 w-8 ml-2 ${openFilter === name ? 'text-gray-300' : 'text-gray-400'}  transform transition-transform ${openFilter === name ? 'rotate-180' : ''}`}
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke="currentColor"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth={2}
                                  d="M19 9l-7 7-7-7"
                                />
                              </svg>
                            </button>

                            {errors?.[name] && (
                              <p className="text-sm text-red-2 mt-1 absolute -bottom-5 left-0">
                                {errors[name].message}
                              </p>
                            )}

                            {openFilter === name && (
                              <FilterComponent
                                filter={filterConfig.name}
                                className={`${filterConfig.name === 'category' ? 'w-80' : 'w-60'} h-80 bg-primary`}
                                savedFilter={
                                  field.value
                                    ? {
                                      filter: filterConfig.name,
                                      value: field.value,
                                    }
                                    : null
                                }
                                filterConfig={filterConfig}
                                onClose={() => {
                                  setOpenFilter(null);
                                }}
                                onApplyFilter={(filterData) => {
                                  field.onChange(filterData?.value);
                                  setOpenFilter(null);
                                }}
                              />
                            )}
                          </div>
                        )}
                      />
                    </div>
                  );
                })}
              </div>

            </div>
          </div>
          <div className='flex flex-col w-1/2 p-5 gap-5 rounded-2xl border-1 border-gray-500'>
            <span>Audience Requirements</span>
            <div className='flex gap-5 w-full'>
              <div className="flex flex-wrap gap-6 w-full">
                {audienceFilterFields.map(({ name, label, filterConfig }, index) => {
                  const isLastOdd =
                    index === audienceFilterFields.length - 1 && audienceFilterFields.length % 2 === 1;

                  return (
                    <div
                      key={name}
                      className={`flex flex-col gap-1 ${isLastOdd ? 'w-full' : 'w-[calc(50%-12px)]'}`}
                    >
                      <label className="text-white">{label}*</label>
                      <Controller
                        name={name}
                        control={control}
                        rules={{ required: `${label} is required` }}
                        render={({ field }) => (
                          <div className="relative w-full">
                            <button
                              type="button"
                              className={`relative w-full ${openFilter === name ? 'border-gray-300' : 'border-gray-500'} px-4 py-2.5 bg-transparent border-1 rounded-lg flex items-center justify-between gap-1.5 text-sm transition-colors cursor-pointer  text-gray-300 hover:text-gray-0`}
                              onClick={() =>
                                setOpenFilter(openFilter === name ? null : name)
                              }
                            >
                              <div className={`line-clamp-1 ${openFilter === name ? 'text-gray-200' : ''}`}>
                                {Array.isArray(field.value)
                                  ? field.value.length > 0
                                    ? getDisplayValue(field.value.map(f => f.value).join(','))
                                    : 'Select'
                                  : field.value
                                    ? getDisplayValue(field?.value)
                                    : 'Select'}
                              </div>
                              <svg
                                className={`h-5 w-8 ml-2 ${openFilter === name ? 'text-gray-300' : 'text-gray-400'}  transform transition-transform ${openFilter === name ? 'rotate-180' : ''}`}
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke="currentColor"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth={2}
                                  d="M19 9l-7 7-7-7"
                                />
                              </svg>
                            </button>

                            {errors?.[name] && (
                              <p className="text-sm text-red-2 mt-1 absolute -bottom-5 left-0">
                                {errors[name].message}
                              </p>
                            )}

                            {openFilter === name && (
                              <FilterComponent
                                filter={filterConfig.name}
                                className="w-60 h-80 bg-primary"
                                savedFilter={
                                  field.value
                                    ? {
                                      filter: filterConfig.name,
                                      value: field.value,
                                    }
                                    : null
                                }
                                filterConfig={filterConfig}
                                onClose={() => {
                                  setOpenFilter(null);
                                }}
                                onApplyFilter={(filterData) => {
                                  field.onChange(filterData?.value);
                                  setOpenFilter(null);
                                }}
                              />
                            )}
                          </div>
                        )}
                      />
                    </div>
                  );
                })}

                {/* Interest */}
                <div
                  key="audienceInterest"
                  className={`flex flex-col gap-1 w-full`}
                >
                  <label className="text-white">Interest*</label>
                  <Controller
                    name="audienceInterest"
                    control={control}
                    rules={{ required: `Audience Interest is required` }}
                    render={({ field }) => (
                      <div className="relative w-full">
                        <button
                          type="button"
                          className={`relative w-full px-4 py-2.5 bg-transparent border rounded-lg flex items-center justify-between gap-1.5 text-sm transition-colors cursor-pointer border-gray-500 text-gray-300 hover:text-gray-0`}
                          onClick={() => setOpenFilter(openFilter === "audienceInterest" ? null : "audienceInterest")}
                        >
                          <span>
                            {Array.isArray(field.value)
                              ? field.value.length > 0
                                ? getDisplayValue(field.value.map(f => f.value).join(','))
                                : 'Select'
                              : field.value
                                ? getDisplayValue(field?.value)
                                : 'Select'}
                          </span>
                          <svg
                            className={`h-5 w-8 ml-2 text-gray-400 transform transition-transform ${openFilter === name ? 'rotate-180' : ''}`}
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M19 9l-7 7-7-7"
                            />
                          </svg>
                        </button>

                        {errors?.["audienceInterest"] && (
                          <p className="text-sm text-red-2 mt-1 absolute -bottom-5 left-0">
                            {errors["audienceInterest"].message}
                          </p>
                        )}

                        {openFilter === "audienceInterest" && (
                          <FilterComponent
                            filter="interest"
                            className={`w-80 h-80 bg-primary`}
                            savedFilter={
                              field.value
                                ? {
                                  filter: "interest",
                                  value: field.value,
                                }
                                : null
                            }
                            filterConfig={{
                              name: "interest",
                              type: "checkbox",
                              minmax: false,
                              icon: "category-icon",
                              enterValue: false,
                              searchBox: true,
                              placeholder: "Select Category", //only needed for enterValue
                              options: [
                                { label: "Camera & Photography", value: "camera_photography", description: "" },
                                { label: "Friends, Family & Relationships", value: "friends_family_relationships", description: "" },
                                { label: "Clothes, Handbags & Accessories", value: "clothes_shoes_handbags_accessories", description: "" },
                                { label: "Travel, Tourism & Aviation", value: "travel_tourism_aviation", description: "" },
                                { label: "Restaurants, Food & Grocery", value: "restaurants_food_grocery", description: "" },
                                { label: "Art & Design", value: "art_design", description: "" },
                                { label: "Television & Film", value: "television_film", description: "" },
                                { label: "Cars & Motorbikes", value: "cars_motorbikes", description: "" },
                                { label: "Sports", value: "sports", description: "" },
                                { label: "Toys, Children & Baby", value: "toys_children_baby", description: "" },
                                { label: "Beauty & Cosmetics", value: "beauty_cosmetics", description: "" },
                                { label: "Wedding", value: "wedding", description: "" }
                              ]
                            }}
                            onClose={() => {
                              setOpenFilter(null);
                            }}
                            onApplyFilter={(filterData) => {
                              field.onChange(filterData?.value);
                              setOpenFilter(null);
                            }}
                          />
                        )}
                      </div>
                    )}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className={`flex py-7.5 px-10 gap-5 bg-[#47C8EC]/15 rounded-[20px] `}>
          <div className="flex gap-2 ">
            <Controller
              name="enableMultipleChannels"
              control={control}
              defaultValue={false}
              render={({ field }) => (
                <Switch
                  checked={field.value}
                  onChange={field.onChange}
                  className="bg-gray-700"
                />
              )}
            />
          </div>
          <div className='flex flex-col gap-1'>
            <label className="text-white text-18-medium">
              Enable creators to participate across different campaign channels
            </label>
            <span className='text-gray-200 text-14-regular'>Let the same creator submit entries across different channels in this campaign.</span>
          </div>
        </div>
      </div>
    </div>
  )
}

export default CreatorAudienceRequirements
