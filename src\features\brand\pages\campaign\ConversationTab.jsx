import React, { useState } from 'react';
import { useParams } from 'react-router-dom';
import { Input } from "@shared/components/UI/input";
import SearchIcon from "@assets/icon/nav/search.svg";
import SettingSliderIcon from '@assets/icon/settings-sliders.svg'
import Filter from '@brand/components/FilterFixed';
import VerifiedIcon from '@assets/icon/blue_verified.svg';

const ConversationTab = () => {
    const { campaignId } = useParams();
    const [selectedConversation, setSelectedConversation] = useState(null);
    const [messageInput, setMessageInput] = useState('');
    const [searchTerm, setSearchTerm] = useState("")
    const [creatorSearchTerm, setCreatorSearchTerm] = useState("")
    const [savedFilters, setSavedFilters] = useState([])
    const [showFilter, setShowFilter] = useState(false);

    // Mock data for conversations - will be replaced with actual data from Redux
    const conversations = [
        {
            id: 1,
            name: "<PERSON> Creators",
            participants: ["<PERSON>", "<PERSON>", "<PERSON>"],
            lastMessage: "Great work on the latest posts!",
            lastMessageTime: "2 hours ago",
            unreadCount: 3,
            isGroup: true
        },
        {
            id: 2,
            name: "Sarah Johnson",
            participants: ["Sarah Johnson"],
            lastMessage: "I've uploaded the new content for review",
            lastMessageTime: "1 hour ago",
            unreadCount: 1,
            isGroup: false,
            avatar: "https://images.unsplash.com/photo-1494790108377-be9c29b29330?w=150&h=150&fit=crop&crop=faces"
        },
        {
            id: 3,
            name: "Mike Chen",
            participants: ["Mike Chen"],
            lastMessage: "When is the deadline for the next batch?",
            lastMessageTime: "3 hours ago",
            unreadCount: 0,
            isGroup: false,
            avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=faces"
        }
    ];

    // Filter options array - keeping the existing array as is
    const filterOptions = [
        {
            optionName: "Status",
            optionFor: "creator",
            channel: "instagram",
            filters: [
                {
                    name: "Status",
                    type: "checkbox",
                    icon: "gender-icon",
                    minmax: false,
                    enterValue: false,
                    placeholder: "Select Status", //only needed for enterValue
                    options: [
                        { label: "Shortlisted", value: "shortlisted", description: "" },
                        { label: "In Progress", value: "in_progress", description: "" },
                        { label: "Negotiation", value: "negotiation", description: "" },
                        { label: "Outreached", value: "outreached", description: "" },
                        { label: "Onboarded", value: "onboarded", description: "" },
                        { label: "Rejected", value: "rejected", description: "" },
                    ]
                },
            ]
        },
        {
            optionName: "Channels",
            optionFor: "creator",
            channel: "instagram",
            filters: [
                {
                    name: "channel",
                    type: "checkbox",
                    minmax: false,
                    icon: "follower-icon",
                    enterValue: false,
                    placeholder: "Select Channel", //only needed for enterValue
                    options: [
                        { label: "Instagram", value: "instagram", description: "Instagram" },
                        { label: "YouTube", value: "youtube", description: "YouTube" },
                    ]
                }
            ]
        },
        {
            optionName: "Audience",
            optionFor: "creator",
            channel: "instagram",
            filters: [
                {
                    name: "audience",
                    type: "checkbox",
                    minmax: true,
                    icon: "age-icon",
                    enterValue: false,
                    placeholder: "Select Age", //only needed for enterValue
                    options: [
                        { label: "Nano", value: "1000-10000", description: "1k-10k" },
                        { label: "Micro", value: "10001-50000", description: "10k-50k" },
                        { label: "Mid", value: "50001-500000", description: "50k-500k" },
                        { label: "Macro", value: "500001-1000000", description: "500k-1M" },
                        { label: "Mega", value: "1000001+", description: "1M+" }
                    ]
                },
            ]
        },
        {
            optionName: "Engagement Rate",
            optionFor: "creator",
            channel: "instagram",
            filters: [
                {
                    name: "Engagement Rate",
                    type: "checkbox",
                    minmax: false,
                    icon: "engagement-icon",
                    enterValue: false,
                    placeholder: "Select Engagement Rate", //only needed for enterValue
                    options: [
                        { label: "Very High", value: "10%+", description: "10%+" },
                        { label: "High", value: "5%-10%", description: "5%-10%" },
                        { label: "Medium", value: "2%-5%", description: "2%-5%" },
                        { label: "Low", value: "<2%", description: "<2%" },
                    ]
                },
            ]
        },
        {
            optionName: "Campaigns",
            optionFor: "creator",
            channel: "instagram",
            filters: [
                {
                    name: "Campaigns",
                    type: "checkbox",
                    searchBox: true,
                    minmax: false,
                    icon: "engagement-icon",
                    enterValue: false,
                    placeholder: "Select Campaigns",
                    options: [
                        { label: "Summer Surge", value: "Summer Surge", description: "" },
                        { label: "Engagement Explosion", value: "Engagement Explosion", description: "" },
                        { label: "Precision Targeting", value: "Precision Targeting", description: "" },
                        { label: "Brand Spotlight", value: "Brand Spotlight", description: "" },
                    ]
                },
            ]
        },
    ];

    const handleSendMessage = () => {
        if (messageInput.trim()) {
            console.log('Sending message:', messageInput);
            // TODO: Implement send message functionality
            setMessageInput('');
        }
    };

    const handleKeyPress = (e) => {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            handleSendMessage();
        }
    };

    function getTimeAgo(dateString) {
        const now = new Date();
        const date = new Date(dateString);
        const diff = Math.floor((now - date) / 1000); // in seconds

        if (isNaN(diff)) return "";

        if (diff < 60) return "A moment ago";
        if (diff < 3600) return `${Math.floor(diff / 60)} min ago`;
        if (diff < 86400) return `${Math.floor(diff / 3600)} hr ago`;
        if (diff < 604800) return `${Math.floor(diff / 86400)} day${Math.floor(diff / 86400) > 1 ? 's' : ''} ago`;
        if (diff < 2592000) return `${Math.floor(diff / 604800)} week${Math.floor(diff / 604800) > 1 ? 's' : ''} ago`;
        if (diff < 31536000) return `${Math.floor(diff / 2592000)} month${Math.floor(diff / 2592000) > 1 ? 's' : ''} ago`;

        return `${Math.floor(diff / 31536000)} year${Math.floor(diff / 31536000) > 1 ? 's' : ''} ago`;
    }

    const renderStatus = (status) => {
        const getColor = () => {
            if (status === "shortlisted") return 'bg-teal text-white';
            if (status === "in_progress") return 'bg-yellow text-primary';
            if (status === "negotiation") return 'bg-orange text-white';
            if (status === "outreached") return 'bg-gray-400 text-white';
            if (status === "onboarded") return 'bg-sky-blue text-primary';
            if (status === "rejected") return 'bg-red-2 text-white';
            if (status === "no_status") return 'bg-transparent text-white';
            if (status === "completed") return 'bg-green-2 text-white';
            if (status === "") return 'bg-gray-400 text-white';
        };

        const formatStatusLabel = (status) => {
            return status
                .replace(/_/g, ' ')
                .replace(/\w\S*/g, (txt) => txt.charAt(0).toUpperCase() + txt.slice(1).toLowerCase());
        };

        return (
            <span className={`flex items-center gap-2 rounded-full text-14-medium py-1 px-3 w-fit ${getColor()}`}>{formatStatusLabel(status)}</span>
        );
    };


    // Dummy data for Creators sidebar
    const creatorsConversations = [
        { id: 'creator1', userProfile: '<EMAIL>', verified: true, name: 'Ethan Carter', status: 'onboarded', time: '2025-07-11T10:30:00', messages: 2, avatar: 'https://placehold.co/40x40/FF5733/FFFFFF?text=EC', lastMessage: "Great work on the latest posts!" },
        { id: 'creator2', userProfile: '<EMAIL>', verified: false, name: 'James Kim', status: 'in_progress', time: '2025-07-12T10:30:00', messages: 4, avatar: 'https://placehold.co/40x40/33FF57/FFFFFF?text=JK', lastMessage: "Great work on the latest posts!" },
        { id: 'creator3', userProfile: '<EMAIL>', verified: true, name: 'Sophia Lee', status: 'negotiation', time: '2025-07-15T10:30:00', messages: 0, avatar: 'https://placehold.co/40x40/3357FF/FFFFFF?text=SL', lastMessage: "Great work on the latest posts!" },
        { id: 'creator4', userProfile: '<EMAIL>', verified: false, name: 'Emily Chen', status: 'rejected', time: '2025-07-15T11:30:00', messages: 4, avatar: 'https://placehold.co/40x40/FF33A1/FFFFFF?text=EC', lastMessage: "Great work on the latest posts!" },
        { id: 'creator5', userProfile: '<EMAIL>', verified: false, name: 'Mike Johnson', status: 'in_progress', time: '2025-08-15T10:30:00', messages: 0, avatar: 'https://placehold.co/40x40/A133FF/FFFFFF?text=MJ', lastMessage: "Great work on the latest posts!" },
        { id: 'creator6', userProfile: '<EMAIL>', verified: true, name: 'Ava Smith', status: 'outreached', time: '2025-07-05T10:30:00', messages: 0, avatar: 'https://placehold.co/40x40/FF8C33/FFFFFF?text=AS', lastMessage: "Great work on the latest posts!" },
        { id: 'creator7', userProfile: '<EMAIL>', verified: false, name: 'Mike Johnson', status: 'in_progress', time: '2025-08-15T10:30:00', messages: 0, avatar: 'https://placehold.co/40x40/A133FF/FFFFFF?text=MJ', lastMessage: "Great work on the latest posts!" },
        { id: 'creator8', userProfile: '<EMAIL>', verified: true, name: 'Ava Smith', status: 'outreached', time: '2025-07-05T10:30:00', messages: 0, avatar: 'https://placehold.co/40x40/FF8C33/FFFFFF?text=AS', lastMessage: "Great work on the latest posts!" },
    ];

    // Mock messages for selected conversation
    const messages = [
        {
            id: 1,
            senderId: "brand",
            senderName: "Brand Team",
            message: "Welcome to the Summer Vibes campaign! We're excited to work with you all.",
            timestamp: "2023-10-12 09:00",
            isOwn: true
        },
        {
            id: 2,
            senderId: "creator1",
            senderName: "Sarah Johnson",
            message: "Thank you! I'm looking forward to creating amazing content for this campaign.",
            timestamp: "2023-10-12 09:15",
            isOwn: false,
            avatar: "https://images.unsplash.com/photo-1494790108377-be9c29b29330?w=150&h=150&fit=crop&crop=faces"
        },
        {
            id: 3,
            senderId: "creator2",
            senderName: "Mike Chen",
            message: "Same here! The brief looks great. When do you need the first deliverables?",
            timestamp: "2023-10-12 09:20",
            isOwn: false,
            avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=faces"
        },
        {
            id: 4,
            senderId: "brand",
            senderName: "Brand Team",
            message: "Great question! The first batch should be ready by October 20th. Please let me know if you have any questions about the guidelines.",
            timestamp: "2023-10-12 10:00",
            isOwn: true
        }
    ];

    return (
        <div className="h-full w-full flex flex-col gap-5 max-h-screen overflow-y-auto">
            {/* Header */}
            <div className='flex items-center justify-between gap-4'>
                <div className='flex items-center gap-4'>
                    {/* Search Box */}
                    <div className="flex items-center space-x-4 ml-0.5">
                        <Input
                            type="text"
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                            icon={SearchIcon}
                            placeholder="Search by name, channel....."
                            className="px-3 py-1 h-10 w-100 bg-transparent text-sm placeholder-gray-400 focus:outline-none"
                        />
                    </div>
                    {/* Filter Buttons */}
                    <button
                        className={`relative px-4 py-2.5 bg-transparent border border-gray-500 rounded-lg flex items-center gap-1.5 text-sm text-gray-50 hover:text-gray-300 transition-colors cursor-pointer`}
                        onClick={() => {
                            setShowFilter(!showFilter);
                            console.log('Filter button clicked');
                        }}
                        title="Toggle Filters"
                    >
                        <img src={SettingSliderIcon} alt="Filter" className="w-3.5 h-3.5" />
                        Filters
                        {savedFilters.length > 0 && (
                            <span className="ml-2 bg-brand-200 text-brand-600 text-xs rounded-full w-5 h-5 flex items-center justify-center">
                                {savedFilters.length}
                            </span>
                        )}

                        {showFilter ? <Filter
                            filterOptions={filterOptions}
                            hidetab={true}
                            savedFilters={savedFilters}
                            onClose={() => setShowFilter(false)}
                            onApplyFilters={(filters) => {
                                setSavedFilters(filters);
                                console.log(filters);
                                // Process your filters here
                            }}
                        /> : null}

                    </button>
                </div>
                <div className="flex items-center gap-3">
                    {/*  */}
                    <button
                        onClick={() => { }}
                        className="w-fit px-4 py-1.5 bg-transparent border border-gray-500 rounded-lg text-gray-100 hover:bg-gray-800 cursor-pointer flex gap-2 items-center"
                    >
                        <i class="fi fi-sr-podcast mt-1"></i>
                        Broadcast Message
                    </button>
                </div>
            </div>

            {/* Chat Area */}
            <div className='flex gap-3 '>
                {/* Conversations List */}
                <div className="flex flex-col gap-5 flex-1 bg-gray-900 rounded-lg p-5 max-w-[350px] overflow-y-auto">
                    <h2 className='text-20-semibold text-white'>Creators</h2>
                    {/* Creator Search Box */}
                    <div className="flex items-center w-full">
                        <Input
                            type="text"
                            value={creatorSearchTerm}
                            onChange={(e) => setCreatorSearchTerm(e.target.value)}
                            icon={SearchIcon}
                            placeholder="Search by name, channel....."
                            className="px-3 py-1 h-10 bg-transparent text-sm placeholder-gray-400 focus:outline-none"
                        />
                    </div>

                    <div className="space-y-4 ">
                        {creatorsConversations.map((conversation) => (
                            <div
                                key={conversation.id}
                                onClick={() => setSelectedConversation(conversation)}
                                className={`p-2.5 flex flex-col gap-4 border-b-1 border-gray-400 cursor-pointer transition ${selectedConversation?.id === conversation.id
                                    ? 'bg-gray-500 rounded-lg'
                                    : 'hover:bg-gray-500 hover:rounded-lg'
                                    }`}
                            >
                                <div className='flex justify-between '>
                                    <span>{renderStatus(conversation.status)}</span>
                                    <span className='text-12-medium text-gray-300'>{getTimeAgo(conversation.time)}</span>
                                </div>
                                <div className="flex items-center gap-3 w-full">
                                    <img src={conversation.avatar} className="w-10 h-10 rounded-full" alt={conversation.name} />
                                    <div className="flex flex-col w-full">
                                        <div className="flex items-center justify-between">
                                            <div className='flex items-center gap-1'>
                                                <span className="font-semibold text-white">{conversation.name}</span>
                                                {conversation.verified && <img src={VerifiedIcon} className="h-3 w-3" alt="Verified" />}
                                            </div>
                                            {conversation.messages > 0 && (
                                                <span className="text-12-medium text-black rounded-full w-5 h-5 bg-gray-100 flex items-center justify-center">{conversation.messages}</span>
                                            )}
                                        </div>
                                        <span className="text-sm text-gray-400 line-clamp-1">{conversation.lastMessage}</span>
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>

                {/* Chat Window */}
                <div className="flex-2 bg-gray-900 rounded-lg flex flex-col">
                    {selectedConversation ? (
                        <>
                            {/* Chat Header */}
                            <div className="p-4 border-b-1 border-gray-900 shadow-[0_4px_6px_-1px_rgba(0,0,0,0.3)]">
                                <div className="flex items-center gap-3 justify-between">
                                    <div className="flex items-center gap-3 w-full">
                                        <img src={selectedConversation.avatar} className="w-10 h-10 rounded-full" alt={selectedConversation.name} />
                                        <div className="flex flex-col w-full">
                                            <div className="flex items-center justify-between">
                                                <div className='flex items-center gap-1'>
                                                    <span className="font-semibold text-white">{selectedConversation.name}</span>
                                                    {selectedConversation.verified && <img src={VerifiedIcon} className="h-3 w-3" alt="Verified" />}
                                                </div>
                                            </div>
                                            <span className="text-sm text-gray-400 line-clamp-1">{selectedConversation.userProfile}</span>
                                        </div>
                                    </div>
                                    {/* <div>
                                        <h3 className="text-16-semibold text-white">
                                            {selectedConversation.name}
                                        </h3>
                                        <p className="text-12-regular text-gray-300">
                                            {selectedConversation.isGroup
                                                ? `${selectedConversation.participants.length} participants`
                                                : 'Online'
                                            }
                                        </p>
                                    </div> */}
                                    <span className='mr-2'>{renderStatus(selectedConversation.status)}</span>

                                </div>
                            </div>

                            {/* Messages */}
                            <div className="flex-1 p-4 overflow-y-auto space-y-4">
                                {messages.map((message) => (
                                    <div
                                        key={message.id}
                                        className={`flex ${message.isOwn ? 'justify-end' : 'justify-start'}`}
                                    >
                                        <div className={`flex gap-3 max-w-[70%] ${message.isOwn ? 'flex-row-reverse' : 'flex-row'}`}>
                                            {!message.isOwn && (
                                                <img
                                                    src={message.avatar}
                                                    alt={message.senderName}
                                                    className="w-8 h-8 rounded-full"
                                                />
                                            )}
                                            <div>
                                                {!message.isOwn && (
                                                    <p className="text-12-medium text-gray-300 mb-1">
                                                        {message.senderName}
                                                    </p>
                                                )}
                                                <div
                                                    className={`p-3 rounded-lg ${message.isOwn
                                                        ? 'bg-brand-500 text-white'
                                                        : 'bg-gray-700 text-gray-100'
                                                        }`}
                                                >
                                                    <p className="text-14-regular">{message.message}</p>
                                                </div>
                                                <p className="text-10-regular text-gray-400 mt-1">
                                                    {message.timestamp}
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                ))}
                            </div>

                            {/* Message Input */}
                            <div className="p-4 border-t border-gray-500">
                                <div className="flex gap-3">
                                    <textarea
                                        value={messageInput}
                                        onChange={(e) => setMessageInput(e.target.value)}
                                        onKeyPress={handleKeyPress}
                                        placeholder="Type your message..."
                                        className="flex-1 p-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 resize-none"
                                        rows={2}
                                    />
                                    <button
                                        onClick={handleSendMessage}
                                        disabled={!messageInput.trim()}
                                        className="px-4 py-2 bg-brand-500 text-white rounded-lg hover:bg-brand-600 transition disabled:opacity-50 disabled:cursor-not-allowed"
                                    >
                                        Send
                                    </button>
                                </div>
                            </div>
                        </>
                    ) : (
                        <div className="flex-1 flex items-center justify-center">
                            <div className="text-center">
                                <div className="text-16-medium text-gray-300 mb-2">
                                    Select a conversation
                                </div>
                                <div className="text-14-regular text-gray-400">
                                    Choose a conversation from the list to start chatting
                                </div>
                            </div>
                        </div>
                    )}
                </div>

                {/* TimeLine */}
                <div className="flex-1 bg-gray-900 rounded-lg p-4 max-w-[350px]">
                    <div className="mb-4">
                        <h3 className="text-16-semibold text-white mb-2">Conversations</h3>
                        <button className="w-full px-3 py-2 bg-brand-500 text-white rounded text-14-medium hover:bg-brand-600 transition">
                            + New Conversation
                        </button>
                    </div>

                    <div className="space-y-2">
                        {conversations.map((conversation) => (
                            <div
                                key={conversation.id}
                                onClick={() => setSelectedConversation(conversation)}
                                className={`p-3 rounded-lg cursor-pointer transition ${selectedConversation?.id === conversation.id
                                    ? 'bg-brand-500/20 border border-brand-500'
                                    : 'bg-gray-700 hover:bg-gray-650'
                                    }`}
                            >
                                <div className="flex items-center gap-3">
                                    {conversation.isGroup ? (
                                        <div className="w-10 h-10 bg-brand-500 rounded-full flex items-center justify-center">
                                            <span className="text-14-semibold text-white">
                                                {conversation.participants.length}
                                            </span>
                                        </div>
                                    ) : (
                                        <img
                                            src={conversation.avatar}
                                            alt={conversation.name}
                                            className="w-10 h-10 rounded-full"
                                        />
                                    )}

                                    <div className="flex-1 min-w-0">
                                        <div className="flex items-center justify-between">
                                            <h4 className="text-14-semibold text-white truncate">
                                                {conversation.name}
                                            </h4>
                                            {conversation.unreadCount > 0 && (
                                                <span className="bg-brand-500 text-white text-12-medium px-2 py-1 rounded-full min-w-[20px] text-center">
                                                    {conversation.unreadCount}
                                                </span>
                                            )}
                                        </div>
                                        <p className="text-12-regular text-gray-300 truncate">
                                            {conversation.lastMessage}
                                        </p>
                                        <p className="text-10-regular text-gray-400">
                                            {conversation.lastMessageTime}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            </div>
        </div>
    );
};

export default ConversationTab;
