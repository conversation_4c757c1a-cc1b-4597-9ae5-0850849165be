import React from "react";
import socialCard1 from "@assets/social-card1.png";
import socialCard2 from "@assets/social-card2.svg";

const SocialSignupLayout = ({ children }) => {
  return (
    <div className="min-h-screen flex items-center justify-center bg-primary text-white gap-20 px-4">
      {/* Left Image */}
      <div className="h-[250px]">
        <img src={socialCard2} alt="Social Card Left" />
      </div>

      {/* Middle Content */}
      <div className="flex flex-col w-full lg:max-w-lg md:max-w-md sm:max-w-sm max-h-[80vh] transition-all duration-500">{children}</div>

      {/* Right Image */}
      <div className="h-[170px] mt-40">
        <img src={socialCard1} alt="Social Card Right" />
      </div>
    </div>
  );
};

export default SocialSignupLayout;
