import React from 'react'
import AuthLayout from '../../../shared/layout/AuthLayout';
import SignUpCard from '../component/signUpCard';
import SignUpCard2 from '../component/signUpCard2';
import { useParams } from "react-router-dom";

const RegisterPage = () => {
    const { version } = useParams();
    console.log("Version:", version);


    return (
        <AuthLayout>
            {version === "v2" ? <SignUpCard2 /> : <SignUpCard />}
        </AuthLayout>
    );
};

export default RegisterPage
