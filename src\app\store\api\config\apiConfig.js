/**
 * Multi-Backend API Configuration
 * 
 * This configuration manages multiple backend services:
 * 1. Auth Service - Handles authentication, user management, and authorization
 * 2. Discovery Service - Handles creator discovery, analytics, and profile data
 * 3. Campaign Service - Handles campaign management, creator assignments, and content approval
 *
 * The configuration supports environment-based switching and service discovery
 * for scalable deployment across different environments.
 */

/**
 * Environment detection utility
 */
const getEnvironment = () => {
  const env = import.meta.env.MODE || 'development';
  // console.log('[API CONFIG] Detected environment:', env);
  return env;
};

/**
 * Service endpoint configurations for different environments
 */
const serviceEndpoints = {
  development: {
    auth: {
      baseURL: import.meta.env.VITE_AUTH_SERVICE_URL || 'https://a6sadfasdfasdfasdfad.ngrok-free.app',
      version: 'v1',
      timeout: 30000,
      retryAttempts: 3,
      retryDelay: 1000,
    },
    discovery: {
      baseURL: import.meta.env.VITE_DISCOVERY_SERVICE_URL || 'http://localhost:8000',
      version: 'v1',
      timeout: 45000,
      retryAttempts: 3,
      retryDelay: 1000,
    },
    campaign: {
      baseURL: import.meta.env.VITE_CAMPAIGN_SERVICE_URL || 'http://localhost:8001',
      version: 'v1',
      timeout: 30000,
      retryAttempts: 3,
      retryDelay: 1000,
    }
  },

  staging: {
    auth: {
      baseURL: import.meta.env.VITE_AUTH_SERVICE_URL || 'https://auth-staging.creatorverse.com',
      version: 'v1',
      timeout: 30000,
      retryAttempts: 3,
      retryDelay: 1000,
    },
    discovery: {
      baseURL: import.meta.env.VITE_DISCOVERY_SERVICE_URL || 'https://discovery-staging.creatorverse.com',
      version: 'v1',
      timeout: 45000,
      retryAttempts: 3,
      retryDelay: 1000,
    },
    campaign: {
      baseURL: import.meta.env.VITE_CAMPAIGN_SERVICE_URL || 'https://campaign-staging.creatorverse.com',
      version: 'v1',
      timeout: 30000,
      retryAttempts: 3,
      retryDelay: 1000,
    }
  },

  production: {
    auth: {
      baseURL: import.meta.env.VITE_AUTH_SERVICE_URL || 'https://auth.creatorverse.com',
      version: 'v1',
      timeout: 30000,
      retryAttempts: 5,
      retryDelay: 2000,
    },
    discovery: {
      baseURL: import.meta.env.VITE_DISCOVERY_SERVICE_URL || 'https://discovery.creatorverse.com',
      version: 'v1',
      timeout: 45000,
      retryAttempts: 5,
      retryDelay: 2000,
    },
    campaign: {
      baseURL: import.meta.env.VITE_CAMPAIGN_SERVICE_URL || 'https://campaign.creatorverse.com',
      version: 'v1',
      timeout: 30000,
      retryAttempts: 5,
      retryDelay: 2000,
    }
  }
};

/**
 * Service-specific configuration
 */
const serviceConfig = {
  auth: {
    name: 'Authentication Service',
    description: 'Handles user authentication, registration, and authorization',
    endpoints: [
      '/auth/influencer/login',
      '/auth/influencer/register',
      '/auth/influencer/verify-otp',
      '/auth/influencer/userinfo',
      '/auth/brand/login',
      '/auth/brand/register',
      '/auth/brand/userinfo',
      '/auth/logout',
      '/auth/me',
      '/oauth/initiate'
    ],
    healthCheck: '/health',
    requiresAuth: false, // Auth service itself doesn't require auth for login endpoints
  },

  discovery: {
    name: 'Discovery & Analytics Service',
    description: 'Handles creator discovery, profile analytics, and filter management',
    endpoints: [
      '/discovery/search',
      '/discovery/filters',
      '/analytics/profiles',
      '/filters/metadata',
      '/filters/transform',
      '/saved/filters'
    ],
    healthCheck: '/health',
    requiresAuth: true, // Discovery service requires auth tokens from auth service
  },

  campaign: {
    name: 'Campaign Management Service',
    description: 'Handles campaign management, creator assignments, content approval, and analytics',
    endpoints: [
      '/campaigns',
      '/campaigns/{id}',
      '/campaigns/{id}/creators',
      '/campaigns/{id}/content',
      '/campaigns/{id}/analytics',
      '/campaigns/{id}/metrics',
      '/campaigns/templates'
    ],
    healthCheck: '/health',
    requiresAuth: true, // Campaign service requires auth tokens from auth service
  }
};

/**
 * Get current environment configuration
 */
const getCurrentConfig = () => {
  // console.log('[API CONFIG] Current environment:', serviceEndpoints[getEnvironment()]);
  const env = getEnvironment();
  return serviceEndpoints[env] || serviceEndpoints.development;
};

/**
 * Get service-specific configuration
 */
const getServiceConfig = (serviceName) => {
  // console.log('[API CONFIG] Service configuration:', serviceName, serviceConfig[serviceName]);
  return {
    ...serviceConfig[serviceName],
    ...getCurrentConfig()[serviceName]
  };
};

/**
 * Build full service URL
 */
const buildServiceURL = (serviceName, endpoint = '') => {
  // console.log('[API CONFIG] Building service URL for', serviceName, 'with endpoint', endpoint);
  const config = getServiceConfig(serviceName);
  const baseURL = config.baseURL.replace(/\/$/, ''); // Remove trailing slash
  const version = config.version;
  const cleanEndpoint = endpoint.replace(/^\//, ''); // Remove leading slash

  return `${baseURL}/${version}${cleanEndpoint ? `/${cleanEndpoint}` : ''}`;
};

/**
 * Check if service is available (health check)
 */
const checkServiceHealth = async (serviceName) => {
  // console.log('[API CONFIG] Checking health of', serviceName);
  try {
    const config = getServiceConfig(serviceName);
    const healthURL = buildServiceURL(serviceName, config.healthCheck);

    const response = await fetch(healthURL, {
      method: 'GET',
      timeout: 5000,
    });

    return {
      service: serviceName,
      status: response.ok ? 'healthy' : 'unhealthy',
      statusCode: response.status,
      timestamp: new Date().toISOString(),
    };
  } catch (error) {
    return {
      service: serviceName,
      status: 'error',
      error: error.message,
      timestamp: new Date().toISOString(),
    };
  }
};

/**
 * Get all service health statuses
 */
const getAllServiceHealth = async () => {
  const services = Object.keys(serviceConfig);
  const healthChecks = await Promise.allSettled(
    services.map(service => checkServiceHealth(service))
  );

  return healthChecks.map((result, index) => ({
    service: services[index],
    ...result.value || { status: 'error', error: result.reason }
  }));
};

/**
 * Main API configuration export
 */
export const apiConfig = {
  // Environment and service configuration
  environment: getEnvironment(),
  services: serviceConfig,
  endpoints: getCurrentConfig(),

  // Utility functions
  getServiceConfig,
  buildServiceURL,
  checkServiceHealth,
  getAllServiceHealth,

  // Service URLs for quick access
  authServiceURL: buildServiceURL('auth'),
  discoveryServiceURL: buildServiceURL('discovery'),
  campaignServiceURL: buildServiceURL('campaign'),

  // Common configuration
  common: {
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    },
    withCredentials: true,
    // Add ngrok header in development
    ...(getEnvironment() === 'development' && {
      'ngrok-skip-browser-warning': 'true'
    })
  }
};

/**
 * Service type constants for type safety
 */
export const SERVICE_TYPES = {
  AUTH: 'auth',
  DISCOVERY: 'discovery',
  CAMPAIGN: 'campaign'
};

/**
 * Environment constants
 */
export const ENVIRONMENTS = {
  DEVELOPMENT: 'development',
  STAGING: 'staging',
  PRODUCTION: 'production'
};

export default apiConfig;
