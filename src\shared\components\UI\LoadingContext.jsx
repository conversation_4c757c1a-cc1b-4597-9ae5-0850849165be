import React, { createContext, useContext, useState } from 'react';
import LoadingState from '@brand/components/LoadingState';

const LoadingContext = createContext();

export const useLoading = () => useContext(LoadingContext);

export const LoadingProvider = ({ children }) => {
    const [isLoading, setIsLoading] = useState(false);

    return (
        <LoadingContext.Provider value={{ isLoading, setIsLoading }}>
            {/* App content below */}
            {children}

            {/* Global loading overlay (visually on top) */}
            {isLoading && (
                <div className="fixed inset-0 z-[9999] flex items-center justify-center bg-black/50">
                    <LoadingState text=""/>
                </div>
            )}
        </LoadingContext.Provider>
    );
};
