import React from "react";
// eslint-disable-next-line no-unused-vars
import { motion } from "framer-motion";

// Import your icons or use props to accept them
import instagramGrayIcon from "@assets/icon/instagram-gray.svg";
import youtubeGrayIcon from "@assets/icon/youtube-gray.svg";

const PlatformToggle = ({ 
  activePlatform, 
  onPlatformChange,
  width = "400",
  height = "56",
  instagramActiveIcon = "https://cdn-icons-png.flaticon.com/512/1384/1384063.png",
  youtubeActiveIcon = "https://cdn-icons-png.flaticon.com/512/1384/1384060.png",
  instagramIcon = instagramGrayIcon,
  youtubeIcon = youtubeGrayIcon,
  className = ""
}) => {
  return (
    <div className={`flex justify-center ${className} overflow-hidden` }>
      <div 
        className="relative rounded-full p-1.5 flex bg-gray-400"
        style={{ width: `${width}px`, height: `${height}px` }}
      >
        {/* Sliding background */}
        <motion.div
          className={`absolute top-1.5 bottom-1.5 w-[calc(50%-8px)] rounded-full ${
            activePlatform === "instagram" ? "bg-[#F3EFFF]" : "bg-light-6"
          } z-0`}
          layout
          animate={{
            left: activePlatform === "instagram" ? "6px" : "calc(50% + 2px)",
          }}
          transition={{ type: "spring", stiffness: 300, damping: 30 }}
        />
        
        {/* Instagram Button */}
        <button
          onClick={() => onPlatformChange("instagram")}
          className={`w-1/2 z-10 text-sm font-semibold px-4 py-1 rounded-full flex items-center justify-center gap-1 ${
            activePlatform === "instagram" ? "text-gray-900" : "text-gray-300"
          }`}
        >
          <img
            src={activePlatform === "instagram" ? instagramActiveIcon : instagramIcon}
            className="w-4 h-4"
            alt="Instagram"
          />
          Instagram
        </button>
        
        {/* YouTube Button */}
        <button
          onClick={() => onPlatformChange("youtube")}
          className={`w-1/2 z-10 text-sm font-semibold px-4 py-1 rounded-full flex items-center justify-center gap-1 ${
            activePlatform === "youtube" ? "text-gray-900" : "text-gray-300"
          }`}
        >
          <img
            src={activePlatform === "youtube" ? youtubeActiveIcon : youtubeIcon}
            className="w-4 h-4"
            alt="YouTube"
          />
          YouTube
        </button>
      </div>
    </div>
  );
};

export default PlatformToggle;