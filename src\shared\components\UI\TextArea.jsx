import React, { useState, useRef, useEffect } from 'react';

const TextArea = ({ notes = '', onUpdate, maxLength = 60 }) => {
  const [isEditing, setIsEditing] = useState(false);
  const [noteText, setNoteText] = useState(notes);
  const [originalNote, setOriginalNote] = useState(notes);
  const [charCount, setCharCount] = useState(notes.length);
  const textareaRef = useRef(null);

  useEffect(() => {
    if (isEditing && textareaRef.current) {
      textareaRef.current.focus();
    }
  }, [isEditing]);

  useEffect(() => {
    setNoteText(notes);
    setOriginalNote(notes);
    setCharCount(notes.length);
  }, [notes]);

  const handleNoteClick = () => {
    setIsEditing(true);
  };

  const handleKeyDown = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      saveNote();
    }
  };

  const handleChange = (e) => {
    const value = e.target.value;
    if (value.length <= maxLength) {
      setNoteText(value);
      setCharCount(value.length);
    }
  };

  const saveNote = async () => {
    const trimmed = noteText.trim();

    if (trimmed === originalNote) {
      setIsEditing(false);
      return;
    }

    const success = await onUpdate(trimmed); // Only new note passed

    if (success) {
      setOriginalNote(trimmed);
    } else {
      setNoteText(originalNote); // Revert on failure
      setCharCount(originalNote.length);
    }

    setIsEditing(false);
  };

  const handleBlur = () => {
    saveNote();
  };

  const handleFocus = () => {
    const el = textareaRef.current;
    if (el) {
      // Move cursor to the end
      const len = el.value.length;
      setTimeout(() => {
        el.setSelectionRange(len, len);
      }, 0);
    }
  };

  return (
    <div
      className={`break-all flex flex-col rounded-md border border-gray-700 ${isEditing ? 'p-0' : 'p-1 cursor-text'}`}
      onClick={!isEditing ? handleNoteClick : undefined}
    >
      {isEditing ? (
        <div className="p-1 relative">
          <textarea
            ref={textareaRef}
            value={noteText}
            onChange={handleChange}
            onKeyDown={handleKeyDown}
            onBlur={handleBlur}
            onFocus={handleFocus}
            placeholder="Add notes"
            className="bg-transparent outline-none resize-none p-3 w-full text-sm text-gray-200 "
            maxLength={maxLength}
          />
          <div className="absolute bottom-0 right-0 text-right text-xs text-gray-400 pr-2.5 pb-2">
            {maxLength - charCount} Characters
          </div>
        </div>
      ) : (
        <div className="relative p-3 w-full text-sm pb-10 max-h-3.5 ">
          {noteText ? (
            <span className="text-gray-200 line-clamp-2">{noteText}</span>
          ) : (
            <span className="text-gray-400">Add notes</span>
          )}
          <div className="absolute bottom-0 right-0 text-right text-xs text-gray-400 pr-2 pb-1">
            {maxLength - charCount} Characters
          </div>
        </div>
      )}
    </div>
  );
};

export default TextArea;
