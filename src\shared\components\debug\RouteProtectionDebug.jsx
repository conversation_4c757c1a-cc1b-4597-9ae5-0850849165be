import React from 'react';
import { useSelector } from 'react-redux';
import { useLocation } from 'react-router-dom';

/**
 * Debug component to help troubleshoot route protection issues
 * This component shows the current auth state and route information
 */
const RouteProtectionDebug = () => {
  const authState = useSelector(state => state.auth);
  const location = useLocation();

  const {
    isAuthenticated,
    user,
    token,
    allocatedBrands,
    organizationBrands,
    socialProfiles,
    status,
    error
  } = authState;

  // Determine user type
  const isBrandUser = (allocatedBrands && allocatedBrands.length > 0) || 
                     (organizationBrands && organizationBrands.length > 0);
  const isInfluencerUser = socialProfiles && socialProfiles.length > 0;

  let userType = null;
  if (isBrandUser) {
    userType = 'brand';
  } else if (isInfluencerUser) {
    userType = 'influencer';
  }

  return (
    <div className="fixed bottom-4 right-4 bg-white border-2 border-gray-300 rounded-lg shadow-lg p-4 max-w-md z-50">
      <h3 className="text-lg font-bold text-gray-800 mb-3">🔍 Route Protection Debug</h3>
      
      {/* Current Route */}
      <div className="mb-3">
        <h4 className="font-semibold text-gray-700">Current Route:</h4>
        <p className="text-sm text-gray-600">{location.pathname}</p>
        <p className="text-xs text-gray-500">Search: {location.search || 'None'}</p>
        <p className="text-xs text-gray-500">State: {location.state ? JSON.stringify(location.state) : 'None'}</p>
      </div>

      {/* Authentication Status */}
      <div className="mb-3">
        <h4 className="font-semibold text-gray-700">Authentication:</h4>
        <div className="text-sm">
          <p className={`${isAuthenticated ? 'text-green-600' : 'text-red-600'}`}>
            ✓ Authenticated: {isAuthenticated ? 'Yes' : 'No'}
          </p>
          <p className={`${token ? 'text-green-600' : 'text-red-600'}`}>
            ✓ Token: {token ? 'Present' : 'Missing'}
          </p>
          <p className={`${user ? 'text-green-600' : 'text-red-600'}`}>
            ✓ User: {user ? 'Present' : 'Missing'}
          </p>
          <p className="text-gray-600">Status: {status}</p>
          {error && <p className="text-red-600">Error: {error}</p>}
        </div>
      </div>

      {/* User Type Detection */}
      <div className="mb-3">
        <h4 className="font-semibold text-gray-700">User Type:</h4>
        <div className="text-sm">
          <p className={`${userType ? 'text-green-600' : 'text-yellow-600'}`}>
            Type: {userType || 'Undetermined'}
          </p>
          <p className={`${isBrandUser ? 'text-green-600' : 'text-gray-500'}`}>
            ✓ Brand User: {isBrandUser ? 'Yes' : 'No'}
          </p>
          <p className={`${isInfluencerUser ? 'text-green-600' : 'text-gray-500'}`}>
            ✓ Influencer User: {isInfluencerUser ? 'Yes' : 'No'}
          </p>
        </div>
      </div>

      {/* User Data */}
      <div className="mb-3">
        <h4 className="font-semibold text-gray-700">User Data:</h4>
        <div className="text-xs text-gray-600">
          <p>Allocated Brands: {allocatedBrands?.length || 0}</p>
          <p>Organization Brands: {organizationBrands?.length || 0}</p>
          <p>Social Profiles: {socialProfiles?.length || 0}</p>
          {user && (
            <div className="mt-1">
              <p>User ID: {user.id || 'N/A'}</p>
              <p>Email: {user.email || 'N/A'}</p>
              <p>Name: {user.name || 'N/A'}</p>
            </div>
          )}
        </div>
      </div>

      {/* Expected Behavior */}
      <div className="mb-3">
        <h4 className="font-semibold text-gray-700">Expected Behavior:</h4>
        <div className="text-xs text-gray-600">
          {!isAuthenticated ? (
            <p className="text-red-600">Should redirect to login page</p>
          ) : (
            <>
              {location.pathname.includes('/brand') && !isBrandUser && (
                <p className="text-yellow-600">Should redirect to influencer dashboard</p>
              )}
              {location.pathname.includes('/influencer') && !isInfluencerUser && (
                <p className="text-yellow-600">Should redirect to brand dashboard</p>
              )}
              {(location.pathname.includes('/login') || location.pathname.includes('/signin')) && (
                <p className="text-yellow-600">Should redirect to appropriate dashboard</p>
              )}
              {!location.pathname.includes('/login') && !location.pathname.includes('/signin') && 
               ((location.pathname.includes('/brand') && isBrandUser) || 
                (location.pathname.includes('/influencer') && isInfluencerUser)) && (
                <p className="text-green-600">Should allow access</p>
              )}
            </>
          )}
        </div>
      </div>

      {/* Quick Actions */}
      <div className="text-xs">
        <h4 className="font-semibold text-gray-700 mb-1">Quick Debug:</h4>
        <button 
          onClick={() => console.log('Auth State:', authState)}
          className="px-2 py-1 bg-blue-500 text-white rounded text-xs mr-2 hover:bg-blue-600"
        >
          Log Auth State
        </button>
        <button 
          onClick={() => console.log('Location:', location)}
          className="px-2 py-1 bg-green-500 text-white rounded text-xs hover:bg-green-600"
        >
          Log Location
        </button>
      </div>
    </div>
  );
};

export default RouteProtectionDebug;
