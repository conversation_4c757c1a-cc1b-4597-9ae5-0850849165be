import React from 'react';
import Section from '@assets/Section.svg';
import brandSection from '@assets/BrandsSection.svg';

const AuthLayout = ({ children, type = 'creator' }) => {
    return (
        <div className="flex flex-col md:flex-row h-screen bg-primary">
            {/* Left Section */}
            <div className="relative w-1/3 -mt-25 hidden md:block overflow-hidden">
                <img
                    src={type === 'creator' ? Section : brandSection}
                    alt="Poster"
                    className="w-full h-full object-cover"
                    fetchPriority='high'
                    loading="eager"
                    decoding="async"
                />
                <div className="absolute top-30 left-1/2 transform -translate-x-1/2 w-full px-4 text-center">
                    {type === 'creator' ? (
                        <h1 className="text-xl md:text-3xl font-semibold text-[#F8FFAA]">
                            Collate, Collab, Create.
                        </h1>
                    ) : (
                        <h1 className="text-xl md:text-3xl font-semibold text-[#F8FFAA] leading-tight">
                            Discover and Partner <br />
                            <span className="text-brand-500">with Creators</span>
                        </h1>
                    )}
                </div>
            </div>

            {/* Right Section */}
            <div className="w-full md:w-2/3  flex justify-center items-center px-4 py-10 md:py-0">
                <div className="w-full max-w-md  max-h-screen">
                    {children}
                </div>
            </div>
        </div>
    );
};

export default AuthLayout;
