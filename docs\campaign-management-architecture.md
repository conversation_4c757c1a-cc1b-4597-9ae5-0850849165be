# Campaign Management Feature - Architecture Documentation

## Overview

This document outlines the comprehensive architecture for the Campaign Management feature in CreatorVerse Frontend. The feature implements nested routing, Redux state management, and multi-backend integration following existing project patterns.

## Route Structure

### Current Implementation
```
/brand/campaigns → CampaignManagement (Campaign List)
/brand/campaigns/:campaignId → CampaignPage (Campaign Details Layout)
/brand/campaigns/:campaignId/creators → CreatorListPage (Basic implementation)
```

### Enhanced Route Structure
```
/brand/campaigns
├── CampaignManagement (Campaign List View)
└── :campaignId
    ├── CampaignPage (Campaign Details Layout)
    ├── creators → CreatorsTab (Creator Table)
    ├── content → ContentTab (Content Approval)
    ├── conversation → ConversationTab (Chat Interface)
    ├── analytics → AnalyticsTab (Campaign Metrics)
    └── details → DetailsTab (Campaign Settings)
```

### Router Configuration
```javascript
<Route path="/brand/campaigns" element={<CampaignManagement />}>
  <Route path=":campaignId" element={<CampaignPage />}>
    <Route index element={<Navigate to="creators" replace />} />
    <Route path="creators" element={<CreatorsTab />} />
    <Route path="content" element={<ContentTab />} />
    <Route path="conversation" element={<ConversationTab />} />
    <Route path="analytics" element={<AnalyticsTab />} />
    <Route path="details" element={<DetailsTab />} />
  </Route>
</Route>
```

## Redux State Architecture

### Campaign Slice Structure
```javascript
const initialState = {
  // Campaign List Management
  campaigns: [],
  listStatus: RequestStatus.IDLE,
  listError: null,
  searchQuery: '',
  statusFilter: 'All',
  
  // Campaign Details
  selectedCampaign: null,
  campaignCreators: [],
  detailsStatus: RequestStatus.IDLE,
  detailsError: null,
  
  // Content Management
  contentItems: [],
  pendingApprovals: [],
  contentStatus: RequestStatus.IDLE,
  contentError: null,
  
  // Conversation Management
  conversations: [],
  activeConversation: null,
  messages: [],
  conversationStatus: RequestStatus.IDLE,
  conversationError: null,
  typingUsers: [],
  
  // Analytics
  campaignMetrics: null,
  performanceData: [],
  analyticsStatus: RequestStatus.IDLE,
  analyticsError: null,
  
  // UI State
  activeTab: 'creators',
  isCreatingCampaign: false,
  selectedCreators: [],
};
```

## Multi-Backend API Integration

### Campaign Management API Service
```javascript
// Uses dedicated campaignInstance for campaign backend service
const campaignManagementApi = {
  // Campaign CRUD Operations
  getCampaigns: (params) => campaignInstance.Get('/campaigns', { params }),
  getCampaignById: (id) => campaignInstance.Get(`/campaigns/${id}`),
  createCampaign: (data) => campaignInstance.Post('/campaigns', data),
  updateCampaign: (id, data) => campaignInstance.Put(`/campaigns/${id}`, data),
  deleteCampaign: (id) => campaignInstance.Delete(`/campaigns/${id}`),
  
  // Campaign Creators
  getCampaignCreators: (campaignId) => campaignInstance.Get(`/campaigns/${campaignId}/creators`),
  addCreatorToCampaign: (campaignId, creatorData) => campaignInstance.Post(`/campaigns/${campaignId}/creators`, creatorData),
  removeCreatorFromCampaign: (campaignId, creatorId) => campaignInstance.Delete(`/campaigns/${campaignId}/creators/${creatorId}`),
  
  // Content Management
  getCampaignContent: (campaignId) => campaignInstance.Get(`/campaigns/${campaignId}/content`),
  approveContent: (campaignId, contentId) => campaignInstance.Post(`/campaigns/${campaignId}/content/${contentId}/approve`),
  rejectContent: (campaignId, contentId, reason) => campaignInstance.Post(`/campaigns/${campaignId}/content/${contentId}/reject`, { reason }),
  
  // Analytics
  getCampaignAnalytics: (campaignId) => campaignInstance.Get(`/campaigns/${campaignId}/analytics`),
  getCampaignMetrics: (campaignId, timeRange) => campaignInstance.Get(`/campaigns/${campaignId}/metrics`, { params: { timeRange } }),
};
```

### Conversation API Service (Separate Backend)
```javascript
const conversationApi = {
  // Conversation Management
  getCampaignConversations: (campaignId) => conversationInstance.Get(`/campaigns/${campaignId}/conversations`),
  createConversation: (campaignId, data) => conversationInstance.Post(`/campaigns/${campaignId}/conversations`, data),
  
  // Messages
  getMessages: (conversationId, params) => conversationInstance.Get(`/conversations/${conversationId}/messages`, { params }),
  sendMessage: (conversationId, message) => conversationInstance.Post(`/conversations/${conversationId}/messages`, message),
  markAsRead: (conversationId, messageIds) => conversationInstance.Post(`/conversations/${conversationId}/mark-read`, { messageIds }),
  
  // Real-time Features
  joinConversation: (conversationId) => conversationInstance.Post(`/conversations/${conversationId}/join`),
  leaveConversation: (conversationId) => conversationInstance.Post(`/conversations/${conversationId}/leave`),
  setTyping: (conversationId, isTyping) => conversationInstance.Post(`/conversations/${conversationId}/typing`, { isTyping }),
};
```

## File Structure

```
src/features/brand/
├── pages/
│   ├── CampaignManagement.jsx (✓ exists)
│   ├── CampaignPage.jsx (✓ exists - needs enhancement)
│   └── campaign/
│       ├── CreatorsTab.jsx
│       ├── ContentTab.jsx
│       ├── ConversationTab.jsx
│       ├── AnalyticsTab.jsx
│       └── DetailsTab.jsx
├── components/
│   ├── CampaignListCard.jsx (✓ exists)
│   ├── campaign/
│   │   ├── CampaignHeader.jsx
│   │   ├── CampaignTabNavigation.jsx
│   │   ├── content/
│   │   │   ├── ContentGrid.jsx
│   │   │   ├── ContentCard.jsx
│   │   │   ├── ApprovalModal.jsx
│   │   │   └── ContentPreview.jsx
│   │   ├── conversation/
│   │   │   ├── ConversationList.jsx
│   │   │   ├── ChatWindow.jsx
│   │   │   ├── MessageBubble.jsx
│   │   │   ├── MessageInput.jsx
│   │   │   └── TypingIndicator.jsx
│   │   ├── analytics/
│   │   │   ├── MetricsCard.jsx
│   │   │   ├── PerformanceChart.jsx
│   │   │   └── ExportButton.jsx
│   │   └── creators/
│   │       ├── CampaignCreatorTable.jsx
│   │       └── AddCreatorModal.jsx
├── services/
│   ├── campaignSlice.js
│   ├── campaignThunks.js
│   └── conversationSlice.js
└── hooks/
    ├── useCampaignDetails.js
    ├── useConversation.js
    └── useCampaignAnalytics.js
```

## Implementation Phases

### Phase 1: Core Campaign Management ✅ COMPLETED
1. ✅ Enhanced CampaignPage with tab navigation
2. ✅ Campaign Redux slice and thunks
3. ✅ Campaign API service integration
4. ✅ Basic CRUD operations
5. ✅ All 5 tab components created (CreatorsTab, ContentTab, ConversationTab, AnalyticsTab, DetailsTab)
6. ✅ Router configuration updated with nested routes
7. ✅ Redux store updated with campaign reducer

### Phase 2: Creator Management Tab
1. CreatorsTab component
2. Creator table with existing patterns
3. Add/remove creator functionality
4. Creator assignment workflows

### Phase 3: Content Management Tab
1. ContentTab component
2. Content grid and approval system
3. Content preview functionality
4. Approval/rejection workflows

### Phase 4: Analytics Tab
1. AnalyticsTab component
2. Campaign metrics integration
3. Performance charts and visualizations
4. Export functionality

### Phase 5: Details Tab
1. DetailsTab component
2. Campaign settings management
3. Creator requirements configuration
4. Content guidelines management

### Phase 6: Conversation System
1. Conversation API integration
2. ConversationTab component
3. Real-time chat functionality
4. WebSocket integration
5. Multi-creator conversation support

## Key Integration Points

### Authentication Flow
- Leverage existing auth system for campaign API calls
- Token sharing between campaign and conversation services
- Role-based access control for campaign features

### Data Consistency
- Campaign context preservation across tabs
- Optimistic updates for better UX
- Error handling and retry mechanisms
- Cache invalidation strategies

### UI/UX Consistency
- Follow existing component patterns (DataTable, PopupLayout, etc.)
- Maintain consistent styling with current design system
- Responsive design for all campaign management features
- Loading states and error boundaries

## Real-time Conversation Integration

### WebSocket Integration Strategy
```javascript
class ConversationWebSocket {
  constructor() {
    this.socket = null;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
  }

  connect(campaignId, token) {
    const wsUrl = `${CONVERSATION_WS_URL}/campaigns/${campaignId}/ws?token=${token}`;
    this.socket = new WebSocket(wsUrl);
    
    this.socket.onmessage = (event) => {
      const data = JSON.parse(event.data);
      store.dispatch(handleWebSocketMessage(data));
    };
  }

  sendMessage(message) {
    if (this.socket?.readyState === WebSocket.OPEN) {
      this.socket.send(JSON.stringify(message));
    }
  }
}
```

## Environment Configuration

### Campaign Management Instance ✅ IMPLEMENTED
```javascript
// src/app/store/api/instances/campaignInstance.js
const campaignInstance = createAlova({
  baseURL: apiConfig.campaignServiceURL,
  requestAdapter: FetchAdapter(),
  statesHook: ReactHook,
  // Token refresh via auth service
  // Development headers support
  // Error handling and logging
});

// API Configuration Updated
const serviceEndpoints = {
  development: {
    // ... existing services
    campaign: {
      baseURL: import.meta.env.VITE_CAMPAIGN_SERVICE_URL || 'http://localhost:8001',
      version: 'v1',
      timeout: 30000,
      retryAttempts: 3,
    },
    conversation: {
      baseURL: import.meta.env.VITE_CONVERSATION_SERVICE_URL || 'http://localhost:8002',
      version: 'v1',
      timeout: 30000,
      retryAttempts: 3,
      websocket: true,
    }
  }
};
```

---

## Current Implementation Status

### ✅ Completed Features
- **Campaign Management Page**: Enhanced with tab navigation and nested routing
- **Redux Architecture**: Complete campaign slice with state management
- **API Service Layer**: Campaign management API service following existing patterns
- **Campaign Instance**: Dedicated campaignInstance for separate backend service
- **Tab Components**: All 5 tab components implemented with mock data
- **Router Integration**: Nested routes configured for campaign tabs
- **Navigation**: Tab-based navigation with active state management
- **Error Handling**: Avatar fallbacks and proper error boundaries

### 🔄 Current State
- All components use mock data for development and testing
- Ready for backend API integration when campaign service is available
- Conversation tab prepared for separate backend integration

### 🎯 Next Steps
1. **Backend Integration**: Replace mock data with actual API calls when campaign service is ready
2. **Real-time Features**: Implement WebSocket integration for conversation tab
3. **Enhanced Features**: Add advanced filtering, search, and export functionality
4. **Testing**: Comprehensive testing of all campaign management features

---

**Status**: Phase 1 Complete - Ready for Backend Integration
**Last Updated**: 2025-07-12
**Next Phase**: Backend API Integration and Real-time Features
