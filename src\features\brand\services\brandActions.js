import { useDispatch } from 'react-redux';
import {
    getBrandInfoThunk,
    createBrandThunk,
    requestBrandAccessThunk,
    searchCreatorsThunk,
    getFilterMetadataThunk,
    transformFiltersThunk,
    invalidateFilterCacheThunk,
    getCacheStatsThunk,
    cleanupCacheThunk,
    getSavedFiltersThunk,
    getSavedFilterSetThunk,
    createSavedFilterThunk,
    updateSavedFilterThunk,
    deleteSavedFilterThunk,
    getGlobalFiltersThunk,
    setSelectedBrand,
    setActiveFilters,
    setSearchQuery,
    clearSearchResults,
    clearErrors,
    clearBrandError,
    clearSearchError,
    clearFilterError,
    clearSavedFilterError,
    clearCacheError,
    setCurrentFilterSet,
    clearTransformedFilters
} from './brandSlice';

/**
 * Custom hook for using brand-related actions in components
 * This provides a clean interface for components to interact with brand state
 */
const useBrandActions = () => {
    const dispatch = useDispatch();

    return {
        // === BRAND MANAGEMENT ACTIONS ===
        
        /**
         * Get brand information for the current user
         */
        getBrandInfo: () => dispatch(getBrandInfoThunk()),
        
        /**
         * Create a new brand
         * @param {Object} brandData - Brand creation data
         */
        createBrand: (brandData) => dispatch(createBrandThunk(brandData)),
        
        /**
         * Request access to a specific brand
         * @param {string} brandId - Brand ID to request access to
         */
        requestBrandAccess: (brandId) => dispatch(requestBrandAccessThunk(brandId)),

        // === CREATOR DISCOVERY ACTIONS ===
        
        /**
         * Search for creators with filters
         * @param {Object} searchParams - Search parameters including query, filters, pagination
         */
        searchCreators: (searchParams) => dispatch(searchCreatorsThunk(searchParams)),
        
        /**
         * Get filter metadata for a platform channel
         * @param {Object} params - Filter metadata parameters
         */
        getFilterMetadata: (params) => dispatch(getFilterMetadataThunk(params)),

        /**
         * Transform filters to provider format
         * @param {Object} transformData - Filter transformation data
         */
        transformFilters: (transformData) => dispatch(transformFiltersThunk(transformData)),

        /**
         * Invalidate filter cache
         * @param {Object} params - Cache invalidation parameters
         */
        invalidateFilterCache: (params = {}) => dispatch(invalidateFilterCacheThunk(params)),

        /**
         * Get cache statistics
         * @param {string} platform - Platform filter (optional)
         */
        getCacheStats: (platform) => dispatch(getCacheStatsThunk(platform)),

        /**
         * Cleanup expired cache entries
         */
        cleanupCache: () => dispatch(cleanupCacheThunk()),

        // === SAVED FILTERS ACTIONS ===
        
        /**
         * Get saved filter sets
         * @param {Object} params - Query parameters for saved filters
         */
        getSavedFilters: (params = {}) => dispatch(getSavedFiltersThunk(params)),

        /**
         * Get specific saved filter set
         * @param {string} filterSetId - Filter set ID
         */
        getSavedFilterSet: (filterSetId) => dispatch(getSavedFilterSetThunk(filterSetId)),

        /**
         * Get global filter sets
         * @param {Object} params - Query parameters for global filters
         */
        getGlobalFilters: (params = {}) => dispatch(getGlobalFiltersThunk(params)),
        
        /**
         * Create a new saved filter set
         * @param {Object} filterData - Filter set data
         */
        createSavedFilter: (filterData) => dispatch(createSavedFilterThunk(filterData)),
        
        /**
         * Update an existing saved filter set
         * @param {string} filterSetId - Filter set ID
         * @param {Object} filterData - Updated filter data
         */
        updateSavedFilter: (filterSetId, filterData) => 
            dispatch(updateSavedFilterThunk({ filterSetId, filterData })),
        
        /**
         * Delete a saved filter set
         * @param {string} filterSetId - Filter set ID to delete
         */
        deleteSavedFilter: (filterSetId) => dispatch(deleteSavedFilterThunk(filterSetId)),

        // === UI STATE MANAGEMENT ACTIONS ===
        
        /**
         * Set the currently selected brand
         * @param {Object} brand - Brand object to select
         */
        setSelectedBrand: (brand) => dispatch(setSelectedBrand(brand)),
        
        /**
         * Set active filters for search
         * @param {Array} filters - Array of active filters
         */
        setActiveFilters: (filters) => dispatch(setActiveFilters(filters)),
        
        /**
         * Set search query
         * @param {string} query - Search query string
         */
        setSearchQuery: (query) => dispatch(setSearchQuery(query)),
        
        /**
         * Clear search results and reset search state
         */
        clearSearchResults: () => dispatch(clearSearchResults()),

        // === ERROR MANAGEMENT ACTIONS ===
        
        /**
         * Clear all brand-related errors
         */
        clearAllErrors: () => dispatch(clearErrors()),
        
        /**
         * Clear brand management errors
         */
        clearBrandError: () => dispatch(clearBrandError()),
        
        /**
         * Clear search-related errors
         */
        clearSearchError: () => dispatch(clearSearchError()),
        
        /**
         * Clear filter-related errors
         */
        clearFilterError: () => dispatch(clearFilterError()),
        
        /**
         * Clear saved filter errors
         */
        clearSavedFilterError: () => dispatch(clearSavedFilterError()),

        /**
         * Clear cache-related errors
         */
        clearCacheError: () => dispatch(clearCacheError()),

        // === ADVANCED UI STATE MANAGEMENT ===

        /**
         * Set current filter set
         * @param {Object} filterSet - Filter set object
         */
        setCurrentFilterSet: (filterSet) => dispatch(setCurrentFilterSet(filterSet)),

        /**
         * Clear transformed filters
         */
        clearTransformedFilters: () => dispatch(clearTransformedFilters()),
    };
};

export default useBrandActions;
