import React from "react";
import PropTypes from "prop-types";
import MessageCircle from '@assets/icon/chat.svg';
import Heart from '@assets/icon/heart.svg';
import Share from '@assets/icon/send.svg';

const PostCard = ({ image, label, likes, comments, shares, username, description, timeAgo, badge }) => {
    return (
        <div className="flex flex-col bg-white rounded-xl shadow-lg flex-shrink-0 border border-gray-200 py-3 gap-2 ">
            <div className="relative w-full">
                <img src={image} alt={`Post by ${username}`} className="w-full md:h-61 lg:h-61 2xl:h-75 object-cover " />
                {badge && (
                    <span className="absolute top-3 right-3 flex items-center justify-center bg-white rounded-full shadow-md px-3 py-2 text-xs font-bold text-[#6C4AB6] border border-[#E0D7F3]">
                        <img src={badge.icon} alt="badge" className="w-6 h-6 mr-2" />
                        {badge.text}
                    </span>
                )}
                {label && (
                    <span className="absolute bottom-3 left-3 bg-[#232C3D] text-white rounded-full px-3 py-1 text-xs font-semibold shadow">
                        {label}
                    </span>
                )}
            </div>
            <div className="flex flex-col gap-2 text-black text-8-medium px-2.5">
                <div className="flex justify-start text-black gap-2">
                    <div className="flex items-center gap-1.5">
                        <img src={Heart} alt="Likes" width={16} height={16} />
                        <span className="font-medium">{likes.toLocaleString()}</span> Likes
                    </div>
                    <div className="flex items-center gap-1.5">
                        <img src={MessageCircle} alt="Comments" width={16} height={16} />
                        <span className="font-medium">{comments.toLocaleString()}</span> Comments
                    </div>
                    <div className="flex items-center gap-1.5">
                        <img src={Share} alt="Shares" width={16} height={16} />
                        <span className="font-medium">{shares.toLocaleString()}</span> Shares
                    </div>
                </div>
                <div className="flex items-center gap-1.5">
                    <span className="font-bold">{username}</span>
                    <span className="text-roboto-light">{description}</span>
                </div>
                <div className="flex justify-start items-center">
                    <span className="text-roboto-light text-black/40">{timeAgo}</span>
                </div>
            </div>
        </div>
    );
};

PostCard.propTypes = {
    image: PropTypes.string.isRequired,
    label: PropTypes.string,
    likes: PropTypes.number,
    comments: PropTypes.number,
    shares: PropTypes.number,
    username: PropTypes.string,
    timeAgo: PropTypes.string,
    badge: PropTypes.shape({
        icon: PropTypes.string,
        text: PropTypes.string,
    }),
};

PostCard.defaultProps = {
    label: "",
    likes: 0,
    comments: 0,
    shares: 0,
    username: "model.susan xyz",
    timeAgo: "3 HOURS AGO",
    badge: null,
};

export default PostCard;
