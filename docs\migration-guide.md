# Migration Guide: Single Backend to Multi-Backend Architecture

This guide helps you migrate from the old single-backend approach to the new multi-backend architecture in CreatorVerse Frontend.

## Overview of Changes

The application has been upgraded from a single backend service to a **multi-backend architecture** with two specialized services:

1. **Authentication Service** - Handles user authentication and authorization
2. **Discovery & Analytics Service** - Handles creator discovery and analytics

## Migration Steps

### 1. Update Imports

#### Before (Single Backend)
```javascript
import alovaInstance from '@/app/store/api/alovaInstance';
```

#### After (Multi-Backend)
```javascript
// For authentication operations
import { apiServices } from '@/app/store/api';
// OR import specific services
import authApi from '@/app/store/api/authApi';
import discoveryApi from '@/app/store/api/discoveryApi';
```

### 2. Update API Calls

#### Authentication Endpoints

**Before:**
```javascript
// Login
const response = await alovaInstance.Post('/v1/auth/influencer/login', credentials);

// Get current user
const user = await alovaInstance.Get('/v1/auth/me');

// Brand operations
const brands = await alovaInstance.Get('/v1/auth/brand/userinfo');
```

**After:**
```javascript
// Login
const response = await apiServices.auth.login(credentials);

// Get current user  
const user = await apiServices.auth.getCurrentUser();

// Brand operations
const brands = await apiServices.brandManagement.requestUserBrandInfo();
```

#### Discovery & Analytics Endpoints

**Before:**
```javascript
// Search creators (if this existed in old system)
const results = await alovaInstance.Post('/v1/discovery/search', searchParams);

// Get analytics (if this existed in old system)
const analytics = await alovaInstance.Get(`/v1/analytics/profiles/${profileId}`);
```

**After:**
```javascript
// Search creators
const results = await apiServices.discovery.searchCreators(searchParams);

// Get analytics
const analytics = await apiServices.analytics.getBasicProfile(profileId);
```

### 3. Update Error Handling

#### Before:
```javascript
try {
  const response = await alovaInstance.Post('/v1/auth/login', credentials);
} catch (error) {
  console.error('API error:', error);
  // Manual error handling
}
```

#### After:
```javascript
import { ERROR_TYPES } from '@/app/store/api';

try {
  const response = await apiServices.auth.login(credentials);
} catch (error) {
  // Enhanced error handling with automatic retry and token refresh
  if (error.type === ERROR_TYPES.AUTHENTICATION_ERROR) {
    // Auth error - token refresh attempted automatically
  } else if (error.type === ERROR_TYPES.SERVICE_UNAVAILABLE) {
    // Service unavailable - circuit breaker may be open
  }
}
```

### 4. Environment Configuration

#### Before:
```javascript
// Single API URL in environment
const API_BASE_URL = 'https://your-api.com';
```

#### After:
```javascript
// .env.development
VITE_AUTH_SERVICE_URL=https://auth-dev.creatorverse.com
VITE_DISCOVERY_SERVICE_URL=https://discovery-dev.creatorverse.com

// .env.production  
VITE_AUTH_SERVICE_URL=https://auth.creatorverse.com
VITE_DISCOVERY_SERVICE_URL=https://discovery.creatorverse.com
```

### 5. Initialize the System

#### New Requirement:
```javascript
// In your main app file or initialization
import { initializeApiSystem } from '@/app/store/api';

// Initialize the multi-backend system
await initializeApiSystem({
  enableHealthMonitoring: true,
  enableRetry: true
});
```

## Backward Compatibility

The old `alovaInstance` is still available for backward compatibility, but it now automatically routes requests to the appropriate backend service. However, we recommend migrating to the new API services for better type safety and features.

### Using Legacy Instance (Not Recommended)
```javascript
// This still works but is deprecated
import alovaInstance from '@/app/store/api/alovaInstance';

// Automatically routes to auth service
const response = await alovaInstance.Post('/v1/auth/login', credentials);

// Automatically routes to discovery service  
const results = await alovaInstance.Post('/v1/discovery/search', params);
```

## Component Migration Examples

### Authentication Component

#### Before:
```javascript
import { useEffect, useState } from 'react';
import alovaInstance from '@/app/store/api/alovaInstance';

const LoginComponent = () => {
  const [loading, setLoading] = useState(false);
  
  const handleLogin = async (credentials) => {
    setLoading(true);
    try {
      const response = await alovaInstance.Post('/v1/auth/influencer/login', credentials);
      // Handle response
    } catch (error) {
      console.error('Login failed:', error);
    } finally {
      setLoading(false);
    }
  };
  
  return (
    // JSX
  );
};
```

#### After:
```javascript
import { useEffect, useState } from 'react';
import { apiServices, ERROR_TYPES } from '@/app/store/api';

const LoginComponent = () => {
  const [loading, setLoading] = useState(false);
  
  const handleLogin = async (credentials) => {
    setLoading(true);
    try {
      const response = await apiServices.auth.login(credentials);
      // Handle response - automatic token refresh and error handling
    } catch (error) {
      if (error.type === ERROR_TYPES.RATE_LIMIT_ERROR) {
        // Handle rate limiting
      } else {
        console.error('Login failed:', error);
      }
    } finally {
      setLoading(false);
    }
  };
  
  return (
    // JSX
  );
};
```

### Discovery Component

#### New Component (Discovery didn't exist in old system):
```javascript
import { useState, useEffect } from 'react';
import { apiServices, healthMonitor } from '@/app/store/api';

const CreatorDiscoveryComponent = () => {
  const [creators, setCreators] = useState([]);
  const [loading, setLoading] = useState(false);
  const [serviceHealth, setServiceHealth] = useState(null);
  
  useEffect(() => {
    // Monitor service health
    const health = healthMonitor.getServiceHealth('discovery');
    setServiceHealth(health);
  }, []);
  
  const searchCreators = async (searchParams) => {
    if (!healthMonitor.isServiceAvailable('discovery')) {
      console.warn('Discovery service is unavailable');
      return;
    }
    
    setLoading(true);
    try {
      const response = await apiServices.discovery.searchCreators(searchParams);
      setCreators(response.data.profiles);
    } catch (error) {
      console.error('Search failed:', error);
    } finally {
      setLoading(false);
    }
  };
  
  return (
    // JSX with service health indicator
  );
};
```

## Testing Migration

### 1. Unit Tests
Update your unit tests to use the new API services:

```javascript
// Before
import alovaInstance from '@/app/store/api/alovaInstance';
vi.mock('@/app/store/api/alovaInstance');

// After
import { apiServices } from '@/app/store/api';
vi.mock('@/app/store/api', () => ({
  apiServices: {
    auth: {
      login: vi.fn(),
      getCurrentUser: vi.fn(),
    },
    discovery: {
      searchCreators: vi.fn(),
    }
  }
}));
```

### 2. Integration Tests
Test the multi-backend system:

```javascript
import { initializeApiSystem, healthMonitor } from '@/app/store/api';

describe('Multi-Backend Integration', () => {
  beforeAll(async () => {
    await initializeApiSystem();
  });
  
  it('should have healthy services', () => {
    const health = healthMonitor.getOverallHealth();
    expect(health.status).not.toBe('unhealthy');
  });
});
```

## Common Issues and Solutions

### 1. CORS Issues
**Problem:** CORS errors when calling different service endpoints.
**Solution:** Ensure both backend services have proper CORS configuration for your frontend domain.

### 2. Authentication Tokens
**Problem:** Tokens not being shared between services.
**Solution:** The new architecture handles this automatically. Ensure you're using the new API services.

### 3. Service Unavailability
**Problem:** One service is down, affecting the entire application.
**Solution:** Use health monitoring and implement graceful degradation:

```javascript
import { healthMonitor, apiServices } from '@/app/store/api';

const isDiscoveryAvailable = healthMonitor.isServiceAvailable('discovery');
if (isDiscoveryAvailable) {
  // Use discovery features
} else {
  // Show fallback UI
}
```

### 4. Environment Configuration
**Problem:** Services not connecting in different environments.
**Solution:** Verify environment variables are set correctly for each environment.

## Performance Considerations

1. **Parallel Requests:** You can now make parallel requests to different services:
```javascript
const [authData, discoveryData] = await Promise.all([
  apiServices.auth.getCurrentUser(),
  apiServices.discovery.getFilters()
]);
```

2. **Service-Specific Caching:** Each service can have its own caching strategy.

3. **Circuit Breakers:** Failed services won't affect others due to circuit breaker pattern.

## Rollback Plan

If you need to rollback to the single backend approach:

1. Revert to using `alovaInstance` directly
2. Update environment variables to point to single backend
3. Disable health monitoring
4. Remove multi-backend specific error handling

The legacy `alovaInstance` provides a safety net during migration.

## Next Steps

1. **Complete Migration:** Gradually migrate all components to use new API services
2. **Remove Legacy Code:** Once migration is complete, remove `alovaInstance` usage
3. **Monitor Performance:** Use health monitoring to track service performance
4. **Optimize:** Fine-tune timeout, retry, and caching settings based on usage patterns

## Support

For questions or issues during migration:
1. Check the health monitor for service status
2. Review error logs for specific error types
3. Consult the multi-backend architecture documentation
4. Test with the comprehensive test suite provided
