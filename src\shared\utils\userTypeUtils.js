/**
 * Utility functions for determining user type and handling user-specific logic
 */

/**
 * Determine user type based on auth state data
 * @param {Object} authState - The auth state from Redux store
 * @returns {string|null} - 'brand', 'influencer', or null if undetermined
 */
export const getUserType = (authState) => {
  if (!authState || !authState.isAuthenticated) {
    return null;
  }

  const { allocatedBrands, organizationBrands, socialProfiles } = authState;

  // Check if user is a brand user
  const isBrandUser = (allocatedBrands && allocatedBrands.length > 0) || 
                     (organizationBrands && organizationBrands.length > 0);

  // Check if user is an influencer user
  const isInfluencerUser = socialProfiles && socialProfiles.length > 0;

  if (isBrandUser) {
    console.log('Brand user detected');
    return 'brand';
  } else if (isInfluencerUser) {
    console.log('Influencer user detected');
    return 'influencer';
  }

  return null;
};

/**
 * Check if user is a brand user
 * @param {Object} authState - The auth state from Redux store
 * @returns {boolean} - True if user is a brand user
 */
export const isBrandUser = (authState) => {
  return getUserType(authState) === 'brand';
};

/**
 * Check if user is an influencer user
 * @param {Object} authState - The auth state from Redux store
 * @returns {boolean} - True if user is an influencer user
 */
export const isInfluencerUser = (authState) => {
  return getUserType(authState) === 'influencer';
};

/**
 * Get the appropriate dashboard path for a user
 * @param {Object} authState - The auth state from Redux store
 * @returns {string} - Dashboard path for the user type
 */
export const getUserDashboardPath = (authState) => {
  const userType = getUserType(authState);
  
  switch (userType) {
    case 'brand':
      return '/brand/dashboard';
    case 'influencer':
      return '/influencer/dashboard';
    default:
      return '/influencer/dashboard'; // Default fallback
  }
};

/**
 * Get the appropriate login path for a user type
 * @param {string} userType - 'brand' or 'influencer'
 * @returns {string} - Login path for the user type
 */
export const getLoginPath = (userType) => {
  switch (userType) {
    case 'brand':
      return '/brand/signin';
    case 'influencer':
      return '/influencer/login';
    default:
      return '/influencer/login'; // Default fallback
  }
};

/**
 * Get the appropriate login path based on current route
 * @param {string} pathname - Current route pathname
 * @returns {string} - Login path based on route context
 */
export const getLoginPathFromRoute = (pathname) => {
  if (pathname.includes('/brand')) {
    return '/brand/signin';
  }
  return '/influencer/login';
};

/**
 * Check if a route is for a specific user type
 * @param {string} pathname - Route pathname
 * @param {string} userType - 'brand' or 'influencer'
 * @returns {boolean} - True if route matches user type
 */
export const isRouteForUserType = (pathname, userType) => {
  if (userType === 'brand') {
    return pathname.includes('/brand');
  } else if (userType === 'influencer') {
    return pathname.includes('/influencer');
  }
  return false;
};

/**
 * Validate if user can access a specific route type
 * @param {Object} authState - The auth state from Redux store
 * @param {string} routeType - 'brand' or 'influencer'
 * @returns {boolean} - True if user can access the route
 */
export const canUserAccessRoute = (authState, routeType) => {
  const userType = getUserType(authState);
  // console.log('User type:', userType);
  // console.log('Route type:', routeType);
  // console.log('Can access:', userType === routeType);
  
  // If user type is undetermined, allow access (will be handled by auth check)
  if (!userType) {
    return false;
  }
  
  // Check if user type matches route type
  return userType === routeType;
};

/**
 * Get redirect path for mismatched user type and route
 * @param {Object} authState - The auth state from Redux store
 * @param {string} attemptedRouteType - The route type user tried to access
 * @returns {string} - Correct dashboard path for user
 */
export const getRedirectPathForUserType = (authState, attemptedRouteType) => {
  const userType = getUserType(authState);
  
  if (!userType) {
    // If user type is undetermined, redirect based on attempted route
    return attemptedRouteType === 'brand' ? '/brand/signin' : '/influencer/login';
  }
  
  // Redirect to user's appropriate dashboard
  return getUserDashboardPath(authState);
};
