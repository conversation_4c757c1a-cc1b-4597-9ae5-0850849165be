/**
 * Analytics API Service - Multi-Backend Architecture
 * 
 * Handles profile analytics and dashboard data using the dedicated discovery service.
 * This service provides endpoints for:
 * - Profile analytics (basic and detailed)
 * - Dashboard KPIs and metrics
 * - Content analytics
 * - Audience demographics
 * - Performance tracking
 */

import discoveryInstance from './instances/discoveryInstance';

/**
 * Analytics API service
 * Contains all endpoints related to profile analytics and dashboard data
 */
const analyticsApi = {
  
  // === PROFILE ANALYTICS ENDPOINTS ===
  
  /**
   * Get basic profile data (Phase 1 only)
   * @param {string} profileId - Profile ID
   * @returns {Promise} Basic profile data
   */
  getBasicProfile: (profileId) => {
    return discoveryInstance.Get(`/analytics/profiles/${profileId}`);
  },

  /**
   * Get detailed analytics (triggers Phase 2 if needed)
   * @param {string} profileId - Profile ID
   * @param {Object} options - Query options (force_refresh)
   * @returns {Promise} Detailed profile analytics
   */
  getDetailedProfile: (profileId, options = {}) => {
    const queryParams = new URLSearchParams();
    
    if (options.force_refresh !== undefined) {
      queryParams.append('force_refresh', options.force_refresh);
    }
    
    const queryString = queryParams.toString();
    return discoveryInstance.Get(`/analytics/profiles/${profileId}/detailed${queryString ? `?${queryString}` : ''}`);
  },

  /**
   * Manually trigger detailed analytics fetch
   * @param {string} profileId - Profile ID
   * @returns {Promise} Analytics fetch trigger result
   */
  triggerAnalyticsFetch: (profileId) => {
    return discoveryInstance.Post(`/analytics/profiles/${profileId}/fetch-analytics`);
  },

  /**
   * Check analytics fetch status for a profile
   * @param {string} profileId - Profile ID
   * @returns {Promise} Analytics fetch status
   */
  getAnalyticsStatus: (profileId) => {
    return discoveryInstance.Get(`/analytics/profiles/${profileId}/status`);
  },

  /**
   * List profiles with basic information and pagination
   * @param {Object} params - Query parameters (platform, has_detailed, limit, offset)
   * @returns {Promise} Paginated profiles list
   */
  getProfiles: (params = {}) => {
    const queryParams = new URLSearchParams();
    
    if (params.platform) queryParams.append('platform', params.platform);
    if (params.has_detailed !== undefined) queryParams.append('has_detailed', params.has_detailed);
    if (params.limit) queryParams.append('limit', Math.min(params.limit, 100));
    if (params.offset) queryParams.append('offset', Math.max(params.offset, 0));
    
    const queryString = queryParams.toString();
    return discoveryInstance.Get(`/analytics/profiles${queryString ? `?${queryString}` : ''}`);
  },

  // === DASHBOARD KPI ENDPOINTS ===

  /**
   * Get dashboard KPI metrics for a specific profile
   * @param {string} profileId - Profile ID
   * @returns {Promise} Dashboard KPI data
   */
  getDashboardKPIs: (profileId) => {
    return discoveryInstance.Get(`/analytics/dashboard/kpi/${profileId}`);
  },

  /**
   * Get top performing posts for a specific profile
   * @param {string} profileId - Profile ID
   * @param {number} postCount - Number of top posts to return (1-20, default: 5)
   * @returns {Promise} Top posts data
   */
  getTopPosts: (profileId, postCount = 5) => {
    const count = Math.min(Math.max(postCount, 1), 20); // Clamp between 1-20
    return discoveryInstance.Get(`/analytics/dashboard/top-posts/${profileId}?post_count=${count}`);
  },

  /**
   * Get monthly impressions data with 3 series breakdown
   * @param {string} profileId - Profile ID
   * @param {number} period - Number of months to return (1-24, default: 12)
   * @returns {Promise} Monthly impressions data
   */
  getMonthlyImpressions: (profileId, period = 12) => {
    const months = Math.min(Math.max(period, 1), 24); // Clamp between 1-24
    return discoveryInstance.Get(`/analytics/dashboard/monthly-impressions/${profileId}?period=${months}`);
  },

  /**
   * Get monthly engagement rate data with 3 series breakdown
   * @param {string} profileId - Profile ID
   * @param {number} period - Number of months to return (1-24, default: 12)
   * @returns {Promise} Monthly engagement rate data
   */
  getMonthlyEngagementRate: (profileId, period = 12) => {
    const months = Math.min(Math.max(period, 1), 24); // Clamp between 1-24
    return discoveryInstance.Get(`/analytics/dashboard/monthly-engagement-rate/${profileId}?period=${months}`);
  },

  /**
   * Get monthly likes breakdown by content type
   * @param {string} profileId - Profile ID
   * @param {number} period - Number of months to return (1-24, default: 12)
   * @returns {Promise} Monthly likes by content type
   */
  getMonthlyLikesByType: (profileId, period = 12) => {
    const months = Math.min(Math.max(period, 1), 24); // Clamp between 1-24
    return discoveryInstance.Get(`/analytics/dashboard/monthly-likes-by-type/${profileId}?period=${months}`);
  },

  /**
   * Get content type distribution for a profile
   * @param {string} profileId - Profile ID
   * @returns {Promise} Content distribution data
   */
  getContentDistribution: (profileId) => {
    return discoveryInstance.Get(`/analytics/dashboard/content-distribution/${profileId}`);
  },

  // === UTILITY METHODS ===

  /**
   * Build analytics query with common parameters
   * @param {Object} params - Analytics parameters
   * @returns {Object} Formatted analytics query
   */
  buildAnalyticsQuery: (params) => {
    const {
      profileId,
      dateRange = 'last_30_days',
      metrics = ['engagement_rate', 'reach', 'impressions'],
      breakdown = 'daily'
    } = params;

    return {
      profileId,
      dateRange,
      metrics,
      breakdown
    };
  },

  /**
   * Validate profile ID format
   * @param {string} profileId - Profile ID to validate
   * @returns {boolean} Whether profile ID is valid
   */
  validateProfileId: (profileId) => {
    // UUID v4 format validation
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return typeof profileId === 'string' && uuidRegex.test(profileId);
  },

  /**
   * Format analytics data for chart consumption
   * @param {Object} data - Raw analytics data
   * @param {string} chartType - Type of chart (line, bar, pie, etc.)
   * @returns {Object} Formatted chart data
   */
  formatForChart: (data, chartType = 'line') => {
    if (!data || !data.data) return null;

    switch (chartType) {
      case 'line':
        return {
          categories: data.data.dates || [],
          series: data.data.series || []
        };
      case 'pie':
        return data.data.distribution || [];
      case 'bar':
        return {
          categories: data.data.categories || [],
          values: data.data.values || []
        };
      default:
        return data.data;
    }
  },

  /**
   * Calculate engagement rate from metrics
   * @param {Object} metrics - Profile metrics
   * @returns {number} Calculated engagement rate
   */
  calculateEngagementRate: (metrics) => {
    const { likes = 0, comments = 0, shares = 0, followers = 1 } = metrics;
    const totalEngagement = likes + comments + shares;
    return followers > 0 ? (totalEngagement / followers) * 100 : 0;
  }
};

export default analyticsApi;
