# Project Documentation

This directory contains the documentation for the Creatorverse Frontend project.

## Table of Contents

- [Project Structure](#project-structure)
- [Getting Started](#getting-started)
- [Features](./features.md) - Application features and functionality
- [API Endpoints](./api_endpoints.md) - Backend API documentation
- [Service Architecture](./SERVICE_ARCHITECTURE.md) - Redux service layer architecture
- [Logout Implementation](./LOGOUT_IMPLEMENTATION.md) - Comprehensive logout functionality
- [Route Protection](./ROUTE_PROTECTION.md) - Authentication and user type-based route protection
- [Recent Changes](./RECENT_CHANGES.md) - Latest architectural improvements and changes
- [Changelog](./CHANGELOG.md) - Complete project change history
- [Components](#components)
- [Hooks](#hooks)
- [Utilities](#utilities)
- [Internationalization](#internationalization)
- [Styling](#styling)
- [Deployment](#deployment)

