// src/hooks/useCommonUtils.js
import { useCallback } from 'react';

const useUserDetails = () => {

  const formatDate = useCallback((dateStr) => {
    const date = new Date(dateStr);
    return date.toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  }, []);

  const truncateText = useCallback((text, maxLength = 100) => {
    if (!text) return '';
    return text.length > maxLength ? `${text.slice(0, maxLength)}...` : text;
  }, []);

  const getRandomNumber = useCallback((min = 0, max = 100) => {
    return Math.floor(Math.random() * (max - min + 1)) + min;
  }, []);

  // Add more functions as needed...

  return {
    formatDate,
    truncateText,
    getRandomNumber,
  };
};

export default useUserDetails;


