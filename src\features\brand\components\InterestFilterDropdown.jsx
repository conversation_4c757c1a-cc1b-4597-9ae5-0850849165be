import React, { useState, useRef, useEffect } from 'react';
import DownIcon from '@assets/icon/down.svg';
import FilterIcon from '@assets/icon/resources.svg';

const InterestFilterDropdown = ({ onSelect, selectedInterests = [] }) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef(null);
  const [searchQuery, setSearchQuery] = useState('');

  // Define interest options
  const interestOptions = [
    { label: 'Fashion', value: 'fashion' },
    { label: 'Beauty', value: 'beauty' },
    { label: 'Fitness', value: 'fitness' },
    { label: 'Travel', value: 'travel' },
    { label: 'Food', value: 'food' },
    { label: 'Technology', value: 'tech' },
    { label: 'Gaming', value: 'gaming' },
    { label: 'Art & Design', value: 'art' },
    { label: 'Music', value: 'music' },
    { label: 'Education', value: 'education' },
  ];

  const filteredOptions = searchQuery ? 
    interestOptions.filter(option => 
      option.label.toLowerCase().includes(searchQuery.toLowerCase())
    ) : 
    interestOptions;

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const toggleDropdown = () => {
    setIsOpen(!isOpen);
  };

  const handleOptionSelect = (value) => {
    onSelect(value);
    // Don't close dropdown when selecting options to allow multiple selections
  };

  // Display selected count if any options are selected
  const selectedCount = selectedInterests.length;
  const displaySelectedCount = selectedCount > 0 ? `(${selectedCount})` : '';

  return (
    <div className="relative" ref={dropdownRef}>
      <button
        className={`px-4 py-2.5 ${isOpen ? 'bg-gray-700' : 'bg-transparent'} border border-gray-500 rounded-lg flex items-center gap-1.5 text-sm text-gray-50 hover:text-gray-300 transition-colors cursor-pointer`}
        onClick={toggleDropdown}
      >
        <img src={FilterIcon} alt="Interests" className="w-3.5 h-3.5" />
        Audience Interests {displaySelectedCount}
        <img
          src={DownIcon}
          alt="Dropdown"
          className={`w-3.5 h-3.5 ml-0.5 transition-transform duration-200 ${isOpen ? 'transform rotate-180' : ''}`}
        />
      </button>
      
      {isOpen && (
        <div className="absolute mt-1 left-0 w-64 bg-gray-900 border border-gray-800 rounded-md shadow-lg z-50 py-2">
          <div className="px-4 pb-2">
            <h3 className="text-white text-lg font-medium mb-2">Audience Interests</h3>
            
            {/* Search Input */}
            <div className="mb-4">
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="Search interests"
                className="w-full bg-gray-800 border border-gray-700 rounded px-3 py-2 text-white placeholder-gray-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
              />
            </div>

            {/* Interest Options */}
            <div className="space-y-2 max-h-60 overflow-y-auto">
              {filteredOptions.map((option) => (
                <div
                  key={option.value}
                  className="flex items-center justify-between py-1.5 cursor-pointer"
                  onClick={() => handleOptionSelect(option.value)}
                >
                  <div className="flex items-center">
                    <div 
                      className={`w-5 h-5 rounded border ${
                        selectedInterests.includes(option.value)
                          ? 'bg-blue-500 border-blue-500' 
                          : 'bg-gray-700 border-gray-600'
                      } flex items-center justify-center mr-2`}
                    >
                      {selectedInterests.includes(option.value) && (
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5 text-white" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                      )}
                    </div>
                    <span className="text-white text-sm">{option.label}</span>
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div className="border-t border-gray-800 p-3 flex justify-between">
            <button 
              className="text-sm text-gray-400 hover:text-white"
              onClick={() => onSelect([])}>
              Clear
            </button>
            <button 
              className="text-sm text-blue-500 hover:text-blue-400"
              onClick={() => setIsOpen(false)}>
              Apply
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default InterestFilterDropdown;
