import React from 'react'
import { useF<PERSON><PERSON>ontext, Controller, useWatch } from 'react-hook-form';
import { FaRegCalendarAlt } from 'react-icons/fa';
import { DatePicker, Switch, Select } from 'antd';
import dayjs from 'dayjs';
// eslint-disable-next-line no-unused-vars
import { motion } from 'framer-motion';


const CampaignOverview = (step) => {

    const { register, control, formState: { errors } } = useFormContext();

    const isPrivate = useWatch({
        control,
        name: 'keepPrivate',
    });

    return (
        <div className='flex flex-col gap-5 p-7.5 rounded-3xl border-1 border-gray-300 bg-gray-900'>
            <div className='flex items-center gap-2'>
                <motion.div
                    // whileHover={{ scale: 1.2, rotate: 3 }}
                    // whileTap={{ scale: 0.95 }}
                    className="text-xl p-2 text-amber-50 w-[36px] h-[36px] flex items-center justify-center"
                    style={{
                        borderRadius: '8px',
                        background: 'var(--color-blue)',
                        boxShadow: '0px 0px 8px 0px rgba(79, 152, 250, 0.50)',
                        color: 'white',
                    }}
                >
                    {step.stepConfig.icon}
                </motion.div>
                <div className="text-20-semibold text-center text-gray-50">{step.stepConfig.title}</div>
            </div>
            {/* Form Step Content */}
            <div className="flex flex-col gap-7.5">
                <div className='relative flex flex-col gap-2'>
                    <label>Campaign Name</label>
                    <input
                        {...register('campaignName', { required: 'Name is required' })}
                        placeholder='Enter Campaign Name'
                        className="input border-1 border-gray-500 placeholder-gray-400 rounded-lg py-2.5 px-3.5 hover:border-gray-400 hover:shadow-xs shadow-gray-200 focus:outline-none focus:ring-1 focus:ring-gray-400"
                    />
                    {errors.campaignName && <p className="absolute -bottom-5 left-0 text-sm text-red-2 ">{errors.campaignName.message}</p>}
                </div>
                <div className='relative flex flex-col gap-2'>
                    <label>Campaign Description</label>
                    <textarea
                        {...register('campaignDescription', { required: 'Description is required' })}
                        placeholder='Describe the tone, style, visual aesthetic, key messages, and any specific requirements for the content...'
                        className="input border-1 border-gray-500 placeholder-gray-400 rounded-lg py-2.5 px-3.5 hover:border-gray-400 hover:shadow-xs shadow-gray-200 focus:outline-none focus:ring-1 focus:ring-gray-400"
                    />
                    {errors.campaignDescription && <p className="absolute -bottom-5 left-0 text-sm text-red-2 ">{errors.campaignDescription.message}</p>}
                </div>
                <div className='flex justify-between gap-7.5 transition-all ease-in-out duration-300'>
                    <div className='flex flex-col gap-7.5 justify-between w-1/2'>
                        {/* <div className="flex flex-col justify-between gap-5 w-full"> */}
                        <div className='flex flex-col gap-6 w-full justify-between h-full'>
                            {/* Live Date */}
                            <div className="flex flex-col gap-2 relative">
                                <label className="text-white">Campaign Live Date</label>
                                <Controller
                                    name="liveDate"
                                    control={control}
                                    rules={{ required: 'Live date is required' }}
                                    render={({ field }) => (
                                        <div className="relative flex items-center px-3.5 py-2.5 border border-gray-700 rounded-lg bg-gray-900 w-full">
                                            <DatePicker
                                                {...field}
                                                value={field.value ? dayjs(field.value) : null}
                                                onChange={(date) => field.onChange(date?.toISOString())}
                                                format="DD/MM/YYYY"
                                                suffixIcon={null}
                                                placeholder="DD/MM/YYYY"
                                                className="!bg-transparent !border-none !text-gray-200 placeholder:!text-gray-200 w-full"
                                            // popupClassName="dark-datepicker-popup"
                                            />
                                            <FaRegCalendarAlt className="w-5 h-5 text-gray-500 ml-2 absolute right-4 pointer-events-none" />
                                        </div>
                                    )}
                                />
                                {errors.liveDate && (
                                    <p className="absolute -bottom-5 left-0 text-sm text-red-2">
                                        {errors.liveDate.message}
                                    </p>
                                )}
                            </div>

                            <div className="flex gap-2 ">
                                <Controller
                                    name="keepPrivate"
                                    control={control}
                                    defaultValue={false}
                                    render={({ field }) => (
                                        <Switch
                                            checked={field.value}
                                            onChange={field.onChange}
                                            className="bg-gray-700"
                                        />
                                    )}
                                />
                                <label className="text-white text-14-medium">
                                    Keep Private <span className="text-10-regular text-gray-300">(Only visible to brand team)</span>
                                </label>
                            </div>
                        </div>
                        {/* </div> */}
                    </div>
                    <div className='flex flex-col gap-7.5 w-1/2'>
                        {/* Campaign Objective */}
                        <div className="flex flex-col gap-2 relative">
                            <label className="text-white">Campaign Objective</label>
                            <Controller
                                name="campaignObjective"
                                control={control}
                                rules={{ required: "Campaign Objective is required" }}
                                render={({ field }) => (
                                    <div className="relative flex items-center px-3.5 py-2.5 border border-gray-700 rounded-lg bg-gray-900 w-full">

                                        <Select
                                            {...field}
                                            placeholder="Select Campaign Type"
                                            className="!bg-gray-900 !text-gray-200 !border-gray-700 placeholder:!text-gray-400 w-full"
                                            style={{ backgroundColor: "#111827", color: "#E5E7EB" }}
                                            dropdownStyle={{
                                                backgroundColor: 'var(--color-primary)',
                                                color: '#ffffff',
                                            }}
                                            // popupClassName="dark-select-popup"
                                            suffixIcon={null}
                                        >
                                            <Select.Option value="awareness">Brand Awareness</Select.Option>
                                            <Select.Option value="engagement">Engagement</Select.Option>
                                            <Select.Option value="conversion">Conversions</Select.Option>
                                        </Select>
                                    </div>
                                )}
                            />
                            {errors.campaignObjective && (
                                <p className="absolute -bottom-5 left-0 text-sm text-red-2">
                                    {errors.campaignObjective.message}
                                </p>
                            )}
                        </div>

                        {/* Application Close Date */}
                        <div className={`${isPrivate ? 'hidden' : 'flex'} flex-col gap-2 relative`}>
                            <label className="text-white">Application Close Date</label>
                            <Controller
                                name="closeDate"
                                control={control}
                                rules={{
                                    required: !isPrivate ? "Close date is required" : false,
                                }}
                                render={({ field }) => (
                                    <div className="relative flex items-center px-3.5 py-2.5 border border-gray-700 rounded-lg w-full bg-gray-900">
                                        <DatePicker
                                            {...field}
                                            disabled={isPrivate}
                                            value={field.value ? dayjs(field.value) : null}
                                            onChange={(date) => field.onChange(date?.toISOString())}
                                            format="DD/MM/YYYY"
                                            suffixIcon={null}
                                            placeholder="DD/MM/YYYY"
                                            className={`!bg-transparent !border-none w-full placeholder:!text-gray-400 
            ${isPrivate ? '!text-gray-500 cursor-not-allowed' : '!text-gray-200'}`}
                                        // popupClassName="dark-datepicker-popup"
                                        />
                                        <FaRegCalendarAlt className="w-5 h-5 text-gray-500 ml-2 absolute right-4 pointer-events-none" />
                                    </div>
                                )}
                            />
                            {!isPrivate && errors.closeDate && (
                                <p className="absolute -bottom-5 left-0 text-sm text-red-2">
                                    {errors.closeDate.message}
                                </p>
                            )}
                        </div>

                    </div>
                </div>
                <div className='flex flex-col gap-5 border-1 border-gray-300 p-5 rounded-xl'>
                    <div className='flex gap-2 items-center'>
                        <div className='flex items-center justify-center h-7 w-7 rounded-full text-brand-500 bg-[#00566E]'>
                            <i className="fi fi-sr-bullseye-arrow mt-0.5"></i>
                        </div>
                        <h2 className='text-gray-100 text-16-semibold'>
                            Campaign Goals
                        </h2>
                    </div>
                    {/* <div className="grid grid-cols-3 gap-6 w-full">
                    {[
                        { label: "Engagement Rate", name: "engagementRate" },
                        { label: "Reach", name: "reach" },
                        { label: "Impressions", name: "impressions" },
                        { label: "Likes", name: "likes" },
                        { label: "Comments", name: "comments" },
                        { label: "Follower Growth", name: "followerGrowth" },
                    ].map((field) => (
                        <div key={field.name} className="relative flex flex-col gap-2">
                            <label>{field.label}</label>
                            <input
                                type="number"
                                {...register(field.name, { required: `${field.label} is required` })}
                                placeholder={`Enter ${field.label}`}
                                className="input border-1 border-gray-500 placeholder-gray-400 rounded-lg py-2.5 px-3.5 hover:border-gray-400 hover:shadow-xs shadow-gray-200 focus:outline-none focus:ring-1 focus:ring-gray-400"
                            />
                            {errors[field.name] && (
                                <p className="absolute -bottom-5 left-0 text-sm text-red-2">
                                    {errors[field.name].message}
                                </p>
                            )}
                        </div>
                    ))}
                </div> */}
                    <div className="grid grid-cols-3 gap-6">
                        {[
                            { label: "Engagement Rate", name: "engagementRate", showPercent: true },
                            { label: "Reach", name: "reach" },
                            { label: "Impressions", name: "impressions" },
                            { label: "Likes", name: "likes" },
                            { label: "Comments", name: "comments" },
                            { label: "Follower Growth", name: "followerGrowth", showPercent: true },
                        ].map((field) => (
                            <div key={field.name} className="relative flex flex-col gap-2">
                                <label>{field.label}</label>
                                <div className="relative">
                                    <input
                                        type="number"
                                        {...register(field.name, { required: `${field.label} is required` })}
                                        placeholder={`Enter ${field.label}`}
                                        className={`no-spinner input w-full border-1 border-gray-500 placeholder-gray-400 rounded-lg py-2.5 px-3.5 pr-10 hover:border-gray-400 hover:shadow-xs shadow-gray-200 focus:outline-none focus:ring-1 focus:ring-gray-400`}
                                    />
                                    {field.showPercent && (
                                        <span className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-200 text-sm pointer-events-none">
                                            %
                                        </span>
                                    )}
                                </div>
                                {errors[field.name] && (
                                    <p className="absolute -bottom-5 left-0 text-sm text-red-2">
                                        {errors[field.name].message}
                                    </p>
                                )}
                            </div>
                        ))}
                    </div>
                </div>
            </div>
        </div>

    )
}

export default CampaignOverview
