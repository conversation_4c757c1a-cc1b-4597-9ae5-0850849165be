import React, { useState, useRef, useEffect } from 'react';
import InlineAutoCompleteInput from '@shared/components/UI/InlineAutoCompleteInput ';
const LabelsComponent = ({ initialLabels = [], onAddLabel, onDeleteLabel, suggestions = [] }) => {
  const [labels, setLabels] = useState(initialLabels);
  const [isAdding, setIsAdding] = useState(false);
  const [editingIndex, setEditingIndex] = useState(null);
  const [newLabel, setNewLabel] = useState('');
  const inputRef = useRef(null);

  // Sync local state if initialLabels change
  useEffect(() => {
    setLabels(initialLabels);
  }, [initialLabels]);

  useEffect(() => {
    if ((isAdding || editingIndex !== null) && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isAdding, editingIndex]);

  const handleAddClick = () => {
    setIsAdding(true);
    setNewLabel('');
  };

  const handleKeyDown = async (e) => {
    if (e.key === 'Enter') {
      if (newLabel.trim()) {
        const trimmed = newLabel.trim();
        if (editingIndex !== null) {
          const updatedLabels = [...labels];
          updatedLabels[editingIndex] = trimmed;
          setLabels(updatedLabels);
          setEditingIndex(null);
          setNewLabel('');
        } else {
          const result = await onAddLabel?.(trimmed);
          if (result === true) {
            setIsAdding(false);
            setNewLabel('');
          }
        }
      }
    } else if (e.key === 'Escape') {
      cancelEdit();
    }
  };

  const handleBlur = async () => {
    const trimmed = newLabel.trim();
    if (!trimmed) return cancelEdit();

    if (editingIndex !== null) {
      const updatedLabels = [...labels];
      updatedLabels[editingIndex] = trimmed;
      setLabels(updatedLabels);
    } else {
      const result = await onAddLabel?.(trimmed);
      if (result === true) {
        setIsAdding(false);
        setNewLabel('');
      }
    }
    cancelEdit();
  };

  const cancelEdit = () => {
    setIsAdding(false);
    setEditingIndex(null);
    setNewLabel('');
  };

  const handleDelete = async (index) => {
    const labelToDelete = labels[index];
    const result = await onDeleteLabel?.(labelToDelete, index);
    if (result === true) {
      // Parent will update initialLabels
    }
  };

  return (
    <div className="flex flex-wrap gap-2 items-center w-full">
      {labels.map((label, index) =>
        editingIndex === index ? (
          <div key={`editing-${index}`} className="flex items-center text-white rounded-full px-2 py-1  bg-red-900 w-full">
            <InlineAutoCompleteInput
              inputRef={inputRef}
              suggestions={suggestions}
              value={newLabel}
              setValue={setNewLabel}
              onKeyDown={handleKeyDown}
              onBlur={handleBlur}
            />
          </div>
        ) : (
          <div key={`label-${index}`} className="group flex items-center text-white rounded-full px-3 py-1 text-sm border border-gray-600">
            <span>{label.name}</span>
            <button
              onClick={(e) => {
                e.stopPropagation();
                handleDelete(index)
              }}
              className="text-gray-400 hover:text-white ml-1 cursor-pointer"
              aria-label="Remove label"
            >
              ×
            </button>
          </div>
        )
      )}

      {isAdding ? (
        <div className="flex items-center text-white rounded-full px-2 py-1 text-sm border border-gray-600">
          <InlineAutoCompleteInput
            inputRef={inputRef}
            suggestions={suggestions}
            value={newLabel}
            setValue={setNewLabel}
            onKeyDown={handleKeyDown}
            onBlur={handleBlur}
          />
        </div>
      ) : (
        labels.length < 5 && (
          <button
            onClick={(e) => {
              e.stopPropagation();
              handleAddClick();
            }}
            className={`flex items-center ${labels.length ? 'w-7 h-7 justify-center rounded-full' : 'rounded-full px-4 py-1'} bg-gray-600 border border-gray-600 text-gray-400 hover:text-gray-200 hover:border-gray-400 text-sm cursor-pointer`}
            aria-label="Add label"
          >
            {labels.length ? '+' : '+ Add label'}
          </button>
        )
      )}
    </div>
  );
};

export default LabelsComponent;
