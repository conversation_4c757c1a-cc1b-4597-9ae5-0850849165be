/**
 * Multi-Backend Error Handler
 * 
 * Provides comprehensive error handling, retry logic, and fallback mechanisms
 * for multi-backend architecture. Handles:
 * - Service-specific error handling
 * - Automatic retry with exponential backoff
 * - Circuit breaker pattern for failing services
 * - Fallback mechanisms
 * - Error reporting and logging
 */

import { apiConfig, SERVICE_TYPES } from '../config/apiConfig';
import authService from './authService';

/**
 * Error types and codes
 */
export const ERROR_TYPES = {
  NETWORK_ERROR: 'NETWORK_ERROR',
  SERVICE_UNAVAILABLE: 'SERVICE_UNAVAILABLE',
  AUTHENTICATION_ERROR: 'AUTHENTICATION_ERROR',
  AUTHORIZATION_ERROR: 'AUTHORIZATION_ERROR',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  RATE_LIMIT_ERROR: 'RATE_LIMIT_ERROR',
  TIMEOUT_ERROR: 'TIMEOUT_ERROR',
  UNKNOWN_ERROR: 'UNKNOWN_ERROR'
};

/**
 * Circuit breaker states
 */
const CIRCUIT_STATES = {
  CLOSED: 'CLOSED',     // Normal operation
  OPEN: 'OPEN',         // Service is failing, requests are blocked
  HALF_OPEN: 'HALF_OPEN' // Testing if service has recovered
};

/**
 * Circuit Breaker for service health management
 */
class CircuitBreaker {
  constructor(serviceName, options = {}) {
    this.serviceName = serviceName;
    this.state = CIRCUIT_STATES.CLOSED;
    this.failureCount = 0;
    this.lastFailureTime = null;
    this.nextAttemptTime = null;
    
    // Configuration
    this.failureThreshold = options.failureThreshold || 5;
    this.recoveryTimeout = options.recoveryTimeout || 60000; // 1 minute
    this.monitoringPeriod = options.monitoringPeriod || 300000; // 5 minutes
  }

  /**
   * Check if request should be allowed
   * @returns {boolean} Whether request is allowed
   */
  canExecute() {
    if (this.state === CIRCUIT_STATES.CLOSED) {
      return true;
    }
    
    if (this.state === CIRCUIT_STATES.OPEN) {
      if (Date.now() >= this.nextAttemptTime) {
        this.state = CIRCUIT_STATES.HALF_OPEN;
        return true;
      }
      return false;
    }
    
    // HALF_OPEN state - allow one request to test
    return true;
  }

  /**
   * Record successful request
   */
  recordSuccess() {
    this.failureCount = 0;
    this.state = CIRCUIT_STATES.CLOSED;
    this.lastFailureTime = null;
    this.nextAttemptTime = null;
  }

  /**
   * Record failed request
   */
  recordFailure() {
    this.failureCount++;
    this.lastFailureTime = Date.now();
    
    if (this.failureCount >= this.failureThreshold) {
      this.state = CIRCUIT_STATES.OPEN;
      this.nextAttemptTime = Date.now() + this.recoveryTimeout;
    }
  }

  /**
   * Get circuit breaker status
   * @returns {Object} Status information
   */
  getStatus() {
    return {
      serviceName: this.serviceName,
      state: this.state,
      failureCount: this.failureCount,
      lastFailureTime: this.lastFailureTime,
      nextAttemptTime: this.nextAttemptTime,
      isHealthy: this.state === CIRCUIT_STATES.CLOSED
    };
  }
}

/**
 * Multi-Backend Error Handler
 */
class MultiBackendErrorHandler {
  constructor() {
    this.circuitBreakers = new Map();
    this.retryConfig = {
      maxRetries: 3,
      baseDelay: 1000,
      maxDelay: 10000,
      backoffFactor: 2
    };
    
    // Initialize circuit breakers for each service
    Object.values(SERVICE_TYPES).forEach(service => {
      this.circuitBreakers.set(service, new CircuitBreaker(service));
    });
  }

  /**
   * Handle API error with retry logic and fallbacks
   * @param {Error} error - Original error
   * @param {Object} context - Request context (service, method, url, etc.)
   * @param {Function} retryFunction - Function to retry the request
   * @returns {Promise} Handled response or throws enhanced error
   */
  async handleError(error, context, retryFunction = null) {
    const enhancedError = this.enhanceError(error, context);
    const circuitBreaker = this.circuitBreakers.get(context.service);
    
    // Record failure in circuit breaker
    if (circuitBreaker) {
      circuitBreaker.recordFailure();
    }
    
    // Log error
    this.logError(enhancedError, context);
    
    // Handle specific error types
    switch (enhancedError.type) {
      case ERROR_TYPES.AUTHENTICATION_ERROR:
        return this.handleAuthError(enhancedError, context, retryFunction);
      
      case ERROR_TYPES.NETWORK_ERROR:
      case ERROR_TYPES.TIMEOUT_ERROR:
      case ERROR_TYPES.SERVICE_UNAVAILABLE:
        return this.handleRetryableError(enhancedError, context, retryFunction);
      
      case ERROR_TYPES.RATE_LIMIT_ERROR:
        return this.handleRateLimitError(enhancedError, context, retryFunction);
      
      default:
        throw enhancedError;
    }
  }

  /**
   * Enhance error with additional context and type classification
   * @param {Error} error - Original error
   * @param {Object} context - Request context
   * @returns {Object} Enhanced error
   */
  enhanceError(error, context) {
    const enhancedError = {
      originalError: error,
      message: error.message || 'Unknown error',
      service: context.service,
      url: context.url,
      method: context.method,
      timestamp: new Date().toISOString(),
      type: this.classifyError(error),
      statusCode: error.status || error.statusCode,
      retryable: false,
      context
    };

    // Determine if error is retryable
    enhancedError.retryable = this.isRetryableError(enhancedError);
    
    return enhancedError;
  }

  /**
   * Classify error type based on error properties
   * @param {Error} error - Error to classify
   * @returns {string} Error type
   */
  classifyError(error) {
    const status = error.status || error.statusCode;
    
    if (status === 401) return ERROR_TYPES.AUTHENTICATION_ERROR;
    if (status === 403) return ERROR_TYPES.AUTHORIZATION_ERROR;
    if (status === 429) return ERROR_TYPES.RATE_LIMIT_ERROR;
    if (status >= 400 && status < 500) return ERROR_TYPES.VALIDATION_ERROR;
    if (status >= 500) return ERROR_TYPES.SERVICE_UNAVAILABLE;
    
    if (error.name === 'TypeError' || error.message.includes('fetch')) {
      return ERROR_TYPES.NETWORK_ERROR;
    }
    
    if (error.name === 'TimeoutError' || error.message.includes('timeout')) {
      return ERROR_TYPES.TIMEOUT_ERROR;
    }
    
    return ERROR_TYPES.UNKNOWN_ERROR;
  }

  /**
   * Check if error is retryable
   * @param {Object} error - Enhanced error object
   * @returns {boolean} Whether error is retryable
   */
  isRetryableError(error) {
    const retryableTypes = [
      ERROR_TYPES.NETWORK_ERROR,
      ERROR_TYPES.TIMEOUT_ERROR,
      ERROR_TYPES.SERVICE_UNAVAILABLE,
      ERROR_TYPES.RATE_LIMIT_ERROR
    ];
    
    return retryableTypes.includes(error.type) || 
           (error.statusCode >= 500 && error.statusCode < 600);
  }

  /**
   * Handle authentication errors
   * @param {Object} error - Enhanced error
   * @param {Object} context - Request context
   * @param {Function} retryFunction - Retry function
   * @returns {Promise} Handled response
   */
  async handleAuthError(error, context, retryFunction) {
    try {
      // Attempt token refresh
      await authService.refreshToken();
      
      // Retry original request with new token
      if (retryFunction) {
        const result = await retryFunction();
        this.recordSuccess(context.service);
        return result;
      }
    } catch (refreshError) {
      // Token refresh failed, redirect to login
      authService.redirectToLogin(context.url);
    }
    
    throw error;
  }

  /**
   * Handle retryable errors with exponential backoff
   * @param {Object} error - Enhanced error
   * @param {Object} context - Request context
   * @param {Function} retryFunction - Retry function
   * @returns {Promise} Handled response
   */
  async handleRetryableError(error, context, retryFunction) {
    if (!retryFunction || !error.retryable) {
      throw error;
    }
    
    const circuitBreaker = this.circuitBreakers.get(context.service);
    if (circuitBreaker && !circuitBreaker.canExecute()) {
      throw new Error(`Service ${context.service} is currently unavailable (circuit breaker open)`);
    }
    
    const maxRetries = context.retryCount || this.retryConfig.maxRetries;
    const currentRetry = context.currentRetry || 0;
    
    if (currentRetry >= maxRetries) {
      throw error;
    }
    
    // Calculate delay with exponential backoff
    const delay = Math.min(
      this.retryConfig.baseDelay * Math.pow(this.retryConfig.backoffFactor, currentRetry),
      this.retryConfig.maxDelay
    );
    
    // Add jitter to prevent thundering herd
    const jitteredDelay = delay + (Math.random() * 1000);
    
    console.warn(`[ERROR HANDLER] Retrying request in ${jitteredDelay}ms (attempt ${currentRetry + 1}/${maxRetries})`);
    
    await this.sleep(jitteredDelay);
    
    try {
      const result = await retryFunction({ 
        ...context, 
        currentRetry: currentRetry + 1 
      });
      this.recordSuccess(context.service);
      return result;
    } catch (retryError) {
      return this.handleError(retryError, {
        ...context,
        currentRetry: currentRetry + 1
      }, retryFunction);
    }
  }

  /**
   * Handle rate limit errors
   * @param {Object} error - Enhanced error
   * @param {Object} context - Request context
   * @param {Function} retryFunction - Retry function
   * @returns {Promise} Handled response
   */
  async handleRateLimitError(error, context, retryFunction) {
    if (!retryFunction) {
      throw error;
    }
    
    // Extract retry-after header if available
    const retryAfter = error.headers?.['retry-after'] || 
                      error.headers?.['Retry-After'] || 
                      60; // Default to 60 seconds
    
    const delay = parseInt(retryAfter) * 1000;
    
    console.warn(`[ERROR HANDLER] Rate limited. Retrying after ${delay}ms`);
    
    await this.sleep(delay);
    
    try {
      const result = await retryFunction(context);
      this.recordSuccess(context.service);
      return result;
    } catch (retryError) {
      throw retryError;
    }
  }

  /**
   * Record successful request
   * @param {string} service - Service name
   */
  recordSuccess(service) {
    const circuitBreaker = this.circuitBreakers.get(service);
    if (circuitBreaker) {
      circuitBreaker.recordSuccess();
    }
  }

  /**
   * Log error with appropriate level
   * @param {Object} error - Enhanced error
   * @param {Object} context - Request context
   */
  logError(error, context) {
    const logData = {
      service: error.service,
      type: error.type,
      message: error.message,
      url: error.url,
      method: error.method,
      statusCode: error.statusCode,
      timestamp: error.timestamp,
      retryable: error.retryable
    };
    
    if (error.type === ERROR_TYPES.AUTHENTICATION_ERROR) {
      console.warn('[ERROR HANDLER] Authentication error:', logData);
    } else if (error.retryable) {
      console.warn('[ERROR HANDLER] Retryable error:', logData);
    } else {
      console.error('[ERROR HANDLER] Non-retryable error:', logData);
    }
  }

  /**
   * Get service health status
   * @returns {Object} Health status for all services
   */
  getServiceHealth() {
    const health = {};
    
    this.circuitBreakers.forEach((breaker, service) => {
      health[service] = breaker.getStatus();
    });
    
    return health;
  }

  /**
   * Sleep utility
   * @param {number} ms - Milliseconds to sleep
   * @returns {Promise} Sleep promise
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Create singleton instance
const errorHandler = new MultiBackendErrorHandler();

export { ERROR_TYPES, CIRCUIT_STATES };
export default errorHandler;
