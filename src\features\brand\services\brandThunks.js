// Brand async thunks for API operations
import { createAsyncThunk } from '@reduxjs/toolkit';
import brandManagementApi from '../../../app/store/api/brandManagementApi';
import discoveryApi from '../../../app/store/api/discoveryApi';
import savedFiltersApi from '../../../app/store/api/savedFiltersApi';
import creatorListApi from '../../../app/store/api/creatorListApi';

/**
 * Get brand information async thunk
 */
export const getBrandInfoThunk = createAsyncThunk(
    'brand/getBrandInfo',
    async (_, { rejectWithValue }) => {
        try {
            const response = await brandManagementApi.requestUserBrandInfo().send();
            if (response.status === 200 && response.data) {
                const { allocated_brands, organization_id, organization_brands } = response.data.data;

                // Store brand data in localStorage
                localStorage.setItem('allocatedBrands', JSON.stringify(allocated_brands));
                localStorage.setItem('organizationBrands', JSON.stringify(organization_brands));
                localStorage.setItem('organizationId', JSON.stringify(organization_id));

                return {
                    message: response.data?.message || 'Brand info retrieved successfully',
                    success: true,
                    data: response.data.data
                };
            } else {
                return rejectWithValue({
                    message: response.data?.message || 'Failed to retrieve brand info',
                    success: false
                });
            }
        } catch (error) {
            return rejectWithValue({
                message: error.response?.data?.message || 'Failed to retrieve brand info. Please try again.',
                success: false
            });
        }
    }
);

/**
 * Create brand async thunk
 */
export const createBrandThunk = createAsyncThunk(
    'brand/createBrand',
    async (brandData, { rejectWithValue }) => {
        try {
            const response = await brandManagementApi.createBrand(brandData).send();
            if (response.status === 200 && response.data) {
                return {
                    message: response.data.data?.message || 'Brand created successfully',
                    success: true,
                    data: response.data.data
                };
            } else {
                return rejectWithValue({
                    message: response.data?.message || 'Brand creation failed',
                    success: false
                });
            }
        } catch (error) {
            return rejectWithValue({
                message: error.response?.data?.message || 'Brand creation failed. Please try again.',
                success: false
            });
        }
    }
);

/**
 * Request brand access async thunk
 */
export const requestBrandAccessThunk = createAsyncThunk(
    'brand/requestBrandAccess',
    async (brandId, { rejectWithValue }) => {
        try {
            const response = await brandManagementApi.requestBrandAccess(brandId).send();
            if (response.status === 200 && response.data) {
                return {
                    message: response.data.data?.message || 'Brand access requested successfully',
                    success: true,
                    data: response.data.data
                };
            } else {
                return rejectWithValue({
                    message: response.data?.message || 'Brand access request failed',
                    success: false
                });
            }
        } catch (error) {
            return rejectWithValue({
                message: error.response?.data?.message || 'Brand access request failed. Please try again.',
                success: false
            });
        }
    }
);

/**
 * Search creators async thunk
 */
export const searchCreatorsThunk = createAsyncThunk(
    'brand/searchCreators',
    async (searchParams, { rejectWithValue }) => {
        try {
            const response = await discoveryApi.searchCreators(searchParams).send();
            if (response.status === 200 && response.data) {
                return {
                    message: 'Creator search completed successfully',
                    success: true,
                    data: response.data.data
                };
            } else if (response.status === 410) {
                // Extract suggestions from the 410 response
                // Check multiple possible locations for suggestions based on API response structure
                const suggestions = response.data?.examples ||
                    response.data?.data?.examples ||
                    response.data?.tips ||
                    response.data?.data?.tips ||
                    [];

                console.log('410 Response - Extracted suggestions:', suggestions);

                return {
                    message: response.data.data.suggestion || 'Query not related to influencer search',
                    success: false,
                    status: 410,
                    suggestions: suggestions,
                    data: response.data.data || null
                };
            }
            else if (response.status === 408) {
                // Extract suggestions from the 410 response
                // Check multiple possible locations for suggestions based on API response structure

                return {
                    message: 'We are working with on the search you requested. Please try again by applying filter.',
                    success: false,
                    status: 408,
                    data: null
                };
            }
            else {
                return rejectWithValue({
                    message: response.data?.message || 'Creator search failed',
                    success: false
                });
            }
        } catch (error) {
            return rejectWithValue({
                message: error.response?.data?.message || 'Creator search failed. Please try again.',
                success: false
            });
        }
    }
);

/**
 * Get filter metadata async thunk
 */
export const getFilterMetadataThunk = createAsyncThunk(
    'brand/getFilterMetadata',
    async (params, { rejectWithValue }) => {
        try {
            const response = await discoveryApi.getFilterMetadata(params).send();
            if (response.status === 200 && response.data) {
                return {
                    message: 'Filter metadata retrieved successfully',
                    success: true,
                    data: response.data.data,
                };
            } else {
                return rejectWithValue({
                    message: response.data?.message || 'Failed to retrieve filter metadata',
                    success: false
                });
            }
        } catch (error) {
            return rejectWithValue({
                message: error.response?.data?.message || 'Failed to retrieve filter metadata. Please try again.',
                success: false
            });
        }
    }
);

/**
 * Transform filters async thunk
 */
export const transformFiltersThunk = createAsyncThunk(
    'brand/transformFilters',
    async (transformData, { rejectWithValue }) => {
        try {
            const response = await discoveryApi.transformFilters(transformData).send();
            if (response.status === 200 && response.data) {
                return {
                    message: 'Filters transformed successfully',
                    success: true,
                    data: response.data.data
                };
            } else {
                return rejectWithValue({
                    message: response.data?.message || 'Failed to transform filters',
                    success: false
                });
            }
        } catch (error) {
            return rejectWithValue({
                message: error.response?.data?.message || 'Failed to transform filters. Please try again.',
                success: false
            });
        }
    }
);

/**
 * Invalidate filter cache async thunk
 */
export const invalidateFilterCacheThunk = createAsyncThunk(
    'brand/invalidateFilterCache',
    async (params, { rejectWithValue }) => {
        try {
            const response = await discoveryApi.invalidateFilterCache(params).send();
            if (response.status === 200) {
                return {
                    message: 'Filter cache invalidated successfully',
                    success: true,
                    data: response.data?.data || null
                };
            } else {
                return rejectWithValue({
                    message: response.data?.message || 'Failed to invalidate filter cache',
                    success: false
                });
            }
        } catch (error) {
            return rejectWithValue({
                message: error.response?.data?.message || 'Failed to invalidate filter cache. Please try again.',
                success: false
            });
        }
    }
);

/**
 * Get cache statistics async thunk
 */
export const getCacheStatsThunk = createAsyncThunk(
    'brand/getCacheStats',
    async (platform, { rejectWithValue }) => {
        try {
            const response = await discoveryApi.getCacheStats(platform).send();
            if (response.status === 200 && response.data) {
                return {
                    message: 'Cache statistics retrieved successfully',
                    success: true,
                    data: response.data.data
                };
            } else {
                return rejectWithValue({
                    message: response.data?.message || 'Failed to retrieve cache statistics',
                    success: false
                });
            }
        } catch (error) {
            return rejectWithValue({
                message: error.response?.data?.message || 'Failed to retrieve cache statistics. Please try again.',
                success: false
            });
        }
    }
);

/**
 * Cleanup cache async thunk
 */
export const cleanupCacheThunk = createAsyncThunk(
    'brand/cleanupCache',
    async (_, { rejectWithValue }) => {
        try {
            const response = await discoveryApi.cleanupCache().send();
            if (response.status === 200) {
                return {
                    message: 'Cache cleanup completed successfully',
                    success: true,
                    data: response.data?.data || null
                };
            } else {
                return rejectWithValue({
                    message: response.data?.message || 'Failed to cleanup cache',
                    success: false
                });
            }
        } catch (error) {
            return rejectWithValue({
                message: error.response?.data?.message || 'Failed to cleanup cache. Please try again.',
                success: false
            });
        }
    }
);

/**
 * Get saved filter sets async thunk
 */
export const getSavedFiltersThunk = createAsyncThunk(
    'brand/getSavedFilters',
    async (params, { rejectWithValue }) => {
        try {
            const response = await savedFiltersApi.getFilterSets(params).send();
            if (response.status === 200 && response.data) {
                return {
                    message: 'Saved filters retrieved successfully',
                    success: true,
                    data: response.data.data
                };
            } else {
                return rejectWithValue({
                    message: response.data?.message || 'Failed to retrieve saved filters',
                    success: false
                });
            }
        } catch (error) {
            return rejectWithValue({
                message: error.response?.data?.message || 'Failed to retrieve saved filters. Please try again.',
                success: false
            });
        }
    }
);

/**
 * Create saved filter set async thunk
 */
export const createSavedFilterThunk = createAsyncThunk(
    'brand/createSavedFilter',
    async (filterData, { rejectWithValue }) => {
        try {
            const response = await savedFiltersApi.createFilterSet(filterData).send();
            if (response.status === 200 && response.data) {
                return {
                    message: response.data.data?.message || 'Filter set saved successfully',
                    success: true,
                    data: response.data.data
                };
            } else {
                return rejectWithValue({
                    message: response.data?.message || 'Failed to save filter set',
                    success: false
                });
            }
        } catch (error) {
            return rejectWithValue({
                message: error.response?.data?.message || 'Failed to save filter set. Please try again.',
                success: false
            });
        }
    }
);

/**
 * Update saved filter set async thunk
 */
export const updateSavedFilterThunk = createAsyncThunk(
    'brand/updateSavedFilter',
    async ({ filterSetId, filterData }, { rejectWithValue }) => {
        try {
            const response = await savedFiltersApi.updateFilterSet(filterSetId, filterData).send();
            if (response.status === 200 && response.data) {
                return {
                    message: response.data.data?.message || 'Filter set updated successfully',
                    success: true,
                    data: response.data.data,
                    filterSetId
                };
            } else {
                return rejectWithValue({
                    message: response.data?.message || 'Failed to update filter set',
                    success: false
                });
            }
        } catch (error) {
            return rejectWithValue({
                message: error.response?.data?.message || 'Failed to update filter set. Please try again.',
                success: false
            });
        }
    }
);

/**
 * Delete saved filter set async thunk
 */
export const deleteSavedFilterThunk = createAsyncThunk(
    'brand/deleteSavedFilter',
    async (filterSetId, { rejectWithValue }) => {
        try {
            const response = await savedFiltersApi.deleteFilterSet(filterSetId).send();
            if (response.status === 200) {
                return {
                    message: 'Filter set deleted successfully',
                    success: true,
                    filterSetId
                };
            } else {
                return rejectWithValue({
                    message: response.data?.message || 'Failed to delete filter set',
                    success: false
                });
            }
        } catch (error) {
            return rejectWithValue({
                message: error.response?.data?.message || 'Failed to delete filter set. Please try again.',
                success: false
            });
        }
    }
);

/**
 * Get specific saved filter set async thunk
 */
export const getSavedFilterSetThunk = createAsyncThunk(
    'brand/getSavedFilterSet',
    async (filterSetId, { rejectWithValue }) => {
        try {
            const response = await savedFiltersApi.getFilterSet(filterSetId).send();
            if (response.status === 200 && response.data) {
                return {
                    message: 'Filter set retrieved successfully',
                    success: true,
                    data: response.data.data
                };
            } else {
                return rejectWithValue({
                    message: response.data?.message || 'Failed to retrieve filter set',
                    success: false
                });
            }
        } catch (error) {
            return rejectWithValue({
                message: error.response?.data?.message || 'Failed to retrieve filter set. Please try again.',
                success: false
            });
        }
    }
);

/**
 * Get global filters async thunk
 */
export const getGlobalFiltersThunk = createAsyncThunk(
    'brand/getGlobalFilters',
    async (params, { rejectWithValue }) => {
        try {
            const response = await savedFiltersApi.getGlobalFilters(params).send();
            if (response.status === 200 && response.data) {
                return {
                    message: 'Global filters retrieved successfully',
                    success: true,
                    data: response.data.data
                };
            } else {
                return rejectWithValue({
                    message: response.data?.message || 'Failed to retrieve global filters',
                    success: false
                });
            }
        } catch (error) {
            return rejectWithValue({
                message: error.response?.data?.message || 'Failed to retrieve global filters. Please try again.',
                success: false
            });
        }
    }
);


/**
 * Get creator lists async thunk
 */
export const getCreatorListsThunk = createAsyncThunk(
    'creator/getCreatorLists',
    async (params, { rejectWithValue }) => {
        try {
            const response = await creatorListApi.getCreatorList(params);
            if (response.status === 200 && response.data) {
                return {
                    message: 'Creator lists retrieved successfully',
                    success: true,
                    data: response.data.data,
                };
            } else if (response.status === 403) {
                return rejectWithValue({
                    message: 'You dont have access to this Brand',
                    success: false,
                    data: null,
                });
            } else {
                return rejectWithValue({
                    message: response.data?.message || 'Failed to retrieve creator lists',
                    success: false
                });
            }
        } catch (error) {
            return rejectWithValue({
                message: error.response?.data?.message || 'Failed to retrieve creator lists. Please try again.',
                success: false
            });
        }
    }
);

/**
 * Create creator lists async thunk
 */

export const createCreatorListThunk = createAsyncThunk(
    'creator/createCreatorList',
    async (payload, { rejectWithValue }) => {
        try {
            const response = await creatorListApi.createCreatorList(payload);
            if (response.status === 200 && response.data) {
                return {
                    message: 'Creator list created successfully',
                    success: true,
                    data: response.data.data,
                };
            } else {
                return rejectWithValue({
                    message: response.data?.message || 'Failed to create creator list',
                    success: false
                });
            }
        } catch (error) {
            return rejectWithValue({
                message: error.response?.data?.message || 'Failed to create creator list. Please try again.',
                success: false
            });
        }
    }
);

/**
 * addMemberTo creator lists async thunk
 */
export const addMemberToListThunk = createAsyncThunk(
    'creator/addMemberToList',
    async ({ listId, brandId, profileIds }, { rejectWithValue }) => {
        try {
            const response = await creatorListApi.addMemberToList(listId, {
                profile_ids: profileIds,
                brand_id: brandId
            });
            if (response.status === 200 && response.data) {
                return {
                    message: 'Member added successfully',
                    success: true,
                    data: response.data.data,
                };
            } else {
                return rejectWithValue({
                    message: response.data?.message || 'Failed to add member to list',
                    success: false
                });
            }
        } catch (error) {
            return rejectWithValue({
                message: error.response?.data?.message || 'Failed to add member. Please try again.',
                success: false
            });
        }
    }
);


/**
 * getListMembers creator lists async thunk
 */
export const getListMembersThunk = createAsyncThunk(
    'creator/getListMembers',
    async ({ listId, brandId }, { rejectWithValue }) => {
        try {
            const response = await creatorListApi.getListMembers(listId, { brand_id: brandId });

            if (response.status === 200 && response.data) {
                return {
                    message: 'List members retrieved successfully',
                    success: true,
                    data: response.data.data,
                };
            } else {
                return rejectWithValue({
                    message: response.data?.message || 'Failed to retrieve list members',
                    success: false
                });
            }
        } catch (error) {
            return rejectWithValue({
                message: error.response?.data?.message || 'Failed to retrieve list members. Please try again.',
                success: false
            });
        }
    }
);


/**
 * delete Creator List async thunk
 */
export const deleteCreatorListThunk = createAsyncThunk(
    'creator/deleteList',
    async ({ listId, body }, { rejectWithValue }) => {
        try {
            const response = await creatorListApi.deleteList(listId, body);

            if (response.status === 200 || response.status === 204) {
                return {
                    message: 'List deleted successfully',
                    success: true,
                };
            } else {
                return rejectWithValue({
                    message: response.data?.message || 'Failed to delete the list',
                    success: false,
                });
            }
        } catch (error) {
            return rejectWithValue({
                message:
                    error.response?.data?.message || 'An error occurred during deletion',
                success: false,
            });
        }
    }
);

/**
 * update Creator List name and description async thunk
 */
export const updateCreatorListThunk = createAsyncThunk(
    'creatorList/update',
    async ({ listId, brandId, body }, { rejectWithValue }) => {
        try {
            const response = await creatorListApi.patchCreatorList(listId, body, { brand_id: brandId });

            if (response.status === 200 || response.status === 204) {
                return {
                    message: 'List deleted successfully',
                    success: true,
                };
            } else {
                return rejectWithValue({
                    message: response.data?.message || 'Failed to delete the list',
                    success: false,
                });
            }
        } catch (error) {
            return rejectWithValue({
                message:
                    error.response?.data?.message || 'An error occurred during deletion',
                success: false,
            });
        }
    }
);

/**
 * update Creator List Member Notes async thunk
 */
export const updateCreatorListMemberNotesThunk = createAsyncThunk(
    'creatorList/updateNotes',
    async ({ listId, profileId, brandId, body }, { rejectWithValue }) => {
        try {
            const response = await creatorListApi.patchCreatorListMemberNotes(
                listId,
                profileId,
                body,
                { brand_id: brandId }
            );

            if (response.status === 200 || response.status === 204) {
                return {
                    message: 'Notes updated successfully',
                    success: true,
                };
            } else {
                return rejectWithValue({
                    message: response.data?.message || 'Failed to update notes',
                    success: false,
                });
            }
        } catch (error) {
            return rejectWithValue({
                message:
                    error.response?.data?.message || 'An error occurred while updating notes',
                success: false,
            });
        }
    }
);

/**
 * Fetch Creator Labels async thunk
 */
export const fetchCreatorListLabelsThunk = createAsyncThunk(
    'creatorList/fetchLabels',
    async ({ brandId, includeDetails = true }, { rejectWithValue }) => {
        try {
            const response = await creatorListApi.getLabels(brandId, includeDetails);

            if (response.status === 200) {
                return {
                    data: response.data.data,
                    success: true,
                };
            } else {
                return rejectWithValue({
                    message: response.data?.message || 'Failed to fetch labels',
                    success: false,
                });
            }
        } catch (error) {
            return rejectWithValue({
                message:
                    error.response?.data?.message || 'An error occurred while fetching labels',
                success: false,
            });
        }
    }
);


/**
 * Create a new Creator Label async thunk
 */
export const createCreatorLabelThunk = createAsyncThunk(
    'creatorList/createLabel',
    async (body, { rejectWithValue }) => {
        try {
            const response = await creatorListApi.createLabel(body);

            if (response.status === 200 || response.status === 201) {
                return {
                    data: response.data.data,
                    message: 'Label created successfully',
                    success: true,
                };
            } else {
                return rejectWithValue({
                    message: response.data?.message || 'Failed to create label',
                    success: false,
                });
            }
        } catch (error) {
            return rejectWithValue({
                message:
                    error.response?.data?.message || 'An error occurred while creating label',
                success: false,
            });
        }
    }
);


/**
 * Delete a Creator Label for a specific profile
 */
export const deleteCreatorLabelThunk = createAsyncThunk(
    'creatorList/deleteLabel',
    async ({ profileId, labelId, body }, { rejectWithValue }) => {
        try {
            const response = await creatorListApi.deleteLabelFromProfile(profileId, labelId, body);

            if (response.status === 200 || response.status === 204) {
                return {
                    message: 'Label deleted successfully',
                    success: true,
                };
            } else {
                return rejectWithValue({
                    message: response.data?.message || 'Failed to delete label',
                    success: false,
                });
            }
        } catch (error) {
            return rejectWithValue({
                message:
                    error.response?.data?.message || 'An error occurred while deleting the label',
                success: false,
            });
        }
    }
);

/**
 * Bulk delete members from a Creator List async thunk
 */
export const deleteCreatorListMembersThunk = createAsyncThunk(
  'creatorList/deleteMembers',
  async ({ listId, body }, { rejectWithValue }) => {
    try {
      const response = await creatorListApi.deleteCreatorListMembers(listId, body);

      if (response.status === 200 || response.status === 204) {
        return {
          message: 'Members removed from list successfully',
          success: true,
        };
      } else {
        return rejectWithValue({
          message: response.data?.message || 'Failed to remove members from list',
          success: false,
        });
      }
    } catch (error) {
      return rejectWithValue({
        message:
          error.response?.data?.message || 'An error occurred while removing members',
        success: false,
      });
    }
  }
);





