import { useState, useRef, useEffect } from "react";
import { HiOutlineTrash } from "react-icons/hi2";
import { Tooltip } from 'antd';



// Main Panel Component
export default function SavedFiltersPanel({ isOpen, onClose, initialData }) {
    const [savedFilters, setSavedFilters] = useState(initialData || []);
    const [isCreating, setIsCreating] = useState(false);
    const panelRef = useRef(null);

    // Close on outside click
    useEffect(() => {
        const handleClickOutside = (event) => {
            if (panelRef.current && !panelRef.current.contains(event.target)) {
                onClose();
            }
        };

        if (isOpen) {
            document.addEventListener("mousedown", handleClickOutside);
        }

        return () => {
            document.removeEventListener("mousedown", handleClickOutside);
        };
    }, [isOpen, onClose]);

    const handleCreate = (name) => {
        const newCard = {
            id: Date.now(),
            name: name || `Untitled ${savedFilters.length + 1}`,
            filters: [],
            createdDate: new Date().toLocaleDateString(),
            resultCount: 0,
        };
        setSavedFilters((prev) => [...prev, newCard]);
        setIsCreating(false);
    };

    const handleDelete = (id) => {
        setSavedFilters((prev) => prev.filter((f) => f.id !== id));
    };

    if (!isOpen) return null;

    return (
        <div
            ref={panelRef}
            className="absolute top-12 right-0 z-50 w-[360px] border border-gray-900 bg-gray-900 shadow-xl flex flex-col rounded-lg max-h-[80vh]"
        >
            {/* Header */}
            <div className="flex justify-between items-center mb-4 p-4 shadow-[0_4px_6px_-1px_rgba(0,0,0,0.3)]">
                <h2 className="text-white text-lg font-semibold">Saved filters</h2>
                <button onClick={onClose} className="text-gray-300 text-xl hover: cursor-pointer hover:text-gray-50">×</button>
            </div>

            {/* Content */}
            <div className="overflow-y-auto flex-1 space-y-4 p-4 max-h-[500px]">
                {savedFilters.map((item, idx) => (
                    <SavedFilterCard
                        key={item.id}
                        name={item.name}
                        filters={item.filters}
                        createdDate={item.createdDate}
                        resultCount={item.resultCount}
                        onDelete={() => handleDelete(item.id)}
                        isNew={false}
                    />
                ))}

                {/* Editable new card */}
                {isCreating && (
                    <SavedFilterCard
                        isNew
                        name=""
                        filters={[]}
                        onNameChange={(newName) => handleCreate(newName)}
                        onCancel={() => setIsCreating(false)}
                    />
                )}
            </div>

            {/* Footer */}
            <div className="mt-4 flex justify-between gap-2 p-4 border-t border-gray-800">
                <button
                    className="flex-1 border border-gray-600 text-gray-300 py-2 rounded-lg"
                    onClick={() => setSavedFilters([])}
                >
                    Clear
                </button>
                <button
                    className="flex-1 bg-sky-400 text-black py-2 rounded-lg font-medium"
                    onClick={() => setIsCreating(true)}
                    disabled={isCreating}
                >
                    + New Filter
                </button>
            </div>
        </div>
    );
}

const capitalizeWords = (input) => {
    return input
        .split(',')
        .map(part =>
            part
                .trim()
                .split('_')
                .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
                .join(' ')
        )
        .join(', ');
};

const isNumberRange = (val) =>
    typeof val === 'string' && /^-?\d+(\.\d+)?\s*-\s*-?\d+(\.\d+)?$/.test(val);

const getDisplayValue = (val) => {
    if (isNumberRange(val)) return formatRange(val);
    return capitalizeWords(val);
};

const formatRange = (value) => {
    const [start, end] = value.split('-');
    return `${formatNumber(start)}-${formatNumber(end)}`;
};

const formatNumber = (num) => {
    const number = parseFloat(num);
    if (number >= 1_000_000) {
        return (number / 1_000_000).toFixed(number % 1_000_000 === 0 ? 0 : 1) + 'M';
    } else if (number >= 1_000) {
        return (number / 1_000).toFixed(number % 1_000 === 0 ? 0 : 1) + 'K';
    }
    return number.toString();
};

// === SavedFilterCard (same file) ===
function SavedFilterCard({
    name,
    filters,
    createdDate,
    resultCount,
    onDelete,
    isNew,
    onNameChange,
    onCancel
}) {
    const [localName, setLocalName] = useState(name);

    return (
        <div className="group rounded-lg p-3 border-1 border-gray-500 space-y-2 ">
            <div className="flex justify-between items-center gap-2">
                {isNew ? (
                    <input
                        autoFocus
                        placeholder="Enter filter name"
                        value={localName}
                        onChange={(e) => setLocalName(e.target.value)}
                        className="w-full px-2 py-1 bg-transparent border border-gray-600 rounded text-white text-sm"
                    />
                ) : (
                    <span className="text-white font-medium text-14-regular">{name}</span>
                )}

                {isNew ? (
                    <button
                        onClick={() => onNameChange(localName)}
                        className="text-xs h-full py-1.5 text-gray-100 border-1 border-brand-600 bg-brand-500 hover:bg-brand-600 hover: cursor-pointer rounded px-2"
                    >
                        Save
                    </button>
                ) : (
                    <button
                        onClick={onDelete}
                        className="text-gray-100 hover:text-red-2 hover:cursor-pointer opacity-0 group-hover:opacity-100"
                    >
                        <HiOutlineTrash className='h-4 w-4' />
                    </button>
                )}
            </div>

            {!isNew && (
                <>
                    <div className="flex flex-wrap gap-2 text-xs">
                        {filters.length > 0 ? (
                            filters.map((filter, i) => (
                                // <span key={i} className="border-1 text-gray-300 px-2 py-1 rounded-full truncate">
                                //     {f.filter}:
                                //     {Array.isArray(f.value)
                                //         ? f.value.map(v => `${v.filter}: ${v.value}`).join(", ")
                                //         : f.value
                                //     }
                                // </span>
                                <Tooltip
                                    title={`${capitalizeWords(filter.filter)}: ${Array.isArray(filter.value)
                                        ? filter.value.map(v => `${v.filter}: ${getDisplayValue(v.value)}`).join(', ')
                                        : getDisplayValue(filter.value)
                                        }`}
                                    color='var(--color-gray-600)'
                                >
                                    <div className='border-1 border-gray-800 text-gray-200 text-sm px-3 py-0.5 rounded-full flex items-center gap-1 max-w-[140px] truncate cursor-default'>
                                        <span className="capitalize font-semibold">
                                            {getDisplayValue(filter.filter).length > 8
                                                ? getDisplayValue(filter.filter).slice(0, 8) + '.. '
                                                : getDisplayValue(filter.filter)}
                                            : &nbsp;
                                        </span>
                                        <div className="flex capitalize truncate line-clamp-1 text-gray-400">
                                            {Array.isArray(filter.value)
                                                ? filter.value.map((sub, idx) => (
                                                    <span key={idx}>{sub.filter}: {getDisplayValue(sub.value)}</span>
                                                ))
                                                : getDisplayValue(filter.value)}
                                        </div>
                                    </div>
                                </Tooltip>
                            ))
                        ) : (
                            <span className="text-gray-400 italic">No filters</span>
                        )}
                    </div>

                    <div className="flex justify-between text-xs text-gray-400">
                        <span className="text-12-medium text-gray-200">{createdDate}</span>
                        <span>{resultCount?.toLocaleString()} results</span>
                    </div>
                </>
            )}
        </div>
    );
}
