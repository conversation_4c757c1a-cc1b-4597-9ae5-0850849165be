# Multi-Backend Architecture Documentation

## Overview

The CreatorVerse frontend has been upgraded to support a **multi-backend architecture** that separates concerns between authentication and discovery/analytics services. This architecture provides better scalability, maintainability, and allows for independent scaling of different service components.

## Architecture Components

### 1. Authentication Service
**Purpose**: Handles all user authentication, authorization, and user management
- User login/registration (influencers and brands)
- OTP verification
- Token management and refresh
- OAuth flows
- Brand management
- User profile management

### 2. Discovery & Analytics Service  
**Purpose**: Handles creator discovery, profile analytics, and data management
- Creator search and discovery
- Filter management and transformation
- Profile analytics (basic and detailed)
- Dashboard KPIs and metrics
- Saved filter sets
- Cache management

## Key Features

### 🔐 Cross-Service Authentication
- Centralized token management across services
- Automatic token refresh with fallback mechanisms
- Secure token sharing between auth and discovery services
- Authentication state synchronization

### 🛡️ Error Handling & Resilience
- Circuit breaker pattern for failing services
- Automatic retry with exponential backoff
- Comprehensive error classification and handling
- Graceful degradation when services are unavailable

### 📊 Health Monitoring
- Real-time health checks for all services
- Performance metrics collection
- Service availability monitoring
- Automatic failover detection

### ⚙️ Environment Configuration
- Environment-specific configurations (dev, staging, production)
- Feature flags per environment
- Service discovery and load balancing
- Security configurations

## File Structure

```
src/app/store/api/
├── config/
│   ├── apiConfig.js              # Multi-backend API configuration
│   └── environmentConfig.js     # Environment-specific settings
├── instances/
│   ├── authInstance.js          # Auth service Alova instance
│   └── discoveryInstance.js     # Discovery service Alova instance
├── services/
│   ├── authService.js           # Cross-service authentication manager
│   ├── errorHandler.js          # Multi-backend error handling
│   └── healthMonitor.js         # Service health monitoring
├── authApi.js                   # Authentication API endpoints
├── brandManagementApi.js        # Brand management endpoints
├── discoveryApi.js              # Discovery and search endpoints
├── analyticsApi.js              # Analytics and metrics endpoints
├── savedFiltersApi.js           # Saved filters management
├── index.js                     # Central API export and manager
└── alovaInstance.js             # Legacy instance (for migration)
```

## Usage Examples

### Basic Setup

```javascript
import { initializeApiSystem, apiServices } from '@/app/store/api';

// Initialize the multi-backend system
await initializeApiSystem({
  enableHealthMonitoring: true,
  enableRetry: true
});

// Use authentication service
const loginResult = await apiServices.auth.login(credentials);

// Use discovery service
const searchResults = await apiServices.discovery.searchCreators({
  searchQuery: 'fashion blogger',
  filters: [/* filter objects */],
  limit: 20
});
```

### Advanced Configuration

```javascript
import { envConfig, healthMonitor, errorHandler } from '@/app/store/api';

// Check service health
const health = healthMonitor.getOverallHealth();
console.log('System health:', health.status);

// Get service metrics
const metrics = healthMonitor.getAllServicesMetrics();
console.log('Service metrics:', metrics);

// Handle custom configuration
envConfig.set('services.auth.timeout', 45000);
```

### Error Handling

```javascript
import { apiServices, ERROR_TYPES } from '@/app/store/api';

try {
  const result = await apiServices.discovery.searchCreators(params);
} catch (error) {
  if (error.type === ERROR_TYPES.AUTHENTICATION_ERROR) {
    // Handle auth error - token refresh will be attempted automatically
  } else if (error.type === ERROR_TYPES.SERVICE_UNAVAILABLE) {
    // Handle service unavailability
  }
}
```

## Environment Configuration

### Development
```javascript
// .env.development
VITE_AUTH_SERVICE_URL=https://your-ngrok-url.ngrok-free.app
VITE_DISCOVERY_SERVICE_URL=http://localhost:8000
```

### Staging
```javascript
// .env.staging
VITE_AUTH_SERVICE_URL=https://auth-staging.creatorverse.com
VITE_DISCOVERY_SERVICE_URL=https://discovery-staging.creatorverse.com
```

### Production
```javascript
// .env.production
VITE_AUTH_SERVICE_URL=https://auth.creatorverse.com
VITE_DISCOVERY_SERVICE_URL=https://discovery.creatorverse.com
```

## Migration Guide

### From Single Backend to Multi-Backend

1. **Update imports**: Replace `alovaInstance` imports with specific service APIs
2. **Update endpoints**: Remove `/v1` prefix from endpoint URLs (handled by instances)
3. **Update error handling**: Use new error handling patterns
4. **Environment variables**: Add service-specific URLs

### Before (Single Backend)
```javascript
import alovaInstance from './alovaInstance';

const response = await alovaInstance.Post('/v1/auth/login', credentials);
```

### After (Multi-Backend)
```javascript
import { apiServices } from '@/app/store/api';

const response = await apiServices.auth.login(credentials);
```

## Best Practices

### 1. Service Selection
- Use `authApi` for authentication-related operations
- Use `discoveryApi` for search and discovery operations
- Use `analyticsApi` for profile analytics and metrics
- Use `savedFiltersApi` for filter management

### 2. Error Handling
- Always wrap API calls in try-catch blocks
- Use the built-in error types for specific error handling
- Let the system handle token refresh automatically
- Implement fallback UI for service unavailability

### 3. Performance
- Enable health monitoring in non-production environments
- Use circuit breakers to prevent cascading failures
- Implement proper caching strategies
- Monitor service metrics regularly

### 4. Security
- Never store sensitive data in localStorage beyond tokens
- Use HTTPS in staging and production environments
- Implement proper CORS policies
- Validate all user inputs before API calls

## Monitoring and Debugging

### Health Monitoring
```javascript
import { healthMonitor } from '@/app/store/api';

// Start monitoring
healthMonitor.startMonitoring();

// Check specific service
const authHealth = healthMonitor.getServiceHealth('auth');

// Get overall system status
const systemStatus = healthMonitor.getOverallHealth();
```

### Error Tracking
```javascript
import { errorHandler } from '@/app/store/api';

// Get service health from circuit breakers
const circuitStatus = errorHandler.getServiceHealth();

// Check if service is available
const isAuthAvailable = healthMonitor.isServiceAvailable('auth');
```

## Troubleshooting

### Common Issues

1. **Service Unavailable**: Check health monitor status and service URLs
2. **Authentication Errors**: Verify token refresh is working properly
3. **CORS Issues**: Ensure proper headers are configured
4. **Timeout Errors**: Adjust timeout settings in environment config

### Debug Mode
Enable debug mode in development to see detailed logs:
```javascript
// Environment config automatically enables debug in development
console.log('Debug mode:', envConfig.isDebugMode());
```

## Future Enhancements

- Service mesh integration
- Advanced load balancing
- Real-time service discovery
- Enhanced metrics and alerting
- Automated failover mechanisms
- GraphQL federation support
