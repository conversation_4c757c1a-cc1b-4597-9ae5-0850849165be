/**
 * Legacy Alova Instance - Backward Compatibility
 *
 * This file maintains backward compatibility for existing code that uses the old
 * single-backend approach. It now routes requests to the appropriate backend
 * service based on the endpoint URL.
 *
 * @deprecated Use the new multi-backend API services instead:
 * - authApi for authentication endpoints
 * - discovery<PERSON>pi for discovery endpoints
 * - analyticsApi for analytics endpoints
 */

import { createAlova } from 'alova';
import FetchAdapter from 'alova/fetch';
import ReactHook from 'alova/react';
import { apiConfig } from './config/apiConfig';
import authService from './services/authService';

// Legacy single backend URL (kept for backward compatibility)
const API_BASE_URL = 'https://a6a2-103-19-196-138.ngrok-free.app';
// const API_BASE_URL = 'http://192.168.0.69:8008';
// const API_BASE_URL = 'http://k8s-ingressn-nginxing-ca1e2e56dd-2a6846d87663b658.elb.ap-south-1.amazonaws.com/cv-user-backend';

/**
 * Route endpoint to appropriate service
 * @param {string} url - Request URL
 * @returns {string} Appropriate service base URL
 */
const routeToService = (url) => {
  // Remove leading slash and version prefix
  const cleanUrl = url.replace(/^\/+/, '').replace(/^v1\//, '');

  // Auth service endpoints
  const authEndpoints = [
    'auth/', 'oauth/', 'common/', 'brands/', 'youtube/'
  ];

  // Check if URL starts with any auth endpoint
  const isAuthEndpoint = authEndpoints.some(endpoint => cleanUrl.startsWith(endpoint));

  if (isAuthEndpoint) {
    return apiConfig.authServiceURL;
  } else {
    // Default to discovery service for other endpoints
    return apiConfig.discoveryServiceURL;
  }
};

/**
 * Create and export the legacy Alova instance with multi-backend routing
 * This serves as backward compatibility for existing code
 */
const alovaInstance = createAlova({
    baseURL: API_BASE_URL, // Default base URL (will be overridden by routing)
    requestAdapter: FetchAdapter(),
    statesHook: ReactHook,

    // Default request options
    defaultOptions: {
        headers: {
            'Content-Type': 'application/json',
            'accept': 'application/json',
        },
    },
    withCredentials: true,

    // Global response handling with multi-backend support
    responded: {
        // Transform successful responses
        onSuccess: async (response, method) => {
            console.log('[LEGACY ALOVA] API response received:', response);
            const json = await response.json();

            if (response.status === 401) {
                console.warn('[LEGACY ALOVA] Access token expired. Attempting token refresh...');

                const storedRefreshToken = authService.getRefreshToken();
                if (!storedRefreshToken) {
                    console.error('[LEGACY ALOVA] Refresh token not found');
                    authService.redirectToLogin(method.url);
                    return;
                }

                try {
                    // Use the new auth service for token refresh
                    await authService.refreshToken();

                    // Update header for the original request
                    const newToken = authService.getAccessToken();
                    method.config.headers['Authorization'] = `Bearer ${newToken}`;

                    // Retry original request with new token
                    return await method.send();
                } catch (refreshErr) {
                    console.error('[LEGACY ALOVA] Token refresh failed:', refreshErr);
                    authService.redirectToLogin(method.url);
                    throw refreshErr;
                }
            }
            return {
                data: json,
                status: response.status
            };
        },

        // Handle request errors
        onError: (error) => {
            console.error('API request failed:', error);
            throw error;
        },
    },

    // Add authorization headers and route to appropriate service
    beforeRequest(method) {
        console.log('[LEGACY ALOVA] Preparing request:', method.url);

        // Route to appropriate service based on endpoint
        const serviceBaseURL = routeToService(method.url);

        // Update the method's base URL to route to correct service
        if (serviceBaseURL !== API_BASE_URL) {
            // Replace the base URL in the full URL
            const fullUrl = method.url;
            const pathWithoutBase = fullUrl.replace(API_BASE_URL, '');
            method.url = serviceBaseURL + pathWithoutBase;
        }

        // Add development headers
        if (import.meta.env.DEV) {
            method.config.headers['ngrok-skip-browser-warning'] = 'true';
        }

        // Add auth token
        const token = authService.getAccessToken();
        if (token) {
            method.config.headers['Authorization'] = `Bearer ${token}`;
        }

        console.log('[LEGACY ALOVA] Routed to service:', method.url);
    }
});

export default alovaInstance;