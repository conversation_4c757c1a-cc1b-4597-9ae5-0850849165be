import { useSelector, useDispatch } from 'react-redux';
import { 
  fetchRolesThunk,
  selectRoles,
  selectRolesStatus,
  selectRolesError,
  selectIsRolesLoading
} from '../../app/store/slices/systemSlice';
import { RequestStatus } from '../../app/store/enum';

/**
 * Custom hook for working with roles
 * This provides an easy interface for components to access and manage roles
 */
const useRoles = () => {
  const dispatch = useDispatch();
  
  // Select role data from the store
  const roles = useSelector(selectRoles);
  const status = useSelector(selectRolesStatus);
  const error = useSelector(selectRolesError);
  
  // Derived states
  const isLoading = useSelector(selectIsRolesLoading);
  const isSuccess = status === RequestStatus.SUCCEEDED;
  const isError = status === RequestStatus.FAILED;
  
  /**
   * Fetch all available roles
   */
  const fetchRoles = () => {
    dispatch(fetchRolesThunk());
  };
  
  /**
   * Get role ID from role name
   * @param {string} roleName - Role name
   * @returns {string|null} Role ID or null if not found
   */
  const getRoleIdByName = (roleName) => {
    if (!roleName || !roles) return null;
    // Since the structure is { name: id } 
    return roles[roleName] || null;
  };
  
  /**
   * Check if a user has a specific role by name
   * @param {Object} user - User object
   * @param {string} roleName - Role name to check
   * @returns {boolean} Whether the user has the role
   */
  const hasRoleByName = (user, roleName) => {
    if (!user || !user.roles || !roleName || !roles) return false;
    const roleId = getRoleIdByName(roleName);
    return user.roles.some(role => role === roleId || (role && role.id === roleId));
  };
  
  /**
   * Get role name from role ID
   * @param {string} roleId - Role ID
   * @returns {string|null} Role name or null if not found
   */
  const getRoleNameById = (roleId) => {
    if (!roleId || !roles) return null;
    // Since data is stored as { name: id }, we need to find the name by id
    const entry = Object.entries(roles).find(([, id]) => id === roleId);
    return entry ? entry[0] : null;
  };
    return {
    // State
    roles,
    status,
    error,
    isLoading,
    isSuccess,
    isError,
    
    // Actions
    fetchRoles,
    
    // Helpers
    getRoleIdByName,
    getRoleNameById,
    hasRoleByName
  };
};

export default useRoles;
