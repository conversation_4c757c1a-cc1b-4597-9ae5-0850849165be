import React from "react";
import ReactDOM from "react-dom";

const DialogBox = ({
    dialogType,
    onTryAnother,
    onLater,
    email,
    onResend,
    onUpdateEmail,
    onDone,
}) => {
    return ReactDOM.createPortal(
        <div className="fixed inset-0 flex items-center justify-center z-[9999] bg-black/70">
            <div className="bg-[#2E2E2E] p-6 rounded-xl text-white max-w-md w-full shadow-2xl shadow-black/70">
                {dialogType === "warning" && (
                    <>
                        <h2 className="text-lg font-semibold mb-2">Oops! You don’t meet the requirements yet</h2>
                        <p className="text-sm">
                            To join <span className="font-medium">CreatorVerse</span> as a Creator, you’ll need:
                        </p>
                        <ul className="text-sm mb-4 space-y-1">
                            <li>✅ 1000+ followers or subscribers on Instagram or YouTube</li>
                            <li>✅ A public creator account</li>
                        </ul>
                        <p className="text-xs text-gray-400 mt-2">
                            We're excited to have you once you're ready!<br />
                            <span className="text-gray-300 italic">Grow your audience and come back soon — we’ll be waiting! 🚀</span>
                        </p>

                        <div className="flex w-full gap-4 justify-end mt-6">
                            <button
                                onClick={onLater}
                                className="px-4 py-2 bg-gray-700 rounded text-white hover:bg-gray-600 transition w-2/5"
                            >
                                Later
                            </button>
                            <button
                                onClick={onTryAnother}
                                className="px-4 py-2 bg-sky-500 rounded text-white font-bold hover:bg-sky-400 transition w-3/5"
                            >
                                Try Another Account
                            </button>
                        </div>
                    </>
                )}

                {dialogType === "otp" && (
                    <>
                        <h2 className="text-md font-semibold mb-2">
                            We’ve sent a code to <span className="font-bold">{email}</span> 📩
                        </h2>
                        <p className="text-sm mb-3">Enter the OTP below to keep things moving</p>
                        <div className="flex justify-between space-x-2 mb-3">
                            {[...Array(4)].map((_, i) => (
                                <input
                                    key={i}
                                    maxLength={1}
                                    className="w-10 h-10 bg-black border border-gray-600 rounded text-center text-xl"
                                />
                            ))}
                        </div>
                        <p className="text-xs text-gray-400 mb-2">
                            Hang tight — we can’t wait to have you on board!<br />
                            Build your audience. Land collabs. Let’s go! 🔥
                        </p>
                        <div className="text-right text-sm text-blue-300 cursor-pointer mb-4" onClick={onResend}>
                            Resend OTP
                        </div>
                        <div className="flex gap-4 justify-end">
                            <button onClick={onUpdateEmail} className="px-4 py-2 bg-gray-700 rounded">Update Email</button>
                            <button onClick={onDone} className="px-4 py-2 bg-sky-500 text-black rounded font-bold">Done!</button>
                        </div>
                    </>
                )}
            </div>
        </div>,
        document.body
    );
};

export default DialogBox;
