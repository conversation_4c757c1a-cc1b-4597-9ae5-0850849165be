import React, { useRef, useEffect } from 'react';
import PropTypes from 'prop-types';

/**
 * A reusable popup layout component with customizable buttons and close functionality.
 * 
 * @param {Object} props Component props
 * @param {React.ReactNode} props.children Content to display inside the popup
 * @param {Function} props.onClose Function to call when popup is closed
 * @param {string} props.title Title to display at the top of the popup
 * @param {boolean} props.isAcceptButton Whether to show the accept button
 * @param {boolean} props.isCancelButton Whether to show the cancel button
 * @param {string} props.acceptText Text to display on the accept button
 * @param {string} props.cancelText Text to display on the cancel button
 * @param {Function} props.onAccept Function to call when accept button is clicked
 * @param {string} props.width Width of the popup (CSS value)
 * @param {string} props.maxWidth Max width of the popup (CSS value)
 * @param {boolean} props.closeOnOutsideClick Whether to close the popup when clicking outside
 */
const PopupLayout = ({
    children,
    onClose,
    className = 'bg-gray-900',
    isAcceptButton = true,
    isCancelButton = true,
    acceptText = 'Accept',
    cancelText = 'Close',
    onAccept = () => { },
    width = '500px',
    maxWidth = '90vw',
    closeOnOutsideClick = true
}) => {
    const popupRef = useRef(null);

    // Handle click outside the popup
    useEffect(() => {
        if (!closeOnOutsideClick) return;

        const handleClickOutside = (e) => {
            if (popupRef.current && !popupRef.current.contains(e.target)) {
                onClose();
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => document.removeEventListener('mousedown', handleClickOutside);
    }, [onClose, closeOnOutsideClick]);

    // Handle escape key press
    useEffect(() => {
        const handleKeyDown = (e) => {
            if (e.key === 'Escape') {
                onClose();
            }
        };

        document.addEventListener('keydown', handleKeyDown);
        return () => document.removeEventListener('keydown', handleKeyDown);
    }, [onClose]);
     
    // Prevent background scroll while popup is open
    useEffect(() => {
        const originalOverflow = document.body.style.overflow;
        document.body.style.overflow = 'hidden';

        return () => {
            document.body.style.overflow = originalOverflow;
        };
    }, []);

    const handleAccept = () => {
        onAccept();
        onClose();
    };

    return (
        <div className="fixed inset-0 flex items-center justify-center bg-black/70 bg-opacity-50 z-50">
            <div
                ref={popupRef}
                className={` rounded-lg shadow-xl overflow-hidden flex flex-col p-7.5 ${className}`}
                style={{ width, maxWidth }}
            >
                {/* Popup content */}
                <div className="relative flex-1  min-h-15">
                    <button
                        onClick={onClose}
                        className="absolute top-0 right-0 text-gray-400 hover:text-white transition cursor-pointer"
                        aria-label="Close"
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                        </svg>
                    </button>
                    {children}
                </div>

                {/* Footer with action buttons */}
                {(isAcceptButton || isCancelButton) && (
                    <div className="flex justify-end gap-4 text-16-semibold">
                        {isCancelButton && (
                            <button
                                onClick={onClose}
                                className="px-8 py-2.5 bg-transparent border border-gray-500 text-gray-300 rounded hover:text-gray-100 hover:border-gray-100 transition"
                            >
                                {cancelText}
                            </button>
                        )}
                        {isAcceptButton && (
                            <button
                                onClick={handleAccept}
                                className="px-8 py-2.5 bg-brand-500 text-white rounded hover:bg-brand-600 transition"
                            >
                                {acceptText}
                            </button>
                        )}
                    </div>
                )}
            </div>
        </div>
    );
};

PopupLayout.propTypes = {
    children: PropTypes.node.isRequired,
    onClose: PropTypes.func.isRequired,
    title: PropTypes.string,
    isAcceptButton: PropTypes.bool,
    isCancelButton: PropTypes.bool,
    acceptText: PropTypes.string,
    cancelText: PropTypes.string,
    onAccept: PropTypes.func,
    width: PropTypes.string,
    maxWidth: PropTypes.string,
    closeOnOutsideClick: PropTypes.bool
};

export default PopupLayout;
