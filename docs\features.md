# Features

This document outlines the key features and functionalities of the Creatorverse Frontend application.

## Authentication

- User registration and login (Brand and Influencer)
- OTP verification
- Google OAuth integration
- Profile onboarding flow
- Comprehensive logout functionality with complete state clearing
- Session management with session_id tracking
- Secure redirection after logout

## Brand Management

- Brand profile creation and management
- Campaign creation and management
- Influencer discovery and collaboration

## Influencer Management

- Influencer profile creation and management
- Campaign browsing and application
- Earning tracking and management

## Shared Features

- Responsive UI/UX for various devices
- Internationalization (i18n) support for English, French, and Thai
- Reusable UI components and custom hooks
- State management with Redux Toolkit
- API communication with Alova

