import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { fetchRolesThunk, selectRolesStatus, setRoleFromStorage } from './store/slices/systemSlice';
import { RequestStatus } from './store/enum';
// import apiConfig from './store/api/config/apiConfig';



/**
 * Component that initializes the app by fetching necessary data
 * Will run once when the app starts to load roles and other essential data
 */
const AppInitializer = ({ children }) => {
  const dispatch = useDispatch();
  const [initialized, setInitialized] = useState(false);
  const rolesStatus = useSelector(selectRolesStatus);

  //  useEffect(() => {
  //   apiConfig.getAllServiceHealth()
  //     .then((response) => {
  //       console.log('Service health:', response);
  //       // handle the response if needed
  //       // console.log('Service health:', response);
  //     })
  //     .catch((error) => {
  //       // handle the error if needed
  //       // console.error('Service health error:', error);
  //     });
  // }, []);

  // Fetch roles on app initialization
  useEffect(() => {
    const roles = localStorage.getItem('role')
    if (roles && roles !== 'undefined') {
      const parsedRole = JSON.parse(roles);
      dispatch(setRoleFromStorage({ data: parsedRole, status: RequestStatus.SUCCEEDED }));
      setInitialized(true);
      return;
    }

    if (rolesStatus === RequestStatus.IDLE && !initialized) {
      console.log('AppInitializer: Dispatching fetchRolesThunk');
      dispatch(fetchRolesThunk());
      setInitialized(true);
    }
  }, [dispatch, rolesStatus, initialized]);

  // Simply render children, this component only handles initialization
  return children;
};

export default AppInitializer;
