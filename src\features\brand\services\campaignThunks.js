// Campaign async thunks for API operations
import { createAsyncThunk } from '@reduxjs/toolkit';
import campaignManagementApi from '../../../app/store/api/campaignManagementApi';

/**
 * Get campaigns list async thunk
 */
export const getCampaignsThunk = createAsyncThunk(
    'campaign/getCampaigns',
    async (params = {}, { rejectWithValue }) => {
        try {
            // Temporary mock data - will be replaced with actual API call
            const mockCampaigns = [
                {
                    id: 1,
                    name: "Summer Vibes",
                    createdOn: "2023-10-01",
                    liveDate: "2023-10-15",
                    createdBy: "<PERSON>",
                    createdByPicture: "https://images.unsplash.com/photo-1494790108377-be9c29b29330?w=150&h=150&fit=crop&crop=faces",
                    status: "Active",
                    creators: ["avatar1", "avatar2", "avatar3", "avatar4"],
                    progress: 75
                },
                {
                    id: 2,
                    name: "Winter Warmers",
                    createdOn: "2023-11-10",
                    liveDate: "2023-11-25",
                    createdBy: "<PERSON>",
                    createdByPicture: "https://images.unsplash.com/photo-1502685104226-ee32379fefbe?w=150&h=150&fit=crop&crop=faces",
                    status: "Planned",
                    creators: ["avatar5", "avatar2"],
                    progress: 25
                }
            ];

            // Simulate API delay
            await new Promise(resolve => setTimeout(resolve, 500));

            return {
                message: 'Campaigns retrieved successfully',
                success: true,
                data: mockCampaigns
            };

            // TODO: Replace with actual API call when campaignManagementApi is ready
            // const response = await campaignManagementApi.getCampaigns(params).send();
            // if (response.status === 200 && response.data) {
            //     return {
            //         message: response.data?.message || 'Campaigns retrieved successfully',
            //         success: true,
            //         data: response.data.data || []
            //     };
            // } else {
            //     return rejectWithValue({
            //         message: response.data?.message || 'Failed to fetch campaigns',
            //         success: false
            //     });
            // }
        } catch (error) {
            return rejectWithValue({
                message: error.response?.data?.message || 'Failed to fetch campaigns. Please try again.',
                success: false
            });
        }
    }
);

/**
 * Get campaign by ID async thunk
 */
export const getCampaignByIdThunk = createAsyncThunk(
    'campaign/getCampaignById',
    async (campaignId, { rejectWithValue }) => {
        try {
            // Temporary mock data - will be replaced with actual API call
            const mockCampaign = {
                id: campaignId,
                name: "Winter Vibes",
                description: "A vibrant summer campaign focusing on lifestyle and fashion",
                createdOn: "2023-10-01",
                liveDate: "2023-10-15",
                closeDate: "2023-10-30",
                createdBy: "John Doe",
                createdByPicture: "https://images.unsplash.com/photo-1494790108377-be9c29b29330?w=150&h=150&fit=crop&crop=faces",
                status: "Active",
                objective: "Brand Awareness",
                budget: 50000,
                isPrivate: false,
                progress: 75,
                creators: ["avatar1", "avatar2", "avatar3", "avatar4"]
            };

            // Simulate API delay
            await new Promise(resolve => setTimeout(resolve, 300));

            return {
                message: 'Campaign details retrieved successfully',
                success: true,
                data: mockCampaign
            };

            // TODO: Replace with actual API call when campaignManagementApi is ready
            // const response = await campaignManagementApi.getCampaignById(campaignId).send();
            // if (response.status === 200 && response.data) {
            //     return {
            //         message: response.data?.message || 'Campaign details retrieved successfully',
            //         success: true,
            //         data: response.data.data
            //     };
            // } else {
            //     return rejectWithValue({
            //         message: response.data?.message || 'Failed to fetch campaign details',
            //         success: false
            //     });
            // }
        } catch (error) {
            return rejectWithValue({
                message: error.response?.data?.message || 'Failed to fetch campaign details. Please try again.',
                success: false
            });
        }
    }
);

/**
 * Create campaign async thunk
 */
export const createCampaignThunk = createAsyncThunk(
    'campaign/createCampaign',
    async (campaignData, { rejectWithValue }) => {
        try {
            // Temporary mock implementation - will be replaced with actual API call
            const newCampaign = {
                id: Date.now(), // Mock ID generation
                ...campaignData,
                createdOn: new Date().toISOString().split('T')[0],
                createdBy: "Current User", // Will be replaced with actual user data
                createdByPicture: "https://images.unsplash.com/photo-1494790108377-be9c29b29330?w=150&h=150&fit=crop&crop=faces",
                progress: 0,
                creators: []
            };

            // Simulate API delay
            await new Promise(resolve => setTimeout(resolve, 800));

            return {
                message: 'Campaign created successfully',
                success: true,
                data: newCampaign
            };

            // TODO: Replace with actual API call when campaignManagementApi is ready
            // const response = await campaignManagementApi.createCampaign(campaignData).send();
            // if (response.status === 200 && response.data) {
            //     return {
            //         message: response.data?.message || 'Campaign created successfully',
            //         success: true,
            //         data: response.data.data
            //     };
            // } else {
            //     return rejectWithValue({
            //         message: response.data?.message || 'Failed to create campaign',
            //         success: false
            //     });
            // }
        } catch (error) {
            return rejectWithValue({
                message: error.response?.data?.message || 'Failed to create campaign. Please try again.',
                success: false
            });
        }
    }
);

/**
 * Update campaign async thunk
 */
export const updateCampaignThunk = createAsyncThunk(
    'campaign/updateCampaign',
    async ({ campaignId, campaignData }, { rejectWithValue }) => {
        try {
            // Temporary mock implementation - will be replaced with actual API call
            const updatedCampaign = {
                id: campaignId,
                ...campaignData,
                updatedOn: new Date().toISOString().split('T')[0]
            };

            // Simulate API delay
            await new Promise(resolve => setTimeout(resolve, 600));

            return {
                message: 'Campaign updated successfully',
                success: true,
                data: updatedCampaign
            };

            // TODO: Replace with actual API call when campaignManagementApi is ready
            // const response = await campaignManagementApi.updateCampaign(campaignId, campaignData).send();
            // if (response.status === 200 && response.data) {
            //     return {
            //         message: response.data?.message || 'Campaign updated successfully',
            //         success: true,
            //         data: response.data.data
            //     };
            // } else {
            //     return rejectWithValue({
            //         message: response.data?.message || 'Failed to update campaign',
            //         success: false
            //     });
            // }
        } catch (error) {
            return rejectWithValue({
                message: error.response?.data?.message || 'Failed to update campaign. Please try again.',
                success: false
            });
        }
    }
);

/**
 * Delete campaign async thunk
 */
export const deleteCampaignThunk = createAsyncThunk(
    'campaign/deleteCampaign',
    async (campaignId, { rejectWithValue }) => {
        try {
            // Simulate API delay
            await new Promise(resolve => setTimeout(resolve, 400));

            return {
                message: 'Campaign deleted successfully',
                success: true,
                data: { id: campaignId }
            };

            // TODO: Replace with actual API call when campaignManagementApi is ready
            // const response = await campaignManagementApi.deleteCampaign(campaignId).send();
            // if (response.status === 200) {
            //     return {
            //         message: response.data?.message || 'Campaign deleted successfully',
            //         success: true,
            //         data: { id: campaignId }
            //     };
            // } else {
            //     return rejectWithValue({
            //         message: response.data?.message || 'Failed to delete campaign',
            //         success: false
            //     });
            // }
        } catch (error) {
            return rejectWithValue({
                message: error.response?.data?.message || 'Failed to delete campaign. Please try again.',
                success: false
            });
        }
    }
);

/**
 * Get campaign creators async thunk
 */
export const getCampaignCreatorsThunk = createAsyncThunk(
    'campaign/getCampaignCreators',
    async (campaignId, { rejectWithValue }) => {
        try {
            // Temporary mock data - will be replaced with actual API call
            const mockCreators = [
                {
                    id: 1,
                    avatar: "https://images.unsplash.com/photo-1580489944761-15a19d654956?w=150&h=150&fit=crop&crop=faces",
                    name: "Ananya Pandey",
                    userName: "ananya_pandey",
                    status: "shortlisted",
                    paymentStatus: "paid",
                    impactScore: 20,
                    contentDueDate: "2024-07-15",
                    followers: 12000,
                    engagementRate: 3,
                    addedBy: "John Doe",
                    links: "https://www.ananypandey.com",
                    deliverables: {
                        instagram: { post: 1, reels: 1, story: 1 },
                        youtube: { video: 1, shorts: 1 },
                    },
                },
                {
                    id: 2,
                    avatar: "https://images.unsplash.com/photo-1502767089025-6572583495b9?w=150&h=150&fit=crop&crop=faces",
                    name: "Rohit Sharma",
                    userName: "rohit_s",
                    status: "outreached",
                    paymentStatus: "pending",
                    impactScore: 35,
                    contentDueDate: "2024-07-20",
                    followers: 25000,
                    engagementRate: 4.5,
                    addedBy: "Alice Smith",
                    links: "https://www.rohitsharma.com",
                    deliverables: {
                        instagram: { post: 2, reels: 1 },
                        youtube: { video: 1 },
                    },
                },
                {
                    id: 3,
                    avatar: "https://images.unsplash.com/photo-1517841905240-472988babdf9?w=150&h=150&fit=crop&crop=faces",
                    name: "Megha Singh",
                    userName: "megha.singh",
                    status: "negotiation",
                    paymentStatus: "paid",
                    impactScore: 18,
                    contentDueDate: "2024-07-22",
                    followers: 9800,
                    engagementRate: 2.8,
                    addedBy: "David Liu",
                    links: "https://www.meghasingh.in",
                    deliverables: {
                        instagram: { story: 2 },
                    },
                },
                {
                    id: 4,
                    avatar: "https://images.unsplash.com/photo-1603415526960-f9e124d91445?w=150&h=150&fit=crop&crop=faces",
                    name: "Arjun Mehta",
                    userName: "arjun_mehta",
                    status: "onboarded",
                    paymentStatus: "paid",
                    impactScore: 28,
                    contentDueDate: "2024-07-18",
                    followers: 15000,
                    engagementRate: 3.6,
                    addedBy: "Sana Khan",
                    links: "https://www.arjunmehta.co",
                    deliverables: {
                        instagram: { post: 1 },
                        youtube: { shorts: 2 },
                    },
                },
                {
                    id: 5,
                    avatar: "https://images.unsplash.com/photo-1531891437562-5c9e1ed401c7?w=150&h=150&fit=crop&crop=faces",
                    name: "Neha Dhawan",
                    userName: "neha_dh",
                    status: "in_progress",
                    paymentStatus: "paid",
                    impactScore: 22,
                    contentDueDate: "2024-07-25",
                    followers: 21000,
                    engagementRate: 3.9,
                    addedBy: "Michael Ray",
                    links: "https://www.nehadhawan.org",
                    deliverables: {
                        instagram: { reels: 2, story: 1 },
                    },
                },
                {
                    id: 6,
                    avatar: "https://images.unsplash.com/photo-1529626455594-4ff0802cfb7e?w=150&h=150&fit=crop&crop=faces",
                    name: "Vikram Kapoor",
                    userName: "vik_kapoor",
                    status: "rejected",
                    paymentStatus: "paid",
                    impactScore: 30,
                    contentDueDate: "2024-07-21",
                    followers: 18000,
                    engagementRate: 4.1,
                    addedBy: "John Doe",
                    links: "https://www.vikramkapoor.com",
                    deliverables: {
                        youtube: { video: 1, shorts: 1 },
                    },
                },
                {
                    id: 7,
                    avatar: "https://images.unsplash.com/photo-1494790108377-be9c29b29330?w=150&h=150&fit=crop&crop=faces",
                    name: "Priya Verma",
                    userName: "priyav",
                    status: "negotiation",
                    paymentStatus: "pending",
                    impactScore: 27,
                    contentDueDate: "2024-07-28",
                    followers: 30000,
                    engagementRate: 5.2,
                    addedBy: "Alice Smith",
                    links: "https://www.priyaverma.in",
                    deliverables: {
                        instagram: { post: 1, reels: 1 },
                        youtube: { video: 1 },
                    },
                },
                {
                    id: 8,
                    avatar: "https://images.unsplash.com/photo-1517363898873-fb96f06bfa7b?w=150&h=150&fit=crop&crop=faces",
                    name: "Kunal Joshi",
                    userName: "kunal.j",
                    status: "shortlisted",
                    paymentStatus: "pending",
                    impactScore: 24,
                    contentDueDate: "2024-07-19",
                    followers: 17000,
                    engagementRate: 3.3,
                    addedBy: "Sana Khan",
                    links: "https://www.kunaljoshi.net",
                    deliverables: {
                        instagram: { story: 2 },
                    },
                },
                {
                    id: 9,
                    avatar: "https://images.unsplash.com/photo-1552058544-f2b08422138a?w=150&h=150&fit=crop&crop=faces",
                    name: "Ritika Sharma",
                    userName: "ritika_s",
                    status: "onboarded",
                    paymentStatus: "paid",
                    impactScore: 32,
                    contentDueDate: "2024-07-17",
                    followers: 26000,
                    engagementRate: 4.7,
                    addedBy: "David Liu",
                    links: "https://www.ritikasharma.org",
                    deliverables: {
                        instagram: { reels: 2 },
                        youtube: { shorts: 1 },
                    },
                },
                {
                    id: 10,
                    avatar: "https://images.unsplash.com/photo-1552374196-c4e7ffc6e126?w=150&h=150&fit=crop&crop=faces",
                    name: "Kabir Anand",
                    userName: "kabir.anand",
                    status: "completed",
                    paymentStatus: "cancelled",
                    impactScore: 79,
                    contentDueDate: "2024-07-23",
                    followers: 11000,
                    engagementRate: 2.9,
                    addedBy: "Michael Ray",
                    links: "https://www.kabiranand.dev",
                    deliverables: {
                        instagram: { post: 1 },
                        youtube: { video: 1 },
                    },
                },
            ];

            // Simulate API delay
            await new Promise(resolve => setTimeout(resolve, 400));

            return {
                message: 'Campaign creators retrieved successfully',
                success: true,
                data: mockCreators
            };

            // TODO: Replace with actual API call when campaignManagementApi is ready
            // const response = await campaignManagementApi.getCampaignCreators(campaignId).send();
            // if (response.status === 200 && response.data) {
            //     return {
            //         message: response.data?.message || 'Campaign creators retrieved successfully',
            //         success: true,
            //         data: response.data.data || []
            //     };
            // } else {
            //     return rejectWithValue({
            //         message: response.data?.message || 'Failed to fetch campaign creators',
            //         success: false
            //     });
            // }
        } catch (error) {
            return rejectWithValue({
                message: error.response?.data?.message || 'Failed to fetch campaign creators. Please try again.',
                success: false
            });
        }
    }
);

/**
 * Add creator to campaign async thunk
 */
export const addCreatorToCampaignThunk = createAsyncThunk(
    'campaign/addCreatorToCampaign',
    async ({ campaignId, creatorData }, { rejectWithValue }) => {
        try {
            // Temporary mock implementation - will be replaced with actual API call
            const newCreatorAssignment = {
                id: Date.now(),
                campaignId,
                creatorId: creatorData.id,
                status: "Pending",
                assignedDate: new Date().toISOString().split('T')[0],
                ...creatorData
            };

            // Simulate API delay
            await new Promise(resolve => setTimeout(resolve, 500));

            return {
                message: 'Creator added to campaign successfully',
                success: true,
                data: newCreatorAssignment
            };
        } catch (error) {
            return rejectWithValue({
                message: error.response?.data?.message || 'Failed to add creator to campaign. Please try again.',
                success: false
            });
        }
    }
);

/**
 * Remove creator from campaign async thunk
 */
export const removeCreatorFromCampaignThunk = createAsyncThunk(
    'campaign/removeCreatorFromCampaign',
    async ({ campaignId, creatorId }, { rejectWithValue }) => {
        try {
            // Simulate API delay
            await new Promise(resolve => setTimeout(resolve, 400));

            return {
                message: 'Creator removed from campaign successfully',
                success: true,
                data: { campaignId, creatorId }
            };
        } catch (error) {
            return rejectWithValue({
                message: error.response?.data?.message || 'Failed to remove creator from campaign. Please try again.',
                success: false
            });
        }
    }
);

/**
 * Get campaign content async thunk
 */
export const getCampaignContentThunk = createAsyncThunk(
    'campaign/getCampaignContent',
    async (campaignId, { rejectWithValue }) => {
        try {
            // Temporary mock data - will be replaced with actual API call
            const mockContent = [
                {
                    id: 1,
                    userName: "Aarav Sharma",
                    profileImage: "https://randomuser.me/api/portraits/men/75.jpg",
                    status: "approved",
                    caption: "Yoga: done. Smoothie: blended. Morning routine: revamped! 🧘‍♀️🍓 Discover my secrets to a balanced life!",
                    type: "Post",
                    approvedDate: "23 May 2025",
                    submittedDate: "23 April 2025",
                    rejectedDate: "",
                    platform: "instagram",
                    remarks: "All clear! Just a few tweaks needed.",
                    actionBy: "John Doe"
                },
                {
                    id: 2,
                    userName: "Ananya Mehta",
                    profileImage: "https://randomuser.me/api/portraits/women/65.jpg",
                    status: "pending",
                    caption: "A seasonal push to promote original content and new series. #Creatorverse",
                    type: "Shorts",
                    approvedDate: "",
                    submittedDate: "24 April 2024",
                    rejectedDate: "",
                    platform: "youtube",
                    remarks: "All clear! Just a few tweaks needed.",
                    actionBy: "Raj Doe"
                },
                {
                    id: 3,
                    userName: "Karan Verma",
                    profileImage: "https://randomuser.me/api/portraits/men/32.jpg",
                    status: "approved",
                    caption: "All good to go!",
                    type: "Story",
                    approvedDate: "25 April 2024",
                    submittedDate: "25 April 2024",
                    rejectedDate: "",
                    platform: "instagram",
                    remarks: "All clear! Just a few tweaks needed.",
                    actionBy: "Rahul Doe"
                },
                {
                    id: 4,
                    userName: "Isha Kapoor",
                    profileImage: "https://randomuser.me/api/portraits/women/44.jpg",
                    status: "posted",
                    caption: "Scheduled for weekend release.",
                    type: "Video",
                    approvedDate: "",
                    submittedDate: "26 April 2024",
                    rejectedDate: "",
                    platform: "youtube",
                    remarks: "All clear! Just a few tweaks needed.",
                    actionBy: "John Smith",
                    metrics: [
                        {
                            label: 'Engagement',
                            value: '5%',
                            icon: 'https://cdn-icons-png.flaticon.com/512/929/929564.png', // replace with your icon
                        },
                        {
                            label: 'Reach',
                            value: '500',
                            icon: 'https://cdn-icons-png.flaticon.com/512/3135/3135715.png',
                        },
                        {
                            label: 'Impressions',
                            value: '1,000',
                            icon: 'https://cdn-icons-png.flaticon.com/512/1828/1828859.png',
                        },
                        {
                            label: 'Likes',
                            value: '212',
                            icon: 'https://cdn-icons-png.flaticon.com/512/833/833472.png',
                        },
                        {
                            label: 'Comments',
                            value: '409',
                            icon: 'https://cdn-icons-png.flaticon.com/512/1380/1380338.png',
                        },
                        {
                            label: 'Shares',
                            value: '7,000',
                            icon: 'https://cdn-icons-png.flaticon.com/512/1380/1380332.png',
                        },
                    ]
                },
                {
                    id: 5,
                    userName: "Rohan Singh",
                    profileImage: "https://randomuser.me/api/portraits/men/23.jpg",
                    status: "posted",
                    caption: "Published successfully.",
                    type: "Short",
                    approvedDate: "22 April 2024",
                    submittedDate: "22 April 2024",
                    rejectedDate: "",
                    platform: "youtube",
                    actionBy: "Raja Doe",
                    metrics: [
                        {
                            label: 'Engagement',
                            value: '5%',
                            icon: 'https://cdn-icons-png.flaticon.com/512/929/929564.png', // replace with your icon
                        },
                        {
                            label: 'Reach',
                            value: '500',
                            icon: 'https://cdn-icons-png.flaticon.com/512/3135/3135715.png',
                        },
                        {
                            label: 'Impressions',
                            value: '1,000',
                            icon: 'https://cdn-icons-png.flaticon.com/512/1828/1828859.png',
                        },
                        {
                            label: 'Likes',
                            value: '212',
                            icon: 'https://cdn-icons-png.flaticon.com/512/833/833472.png',
                        },
                        {
                            label: 'Comments',
                            value: '409',
                            icon: 'https://cdn-icons-png.flaticon.com/512/1380/1380338.png',
                        },
                        {
                            label: 'Shares',
                            value: '7,000',
                            icon: 'https://cdn-icons-png.flaticon.com/512/1380/1380332.png',
                        },
                    ]

                },
                {
                    id: 6,
                    userName: "Priya Desai",
                    profileImage: "https://randomuser.me/api/portraits/women/58.jpg",
                    status: "approved",
                    caption: "Great work! Ready to go live.",
                    type: "Post",
                    approvedDate: "21 April 2024",
                    submittedDate: "21 April 2024",
                    rejectedDate: "",
                    platform: "instagram"
                },
                {
                    id: 7,
                    userName: "Neeraj Khanna",
                    profileImage: "https://randomuser.me/api/portraits/men/47.jpg",
                    status: "rejected",
                    caption: "Please update the content tone and re-submit.",
                    type: "Video",
                    approvedDate: "",
                    submittedDate: "20 April 2024",
                    rejectedDate: "20 April 2024",
                    platform: "youtube"

                },
                {
                    id: 8,
                    userName: "Sanya Arora",
                    profileImage: "https://randomuser.me/api/portraits/women/36.jpg",
                    status: "rejected",
                    caption: "Showcasing curated global products for summer travel.",
                    type: "Story",
                    approvedDate: "",
                    submittedDate: "19 April 2024",
                    rejectedDate: "",
                    platform: "instagram"
                }
            ];

            // Simulate API delay
            await new Promise(resolve => setTimeout(resolve, 400));

            return {
                message: 'Campaign content retrieved successfully',
                success: true,
                data: mockContent
            };
        } catch (error) {
            return rejectWithValue({
                message: error.response?.data?.message || 'Failed to fetch campaign content. Please try again.',
                success: false
            });
        }
    }
);

/**
 * Approve content async thunk
 */
export const approveContentThunk = createAsyncThunk(
    'campaign/approveContent',
    async ({ campaignId, contentId }, { rejectWithValue }) => {
        try {
            // Simulate API delay
            await new Promise(resolve => setTimeout(resolve, 500));

            return {
                message: 'Content approved successfully',
                success: true,
                data: { campaignId, contentId, status: 'approved', approvedDate: new Date().toISOString().split('T')[0] }
            };
        } catch (error) {
            return rejectWithValue({
                message: error.response?.data?.message || 'Failed to approve content. Please try again.',
                success: false
            });
        }
    }
);

/**
 * Reject content async thunk
 */
export const rejectContentThunk = createAsyncThunk(
    'campaign/rejectContent',
    async ({ campaignId, contentId, reason }, { rejectWithValue }) => {
        try {
            // Simulate API delay
            await new Promise(resolve => setTimeout(resolve, 500));

            return {
                message: 'Content rejected successfully',
                success: true,
                data: { campaignId, contentId, status: 'rejected', rejectedDate: new Date().toISOString().split('T')[0], reason }
            };
        } catch (error) {
            return rejectWithValue({
                message: error.response?.data?.message || 'Failed to reject content. Please try again.',
                success: false
            });
        }
    }
);

/**
 * Get campaign analytics async thunk
 */
export const getCampaignAnalyticsThunk = createAsyncThunk(
    'campaign/getCampaignAnalytics',
    async (campaignId, { rejectWithValue }) => {
        try {
            // Temporary mock data - will be replaced with actual API call
            const mockAnalytics = {
                campaignId,
                totalReach: 1250000,
                totalEngagement: 85000,
                engagementRate: 6.8,
                totalImpressions: 2100000,
                clickThroughRate: 2.4,
                conversionRate: 1.2,
                totalSpent: 35000,
                costPerEngagement: 0.41,
                lastUpdated: new Date().toISOString()
            };

            // Simulate API delay
            await new Promise(resolve => setTimeout(resolve, 600));

            return {
                message: 'Campaign analytics retrieved successfully',
                success: true,
                data: mockAnalytics
            };
        } catch (error) {
            return rejectWithValue({
                message: error.response?.data?.message || 'Failed to fetch campaign analytics. Please try again.',
                success: false
            });
        }
    }
);

/**
 * Get campaign metrics async thunk
 */
export const getCampaignMetricsThunk = createAsyncThunk(
    'campaign/getCampaignMetrics',
    async ({ campaignId, timeRange = '30d' }, { rejectWithValue }) => {
        try {
            // Temporary mock data - will be replaced with actual API call
            const mockMetrics = [
                { date: '2023-10-01', reach: 45000, engagement: 3200, impressions: 78000 },
                { date: '2023-10-02', reach: 52000, engagement: 3800, impressions: 89000 },
                { date: '2023-10-03', reach: 48000, engagement: 3500, impressions: 82000 },
                { date: '2023-10-04', reach: 61000, engagement: 4200, impressions: 95000 },
                { date: '2023-10-05', reach: 58000, engagement: 4000, impressions: 91000 }
            ];

            // Simulate API delay
            await new Promise(resolve => setTimeout(resolve, 500));

            return {
                message: 'Campaign metrics retrieved successfully',
                success: true,
                data: mockMetrics
            };
        } catch (error) {
            return rejectWithValue({
                message: error.response?.data?.message || 'Failed to fetch campaign metrics. Please try again.',
                success: false
            });
        }
    }
);
