import { useEffect, useState } from "react";
import { useSelector } from 'react-redux';
import { selectAllChannelsWithIcons } from '@/app/store/slices/systemSlice';
// eslint-disable-next-line no-unused-vars
import { motion } from 'framer-motion';
import { useNavigate } from "react-router-dom";
import { useDispatch } from "react-redux";
import { useLoading } from '@shared/components/UI/LoadingContext';
import { useSnackbar } from '@shared/components/UI/SnackbarContext';

import useInfluencerSelectors from '@influencer/services/influencerSelectors';
import { getInfluencerInfoThunk } from '@influencer/services/influencerThunks';


import DashboardLayout from "@shared/layout/DashboardLayout";
import PerformanceMetricsGrid from "@influencer/components/PerformanceMetricsGrid";
import PostCarousel from "@influencer/components/PostCarousel";
import YoutubeGrayIcon from '@assets/icon/youtube-gray.svg';
import YoutubeBlueIcon from '@assets/icon/youtube-blue.svg';
import InstagramBlueIcon from '@assets/icon/instagram-blue.svg';
import InstagramGrayIcon from '@assets/icon/instagram-gray.svg';
import SocialPerformanceSlider from "../components/SocialPerformanceSlider";
import OpportunitiesContainer from "../components/OpportunitiesContainer";
import SideProfile from "../components/SideProfile";
import TaskBoard from "../components/TaskBoard";
import EarningsCard from "../components/EarningsCard";
import ChannelTabs from "../../../shared/components/ChannelTabs";

// Task data for TaskBoard
const Taskdata = {
  sections: [
    {
      title: "New Assignment",
      tasks: [
        {
          brand: "Netflix",
          campaign: "Summer Vision",
          logoUrl: "https://upload.wikimedia.org/wikipedia/commons/thumb/c/c1/Google_%22G%22_logo.svg/800px-Google_%22G%22_logo.svg.png",
          messageType: "invitation"
        },
        {
          brand: "Amway",
          campaign: "Sales Campaign",
          logoUrl: "https://upload.wikimedia.org/wikipedia/commons/thumb/c/c1/Google_%22G%22_logo.svg/800px-Google_%22G%22_logo.svg.png",
          messageType: "accepted"
        }
      ],
      ctaText: "View Campaign Details",
      borderColorClass: "border-l-purple",
      titleColorClass: "bg-light-1"
    },
    {
      title: "Content Approval",
      tasks: [
        {
          brand: "Netflix",
          campaign: "Summer Campaign",
          logoUrl: "https://upload.wikimedia.org/wikipedia/commons/thumb/c/c1/Google_%22G%22_logo.svg/800px-Google_%22G%22_logo.svg.png",
          messageType: "approval"
        }
      ],
      ctaText: "Review Feedback",
      borderColorClass: "border-l-yellow",
      titleColorClass: "bg-light-3"
    },
    {
      title: "Pending Actions",
      tasks: [
        {
          brand: "Netflix",
          campaign: "Summer Campaign",
          logoUrl: "https://upload.wikimedia.org/wikipedia/commons/thumb/c/c1/Google_%22G%22_logo.svg/800px-Google_%22G%22_logo.svg.png",
          messageType: "draft_pending"
        },
        {
          brand: "Netflix",
          campaign: "Summer Campaign",
          logoUrl: "https://upload.wikimedia.org/wikipedia/commons/thumb/c/c1/Google_%22G%22_logo.svg/800px-Google_%22G%22_logo.svg.png",
          messageType: "schedule_pending"
        }
      ],
      ctaText: "Continue Draft",
      borderColorClass: "border-l-red-2",
      titleColorClass: "bg-light-2"
    }
  ]
};

const earningsData = {
  totalExpected: 134550,
  breakdown: [
    {
      label: "Average Earnings per Post",
      amount: 72000,
      iconKey: "post"
    },
    {
      label: "Average Earnings per Campaign",
      amount: 45000,
      iconKey: "campaign"
    },
    {
      label: "Performance Based Earning",
      amount: 5000,
      iconKey: "performance"
    }
  ],
  pieChart: {
    received: 100550,
    upcoming: 34000
  }
};


const data = {
  "profiles": [
    {
      "username": "@aditya_jain",
      "title": "Product Manager | Locobuzz | RCB",
      "channel": 1,
      "posts": 200,
      "followers": "9K",
      "avatarUrl": "https://randomuser.me/api/portraits/men/45.jpg"
    },
    {
      "username": "sneha_dedh",
      "title": "Product Manager | RCB",
      "channel": 2,
      "posts": 180,
      "followers": "7.5K",
      "avatarUrl": "https://randomuser.me/api/portraits/women/44.jpg"
    }
  ],
  "portfolioLink": "https://www.behance.net/snehajdedh46ff",
  "profileProgress": {
    "stepsDone": 4,
    "totalSteps": 6,
    "missingSteps": [
      {
        "title": "Add your payment method",
        "description": "Don't miss payouts—get paid instantly when you land deals",
        "isCompleted": false
      },
      {
        "id": "profile-link",
        "title": "Add your profile links",
        "description": "Don't miss out on brand deals—add your profile links to get discovered",
        "isCompleted": true,
        "value": "https://www.instagram.com/aditya_jain/"
      },
      {
        "id": "collaboration-rate",
        "title": "Add collaborations rate",
        "description": "Set your collaboration rates to get more brand deals",
        "isCompleted": false
      }
    ]
  },
  "achievements": [
    {
      "emoji": "🏆",
      "title": "9K Followers Milestone!"
    },
    {
      "emoji": "📈",
      "title": "500K Total Views Achieved!"
    },
    {
      "emoji": "🔥",
      "title": "High Engagement This Month!"
    }
  ],
  "protips": [
    {
      "title": "👑 Try reposting your <b> top 3 performing posts </b> from the last 90 days"
    },
    {
      "title": "♾️ Stories with <b> polls get 2x </b> more engagement"
    },
    {
      "title": "😴 Your followers are most active at <b> 8 PM </b>"
    },
    {
      "title": "🔥 <b>Post more Reels this week</b> -trending content!"
    }
  ],
  "brandCollaborations": [
    {
      "icon": "https://randomuser.me/api/portraits/women/44.jpg",
      "title": "JIO",
      "date": "2023-01-01"
    },
    {
      "icon": "https://randomuser.me/api/portraits/men/45.jpg",
      "title": "Tesla",
      "date": "2023-01-01"
    },
    {
      "icon": "https://randomuser.me/api/portraits/men/45.jpg",
      "title": "TATA",
      "date": "2023-01-01"
    }
  ],
  "categories": {
    "primary": ["AI & Future Tech", "Tech", "Apps & Software"],
    "secondary": ["Tutorials"]
  },
  "languages": ["English", "Hindi", "Gujarati", "French"]
};

const CreatorDashboard = () => {
  const dispatch = useDispatch();
  const { selectedProfile, socialProfiles } = useInfluencerSelectors();
  const { setIsLoading } = useLoading();
  const { showSnackbar } = useSnackbar();

  const channels = useSelector((state) => selectAllChannelsWithIcons(state));
  const [platformConfig, setPlatformConfig] = useState({});

  useEffect(() => {
    if (channels?.length) {
      const config = {};
      channels.forEach((channel) => {
        config[channel.name.toLowerCase()] = false;
      });
      setPlatformConfig(config);
    }
  }, [channels]);

  const [activeTab, setActiveTab] = useState(selectedProfile?.service ? selectedProfile?.service : "instagram");
  const navigate = useNavigate();

  useEffect(() => {
    const runEffect = async () => {
      setIsLoading(true);

      try {
        const searchParams = new URLSearchParams(location.search);
        if (searchParams.get('accessToken')) {
          const accessToken = searchParams.get('accessToken');
          const refreshToken = searchParams.get('refreshToken');

          localStorage.setItem('auth_token', accessToken);
          localStorage.setItem('refresh_token', refreshToken);
          await getUserData();

          // Remove query params from URL
          const newUrl = window.location.pathname;
          window.history.replaceState({}, document.title, newUrl);
        }
      } catch (error) {
        console.error('Get global fliter request error:', error);
        showSnackbar(error.message || 'Failed to request Get global fliter.', 'error');
      } finally {
        setIsLoading(false);
      }
    };
    runEffect();

  }, [dispatch]);

  useEffect(() => {
    if (!channels?.length) return;

    const updatedConfig = {};
    channels.forEach((channel) => {
      const name = channel.name.toLowerCase();
      const isActive = socialProfiles.some(profile => profile.service === name);
      updatedConfig[name] = isActive;
    });

    setPlatformConfig(updatedConfig);
    setActiveTab(selectedProfile?.service || "instagram");
  }, [socialProfiles, selectedProfile, channels]);

  const getUserData = async () => {

    setIsLoading(true);

    try {
      const result = await dispatch(getInfluencerInfoThunk());

      console.log('Creator User fetched result:', result);

      if (result.meta.requestStatus === 'fulfilled') {
        // Get the response data from the thunk
        const responseData = result.payload.data;
        console.log('Creator User fetched result:', responseData);

        if (responseData.social_profiles.length === 0) {
          showSnackbar("No social profiles found", 'info');
        }
      } else {
        // Handle failure case
        const errorMessage = result.payload.message || 'Brand access request failed. Please try again.';
        showSnackbar(errorMessage, 'error');
      }
    } catch (error) {
      console.error('Brand access request error:', error);
      showSnackbar(error.message || 'Failed to request brand access.', 'error');
    } finally {
      setIsLoading(false);
    }
  }
  // const navigate = useNavigate();

  return (
    <div className="max-w-[1670px] w-full flex flex-col gap-4 h-full ">
      {/* Header Section */}
      <div className="flex items-center">
        <h1 className="mb-4 text-2xl font-bold text-white">Hello, {selectedProfile?.display_name ? selectedProfile?.display_name : 'Creator'}</h1>
      </div>
      <div className="flex gap-4 h-full w-full">
        {/* Main Content */}
        <div className="flex-1 flex flex-col gap-4">
          {/* Social Performance Slider */}

          {/* <SocialPerformanceSlider initialTab={selectedProfile?.service} platformConfig={platformConfig} /> */}
          <div className="w-full max-w-full  bg-gray-600 rounded-lg shadow-lg p-5 relative">
            {/* Tabs */}
            <ChannelTabs activeTab={activeTab} setActiveTab={setActiveTab} />

            {
              (platformConfig[activeTab]) === true ? (
                <div className="absolute right-7 top-10">
                  <span
                    className="text-16-medium text-white cursor-pointer hover:text-brand-500 hover:underline"
                    onClick={() => navigate("/influencer/profile-analytics")}
                  >
                    View Analytics
                  </span>
                </div>
              ) : null
            }


            {/* Slider */}
            <div className="w-full h-auto overflow-hidden">
              <div className="flex transition-transform duration-300 ease-in-out">
                {/* Instagram View */}
                <div className="w-full">
                  <PerformanceMetricsGrid platform={activeTab} isConfigured={platformConfig[activeTab]} />
                  <PostCarousel platform={activeTab} isConfigured={platformConfig[activeTab]} />
                </div>


                {/* Instagram View */}
                {
                  /* <div className="w-full">
                          <PerformanceMetricsGrid platform="instagram" />
                          <PostCarousel platform="instagram" />
                      </div> */
                }

                {/* YouTube View */}
                {
                  /* <div className="w-full">
                          <PerformanceMetricsGrid platform="youtube" />
                          <PostCarousel platform="youtube" />
                      </div> */
                }
              </div>
            </div>
          </div>

          <OpportunitiesContainer />

          <TaskBoard data={Taskdata} />

          <EarningsCard earningsData={earningsData} />
          <div className="py-5 w-full"></div>

        </div>

        {/* Right Sidebar */}
        <div className="h-full w-20/100 flex flex-col gap-4">
          <SideProfile data={data} />
        </div>

      </div>
    </div>
  );
};

export default CreatorDashboard;
