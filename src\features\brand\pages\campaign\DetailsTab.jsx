import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { getCampaignByIdThunk, updateCampaignThunk } from '../../services/campaignThunks';
import { RequestStatus } from '../../../../app/store/enum';
import LoadingState from '../../components/LoadingState';
import ErrorState from '../../components/ErrorState';
import { useSnackbar } from '../../../../shared/components/UI/SnackbarContext';

const DetailsTab = () => {
    const { campaignId } = useParams();
    const dispatch = useDispatch();
    const { showSnackbar } = useSnackbar();

    const {
        selectedCampaign,
        detailsStatus,
        detailsError,
        status
    } = useSelector(state => state.campaign);

    const [isEditing, setIsEditing] = useState(false);
    const [formData, setFormData] = useState({
        name: '',
        description: '',
        objective: '',
        liveDate: '',
        closeDate: '',
        budget: '',
        isPrivate: false
    });

    useEffect(() => {
        if (selectedCampaign?.id != campaignId) {
            dispatch(getCampaignByIdThunk(campaignId));
        }
    }, [dispatch, campaignId, selectedCampaign?.id]);

    useEffect(() => {
        if (selectedCampaign) {
            setFormData({
                name: selectedCampaign.name || '',
                description: selectedCampaign.description || '',
                objective: selectedCampaign.objective || '',
                liveDate: selectedCampaign.liveDate || '',
                closeDate: selectedCampaign.closeDate || '',
                budget: selectedCampaign.budget || '',
                isPrivate: selectedCampaign.isPrivate || false
            });
        }
    }, [selectedCampaign]);

    const handleInputChange = (e) => {
        const { name, value, type, checked } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: type === 'checkbox' ? checked : value
        }));
    };

    const handleSave = () => {
        dispatch(updateCampaignThunk({ campaignId, campaignData: formData }))
            .unwrap()
            .then(() => {
                setIsEditing(false);
                showSnackbar('Campaign updated successfully', 'success');
            })
            .catch((error) => {
                showSnackbar(error.message || 'Failed to update campaign', 'error');
            });
    };

    const handleCancel = () => {
        if (selectedCampaign) {
            setFormData({
                name: selectedCampaign.name || '',
                description: selectedCampaign.description || '',
                objective: selectedCampaign.objective || '',
                liveDate: selectedCampaign.liveDate || '',
                closeDate: selectedCampaign.closeDate || '',
                budget: selectedCampaign.budget || '',
                isPrivate: selectedCampaign.isPrivate || false
            });
        }
        setIsEditing(false);
    };

    if (detailsStatus === RequestStatus.LOADING) {
        return <LoadingState message="Loading campaign details..." />;
    }

    if (detailsStatus === RequestStatus.FAILED) {
        return <ErrorState message={detailsError} />;
    }

    return (
        <div className="h-full w-full flex flex-col gap-5">
            {/* Header */}
            <div className="flex items-center justify-between">
                <div>
                    <h2 className="text-20-semibold text-gray-50">Campaign Details</h2>
                    <p className="text-14-regular text-gray-300">
                        Manage campaign settings and requirements
                    </p>
                </div>
                <div className="flex items-center gap-3">
                    {isEditing ? (
                        <>
                            <button
                                onClick={handleCancel}
                                className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 transition"
                            >
                                Cancel
                            </button>
                            <button
                                onClick={handleSave}
                                disabled={status === RequestStatus.LOADING}
                                className="px-4 py-2 bg-brand-500 text-white rounded hover:bg-brand-600 transition disabled:opacity-50"
                            >
                                {status === RequestStatus.LOADING ? 'Saving...' : 'Save Changes'}
                            </button>
                        </>
                    ) : (
                        <button
                            onClick={() => setIsEditing(true)}
                            className="px-4 py-2 bg-brand-500 text-white rounded hover:bg-brand-600 transition"
                        >
                            Edit Campaign
                        </button>
                    )}
                </div>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Basic Information */}
                <div className="bg-gray-600 rounded-lg p-6">
                    <h3 className="text-18-semibold text-white mb-4">Basic Information</h3>
                    <div className="space-y-4">
                        <div>
                            <label className="block text-14-medium text-gray-300 mb-2">Campaign Name</label>
                            {isEditing ? (
                                <input
                                    type="text"
                                    name="name"
                                    value={formData.name}
                                    onChange={handleInputChange}
                                    className="w-full p-3 bg-gray-700 border border-gray-600 rounded-md text-white"
                                />
                            ) : (
                                <div className="text-16-regular text-white">{selectedCampaign?.name}</div>
                            )}
                        </div>

                        <div>
                            <label className="block text-14-medium text-gray-300 mb-2">Description</label>
                            {isEditing ? (
                                <textarea
                                    name="description"
                                    value={formData.description}
                                    onChange={handleInputChange}
                                    rows={4}
                                    className="w-full p-3 bg-gray-700 border border-gray-600 rounded-md text-white"
                                />
                            ) : (
                                <div className="text-14-regular text-gray-100">
                                    {selectedCampaign?.description || 'No description provided'}
                                </div>
                            )}
                        </div>

                        <div>
                            <label className="block text-14-medium text-gray-300 mb-2">Campaign Objective</label>
                            {isEditing ? (
                                <select
                                    name="objective"
                                    value={formData.objective}
                                    onChange={handleInputChange}
                                    className="w-full p-3 bg-gray-700 border border-gray-600 rounded-md text-white"
                                >
                                    <option value="">Select Objective</option>
                                    <option value="Brand Awareness">Brand Awareness</option>
                                    <option value="Product Launch">Product Launch</option>
                                    <option value="Sales Promotion">Sales Promotion</option>
                                    <option value="Engagement">Engagement</option>
                                    <option value="Lead Generation">Lead Generation</option>
                                </select>
                            ) : (
                                <div className="text-14-regular text-gray-100">
                                    {selectedCampaign?.objective || 'Not specified'}
                                </div>
                            )}
                        </div>
                    </div>
                </div>

                {/* Timeline & Budget */}
                <div className="bg-gray-600 rounded-lg p-6">
                    <h3 className="text-18-semibold text-white mb-4">Timeline & Budget</h3>
                    <div className="space-y-4">
                        <div>
                            <label className="block text-14-medium text-gray-300 mb-2">Live Date</label>
                            {isEditing ? (
                                <input
                                    type="date"
                                    name="liveDate"
                                    value={formData.liveDate}
                                    onChange={handleInputChange}
                                    className="w-full p-3 bg-gray-700 border border-gray-600 rounded-md text-white"
                                />
                            ) : (
                                <div className="text-14-regular text-gray-100">
                                    {selectedCampaign?.liveDate || 'Not set'}
                                </div>
                            )}
                        </div>

                        <div>
                            <label className="block text-14-medium text-gray-300 mb-2">Close Date</label>
                            {isEditing ? (
                                <input
                                    type="date"
                                    name="closeDate"
                                    value={formData.closeDate}
                                    onChange={handleInputChange}
                                    className="w-full p-3 bg-gray-700 border border-gray-600 rounded-md text-white"
                                />
                            ) : (
                                <div className="text-14-regular text-gray-100">
                                    {selectedCampaign?.closeDate || 'Not set'}
                                </div>
                            )}
                        </div>

                        <div>
                            <label className="block text-14-medium text-gray-300 mb-2">Budget ($)</label>
                            {isEditing ? (
                                <input
                                    type="number"
                                    name="budget"
                                    value={formData.budget}
                                    onChange={handleInputChange}
                                    className="w-full p-3 bg-gray-700 border border-gray-600 rounded-md text-white"
                                />
                            ) : (
                                <div className="text-14-regular text-gray-100">
                                    ${selectedCampaign?.budget?.toLocaleString() || '0'}
                                </div>
                            )}
                        </div>

                        <div className="flex items-center gap-3">
                            {isEditing ? (
                                <label className="flex items-center gap-2 cursor-pointer">
                                    <input
                                        type="checkbox"
                                        name="isPrivate"
                                        checked={formData.isPrivate}
                                        onChange={handleInputChange}
                                        className="w-4 h-4"
                                    />
                                    <span className="text-14-medium text-gray-300">Private Campaign</span>
                                </label>
                            ) : (
                                <div className="flex items-center gap-2">
                                    <div className={`w-4 h-4 rounded ${selectedCampaign?.isPrivate ? 'bg-brand-500' : 'bg-gray-500'}`}></div>
                                    <span className="text-14-medium text-gray-300">
                                        {selectedCampaign?.isPrivate ? 'Private Campaign' : 'Public Campaign'}
                                    </span>
                                </div>
                            )}
                        </div>
                    </div>
                </div>

                {/* Campaign Status */}
                <div className="bg-gray-600 rounded-lg p-6">
                    <h3 className="text-18-semibold text-white mb-4">Campaign Status</h3>
                    <div className="space-y-4">
                        <div className="flex items-center justify-between">
                            <span className="text-14-medium text-gray-300">Current Status</span>
                            <span className={`px-3 py-1 rounded-full text-12-medium ${selectedCampaign?.status === 'Active' ? 'bg-green-500/20 text-green-400' :
                                    selectedCampaign?.status === 'Planned' ? 'bg-blue-500/20 text-blue-400' :
                                        selectedCampaign?.status === 'Completed' ? 'bg-gray-500/20 text-gray-400' :
                                            'bg-yellow-500/20 text-yellow-400'
                                }`}>
                                {selectedCampaign?.status}
                            </span>
                        </div>
                        <div className="flex items-center justify-between">
                            <span className="text-14-medium text-gray-300">Progress</span>
                            <span className="text-14-medium text-white">{selectedCampaign?.progress || 0}%</span>
                        </div>
                        <div className="w-full bg-gray-700 rounded-full h-2">
                            <div
                                className="bg-brand-500 h-2 rounded-full transition-all duration-300"
                                style={{ width: `${selectedCampaign?.progress || 0}%` }}
                            ></div>
                        </div>
                    </div>
                </div>

                {/* Creator Requirements */}
                <div className="bg-gray-600 rounded-lg p-6">
                    <h3 className="text-18-semibold text-white mb-4">Creator Requirements</h3>
                    <div className="space-y-3">
                        <div className="text-14-regular text-gray-300">
                            • Minimum 10K followers on Instagram
                        </div>
                        <div className="text-14-regular text-gray-300">
                            • Engagement rate above 3%
                        </div>
                        <div className="text-14-regular text-gray-300">
                            • Fashion/Lifestyle content focus
                        </div>
                        <div className="text-14-regular text-gray-300">
                            • Available for campaign duration
                        </div>
                        {isEditing && (
                            <button className="text-14-medium text-brand-400 hover:text-brand-300 transition">
                                + Edit Requirements
                            </button>
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
};

export default DetailsTab;
