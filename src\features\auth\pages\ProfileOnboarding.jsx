import React, { useEffect, useState } from 'react'

import { useDispatch } from 'react-redux';
import { Link, useNavigate } from "react-router-dom";
import { useSnackbar } from '@shared/components/UI/SnackbarContext';
import ProfileOnboardingFlow from '@auth/component/ProfileOnboardingFlow'
import { useLoading } from '@shared/components/UI/LoadingContext';

import { requestUserCreatorInfoThunk } from '../service/authThunks';


const ProfileOnboarding = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { showSnackbar } = useSnackbar();

  const [socialProfiles, setSocialProfiles] = useState([]);


  const { setIsLoading } = useLoading();

  useEffect(() => {
    const runEffect = async () => {
      try {
        console.log('Location', location);
        setIsLoading(true);

        // const searchParams = new URLSearchParams(location.search);

        // if (searchParams.get('accessToken')) {
        //   const accessToken = searchParams.get('accessToken');
        //   const refreshToken = searchParams.get('refreshToken');

        //   localStorage.setItem('auth_token', accessToken);
        //   localStorage.setItem('refresh_token', refreshToken);

        //   await getUserData(); // assuming it's async
        // } else {
        //   setTimeout(() => navigate('/brand/login'), 500);
        // }
        await getUserData(); // assuming it's async
      } catch (err) {
        console.error("Error in useEffect:", err);
      } finally {
        setIsLoading(false);
      }
    };

    runEffect();
  }, [navigate, location, setIsLoading]);

  const getUserData = async () => {

    setIsLoading(true);

    try {
      const result = await dispatch(requestUserCreatorInfoThunk());

      console.log('Creator User fetched result:', result);

      if (result.meta.requestStatus === 'fulfilled') {
        // Get the response data from the thunk
        const responseData = result.payload.data;
        console.log('Creator User fetched result:', responseData);

        const transformed = responseData.social_profiles.map((profile) => ({
          id: profile.id,
          name: profile.display_name,
          profile: profile.username,
          avatar: profile.avatar_url,
          followers: profile.follower_count,
          channel: profile.service,
        }));
        if (transformed.length > 0) {
          setSocialProfiles(transformed);
          console.log('Social Profiles:', transformed);
        } else {
          showSnackbar("No social profiles found", 'info');
        }
      } else {
        // Handle failure case
        const errorMessage = result.payload.message || 'Brand access request failed. Please try again.';
        showSnackbar(errorMessage, 'error');
      }
    } catch (error) {
      console.error('Brand access request error:', error);
      showSnackbar(error.message || 'Failed to request brand access.', 'error');
    } finally {
      setIsLoading(false);
    }
  }


  return (
    <div className='w-full h-screen flex items-center justify-center bg-primary'>
      {socialProfiles.length > 0 ? (
        <ProfileOnboardingFlow socialProfiles={socialProfiles} />
      ) : null}
    </div>
  )
}

export default ProfileOnboarding
