# Utilities

This document describes the utility functions available in the Creatorverse Frontend application, located in `src/lib/utils.js`.

## `cn(...inputs)`

A utility function that combines `clsx` and `tailwind-merge` to conditionally join CSS class names and merge Tailwind CSS classes without style conflicts.

- **Parameters**:
    - `...inputs`: Accepts multiple arguments, which can be strings, objects, or arrays of class names.

- **Returns**:
    - A single string containing merged and optimized CSS class names.

- **Usage Example**:

    ```javascript
    import { cn } from '@/lib/utils';

    function MyComponent({ isActive }) {
      return (
        <div className={cn(
          "text-red-500",
          isActive && "font-bold",
          "p-4",
          { "bg-blue-200": isActive }
        )}>
          Hello World
        </div>
      );
    }
    ```

