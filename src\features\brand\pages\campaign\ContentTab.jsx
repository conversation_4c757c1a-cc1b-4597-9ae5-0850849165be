import React, { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { getCampaignContentThunk, approveContentThunk, rejectContentThunk } from '../../services/campaignThunks';
import { RequestStatus } from '../../../../app/store/enum';
import LoadingState from '../../components/LoadingState';
import ErrorState from '../../components/ErrorState';
import PopupLayout from '../../../../shared/components/UI/PopupLayout';

import Filter from '@brand/components/FilterFixed';
import { Input } from "@shared/components/UI/input";
import SearchIcon from "@assets/icon/nav/search.svg";
import SettingSliderIcon from '@assets/icon/settings-sliders.svg'
import {
    TbLayoutGrid,        // All
    TbCheck,             // Approved
    TbFileUpload,        // Submitted
    TbX,                 // Rejected
    TbClock,             // Scheduled
    TbSend,              // Posted
    TbFileText           // Draft
} from "react-icons/tb"; // or from other icon sets if preferred
import ContentApprovalCard from "@brand/components/ContentApprovalCard";

import { selectAllChannelsWithIcons } from '@/app/store/slices/systemSlice';


const cardData = [
    {
        id: 1,
        userName: "Aarav Sharma",
        profileImage: "https://randomuser.me/api/portraits/men/75.jpg",
        status: "approved",
        caption: "Yoga: done. Smoothie: blended. Morning routine: revamped! 🧘‍♀️🍓 Discover my secrets to a balanced life!",
        type: "Post",
        approvedDate: "23 May 2025",
        submittedDate: "23 April 2025",
        rejectedDate: "",
        platform: "instagram",
        remarks: "All clear! Just a few tweaks needed.",
        actionBy: "John Doe"
    },
    {
        id: 2,
        userName: "Ananya Mehta",
        profileImage: "https://randomuser.me/api/portraits/women/65.jpg",
        status: "pending",
        caption: "A seasonal push to promote original content and new series. #Creatorverse",
        type: "Shorts",
        approvedDate: "",
        submittedDate: "24 April 2024",
        rejectedDate: "",
        platform: "youtube",
        remarks: "All clear! Just a few tweaks needed.",
        actionBy: "Raj Doe"
    },
    {
        id: 3,
        userName: "Karan Verma",
        profileImage: "https://randomuser.me/api/portraits/men/32.jpg",
        status: "approved",
        caption: "All good to go!",
        type: "Story",
        approvedDate: "25 April 2024",
        submittedDate: "25 April 2024",
        rejectedDate: "",
        platform: "instagram",
        remarks: "All clear! Just a few tweaks needed.",
        actionBy: "Rahul Doe"
    },
    {
        id: 4,
        userName: "Isha Kapoor",
        profileImage: "https://randomuser.me/api/portraits/women/44.jpg",
        status: "posted",
        caption: "Scheduled for weekend release.",
        type: "Video",
        approvedDate: "",
        submittedDate: "26 April 2024",
        rejectedDate: "",
        platform: "youtube",
        remarks: "All clear! Just a few tweaks needed.",
        actionBy: "John Smith",
        metrics: [
            {
                label: 'Engagement',
                value: '5%',
                icon: 'https://cdn-icons-png.flaticon.com/512/929/929564.png', // replace with your icon
            },
            {
                label: 'Reach',
                value: '500',
                icon: 'https://cdn-icons-png.flaticon.com/512/3135/3135715.png',
            },
            {
                label: 'Impressions',
                value: '1,000',
                icon: 'https://cdn-icons-png.flaticon.com/512/1828/1828859.png',
            },
            {
                label: 'Likes',
                value: '212',
                icon: 'https://cdn-icons-png.flaticon.com/512/833/833472.png',
            },
            {
                label: 'Comments',
                value: '409',
                icon: 'https://cdn-icons-png.flaticon.com/512/1380/1380338.png',
            },
            {
                label: 'Shares',
                value: '7,000',
                icon: 'https://cdn-icons-png.flaticon.com/512/1380/1380332.png',
            },
        ]
    },
    {
        id: 5,
        userName: "Rohan Singh",
        profileImage: "https://randomuser.me/api/portraits/men/23.jpg",
        status: "posted",
        caption: "Published successfully.",
        type: "Short",
        approvedDate: "22 April 2024",
        submittedDate: "22 April 2024",
        rejectedDate: "",
        platform: "youtube",
        actionBy: "Raja Doe",
        metrics: [
            {
                label: 'Engagement',
                value: '5%',
                icon: 'https://cdn-icons-png.flaticon.com/512/929/929564.png', // replace with your icon
            },
            {
                label: 'Reach',
                value: '500',
                icon: 'https://cdn-icons-png.flaticon.com/512/3135/3135715.png',
            },
            {
                label: 'Impressions',
                value: '1,000',
                icon: 'https://cdn-icons-png.flaticon.com/512/1828/1828859.png',
            },
            {
                label: 'Likes',
                value: '212',
                icon: 'https://cdn-icons-png.flaticon.com/512/833/833472.png',
            },
            {
                label: 'Comments',
                value: '409',
                icon: 'https://cdn-icons-png.flaticon.com/512/1380/1380338.png',
            },
            {
                label: 'Shares',
                value: '7,000',
                icon: 'https://cdn-icons-png.flaticon.com/512/1380/1380332.png',
            },
        ]

    },
    {
        id: 6,
        userName: "Priya Desai",
        profileImage: "https://randomuser.me/api/portraits/women/58.jpg",
        status: "approved",
        caption: "Great work! Ready to go live.",
        type: "Post",
        approvedDate: "21 April 2024",
        submittedDate: "21 April 2024",
        rejectedDate: "",
        platform: "instagram"
    },
    {
        id: 7,
        userName: "Neeraj Khanna",
        profileImage: "https://randomuser.me/api/portraits/men/47.jpg",
        status: "rejected",
        caption: "Please update the content tone and re-submit.",
        type: "Video",
        approvedDate: "",
        submittedDate: "20 April 2024",
        rejectedDate: "20 April 2024",
        platform: "youtube"

    },
    {
        id: 8,
        userName: "Sanya Arora",
        profileImage: "https://randomuser.me/api/portraits/women/36.jpg",
        status: "rejected",
        caption: "Showcasing curated global products for summer travel.",
        type: "Story",
        approvedDate: "",
        submittedDate: "19 April 2024",
        rejectedDate: "",
        platform: "instagram"
    }
];


const campaignStatus = ["All", "Pending for approval", "Approved", "Sent for rework", "Posted"];

const statusIcons = {
    approved: <TbCheck />,
    pending: <TbFileUpload />,
    // posted: <TbClock />,
    posted: <TbSend />,
    rejected: <TbX />,
    // Draft: <TbFileText />,
};


const ContentTab = () => {
    const { campaignId } = useParams();
    const dispatch = useDispatch();
    const [selectedContent, setSelectedContent] = useState(null);
    const [showApprovalModal, setShowApprovalModal] = useState(false);
    const [rejectionReason, setRejectionReason] = useState('');
    const [selectedStatus, setSelectedStatus] = useState("All");
    const [searchTerm, setSearchTerm] = useState("")
    const [savedFilters, setSavedFilters] = useState([])
    const [showFilter, setShowFilter] = useState(false);
    const channels = useSelector((state) => selectAllChannelsWithIcons(state));



    const {
        contentItems,
        pendingApprovals,
        contentStatus,
        contentError
    } = useSelector(state => state.campaign);

    useEffect(() => {
        if (campaignId) {
            dispatch(getCampaignContentThunk(campaignId));
        }
    }, [dispatch, campaignId]);

    const handleApproveContent = (contentId) => {
        dispatch(approveContentThunk({ campaignId, contentId }));
    };

    const handleRejectContent = (contentId) => {
        if (rejectionReason.trim()) {
            dispatch(rejectContentThunk({ campaignId, contentId, reason: rejectionReason }));
            setRejectionReason('');
            setSelectedContent(null);
            setShowApprovalModal(false);
        }
    };

    const handlePreviewContent = (content) => {
        console.log('Previewing content:', content);
        setSelectedContent(content);
        setShowApprovalModal(true);
    };

    const statusColors = {
        approved: "bg-green-100 text-green-700",
        pending: "bg-purple-100 text-purple-700",
        posted: "bg-blue-100 text-blue-700",
        rejected: "bg-red-100 text-red-700",
        // Draft: "bg-gray-100 text-gray-700",
    };
    

    function getTimeAgo(dateString) {
        const now = new Date();
        const date = new Date(dateString);
        const diff = Math.floor((now - date) / 1000); // in seconds
        if (isNaN(diff)) return "";
        if (diff < 60) return `${diff} min ago`;
        if (diff < 3600) return `${Math.floor(diff / 60)} min ago`;
        if (diff < 86400) return `${Math.floor(diff / 3600)} hr ago`;
        if (diff < 604800) return `${Math.floor(diff / 86400)} day${Math.floor(diff / 86400) > 1 ? 's' : ''} ago`;
        if (diff < 2592000) return `${Math.floor(diff / 604800)} week${Math.floor(diff / 604800) > 1 ? 's' : ''} ago`;
        if (diff < 31536000) return `${Math.floor(diff / 2592000)} month${Math.floor(diff / 2592000) > 1 ? 's' : ''} ago`;
        return `${Math.floor(diff / 31536000)} year${Math.floor(diff / 31536000) > 1 ? 's' : ''} ago`;
    }

    if (contentStatus === RequestStatus.LOADING) {
        return <LoadingState message="Loading campaign content..." />;
    }

    if (contentStatus === RequestStatus.FAILED) {
        return <ErrorState message={contentError} />;
    }

    const platformIcons = {
        instagram: 'fi fi-brands-instagram text-gray-200',
        youtube: 'fi fi-brands-youtube text-gray-200',
    };

    // Filter options array - keeping the existing array as is
    const filterOptions = [
        {
            optionName: "Status",
            optionFor: "creator",
            channel: "instagram",
            filters: [
                {
                    name: "Status",
                    type: "checkbox",
                    icon: "gender-icon",
                    minmax: false,
                    enterValue: false,
                    placeholder: "Select Status", //only needed for enterValue
                    options: [
                        { label: "Shortlisted", value: "shortlisted", description: "" },
                        { label: "In Progress", value: "in_progress", description: "" },
                        { label: "Negotiation", value: "negotiation", description: "" },
                        { label: "Outreached", value: "outreached", description: "" },
                        { label: "Onboarded", value: "onboarded", description: "" },
                        { label: "Rejected", value: "rejected", description: "" },
                    ]
                },
            ]
        },
        {
            optionName: "Channels",
            optionFor: "creator",
            channel: "instagram",
            filters: [
                {
                    name: "channel",
                    type: "checkbox",
                    minmax: false,
                    icon: "follower-icon",
                    enterValue: false,
                    placeholder: "Select Channel", //only needed for enterValue
                    options: [
                        { label: "Instagram", value: "instagram", description: "Instagram" },
                        { label: "YouTube", value: "youtube", description: "YouTube" },
                    ]
                }
            ]
        },
        {
            optionName: "Audience",
            optionFor: "creator",
            channel: "instagram",
            filters: [
                {
                    name: "audience",
                    type: "checkbox",
                    minmax: true,
                    icon: "age-icon",
                    enterValue: false,
                    placeholder: "Select Age", //only needed for enterValue
                    options: [
                        { label: "Nano", value: "1000-10000", description: "1k-10k" },
                        { label: "Micro", value: "10001-50000", description: "10k-50k" },
                        { label: "Mid", value: "50001-500000", description: "50k-500k" },
                        { label: "Macro", value: "500001-1000000", description: "500k-1M" },
                        { label: "Mega", value: "1000001+", description: "1M+" }
                    ]
                },
            ]
        },
        {
            optionName: "Engagement Rate",
            optionFor: "creator",
            channel: "instagram",
            filters: [
                {
                    name: "Engagement Rate",
                    type: "checkbox",
                    minmax: false,
                    icon: "engagement-icon",
                    enterValue: false,
                    placeholder: "Select Engagement Rate", //only needed for enterValue
                    options: [
                        { label: "Very High", value: "10%+", description: "10%+" },
                        { label: "High", value: "5%-10%", description: "5%-10%" },
                        { label: "Medium", value: "2%-5%", description: "2%-5%" },
                        { label: "Low", value: "<2%", description: "<2%" },
                    ]
                },
            ]
        },
        {
            optionName: "Campaigns",
            optionFor: "creator",
            channel: "instagram",
            filters: [
                {
                    name: "Campaigns",
                    type: "checkbox",
                    searchBox: true,
                    minmax: false,
                    icon: "engagement-icon",
                    enterValue: false,
                    placeholder: "Select Campaigns",
                    options: [
                        { label: "Summer Surge", value: "Summer Surge", description: "" },
                        { label: "Engagement Explosion", value: "Engagement Explosion", description: "" },
                        { label: "Precision Targeting", value: "Precision Targeting", description: "" },
                        { label: "Brand Spotlight", value: "Brand Spotlight", description: "" },
                    ]
                },
            ]
        },
    ];

    const getFilteredData = (data, searchTerm, selectedStatus) => {
        let filtered = [...data];

        // Filter by status
        if (selectedStatus && selectedStatus !== "All") {
            filtered = filtered.filter(item =>
                selectedStatus.toLowerCase().includes(item.status?.toLowerCase())
            );
        }

        // No search term, return filtered list
        if (!searchTerm || !searchTerm.trim()) return filtered;

        const normalizedSearchTerm = searchTerm.trim().toLowerCase();

        return filtered.filter(item => {
            return (
                item.userName?.toLowerCase().includes(normalizedSearchTerm) ||
                item.caption?.toLowerCase().includes(normalizedSearchTerm) ||
                item.status?.toLowerCase().includes(normalizedSearchTerm) ||
                item.type?.toLowerCase().includes(normalizedSearchTerm) ||
                item.platform?.toLowerCase().includes(normalizedSearchTerm) ||
                item.approvedDate?.toLowerCase().includes(normalizedSearchTerm) ||
                item.submittedDate?.toLowerCase().includes(normalizedSearchTerm) ||
                item.rejectedDate?.toLowerCase().includes(normalizedSearchTerm) ||
                item.remarks?.toLowerCase().includes(normalizedSearchTerm) ||
                item.actionBy?.toLowerCase().includes(normalizedSearchTerm) ||
                (Array.isArray(item.metrics) &&
                    item.metrics.some(metric =>
                        metric.label?.toLowerCase().includes(normalizedSearchTerm) ||
                        metric.value?.toLowerCase().includes(normalizedSearchTerm)
                    )
                )
            );
        });
    };


    return (
        <div className="h-full w-full flex flex-col gap-5">

            <div className="flex flex-col gap-7.5">
                <div className='flex items-center gap-4'>
                    {/* Search Box */}
                    <div className="flex items-center space-x-4 ml-0.5">
                        <Input
                            type="text"
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                            icon={SearchIcon}
                            placeholder="Search by name, channel....."
                            className="px-3 py-1 h-10 w-100 bg-transparent text-sm placeholder-gray-400 focus:outline-none"
                        />
                    </div>
                    {/* Filter Buttons */}
                    <button
                        className={`relative px-4 py-2.5 bg-transparent border border-gray-500 rounded-lg flex items-center gap-1.5 text-sm text-gray-50 hover:text-gray-300 transition-colors cursor-pointer`}
                        onClick={() => {
                            setShowFilter(!showFilter);
                            console.log('Filter button clicked');
                        }}
                        title="Toggle Filters"
                    >
                        <img src={SettingSliderIcon} alt="Filter" className="w-3.5 h-3.5" />
                        Filters
                        {savedFilters.length > 0 && (
                            <span className="ml-2 bg-brand-200 text-brand-600 text-xs rounded-full w-5 h-5 flex items-center justify-center">
                                {savedFilters.length}
                            </span>
                        )}

                        {showFilter ? <Filter
                            filterOptions={filterOptions}
                            hidetab={true}
                            savedFilters={savedFilters}
                            onClose={() => setShowFilter(false)}
                            onApplyFilters={(filters) => {
                                setSavedFilters(filters);
                                console.log(filters);
                                // Process your filters here
                            }}
                        /> : null}

                    </button>
                </div>
                <div className="flex items-center gap-3">
                    {campaignStatus.map((status, index) => (
                        <button
                            key={index}
                            className={`px-4 py-2.5 border border-gray-400 rounded-full flex items-center gap-1.5 text-14-medium hover:text-gray-100 transition-colors cursor-pointer ${status === selectedStatus ? "bg-gray-500 text-white" : ""
                                }`}
                            onClick={() => setSelectedStatus(status)}
                            title={status}
                        >
                            {statusIcons[status]}
                            {status}
                        </button>
                    ))}
                </div>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                    {getFilteredData(contentItems, searchTerm, selectedStatus).map(card => (
                        <ContentApprovalCard key={card.id} card={card} onPreview={() => handlePreviewContent(card)} />
                    ))}
                </div>
            </div>

            {/* Content Review Modal */}
            {showApprovalModal && selectedContent && (
                <PopupLayout
                    title="Review Content"
                    onClose={() => {
                        setShowApprovalModal(false);
                        setSelectedContent(null);
                        setRejectionReason('');
                    }}
                    isAcceptButton={false}
                    isCancelButton={false}
                    width="800px"
                >
                    <div className="flex flex-col gap-5">

                        <div className='flex justify-between'>
                            <div className={`flex capitalize w-fit px-3 py-1 rounded-lg text-14-medium gap-1 items-center ${statusColors[selectedContent.status]}`}>
                                <div>{statusIcons[selectedContent.status]}</div>
                                <div className='h-fit mt-0.5 flex items-center'>{selectedContent.status}</div>
                            </div>
                            <div className="text-gray-300 px-3 py-1 flex h-6 text-12-regular pr-10 items-end">
                                {selectedContent.submittedDate ? (
                                    selectedContent.approvedDate ? (
                                        <span>Approved On: {getTimeAgo(selectedContent.approvedDate)}</span>
                                    ) : selectedContent.rejectedDate ? (
                                        <span>Sent for rework: {getTimeAgo(selectedContent.rejectedDate)}</span>
                                    ) : (
                                        <span>Submitted: {getTimeAgo(selectedContent.submittedDate)}</span>
                                    )
                                ) : null}
                            </div>
                        </div>
                        <div className='flex gap-5'>
                            <div className='flex-1 bg-amber-100 h-[250px]'>
                                <img
                                    src={selectedContent.mediaUrl || selectedContent.profileImage}
                                    alt={selectedContent.mediaTitle}
                                    className="w-full h-full object-cover bg-black"
                                />
                            </div>
                            <div className="flex flex-col flex-1 gap-5">
                                {/* User Info */}
                                <div className="flex items-center gap-3">
                                    <img
                                        src={selectedContent.profileImage}
                                        className="w-8 h-8 rounded-full"
                                        alt={selectedContent.name}
                                    />
                                    <div className="flex flex-col">
                                        <div className="flex items-center gap-1">
                                            <span className="font-semibold text-white">
                                                {selectedContent.userName}
                                            </span>
                                        </div>
                                    </div>
                                </div>

                                {/* Caption with Hashtag Highlight */}
                                <div className="text-14-regular text-gray-50 flex flex-wrap gap-0.75">
                                    {selectedContent.caption.split(/(\s+)/).map((word, index) =>
                                        word.startsWith("#") ? (
                                            <span key={index} className="text-brand-500">{word}</span>
                                        ) : (
                                            <span key={index}>{word}</span>
                                        )
                                    )}
                                </div>
                            </div>
                        </div>
                        <div className='flex items-center gap-2 w-fit px-3 py-1 rounded-full border border-gray-500 bg-transparent text-gray-200 text-14-medium'>
                            {channels.find(channel => channel.name.toLowerCase() === selectedContent.platform)?.icon.url ? (
                                <img src={channels.find(channel => channel.name.toLowerCase() === selectedContent.platform)?.icon.url} className="h-4 w-4" alt={selectedContent.platform} />
                            ) : (
                                <i className={`${platformIcons[selectedContent.platform]} text-base mt-1`} />
                            )}
                            <span className='capitalize mt-[2px]'>{selectedContent.type}</span>
                        </div>

                        {/* PENDING APPROVAL */}
                        {selectedContent.status.toLowerCase() === 'submitted' && (
                            <div className="space-y-4">
                                <div>
                                    <label className="block text-14-medium text-gray-50 mb-2">
                                        Remarks*
                                    </label>
                                    <textarea
                                        value={rejectionReason}
                                        onChange={(e) => setRejectionReason(e.target.value)}
                                        placeholder="Add Remarks.."
                                        className="w-full p-3 border-1 border-gray-400 rounded-md text-white placeholder-gray-400"
                                        rows={2}
                                    />
                                </div>

                                <div className="flex justify-end items-center gap-3">
                                    <button
                                        onClick={() => handleRejectContent(selectedContent.id)}
                                        disabled={rejectionReason.length < 10}
                                        className="w-fit px-4 py-1.5 bg-transparent border border-gray-500 rounded-lg text-gray-100 hover:bg-gray-800 cursor-pointer flex gap-2 items-center"
                                    >
                                        <i class="fi fi-sr-cross-small text-lg mt-0.5"></i>
                                        Rework
                                    </button>
                                    <button
                                        onClick={() => {
                                            handleApproveContent(selectedContent.id);
                                            setShowApprovalModal(false);
                                            setSelectedContent(null);
                                        }}
                                        className={`w-fit h-[44px] px-[18px] py-[10px] rounded-[8px] transition duration-300 text-16-regular flex gap-2 items-center
                                         ${rejectionReason.length > 10
                                                ? "bg-brand-500 hover:bg-brand-600 text-white cursor-pointer"
                                                : "bg-gray-500 text-gray-300"
                                            }`}
                                        disabled={rejectionReason.length < 10}
                                    >
                                        <i class="fi fi-br-check"></i>
                                        Approve
                                    </button>
                                </div>
                            </div>
                        )}

                        {/* APPROVED */}
                        {selectedContent.status.toLowerCase() === 'approved' && (
                            <div className="space-y-4">
                                <div>
                                    <label className="block text-14-medium text-gray-50 mb-2">
                                        Remarks
                                    </label>
                                    <label className="block text-12-regular text-gray-50 mb-2">
                                        {selectedContent.remarks}
                                    </label>
                                </div>
                                <div className='border-b border-gray-500 -mt-2'></div>
                                <div className="flex items-center gap-3">
                                    <img
                                        src={selectedContent.profileImage}
                                        className="w-8 h-8 rounded-full"
                                        alt={selectedContent.name}
                                    />
                                    <div className="flex items-center gap-1">
                                        <span className='text-14-medium text-gray-200'>Approved by </span>
                                        <span className="text-14-semibold text-white">
                                            {selectedContent.actionBy}
                                        </span>
                                        <span className='text-14-medium text-gray-200'>on {selectedContent.approvedDate} </span>
                                    </div>
                                </div>
                            </div>
                        )}

                        {/* REJECTED */}
                        {selectedContent.status.toLowerCase() === 'reject' && (
                            <div className="space-y-4">
                                <div>
                                    <label className="block text-14-medium text-gray-50 mb-2">
                                        Remarks
                                    </label>
                                    <label className="block text-12-regular text-gray-50 mb-2">
                                        {selectedContent.remarks}
                                    </label>
                                </div>
                                <div className='border-b border-gray-500 -mt-2'></div>
                                <div className="flex items-center gap-3">
                                    <img
                                        src={selectedContent.profileImage}
                                        className="w-8 h-8 rounded-full"
                                        alt={selectedContent.name}
                                    />
                                    <div className="flex items-center gap-1">
                                        <span className='text-14-medium text-gray-200'>Sent for rework by </span>
                                        <span className="text-14-semibold text-white">
                                            {selectedContent.actionBy}
                                        </span>
                                        <span className='text-14-medium text-gray-200'>on {selectedContent.rejectedDate} </span>
                                    </div>
                                </div>
                            </div>
                        )}

                        {/* POSTED */}
                        {selectedContent.status.toLowerCase() === 'posted' && (
                            <div className="space-y-5">
                                <div className="rounded-lg p-4 grid grid-cols-3 gap-6 text-white w-full border border-gray-700">
                                    {selectedContent.metrics.map((item, index) => (
                                        <div key={index} className="flex items-center gap-3">
                                            <img src={item.icon} alt={item.label} className="w-5 h-5 text-gray-400" />
                                            <div className="flex flex-col text-xs leading-tight gap-0.5">
                                                <span className="text-gray-300">{item.label}</span>
                                                <span className="text-white text-12-semibold">{item.value}</span>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                                <div className="flex items-center gap-3">
                                    <img
                                        src={selectedContent.profileImage}
                                        className="w-8 h-8 rounded-full"
                                        alt={selectedContent.name}
                                    />
                                    <div className="flex items-center gap-1">
                                        <span className='text-14-medium text-gray-200'>Approved by </span>
                                        <span className="text-14-semibold text-white">
                                            {selectedContent.actionBy}
                                        </span>
                                        <span className='text-14-medium text-gray-200'>on {selectedContent.approvedDate} </span>
                                    </div>
                                </div>
                            </div>
                        )}

                        {/* SCHEDULED */}
                        {selectedContent.status.toLowerCase() === 'scheduled' && (
                            <div className="mt-4 text-yellow-400">
                                📅 Scheduled for {selectedContent.scheduledDate || 'TBD'}
                                <p className="text-gray-300 mt-1">Good. You can publish.</p>
                            </div>
                        )}

                        {/* DRAFT */}
                        {selectedContent.status.toLowerCase() === 'draft' && (
                            <div className="mt-4 text-gray-400 italic">
                                📝 This content is currently in draft and not submitted for review.
                            </div>
                        )}
                    </div>
                </PopupLayout>

            )}
        </div>
    );
};

export default ContentTab;
