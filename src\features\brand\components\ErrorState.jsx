import React from 'react';
import ErrorIcon from '@assets/icon/error.svg';

const ErrorState = ({ message, onRetry }) => {
  return (
    <div className="w-full flex flex-col items-center justify-center py-12">
      <div className="flex flex-col items-center gap-4 max-w-md text-center">
        <img src={ErrorIcon} alt="Error" className="w-12 h-12 text-red-500" />
        <h3 className="text-lg font-semibold text-white">Something went wrong</h3>
        <p className="text-gray-400">{message || "We encountered an error while loading creator data. Please try again."}</p>
        {onRetry && (
          <button 
            onClick={onRetry}
            className="mt-2 px-4 py-2 bg-brand-500 hover:bg-brand-600 text-gray-100 rounded-md transition-colors cursor-pointer"
          >
            Try Again
          </button>
        )}
      </div>
    </div>
  );
};

export default ErrorState;
