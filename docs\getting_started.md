# Getting Started

This document provides instructions on how to set up and run the Creatorverse Frontend project locally.

## Prerequisites

Before you begin, ensure you have the following installed:

- [Node.js](https://nodejs.org/en/download/) (LTS version recommended)
- [pnpm](https://pnpm.io/installation) (Package manager)

## Installation

1.  **Clone the repository:**

    ```bash
    git clone <repository-url>
    cd creatorverse_frontend
    ```

2.  **Install dependencies:**

    ```bash
    pnpm install
    ```

## Running the Development Server

To start the development server, run the following command:

```bash
pnpm dev
```

This will start the Vite development server, and you can access the application in your browser at `http://localhost:5173` (or another port if 5173 is in use).

## Building for Production

To build the application for production, run:

```bash
pnpm build
```

This will generate optimized and minified assets in the `dist/` directory, ready for deployment.

## Linting

To run ESLint to check for code style and quality issues:

```bash
pnpm lint
```

## Preview Production Build

To preview the production build locally:

```bash
pnpm preview
```
