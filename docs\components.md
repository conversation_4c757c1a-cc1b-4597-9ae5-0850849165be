# Components

This document provides an overview of the reusable React components used in the Creatorverse Frontend application.

## Shared Components (`src/shared/components/`)

These components are generic and can be used across different features of the application.

- **`AvatarStack.jsx`**: Displays a stack of avatars, typically for showing multiple users or participants.
- **`CampaignPopup.jsx`**: A popup component likely used for displaying campaign-related information or actions.
- **`DialogBox.jsx`**: A generic dialog or modal component for displaying messages or forms.
- **`FilterComponent.jsx`**: A component for applying filters to data or lists.
- **`Navbar.jsx`**: The main navigation bar of the application.
- **`NavElement.jsx`**: Individual navigation items used within the `Navbar` or other navigation structures.
- **`PlatformToggle.jsx`**: A toggle switch component, possibly for switching between different platforms or views.
- **`ProtectedRoute.jsx`**: A route component that restricts access to authenticated users.
- **`PublicRoute.jsx`**: A route component that allows access to unauthenticated users.
- **`examples/`**: Directory containing example usage of shared components.
- **`UI/`**: Directory containing basic UI elements or atomic components.

## Authentication Components (`src/features/auth/component/`)

These components are specific to the authentication and user onboarding flows.

- **`GradientSignupCard.jsx`**: A signup card component with a gradient background.
- **`NotificationStrip.jsx`**: A component for displaying notification messages, often at the top of the screen.
- **`OtpVerificationDialog.jsx`**: A dialog for users to enter and verify One-Time Passwords.
- **`ProfileOnboardingFlow.jsx`**: Manages the multi-step process of user profile creation or completion after signup.
- **`signUpCard.jsx`**: A generic signup form card component.
- **`signUpCard2.jsx`**: Another variation of a signup form card component.

