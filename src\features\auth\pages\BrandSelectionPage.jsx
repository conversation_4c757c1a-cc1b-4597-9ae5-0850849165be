import React, { useState, useEffect, useRef } from 'react';
import { useDispatch } from 'react-redux';
import { requestBrandAccessThunk, requestUserBrandInfoThunk } from '../service/authThunks';
import {  createBrandThunk } from '@brand/services/brandThunks';

import useAuthSelectors from '../service/authSelectors';
import { useNavigate, useParams, useLocation } from "react-router-dom";
import BrandOnboardingLayout from "@shared/layout/BrandOnboardingLayout";
import { Toast } from '@shared/components/UI/Toast';
import { useLoading } from '@shared/components/UI/LoadingContext';
import ArrowLeft from "@assets/icon/arrow-left.svg";
import EditIcon from "@assets/icon/edit.svg";
import SearchIcon from "@assets/icon/nav/search.svg";
import { Input } from '@shared/components/UI/input';
import { Link } from "react-router-dom";
import SocialButton from "@shared/components/UI/button";
import { IoChevronBack } from "react-icons/io5";




// eslint-disable-next-line no-unused-vars
import { motion, AnimatePresence } from "framer-motion";
import LaunchScreen from './LaunchScreen';

const BrandSelectionPage = ({ brands: initialBrands = [] }) => {
    const navigate = useNavigate();
    const dispatch = useDispatch();
    const location = useLocation();


    const [brands, setBrands] = useState(initialBrands); // ✅ useState added
    const { organisationName: initialBrandName } = useParams(); // Get brand name from URL params  

    const { organizationBrands, user } = useAuthSelectors();

    useEffect(() => {
        const runEffect = async () => {
            try {
                console.log('Location', location);
                setIsLoading(true);

                const searchParams = new URLSearchParams(location.search);

                if (location.pathname.endsWith('/brands') && organizationBrands !== undefined) {
                    const transformed = (organizationBrands || []).map((brand) => ({
                        brandId: brand.id,
                        brandName: brand.name,
                        isRequested:
                            brand.user_relationship?.status === 'none'
                                ? false
                                : brand.user_relationship?.status === 'pending'
                                    ? true
                                    : false,
                    }));

                    if (transformed.length > 0) {
                        setBrands(transformed);
                    } else {
                        setTimeout(() => navigate('/brand/login'), 500);
                    }
                } else if (searchParams.get('isAllocated')) {
                    const accessToken = searchParams.get('accessToken');
                    const refreshToken = searchParams.get('refreshToken');

                    localStorage.setItem('auth_token', accessToken);
                    localStorage.setItem('refresh_token', refreshToken);
                    
                    var isAllocated = searchParams.get('isAllocated');
                    if(isAllocated.includes('true')){
                        setTimeout(() => navigate('/brand/dashboard'), 0);
                    }
                }
                else if (searchParams.get('accessToken')) {
                    const accessToken = searchParams.get('accessToken');
                    const refreshToken = searchParams.get('refreshToken');

                    localStorage.setItem('auth_token', accessToken);
                    localStorage.setItem('refresh_token', refreshToken);

                    await getBrandUserData(); 
                } else if (initialBrandName) {
                    console.log('initialBrandName:', initialBrandName);
                } else {
                    setTimeout(() => navigate('/brand/login'), 0);
                }
            } catch (err) {
                console.error("Error in useEffect:", err);
            } finally {
                setIsLoading(false);
            }
        };

        runEffect();
    }, [initialBrandName, organizationBrands, navigate, location.pathname, location.search]);


    // useEffect(() => {
    //     const handleBeforeUnload = (e) => {
    //         e.preventDefault();
    //         e.returnValue = 'Wait! You have unsaved changes!';
    //     };

    //     window.addEventListener('beforeunload', handleBeforeUnload);

    //     return () => {
    //         window.removeEventListener('beforeunload', handleBeforeUnload);
    //     };
    // }, []);

    const getBrandUserData = async () => {

        setIsLoading(true);

        try {
            const result = await dispatch(requestUserBrandInfoThunk());

            console.log('Brand User fetched result:', result);

            if (result.meta.requestStatus === 'fulfilled') {
                // Get the response data from the thunk
                const responseData = result.payload.data;
                if (responseData.allocated_brands.length > 0) {
                    localStorage.setItem('allocatedBrands', JSON.stringify(responseData.allocated_brands));
                    localStorage.setItem('organizationBrands', JSON.stringify(responseData.organization_brands));

                    setTimeout(() => {
                        navigate('/brand/dashboard');
                    }, 500);
                }
                else if (responseData.organization_brands.length === 0) {
                    setTimeout(() => {
                        navigate(`/brand/brand-selection/${responseData.organization_name}`);
                    }, 0);
                }
                else {
                    const transformed = (responseData.organization_brands || []).map((brand) => ({
                        brandId: brand.id,
                        brandName: brand.name,
                        isRequested: brand.user_relationship?.status === 'none' ? false : brand.user_relationship?.status === 'pending' ? true : false, // or derive from backend state if available
                    }));
                    if (transformed.length > 0) {
                        localStorage.setItem('organizationBrands', JSON.stringify(responseData.organization_brands));
                        setBrands(transformed);
                        return;
                    } else {

                        setTimeout(() => {
                            navigate('/brand/login');
                        }, 500);
                    }
                }

                setShowAllBrandsModal(false);
            } else {
                // Handle failure case
                const errorMessage = result.payload.message || 'Brand access request failed. Please try again.';
                Toast.error(errorMessage);
            }
        } catch (error) {
            console.error('Brand access request error:', error);
            Toast.error(error.message || 'Failed to request brand access.');
        } finally {
            setIsLoading(false);
        }
    }


    const { isLoading, setIsLoading } = useLoading();
    const [showLaunchScreen, setShowLaunchScreen] = useState(false);
    const [showRequestSent, setShowRequestSent] = useState(false);
    const [selectedBrand, setSelectedBrand] = useState(null);
    const [isOpen, setIsOpen] = useState(false);
    const [brandName, setBrandName] = useState(initialBrandName);
    const [organizationName] = useState(initialBrandName);
    const [showAllBrandsModal, setShowAllBrandsModal] = useState(false);
    const [searchTerm, setSearchTerm] = useState('');
    const [addBrandError, setAddBrandError] = useState('');
    const inputRef = useRef(null);
    const searchInputRef = useRef(null);

    useEffect(() => {
        if (isOpen && inputRef.current) {
            inputRef.current.focus();
        }
        document.body.style.overflow = isOpen || showAllBrandsModal ? "hidden" : "";
    }, [isOpen, showAllBrandsModal]);

    useEffect(() => {
        if (showAllBrandsModal && searchInputRef.current) {
            searchInputRef.current.focus();
        }
    }, [showAllBrandsModal]);

    const handleBack = () => {
        if (showRequestSent) {
            setShowRequestSent(false);
            return;
        }
        navigate(-1)
    };

    const filteredBrands = brands
        .filter(brand => brand.brandName.toLowerCase().includes(searchTerm.toLowerCase()))
        .sort((a, b) => {
            if (a.isRequested && !b.isRequested) return -1;
            if (!a.isRequested && b.isRequested) return 1;
            return a.brandName.localeCompare(b.brandName);
        });

    const visibleBrands = filteredBrands.slice(0, 3);

    const handleRequestAccess = async (brand) => {
        if (brand.isRequested) return;

        setSelectedBrand(brand);
        setIsLoading(true);

        try {
            // Dispatch the verifyOtp thunk to update Redux state
            const result = await dispatch(requestBrandAccessThunk({
                brandId: brand.brandId,
            }));

            console.log('Brand access request result:', result);

            if (result.meta.requestStatus === 'fulfilled') {

                // Get the response data from the thunk
                const responseData = result.payload;
                console.log('Brand access request result:', responseData);

                const updatedBrands = brands.map(b =>
                    b.brandId === brand.brandId ? { ...b, isRequested: true } : b
                );

                setBrands(updatedBrands); // ✅ update state
                Toast.success(`Access requested for ${brand.brandName}`);
                setShowAllBrandsModal(false);
                setShowRequestSent(true);
            } else {
                // Handle failure case
                const errorMessage = result.payload.message || 'Brand access request failed. Please try again.';
                Toast.error(errorMessage);
            }
        } catch (error) {
            console.error('Brand access request error:', error);
            Toast.error(error.message || 'Failed to request brand access.');
        } finally {
            setIsLoading(false);
        }
    };

    const toggleForm = () => {
        console.log('Toggle form Ankit');
        setIsOpen(prev => !prev);
        if (!isOpen) { setBrandName(''); setAddBrandError(''); }
    };

    const handleBrandNameChange = (e) => { setBrandName(e.target.value); setAddBrandError(''); }

    const handleBrandCreation = async (e) => {
        e?.preventDefault(); // ✅ Prevent browser form submit

        if (!brandName.trim()) {
            Toast.error("Please enter a brand name");
            return;
        }

        setIsLoading(true);

        try {
            // Dispatch the verifyOtp thunk to update Redux state
            const result = await dispatch(createBrandThunk({
                name: brandName,
            }));

            console.log('Brand creation result:', result);

            if (result.meta.requestStatus === 'fulfilled') {

                // Get the response data from the thunk
                const responseData = result.payload;
                console.log('Brand creation result:', responseData.data);

                if (responseData.success) {
                    Toast.success(`Brand "${brandName}" added successfully!`);
                    setShowLaunchScreen(true);
                    toggleForm();
                } else {
                    setAddBrandError(responseData.message);
                    // Toast.error(responseData.message);
                }

            } else {
                // Handle failure case
                const errorMessage = result.payload.message || 'Brand access request failed. Please try again.';
                Toast.error(errorMessage);
            }
        } catch (error) {
            console.error('Brand access request error:', error);
            Toast.error(error.message || 'Failed to request brand access.');
        } finally {
            setIsLoading(false);
        }
    };

    const handleSearchChange = (e) => setSearchTerm(e.target.value);
    const toggleAllBrandsModal = () => {
        setShowAllBrandsModal(prev => !prev);
        setSearchTerm('');
    };

    // const handleSubmit = (e) => {
    //     e.preventDefault();
    //     if (!brandName.trim()) {
    //         Toast.error("Please enter a brand name");
    //         return;
    //     }

    //     setIsLoading(true);
    //     setTimeout(() => {
    //         Toast.success(`Brand "${brandName}" added successfully!`);
    //         setIsLoading(false);
    //         setBrandName('');
    //         setIsOpen(false);
    //         setShowLaunchScreen(true);
    //     }, 1000);
    // }

    return (
        showLaunchScreen ?
            <LaunchScreen />
            :
            <BrandOnboardingLayout>
                {(brands && brands.length === 0) ? (
                    <div className='flex flex-col items-center justify-center h-full w-md gap-5 max-w-md mx-auto z-10'>
                        {/* Back Button */}
                        <div className='flex justify-start w-full mb-4 cursor-pointer'>
                            <Link
                                to="/brand/signup"
                                className="flex justify-center items-center bg-gray-500 rounded-full w-10 h-10 text-gray-300 hover:text-white hover:bg-gray-300 transition-colors cursor-pointer"
                            >
                                <img src={ArrowLeft} alt="Back" className="w-5 h-5" />
                            </Link>
                        </div>
                        <div className='flex justify-start flex-col items-start w-full'>
                            <h1 className="text-30-medium text-white mb-2 capitalize">Hello {organizationName}!</h1>
                            <p className="text-gray-200 text-18-regular">Let's get you started</p>
                        </div>
                        <form onSubmit={handleBrandCreation} className="space-y-6 w-md">
                            <div className="relative gradient-border-container">
                                <Input
                                    type="text"
                                    ref={inputRef}
                                    value={brandName}
                                    onChange={handleBrandNameChange}
                                    placeholder="Enter your brand name"
                                    className="w-full px-5 py-3 h-15 bg-[#717171]/22 border border-gray-500 text-white text-20-bold rounded-md focus:border-transparent focus:outline-none focus:ring-0"
                                    autoFocus
                                    style={{
                                        position: "relative",
                                        zIndex: "2"
                                    }}
                                />
                                <div className="gradient-border"></div>
                                <button
                                    type="button"
                                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white z-10"
                                    onClick={() => {
                                        inputRef.current?.focus();
                                        inputRef.current?.select();
                                    }}
                                >
                                    <img src={EditIcon} alt="Edit" />
                                </button>
                            </div>

                            <SocialButton
                                type="submit"
                                label={isLoading ? "Let's Go..." : "Let's Go"}
                                variant="filled"
                                className="w-full text-16-semibold bg-cyan-500 hover:bg-cyan-600 text-center font-medium py-3 rounded-md"
                                disabled={isLoading || !brandName.trim()}
                            />

                        </form>
                    </div>
                )
                    :
                    (
                        <div className="relative z-10 p-6 max-w-md w-full mx-auto">
                            {/* Back Button */}
                            {showRequestSent ? (
                                <button
                                    onClick={handleBack}
                                    className="flex justify-center items-center bg-gray-500 rounded-full w-10 h-10 text-gray-300 hover:text-gray-200 hover:bg-gray-400 transition-colors cursor-pointer"
                                >
                                    <IoChevronBack />
                                </button>
                            ) : null}

                            {showRequestSent ? (
                                <div className="mt-10 flex flex-col items-start justify-center h-full w-md gap-5 max-w-md mx-auto z-10">
                                    <h1 className="text-30-medium text-white mb-2 capitalize">Your request has been sent!</h1>
                                    <p className="text-gray-200 text-18-regular">We have sent a request to the brand admin. You will receive an email on <span className='text-brand-500'>{user.email}</span> once your request is approved.</p>
                                </div>
                            ) : (
                                <>
                                    {/* Header */}
                                    < div className="text-left mb-8">
                                        <h1 className="text-30-medium text-white mb-2">Hello {organizationName}!</h1>
                                        <div className="text-gray-200 text-18-regular">Let's get you started</div>
                                    </div>

                                    {/* Brand List */}
                                    <div className="space-y-4 mb-5">
                                        {visibleBrands.map(brand => (
                                            <div
                                                key={brand.brandId}
                                                className="flex justify-between items-center p-4 rounded-lg bg-[#717171]/28 border-1 border-[#5E5E5E] hover:bg-[#717171]/38 cursor-pointer transition-transform duration-200 hover:scale-[1.02] transform-gpu"
                                            >
                                                <span className="text-white font-medium">{brand.brandName}</span>
                                                <button
                                                    onClick={() => handleRequestAccess(brand)}
                                                    className={`${brand.isRequested
                                                        ? "text-green-2"
                                                        : "text-cyan-400 hover:text-cyan-300"
                                                        } text-14-medium`}
                                                    disabled={brand.isRequested || (isLoading && selectedBrand?.brandId === brand.brandId)}
                                                    style={{ cursor: brand.isRequested ? 'not-allowed' : 'pointer' }}
                                                >
                                                    {isLoading && selectedBrand?.brandId === brand.brandId
                                                        ? 'Requesting...'
                                                        : brand.isRequested
                                                            ? 'Requested'
                                                            : 'Request Access'}
                                                </button>
                                            </div>
                                        ))}
                                    </div>

                                    {/* Show all brands toggle */}
                                    {brands.length > 3 && (
                                        <div className="flex justify-end">
                                            <button
                                                onClick={toggleAllBrandsModal}
                                                className="text-brand-500 hover:text-cyan-300 py-2 text-16-semibold border-b border-brand-600 mb-6 cursor-pointer"
                                            >
                                                {`Show all ${brands.length} brands`}
                                            </button>
                                        </div>
                                    )}


                                    {/* Add Brand Button */}
                                    <button
                                        onClick={toggleForm}
                                        className="flex justify-between cursor-pointer items-center w-full px-4 py-2.5 rounded-lg bg-[#717171]/8 border border-[#333333] text-white hover:bg-[#717171]/23 transition-transform duration-200 hover:scale-[1.02] transform-gpu"
                                    >
                                        <span className="font-medium">Add Brand</span>
                                        <motion.span
                                            initial={false}
                                            animate={{ rotate: isOpen ? 45 : 0 }}
                                            transition={{ duration: 0.3 }}
                                            className="text-xl flex items-center"
                                        >
                                            +
                                        </motion.span>
                                    </button>

                                    {/* Add Brand Modal */}
                                    <AnimatePresence>
                                        {isOpen && (
                                            <>
                                                <motion.div
                                                    className="fixed inset-0 bg-black/60 backdrop-blur-sm z-40"
                                                    initial={{ opacity: 0 }}
                                                    animate={{ opacity: 1 }}
                                                    exit={{ opacity: 0 }}
                                                    onClick={toggleForm}
                                                />

                                                <motion.div
                                                    initial={{ opacity: 0, scale: 0.95 }}
                                                    animate={{ opacity: 1, scale: 1 }}
                                                    exit={{ opacity: 0, scale: 0.95 }}
                                                    transition={{ duration: 0.3 }}
                                                    className="fixed top-1/2 left-1/2 w-[90%] max-w-md transform -translate-x-1/2 -translate-y-1/2 bg-[#1e1e1e] rounded-2xl border border-gray-600 z-50 p-6 shadow-lg"
                                                >
                                                    <div className="flex justify-between items-center mb-1">
                                                        <h3 className="text-gray-200 text-16-semibold">Add Brand</h3>
                                                        <button onClick={toggleForm} className="text-white text-xl cursor-pointer">
                                                            <motion.span
                                                                initial={false}
                                                                animate={{ rotate: isOpen ? 45 : 0 }}
                                                                transition={{ duration: 0.3 }}
                                                                className="text-xl"
                                                            >
                                                                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                                                    <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                                                                </svg>
                                                            </motion.span>
                                                        </button>
                                                    </div>
                                                    <p className="text-14-regular text-gray-300 mb-9">Add a new brand to this organisation</p>
                                                    <p className='text-14-medium mb-1.5'>Name</p>
                                                    <div className='flex flex-col gap-0.5 mb-5'>
                                                        <input
                                                            ref={inputRef}
                                                            type="text"
                                                            value={brandName}
                                                            onChange={handleBrandNameChange}
                                                            placeholder="Enter brand name here"
                                                            className={`w-full px-4 py-2 rounded-md bg-[#2b2b2b] border-1 ${addBrandError !== '' ? 'border-red-2' : 'border-gray-600'} text-white placeholder-gray-500 `}
                                                        />
                                                        {addBrandError && (
                                                            <p className="text-red-2 text-sm">{addBrandError}</p>
                                                        )}
                                                    </div>
                                                    <button
                                                        onClick={handleBrandCreation}
                                                        disabled={!brandName.trim() || isLoading}
                                                        className={`w-full py-2 rounded-md ${!brandName.trim()
                                                            ? "bg-gray-500 text-gray-300 opacity-60 cursor-not-allowed"
                                                            : "bg-cyan-500 text-white hover:bg-cyan-600 cursor-pointer"
                                                            } font-semibold transition-colors`}
                                                    >
                                                        {isLoading ? 'Adding...' : 'Add Brand'}
                                                    </button>
                                                </motion.div>
                                            </>
                                        )}
                                    </AnimatePresence>

                                    {/* All Brands Modal */}
                                    <AnimatePresence>
                                        {showAllBrandsModal && (
                                            <>
                                                <motion.div
                                                    className="fixed inset-0 bg-black/60 backdrop-blur-sm z-40"
                                                    initial={{ opacity: 0 }}
                                                    animate={{ opacity: 1 }}
                                                    exit={{ opacity: 0 }}
                                                    onClick={toggleAllBrandsModal}
                                                />

                                                <motion.div
                                                    initial={{ opacity: 0, y: 50 }}
                                                    animate={{ opacity: 1, y: 0 }}
                                                    exit={{ opacity: 0, y: 50 }}
                                                    transition={{ duration: 0.3 }}
                                                    className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-full max-w-[480px] h-140 mx-auto bg-[#1e1e1e] rounded-2xl border border-gray-600 z-50 overflow-hidden flex flex-col"
                                                    style={{ maxHeight: '100vh' }}
                                                >
                                                    <div className="p-4 border-b border-[#333333] flex items-center gap-2">
                                                        <button
                                                            onClick={toggleAllBrandsModal}
                                                            className="flex justify-center items-center bg-gray-500 rounded-full w-10 h-10 text-gray-300 hover:text-gray-200 hover:bg-gray-400 transition-colors cursor-pointer"
                                                        >
                                                            <IoChevronBack />
                                                        </button>
                                                        <h3 className="text-white text-lg font-medium">Brands</h3>
                                                    </div>

                                                    {/* Search Bar */}
                                                    <div className="p-4">
                                                        <div className="relative">
                                                            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                                <img src={SearchIcon} alt="Search" className="w-5 h-5 text-gray-400" />
                                                            </div>
                                                            <input
                                                                ref={searchInputRef}
                                                                type="text"
                                                                value={searchTerm}
                                                                onChange={handleSearchChange}
                                                                placeholder="Search brand..."
                                                                className="w-full pl-10 pr-4 py-3 bg-[#292929] border border-[#444444] rounded-md text-white placeholder-gray-400 focus:outline-none focus:border-cyan-500"
                                                            />
                                                        </div>
                                                    </div>

                                                    {/* Brands List */}
                                                    <div className="flex-1 overflow-y-auto p-4 space-y-2">
                                                        {filteredBrands.length > 0 ? (
                                                            filteredBrands.map(brand => (
                                                                <div
                                                                    key={brand.brandId}
                                                                    className="flex justify-between items-center p-4 rounded-lg bg-[#252525] hover:bg-[#2a2a2a] transition-transform duration-200 hover:scale-[1.02] transform-gpu"
                                                                >
                                                                    <span className="text-white">{brand.brandName}</span>
                                                                    <button
                                                                        onClick={() => handleRequestAccess(brand)}
                                                                        className={`${brand.isRequested
                                                                            ? "text-green-2"
                                                                            : "text-cyan-400 hover:text-cyan-300"
                                                                            } text-14-medium`}
                                                                        disabled={brand.isRequested || (isLoading && selectedBrand?.brandId === brand.brandId)}
                                                                        style={{ cursor: brand.isRequested ? 'not-allowed' : 'pointer' }}
                                                                    >
                                                                        {isLoading && selectedBrand?.brandId === brand.brandId
                                                                            ? 'Requesting...'
                                                                            : brand.isRequested
                                                                                ? 'Requested'
                                                                                : 'Request Access'}
                                                                    </button>
                                                                </div>
                                                            ))
                                                        ) : (
                                                            <div className="text-center text-gray-400 py-8">
                                                                No brands found matching "{searchTerm}"
                                                            </div>
                                                        )}
                                                    </div>
                                                </motion.div>
                                            </>
                                        )}
                                    </AnimatePresence>

                                </>
                            )}
                        </div>
                    )
                }
            </BrandOnboardingLayout >
    );
};

export default BrandSelectionPage;
