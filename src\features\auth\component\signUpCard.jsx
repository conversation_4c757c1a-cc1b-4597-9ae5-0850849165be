import React, { useState } from 'react';
import { Link, useNavigate } from "react-router-dom";
import Section from '../../../assets/Section.svg';
import googleIcon from '../../../assets/icon/google-icon.svg';
import handwave from '../../../assets/emoji/Waving_Hand_Light_Skin_Tone.png';
import { Input } from '../../../shared/components/UI/input';
import mailIcon from "../../../assets/icon/mail-icon.svg";


const SignUpCard = () => {
    const [email, setEmail] = useState("");
    const [isLoading, setIsLoading] = useState(false);
    const [isPending, startTransition] = useState(false);
    const inputId = "email-input";
    const navigate = useNavigate();

    // Simple email validation
    const isEmailValid = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);

    const handleSubmit = async (e) => {
        e.preventDefault();

        startTransition(async () => {
            try {
                setIsLoading(true);
            } catch (err) {
                console.error("Sign in error:", err);
            }
        });
    };

    const handleContinueWithGoogle = () => {
        navigate("/socialsignup");
    };

    return (
        <div className="flex flex-col w-full max-w-md ">
            <div className='flex flex-col space-y-8'>
                <div className="text-center md:text-left">
                    <h1 className="text-heading-36 mb-1">
                        Hey there, <span className="creator-gradient-text">Creator!</span>
                        <div className="bg-[#D6F7EE] w-10 h-10 rounded-full p-1.5 ml-2 inline-flex items-center justify-center align-middle">
                            <img src={handwave} alt="Waving Hand" className="w-6 h-6" />
                        </div>
                    </h1>
                    <p className="text-gray-400 text-label-18">Land collabs. Get paid. Let's make waves.</p>
                </div>

                <form onSubmit={handleSubmit} className="space-y-6">
                    <div className="space-y-2">
                        <div className="relative">
                            <Input
                                id={inputId}
                                type="email"
                                icon={mailIcon}
                                value={email}
                                onChange={(e) => setEmail(e.target.value)}
                                placeholder="<EMAIL>"
                                className="w-full bg-[#1D1D1D] border-gray-700 focus:border-none focus:ring-blue-500"
                                required
                                disabled={isLoading || isPending}
                                aria-busy={isLoading || isPending}
                            />
                        </div>
                    </div>

                    <button
                        type="submit"
                        className={`w-full h-[44px] px-[18px] py-[10px] rounded-[8px] transition duration-300 text-16-regular
                                ${isEmailValid && !isLoading && !isPending
                                ? "bg-[rgba(71,200,236,1)] hover:bg-[rgba(71,200,236,0.9)] text-white"
                                : "bg-gray-500 text-white"
                            }`}
                        disabled={!isEmailValid || isLoading || isPending}
                    >
                        {(isLoading || isPending) ? "Signing in..." : "Create Account"}
                    </button>

                    <div className="relative flex items-center justify-center">
                        <hr className="w-3/5 border-gray-700" />
                        <div className="px-2 text-16-regular text-gray-200 bg-primary absolute">Or</div>
                    </div>

                    <button
                        type="button"
                        variant="outline"
                        className="text-label-16 w-full rounded-[8px] flex items-center justify-center gap-2 py-2 bg-white border border-gray-700 hover:bg-gray-200 text-gray-700 transition duration-300 cursor-pointer"
                        disabled={isLoading || isPending}
                        onClick={handleContinueWithGoogle}
                    >
                        <img src={googleIcon} alt="Google" className="w-5 h-5" />
                        Continue with Google
                    </button>
                </form>

                <div className="text-center text-18-regular text-gray-200">
                    <p>
                        Already part of the fam? <Link to="/login" className="text-[rgba(71,200,236,1)] hover:underline">Sign in</Link>
                    </p>
                </div>
            </div>

            <div className="mt-10">
                <Link
                    to="/signup"
                    className="business-gradient-border block py-6 px-10 md:p-5 rounded-xl text-left w-full shadow-glow"
                >
                    <div className="flex items-center justify-between group">
                        <div className="w-3/5 pl-2 transition-transform duration-500 ease-in-out group-hover:-translate-x-2">
                            <p className="text-white text-lg font-semibold leading-tight">
                                Sign up as a <span className="text-[#48C9EC]">Business!</span>
                            </p>
                            <p className="text-gray-400 text-10-regular mt-1">
                                Tap into influencer marketing—build real campaigns with real results
                            </p>
                        </div>
                        <div className="w-2/5 flex items-center ml-4 group">
                            <div className="w-12 h-12 rounded-full bg-blue-300 transition-all duration-500 ease-in-out ml-0 group-hover:ml-0"></div>
                            <div className="w-12 h-12 rounded-full bg-green-300 transition-all duration-500 ease-in-out -ml-2 group-hover:-ml-1"></div>
                            <div className="w-12 h-12 rounded-full bg-yellow-300 transition-all duration-500 ease-in-out -ml-2 group-hover:-ml-1"></div>
                        </div>
                    </div>
                </Link>
            </div>
        </div>
    );
};

export default SignUpCard;
