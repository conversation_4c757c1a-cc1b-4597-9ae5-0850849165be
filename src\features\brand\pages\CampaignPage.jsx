import React, { useLayoutEffect, useRef, useState, useEffect } from 'react';
import { useParams, useNavigate, useLocation, Outlet } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { getCampaignByIdThunk } from '../services/campaignThunks';
import { setActiveTab } from '../services/campaignSlice';
import { RequestStatus } from '../../../app/store/enum';
import { useSnackbar } from '../../../shared/components/UI/SnackbarContext';
// eslint-disable-next-line no-unused-vars
import { motion } from 'framer-motion';
import { IoChevronBack } from 'react-icons/io5';
import LoadingState from '../components/LoadingState';
import ErrorState from '../components/ErrorState';

const tabs = [
    { id: 'creators', label: 'Creators', path: 'creators' },
    { id: 'content', label: 'Content', path: 'content' },
    { id: 'conversation', label: 'Conversation', path: 'conversation' },
    { id: 'analytics', label: 'Analytics', path: 'analytics' },
    { id: 'brief', label: 'Brief', path: 'brief' }
];

const CampaignPage = () => {
    const { campaignId } = useParams();
    const navigate = useNavigate();
    const location = useLocation();
    const dispatch = useDispatch();
    const { showSnackbar } = useSnackbar();
    const tabContainerRef = useRef(null);
    const [tabPositions, setTabPositions] = useState({});


    const {
        selectedCampaign,
        detailsStatus,
        detailsError,
        activeTab
    } = useSelector(state => state.campaign);

    useEffect(() => {
        console.log('Campaign Selected Details:', selectedCampaign);
    }, [selectedCampaign]);


    useEffect(() => {
        async function fetchData() {
            if (campaignId) {
                var result = await dispatch(getCampaignByIdThunk(campaignId));
                if (result.meta.requestStatus === 'fulfilled') {
                    const responseData = result.payload.data;
                    console.log('Campaign Details:', responseData);
                } else {
                    showSnackbar(result.payload.message || 'Failed to load lists.', 'error');
                }
            }
        }
        if (selectedCampaign?.id != campaignId) {
            fetchData();
        }
    }, [dispatch, campaignId]);

    useEffect(() => {
        const currentPath = location.pathname.split('/').pop();
        if (tabs.map(tab => tab.id).includes(currentPath)) {
            dispatch(setActiveTab(currentPath));
        }
    }, [location.pathname, dispatch]);

    useLayoutEffect(() => {
        if (tabContainerRef.current) {
            const positions = {};
            Array.from(tabContainerRef.current.children).forEach((child) => {
                const tab = child.dataset.tab;
                if (tab) {
                    positions[tab] = {
                        left: child.offsetLeft,
                        width: child.offsetWidth,
                    };
                }
            });
            setTabPositions(positions);
        }
    }, [activeTab]);

    const handleBackClick = () => {
        navigate('/brand/campaigns');
    };

    function formatDateToReadable(dateString, dateMonthYear = true) {
        if (!dateString) return '';
        const date = new Date(dateString);
        if (isNaN(date)) return '';
        return dateMonthYear
            ? date.toLocaleDateString('en-GB', { day: '2-digit', month: 'long', year: 'numeric' })
            : date.toLocaleDateString('en-GB', { day: '2-digit', month: 'short' });
    }

    const campaign = selectedCampaign;

    if (detailsStatus === RequestStatus.LOADING) {
        return <LoadingState message="Loading campaign details..." />;
    }

    if (detailsStatus === RequestStatus.FAILED) {
        return <ErrorState message={detailsError} />;
    }

    return (
        <div className="h-full w-full bg-primary/70 flex flex-col gap-5 overflow-y-auto scrollbar-hidden">
            {/* Campaign Header */}
            <div className='flex justify-between items-start gap-5 w-full border-b-1 border-gray-900 pb-3'>
                <div className='flex items-center'>
                    <button onClick={handleBackClick} className="mr-2 text-lg hover:text-gray-300 transition cursor-pointer">
                        <IoChevronBack className='h-6 w-6' />
                    </button>
                    <h1 className="text-24-semibold text-gray-50 inline">{campaign?.name || 'Campaign Name'}</h1>
                </div>
                <div className='text-green-2 text-16-medium flex items-center gap-4.5'>
                    <div className='px-2.5 py-1.5 bg-gray-600 rounded-md text-14-regular'>{campaign?.status}</div>
                    <div className='px-2.5 py-1.5 bg-gray-600 rounded-md text-14-regular text-brand-500'>
                        Live Date: {formatDateToReadable(campaign?.liveDate)}
                    </div>
                    <div className='px-2.5 py-1.5 bg-gray-600 rounded-md text-14-regular text-brand-500'>
                        Created By: {campaign?.createdBy}
                    </div>
                </div>
            </div>

            {/* Tabs */}
            <div className="relative z-40 flex justify-start py-1 gap-2.5 -mt-2" ref={tabContainerRef}>
                {tabs.map((tab) => (
                    <button
                        key={tab.id}
                        data-tab={tab.id}
                        onClick={() => {
                            navigate(`/brand/campaigns/${campaignId}/${tab.path}`);
                            dispatch(setActiveTab(tab.id));
                        }}
                        className={`w-fit relative cursor-pointer px-5 py-2.5 text-sm font-medium transition-colors duration-300 ${activeTab === tab.id ? "text-brand-500" : "text-gray-200 hover:text-brand-500"
                            }`}
                    >
                        {tab.label}
                    </button>
                ))}

                {/* Animated Indicator */}
                {activeTab && tabPositions[activeTab] && (
                    <motion.div
                        className="absolute bottom-0 h-0.5 bg-[#38BDF8] rounded-full"
                        layout
                        transition={{ type: "spring", stiffness: 500, damping: 30 }}
                        style={{
                            left: tabPositions[activeTab].left,
                            width: tabPositions[activeTab].width,
                        }}
                    />
                )}
            </div>

            {/* Tab Content */}
            <div className="hidden flex-1 mt-0 ">
                <Outlet />
            </div>
        </div>
    );
};

export default CampaignPage;
