/**
 * Multi-Backend API Architecture Tests
 * 
 * Comprehensive test suite for the multi-backend architecture including:
 * - Service configuration
 * - Authentication flow
 * - Error handling
 * - Health monitoring
 * - Cross-service communication
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';

// Import the multi-backend API components
import {
  apiManager,
  apiServices,
  serviceUtils,
  authService,
  errorHandler,
  healthMonitor,
  envConfig,
  SERVICE_TYPES,
  ERROR_TYPES,
  HEALTH_STATUS
} from '../index';

// Mock fetch for testing
global.fetch = vi.fn();

describe('Multi-Backend API Architecture', () => {
  beforeEach(() => {
    // Reset all mocks
    vi.clearAllMocks();
    
    // Reset service states
    authService.clearAuth();
    healthMonitor.stopMonitoring();
    
    // Mock successful responses by default
    fetch.mockResolvedValue({
      ok: true,
      status: 200,
      json: () => Promise.resolve({
        success: true,
        data: { message: 'Success' }
      })
    });
  });

  afterEach(() => {
    // Clean up
    healthMonitor.stopMonitoring();
  });

  describe('API Manager Initialization', () => {
    it('should initialize the API system successfully', async () => {
      const result = await apiManager.initialize();
      
      expect(result.success).toBe(true);
      expect(result.services).toContain('auth');
      expect(result.services).toContain('discovery');
      expect(result.environment).toBeDefined();
    });

    it('should validate configuration during initialization', async () => {
      // Mock invalid configuration
      const originalGet = envConfig.get;
      envConfig.get = vi.fn().mockImplementation((path, defaultValue) => {
        if (path === 'services.auth.baseURL') return null;
        return originalGet.call(envConfig, path, defaultValue);
      });

      await expect(apiManager.initialize()).rejects.toThrow('Configuration validation failed');
      
      // Restore original method
      envConfig.get = originalGet;
    });

    it('should provide system status information', () => {
      const status = apiManager.getSystemStatus();
      
      expect(status).toHaveProperty('initialized');
      expect(status).toHaveProperty('environment');
      expect(status).toHaveProperty('health');
      expect(status).toHaveProperty('authentication');
    });
  });

  describe('Service Configuration', () => {
    it('should provide correct service configurations for different environments', () => {
      const authConfig = envConfig.getServiceConfig('auth');
      const discoveryConfig = envConfig.getServiceConfig('discovery');
      
      expect(authConfig).toHaveProperty('baseURL');
      expect(authConfig).toHaveProperty('timeout');
      expect(discoveryConfig).toHaveProperty('baseURL');
      expect(discoveryConfig).toHaveProperty('timeout');
    });

    it('should support environment-specific feature flags', () => {
      const analyticsEnabled = envConfig.isFeatureEnabled('enableAnalytics');
      const cachingEnabled = envConfig.isFeatureEnabled('enableCaching');
      
      expect(typeof analyticsEnabled).toBe('boolean');
      expect(typeof cachingEnabled).toBe('boolean');
    });

    it('should validate configuration correctly', () => {
      const validation = envConfig.validateConfig();
      
      expect(validation).toHaveProperty('isValid');
      expect(validation).toHaveProperty('errors');
      expect(validation).toHaveProperty('warnings');
    });
  });

  describe('Authentication Service', () => {
    it('should manage tokens correctly', () => {
      const accessToken = 'test-access-token';
      const refreshToken = 'test-refresh-token';
      
      authService.setTokens(accessToken, refreshToken);
      
      expect(authService.getAccessToken()).toBe(accessToken);
      expect(authService.getRefreshToken()).toBe(refreshToken);
    });

    it('should clear authentication data', () => {
      authService.setTokens('access', 'refresh');
      authService.setUserData({ id: 1, name: 'Test User' });
      
      authService.clearAuth();
      
      expect(authService.getAccessToken()).toBeNull();
      expect(authService.getRefreshToken()).toBeNull();
      expect(authService.getUserData()).toBeNull();
    });

    it('should handle token refresh', async () => {
      // Mock refresh token response
      fetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: () => Promise.resolve({
          data: {
            access_token: 'new-access-token',
            refresh_token: 'new-refresh-token',
            expires_in: 3600
          }
        })
      });

      authService.setTokens('old-token', 'refresh-token');
      
      const result = await authService.refreshToken();
      
      expect(result.accessToken).toBe('new-access-token');
      expect(authService.getAccessToken()).toBe('new-access-token');
    });

    it('should handle token refresh failure', async () => {
      // Mock failed refresh response
      fetch.mockResolvedValueOnce({
        ok: false,
        status: 401,
        json: () => Promise.resolve({
          message: 'Invalid refresh token'
        })
      });

      authService.setTokens('old-token', 'invalid-refresh-token');
      
      await expect(authService.refreshToken()).rejects.toThrow('Invalid refresh token');
    });
  });

  describe('Error Handling', () => {
    it('should classify errors correctly', () => {
      const authError = { status: 401, message: 'Unauthorized' };
      const networkError = { name: 'TypeError', message: 'fetch failed' };
      const serverError = { status: 500, message: 'Internal Server Error' };
      
      const enhancedAuthError = errorHandler.enhanceError(authError, { service: 'auth' });
      const enhancedNetworkError = errorHandler.enhanceError(networkError, { service: 'discovery' });
      const enhancedServerError = errorHandler.enhanceError(serverError, { service: 'discovery' });
      
      expect(enhancedAuthError.type).toBe(ERROR_TYPES.AUTHENTICATION_ERROR);
      expect(enhancedNetworkError.type).toBe(ERROR_TYPES.NETWORK_ERROR);
      expect(enhancedServerError.type).toBe(ERROR_TYPES.SERVICE_UNAVAILABLE);
    });

    it('should determine retryable errors correctly', () => {
      const retryableError = { type: ERROR_TYPES.NETWORK_ERROR, statusCode: 503 };
      const nonRetryableError = { type: ERROR_TYPES.VALIDATION_ERROR, statusCode: 400 };
      
      expect(errorHandler.isRetryableError(retryableError)).toBe(true);
      expect(errorHandler.isRetryableError(nonRetryableError)).toBe(false);
    });

    it('should handle circuit breaker functionality', () => {
      const serviceHealth = errorHandler.getServiceHealth();
      
      expect(serviceHealth).toHaveProperty(SERVICE_TYPES.AUTH);
      expect(serviceHealth).toHaveProperty(SERVICE_TYPES.DISCOVERY);
    });
  });

  describe('Health Monitoring', () => {
    it('should check service health', async () => {
      // Mock health check response
      fetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: () => Promise.resolve({
          success: true,
          data: {
            status: 'healthy',
            service: 'Auth Service'
          }
        })
      });

      const health = await healthMonitor.checkServiceHealth(SERVICE_TYPES.AUTH);
      
      expect(health.status).toBe(HEALTH_STATUS.HEALTHY);
    });

    it('should handle service health check failure', async () => {
      // Mock failed health check
      fetch.mockRejectedValueOnce(new Error('Network error'));

      const health = await healthMonitor.checkServiceHealth(SERVICE_TYPES.AUTH);
      
      expect(health.status).toBe(HEALTH_STATUS.UNHEALTHY);
      expect(health.error).toBe('Network error');
    });

    it('should provide overall system health', () => {
      const overallHealth = healthMonitor.getOverallHealth();
      
      expect(overallHealth).toHaveProperty('status');
      expect(overallHealth).toHaveProperty('services');
      expect(overallHealth).toHaveProperty('timestamp');
    });

    it('should collect service metrics', () => {
      const metrics = healthMonitor.getAllServicesMetrics();
      
      expect(metrics).toHaveProperty(SERVICE_TYPES.AUTH);
      expect(metrics).toHaveProperty(SERVICE_TYPES.DISCOVERY);
      
      const authMetrics = metrics[SERVICE_TYPES.AUTH];
      expect(authMetrics).toHaveProperty('avgResponseTime');
      expect(authMetrics).toHaveProperty('successRate');
      expect(authMetrics).toHaveProperty('uptime');
    });
  });

  describe('API Services', () => {
    it('should provide all required API services', () => {
      expect(apiServices).toHaveProperty('auth');
      expect(apiServices).toHaveProperty('brandManagement');
      expect(apiServices).toHaveProperty('discovery');
      expect(apiServices).toHaveProperty('analytics');
      expect(apiServices).toHaveProperty('savedFilters');
    });

    it('should check service availability', () => {
      const isAuthAvailable = apiManager.isServiceAvailable('auth');
      const isDiscoveryAvailable = apiManager.isServiceAvailable('discovery');
      
      expect(typeof isAuthAvailable).toBe('boolean');
      expect(typeof isDiscoveryAvailable).toBe('boolean');
    });

    it('should get available services list', () => {
      const availableServices = apiManager.getAvailableServices();
      
      expect(Array.isArray(availableServices)).toBe(true);
    });
  });

  describe('Cross-Service Integration', () => {
    it('should share authentication tokens between services', () => {
      const token = 'shared-auth-token';
      authService.setTokens(token);
      
      // Both auth and discovery services should have access to the token
      expect(authService.getAccessToken()).toBe(token);
      
      // Verify token is used in API calls (would be tested with actual API calls)
      expect(authService.getAuthStatus().hasValidToken).toBe(true);
    });

    it('should handle cross-service authentication errors', async () => {
      // Mock authentication error from discovery service
      const authError = new Error('Unauthorized');
      authError.status = 401;
      
      const context = {
        service: SERVICE_TYPES.DISCOVERY,
        url: '/discovery/search',
        method: 'POST'
      };
      
      // Mock successful token refresh
      fetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: () => Promise.resolve({
          data: {
            access_token: 'new-token',
            refresh_token: 'new-refresh-token'
          }
        })
      });
      
      authService.setTokens('old-token', 'refresh-token');
      
      // The error handler should attempt token refresh
      try {
        await errorHandler.handleError(authError, context);
      } catch (error) {
        // Expected to throw since we don't have a retry function
        expect(error).toBeDefined();
      }
    });
  });

  describe('Environment-Specific Behavior', () => {
    it('should behave differently in different environments', () => {
      const isDev = envConfig.isDevelopment();
      const isProd = envConfig.isProduction();
      const isStaging = envConfig.isStaging();
      
      // Only one should be true
      const trueCount = [isDev, isProd, isStaging].filter(Boolean).length;
      expect(trueCount).toBe(1);
    });

    it('should have appropriate security settings per environment', () => {
      const securityConfig = envConfig.getSecurityConfig();
      
      expect(securityConfig).toHaveProperty('enableHTTPS');
      expect(securityConfig).toHaveProperty('tokenRefreshBuffer');
      
      if (envConfig.isProduction()) {
        expect(securityConfig.enableHTTPS).toBe(true);
      }
    });
  });
});

// Integration tests for actual API calls (these would require test servers)
describe('API Integration Tests', () => {
  // These tests would be run against actual test servers
  it.skip('should perform end-to-end authentication flow', async () => {
    // Test actual login flow
    const credentials = { email: '<EMAIL>', password: 'password' };
    const result = await apiServices.auth.login(credentials);
    expect(result).toBeDefined();
  });

  it.skip('should perform end-to-end discovery search', async () => {
    // Test actual search flow
    const searchParams = {
      searchQuery: 'test',
      filters: [],
      limit: 10
    };
    const result = await apiServices.discovery.searchCreators(searchParams);
    expect(result).toBeDefined();
  });
});

// Performance tests
describe('Performance Tests', () => {
  it('should handle concurrent API calls efficiently', async () => {
    const startTime = Date.now();
    
    // Simulate multiple concurrent calls
    const promises = Array(10).fill().map(() => 
      healthMonitor.checkServiceHealth(SERVICE_TYPES.AUTH)
    );
    
    await Promise.all(promises);
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    // Should complete within reasonable time (adjust threshold as needed)
    expect(duration).toBeLessThan(5000); // 5 seconds
  });

  it('should handle memory efficiently with large datasets', () => {
    // Test memory usage with large metric collections
    for (let i = 0; i < 1000; i++) {
      healthMonitor.recordSuccess(SERVICE_TYPES.AUTH, Math.random() * 1000, {});
    }
    
    const metrics = healthMonitor.getServiceMetrics(SERVICE_TYPES.AUTH);
    expect(metrics.responseTime.length).toBeLessThanOrEqual(100); // Should cap at 100
  });
});
