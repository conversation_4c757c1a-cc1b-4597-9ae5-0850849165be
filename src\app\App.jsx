import React from 'react';
import { <PERSON>rowser<PERSON>outer } from 'react-router-dom';
import { I18nextProvider } from 'react-i18next';
import { Provider } from 'react-redux';

import i18n from "../i18n/config";
import Router from './router';
import store from './store';
import { StrictMode } from 'react';
import AppInitializer from './AppInitializer';
import { ToastNotificationContainer } from '../shared/components/UI/Toast';
import { LoadingProvider } from '../shared/components/UI/LoadingContext';
import { SnackbarProvider } from '../shared/components/UI/SnackbarContext';



const App = () => {
  return (
    <StrictMode>
      <I18nextProvider i18n={i18n}>
        <Provider store={store}>
          <LoadingProvider>
            <SnackbarProvider>
              <AppInitializer>
                <BrowserRouter>
                  <ToastNotificationContainer />
                  <Router />
                </BrowserRouter>
              </AppInitializer>
            </SnackbarProvider>
          </LoadingProvider>
        </Provider>
      </I18nextProvider>
    </StrictMode >
  );
};

export default App;
