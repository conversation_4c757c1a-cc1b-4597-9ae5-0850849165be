import React, { useState, useRef, useEffect, useCallback } from 'react';
import DownIcon from '@assets/icon/down.svg';
import MapPinIcon from '@assets/icon/location.svg';
import { useLoading } from '@shared/components/UI/LoadingContext';
import { useSnackbar } from '@shared/components/UI/SnackbarContext';

/**
 * Mock API functions for location data
 * These simulate real API calls with Promise-based responses and realistic delays
 */
const mockLocationApi = {
  /**
   * Fetch countries based on search query
   * @param {string} query - Search query for countries
   * @returns {Promise<Array>} Promise resolving to array of countries
   */
  searchCountries: async (query) => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 300));

    const mockCountries = [
      { name: "India", groups: ["Tier 1", "Tier 2", "Tier 3", "Tier 4"] },
      { name: "Thailand", groups: ["Province 1", "Province 2"] },
      { name: "USA", groups: ["Metropolitan", "Suburban", "Rural"] },
      { name: "Germany", groups: [] },
      { name: "Brazil", groups: ["Major Cities", "Secondary Cities"] },
      { name: "United Kingdom", groups: ["London", "Manchester", "Birmingham"] },
      { name: "Canada", groups: ["Major Cities", "Secondary Cities"] },
      { name: "Australia", groups: ["Capital Cities", "Regional Areas"] },
      { name: "France", groups: [] },
      { name: "Japan", groups: ["Tokyo Metro", "Osaka Metro", "Other Cities"] }
    ];

    if (!query) return mockCountries;

    return mockCountries.filter(country =>
      country.name.toLowerCase().includes(query.toLowerCase())
    );
  },

  /**
   * Fetch cities/states for a selected country
   * @param {string} countryName - Name of the selected country
   * @param {string} query - Search query for cities
   * @returns {Promise<Array>} Promise resolving to array of cities
   */
  searchCities: async (countryName, query = '') => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 400));

    const mockCitiesData = {
      "India": [
        { name: "Mumbai", group: "Tier 1" },
        { name: "Delhi", group: "Tier 1" },
        { name: "Bangalore", group: "Tier 1" },
        { name: "Chennai", group: "Tier 1" },
        { name: "Pune", group: "Tier 2" },
        { name: "Hyderabad", group: "Tier 2" },
        { name: "Ahmedabad", group: "Tier 2" },
        { name: "Kolkata", group: "Tier 2" },
        { name: "Jaipur", group: "Tier 3" },
        { name: "Lucknow", group: "Tier 3" },
        { name: "Kanpur", group: "Tier 4" },
        { name: "Nagpur", group: "Tier 4" }
      ],
      "Thailand": [
        { name: "Bangkok", group: "Province 1" },
        { name: "Chiang Mai", group: "Province 2" },
        { name: "Phuket", group: "Province 1" },
        { name: "Pattaya", group: "Province 2" }
      ],
      "USA": [
        { name: "New York", group: "Metropolitan" },
        { name: "Los Angeles", group: "Metropolitan" },
        { name: "Chicago", group: "Metropolitan" },
        { name: "Houston", group: "Suburban" },
        { name: "Phoenix", group: "Suburban" },
        { name: "Philadelphia", group: "Suburban" },
        { name: "San Antonio", group: "Rural" },
        { name: "San Diego", group: "Rural" }
      ],
      "Germany": [
        { name: "Berlin", group: "" },
        { name: "Munich", group: "" },
        { name: "Hamburg", group: "" },
        { name: "Cologne", group: "" }
      ],
      "Brazil": [
        { name: "São Paulo", group: "Major Cities" },
        { name: "Rio de Janeiro", group: "Major Cities" },
        { name: "Brasília", group: "Secondary Cities" },
        { name: "Salvador", group: "Secondary Cities" }
      ]
    };

    const cities = mockCitiesData[countryName] || [];

    if (!query) return cities;

    return cities.filter(city =>
      city.name.toLowerCase().includes(query.toLowerCase())
    );
  }
};

/**
 * Custom hook for debouncing values
 * @param {any} value - Value to debounce
 * @param {number} delay - Delay in milliseconds
 * @returns {any} Debounced value
 */
const useDebounce = (value, delay) => {
  const [debouncedValue, setDebouncedValue] = useState(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
};

/**
 * Dynamic LocationFilterDropdown Component
 *
 * A two-phase location filter that allows users to:
 * 1. Search and select a country with typeahead functionality
 * 2. Search and select cities/states within the selected country
 *
 * Features:
 * - Debounced search (300ms delay) for optimal API performance
 * - Loading states during API requests
 * - Error handling with user feedback
 * - Group display logic (conditional based on country data)
 * - Reusable and configurable design
 *
 * @param {Function} onSelect - Callback function when location is selected
 * @param {Object} selectedLocation - Currently selected location object
 * @param {Array} selectedLocations - Array of selected locations (for multi-select)
 * @param {boolean} multiSelect - Whether to allow multiple selections
 * @param {string} placeholder - Placeholder text for the dropdown button
 * @param {string} className - Additional CSS classes
 */
const LocationFilterDropdown = ({
  onSelect,
  selectedLocation = null,
  selectedLocations = [],
  multiSelect = false,
  placeholder = "Select Location",
  className = ""
}) => {
  // Component state
  const [isOpen, setIsOpen] = useState(false);
  const [phase, setPhase] = useState('country'); // 'country' or 'city'
  const [selectedCountry, setSelectedCountry] = useState(null);
  const [countryQuery, setCountryQuery] = useState('');
  const [cityQuery, setCityQuery] = useState('');
  const [countries, setCountries] = useState([]);
  const [cities, setCities] = useState([]);
  const [isLoadingCountries, setIsLoadingCountries] = useState(false);
  const [isLoadingCities, setIsLoadingCities] = useState(false);
  const [error, setError] = useState(null);

  // Refs and hooks
  const dropdownRef = useRef(null);
  const { showSnackbar } = useSnackbar();

  // Debounced search queries
  const debouncedCountryQuery = useDebounce(countryQuery, 300);
  const debouncedCityQuery = useDebounce(cityQuery, 300);

  // Fetch countries when country query changes
  useEffect(() => {
    const fetchCountries = async () => {
      if (phase !== 'country') return;

      setIsLoadingCountries(true);
      setError(null);

      try {
        const results = await mockLocationApi.searchCountries(debouncedCountryQuery);
        setCountries(results);
      } catch (err) {
        setError('Failed to fetch countries');
        showSnackbar('Failed to fetch countries', 'error');
      } finally {
        setIsLoadingCountries(false);
      }
    };

    fetchCountries();
  }, [debouncedCountryQuery, phase, showSnackbar]);

  // Fetch cities when city query or selected country changes
  useEffect(() => {
    const fetchCities = async () => {
      if (phase !== 'city' || !selectedCountry) return;

      setIsLoadingCities(true);
      setError(null);

      try {
        const results = await mockLocationApi.searchCities(selectedCountry.name, debouncedCityQuery);
        setCities(results);
      } catch (err) {
        setError('Failed to fetch cities');
        showSnackbar('Failed to fetch cities', 'error');
      } finally {
        setIsLoadingCities(false);
      }
    };

    fetchCities();
  }, [debouncedCityQuery, selectedCountry, phase, showSnackbar]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Initialize countries on first open
  useEffect(() => {
    if (isOpen && phase === 'country' && countries.length === 0) {
      setCountryQuery('');
    }
  }, [isOpen, phase, countries.length]);

  // Handle dropdown toggle
  const toggleDropdown = () => {
    setIsOpen(!isOpen);
    if (!isOpen) {
      // Reset to country phase when opening
      setPhase('country');
      setSelectedCountry(null);
      setCountryQuery('');
      setCityQuery('');
      setError(null);
    }
  };

  // Handle country selection
  const handleCountrySelect = (country) => {
    setSelectedCountry(country);
    setPhase('city');
    setCityQuery('');
    setError(null);
  };

  // Handle city selection
  const handleCitySelect = (city) => {
    const locationData = {
      country: selectedCountry.name,
      city: city.name,
      group: city.group,
      fullLocation: `${city.name}, ${selectedCountry.name}`
    };

    onSelect(locationData);
    setIsOpen(false);

    // Reset state for next use
    setPhase('country');
    setSelectedCountry(null);
    setCountryQuery('');
    setCityQuery('');
  };

  // Handle back to country selection
  const handleBackToCountry = () => {
    setPhase('country');
    setSelectedCountry(null);
    setCityQuery('');
    setError(null);
  };

  // Clear all selections
  const handleClear = () => {
    onSelect(null);
    setIsOpen(false);
    setPhase('country');
    setSelectedCountry(null);
    setCountryQuery('');
    setCityQuery('');
  };

  // Display selected count or selected location
  const getDisplayText = () => {
    if (selectedLocations.length > 0) {
      return `Location (${selectedLocations.length})`;
    }
    return placeholder;
  };

  // Render loading spinner
  const renderLoadingSpinner = () => (
    <div className="flex items-center justify-center py-4">
      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
    </div>
  );

  // Render error state
  const renderError = () => (
    <div className="text-red-400 text-sm text-center py-3">
      {error}
    </div>
  );

  // Render country selection phase
  const renderCountryPhase = () => (
    <>
      <div className="px-4 pb-2">
        <h3 className="text-white text-lg font-medium mb-2">Select Country</h3>

        {/* Country Search Input */}
        <div className="mb-4">
          <input
            type="text"
            value={countryQuery}
            onChange={(e) => setCountryQuery(e.target.value)}
            placeholder="Search countries..."
            className="w-full bg-gray-800 border border-gray-700 rounded px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-blue-500"
            autoFocus
          />
        </div>

        {/* Country Options */}
        <div className="space-y-1 max-h-60 overflow-y-auto">
          {isLoadingCountries ? (
            renderLoadingSpinner()
          ) : error ? (
            renderError()
          ) : countries.length > 0 ? (
            countries.map((country) => (
              <div
                key={country.name}
                className="flex items-center justify-between py-2 px-2 cursor-pointer hover:bg-gray-800 rounded"
                onClick={() => handleCountrySelect(country)}
              >
                <div className="flex flex-col">
                  <span className="text-white text-sm">{country.name}</span>
                  {country.groups.length > 0 && (
                    <span className="text-gray-400 text-xs">
                      Groups: {country.groups.join(', ')}
                    </span>
                  )}
                </div>
                <svg className="h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </div>
            ))
          ) : (
            <div className="text-gray-400 text-sm text-center py-3">
              {countryQuery ? 'No countries found' : 'Start typing to search countries'}
            </div>
          )}
        </div>
      </div>
    </>
  );

  // Render city selection phase
  const renderCityPhase = () => (
    <>
      <div className="px-4 pb-2">
        <div className="flex items-center mb-2">
          <button
            onClick={handleBackToCountry}
            className="mr-2 p-1 hover:bg-gray-800 rounded"
          >
            <svg className="h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </button>
          <h3 className="text-white text-lg font-medium">
            Cities in {selectedCountry?.name}
          </h3>
        </div>

        {/* City Search Input */}
        <div className="mb-4">
          <input
            type="text"
            value={cityQuery}
            onChange={(e) => setCityQuery(e.target.value)}
            placeholder="Search cities..."
            className="w-full bg-gray-800 border border-gray-700 rounded px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-blue-500"
            autoFocus
          />
        </div>

        {/* City Options */}
        <div className="space-y-1 max-h-60 overflow-y-auto">
          {isLoadingCities ? (
            renderLoadingSpinner()
          ) : error ? (
            renderError()
          ) : cities.length > 0 ? (
            cities.map((city) => (
              <div
                key={city.name}
                className="flex items-center justify-between py-2 px-2 cursor-pointer hover:bg-gray-800 rounded"
                onClick={() => handleCitySelect(city)}
              >
                <div className="flex flex-col">
                  <span className="text-white text-sm">{city.name}</span>
                  {city.group && (
                    <span className="text-gray-400 text-xs">
                      Group: {city.group}
                    </span>
                  )}
                </div>
              </div>
            ))
          ) : (
            <div className="text-gray-400 text-sm text-center py-3">
              {cityQuery ? 'No cities found' : 'Start typing to search cities'}
            </div>
          )}
        </div>
      </div>
    </>
  );

  // Main render
  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      <button
        className={`px-4 py-2.5 ${
          isOpen ? 'bg-gray-700' : 'bg-transparent'
        } border border-gray-500 rounded-lg flex items-center gap-1.5 text-sm text-gray-50 hover:text-gray-300 transition-colors cursor-pointer`}
        onClick={toggleDropdown}
      >
        <img src={MapPinIcon} alt="Location" className="w-3.5 h-3.5" />
        {getDisplayText()}
        <img
          src={DownIcon}
          alt="Dropdown"
          className={`w-3.5 h-3.5 ml-0.5 transition-transform duration-200 ${
            isOpen ? 'transform rotate-180' : ''
          }`}
        />
      </button>

      {isOpen && (
        <div className="absolute mt-1 left-0 w-80 bg-gray-900 border border-gray-800 rounded-md shadow-lg z-50 py-2">
          {phase === 'country' ? renderCountryPhase() : renderCityPhase()}

          {/* Footer */}
          <div className="border-t border-gray-800 p-3 flex justify-between">
            <button
              className="text-sm text-gray-400 hover:text-white transition-colors"
              onClick={handleClear}
            >
              Clear
            </button>
            <button
              className="text-sm text-blue-500 hover:text-blue-400 transition-colors"
              onClick={() => setIsOpen(false)}
            >
              Close
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default LocationFilterDropdown;
