import React, { useState, useRef, useEffect } from 'react';
import DownIcon from '@assets/icon/down.svg';
import MapPinIcon from '@assets/icon/location.svg';

const LocationFilterDropdown = ({ onSelect, selectedLocations = [], filterOptions = [] }) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [expandedTiers, setExpandedTiers] = useState({});
  
  // Toggle expanded state of a tier
  const toggleTierExpanded = (tierId) => {
    setExpandedTiers(prev => ({
      ...prev,
      [tierId]: !prev[tierId]
    }));
  };

  // Extract location filter data from filterOptions
  const getLocationFilter = () => {
    // Look for location filter in both creator and audience tabs for both instagram and youtube
    for (const option of filterOptions) {
      const locationFilter = option.filters?.find(
        filter => filter.name === "Location" && filter.type === "multilevel-checkbox"
      );
      if (locationFilter) {
        return locationFilter;
      }
    }
    return null;
  };

  // Get location options from the location filter
  const getLocationOptions = () => {
    const locationFilter = getLocationFilter();
    if (!locationFilter || !locationFilter.options) {
      return [];
    }
    
    // Flatten the multilevel structure for the dropdown
    const flatOptions = [];
    locationFilter.options.forEach(tier => {
      // Add the tier as a group header
      const tierId = tier.subOptionName.toLowerCase().replace(/\s+/g, '-');
      flatOptions.push({
        isHeader: true,
        label: tier.subOptionName,
        value: tierId,
      });
      
      // Add the cities in this tier
      if (tier.subOptions) {
        tier.subOptions.forEach(city => {
          flatOptions.push({
            isHeader: false,
            label: city.label,
            value: city.value,
            tier: tierId
          });
        });
      }
    });
    
    return flatOptions;
  };
  
  const allLocationOptions = getLocationOptions();
  
  // Filter options based on search query
  const filteredOptions = searchQuery ? 
    allLocationOptions.filter(option => 
      option.label.toLowerCase().includes(searchQuery.toLowerCase())
    ) : 
    allLocationOptions;

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const toggleDropdown = () => {
    setIsOpen(!isOpen);
  };

  const handleOptionSelect = (value) => {
    onSelect(value);
    // Don't close dropdown when selecting options to allow multiple selections
  };

  // Display selected count if any options are selected
  const selectedCount = selectedLocations.length;
  const displaySelectedCount = selectedCount > 0 ? '(' + selectedCount + ')' : '';
  
  // Check if a city should be displayed based on its tier's expanded state
  const shouldShowCity = (option) => {
    if (!option.tier) return true;
    if (searchQuery) return true; // Always show during search
    return expandedTiers[option.tier];
  };

  // Rendering the UI
  return (
    <div className="relative" ref={dropdownRef}>
      <button
        className={'px-4 py-2.5 ' + (isOpen ? 'bg-gray-700' : 'bg-transparent') + ' border border-gray-500 rounded-lg flex items-center gap-1.5 text-sm text-gray-50 hover:text-gray-300 transition-colors cursor-pointer'}
        onClick={toggleDropdown}
      >
        <img src={MapPinIcon} alt="Location" className="w-3.5 h-3.5" />
        Audience Location {displaySelectedCount}
        <img
          src={DownIcon}
          alt="Dropdown"
          className={'w-3.5 h-3.5 ml-0.5 transition-transform duration-200 ' + (isOpen ? 'transform rotate-180' : '')}
        />
      </button>
      
      {isOpen && (
        <div className="absolute mt-1 left-0 w-64 bg-gray-900 border border-gray-800 rounded-md shadow-lg z-50 py-2">
          <div className="px-4 pb-2">
            <h3 className="text-white text-lg font-medium mb-2">Location</h3>
            
            {/* Search Input */}
            <div className="mb-4">              
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="Enter Location"
                className="w-full bg-gray-800 border border-gray-700 rounded px-3 py-2 text-white placeholder-gray-50 focus:outline-none focus:ring-1 focus:ring-blue-500"
              />
            </div>

            {/* Location Options */}
            <div className="space-y-2 max-h-60 overflow-y-auto">
              {filteredOptions.length > 0 ? 
                filteredOptions.map((option) => {
                  if (option.isHeader) {
                    // Tier header
                    return (
                      <div 
                        key={"header-" + option.value}
                        className="flex items-center justify-between py-2 mt-2 first:mt-0 border-t border-gray-700 first:border-t-0 cursor-pointer"
                        onClick={() => toggleTierExpanded(option.value)}
                      >
                        <span className="text-gray-300 font-medium text-sm">{option.label}</span>
                        <svg 
                          className={"h-4 w-4 text-gray-400 transition-transform " + (expandedTiers[option.value] ? 'transform rotate-180' : '')}
                          fill="none" 
                          viewBox="0 0 24 24" 
                          stroke="currentColor"
                        >
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                        </svg>
                      </div>
                    );
                  } else {
                    // City option
                    if (shouldShowCity(option)) {
                      return (
                        <div
                          key={"option-" + option.value}
                          className="flex items-center justify-between py-1.5 cursor-pointer pl-3"
                          onClick={() => handleOptionSelect(option.value)}
                        >
                          <div className="flex items-center">
                            <div 
                              className={"w-5 h-5 rounded border " + (
                                selectedLocations.includes(option.value)
                                  ? 'bg-blue-500 border-blue-500' 
                                  : 'bg-gray-700 border-gray-600'
                              ) + " flex items-center justify-center mr-2"}
                            >
                              {selectedLocations.includes(option.value) && (
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5 text-white" viewBox="0 0 20 20" fill="currentColor">
                                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                </svg>
                              )}
                            </div>
                            <span className="text-white text-sm">{option.label}</span>
                          </div>
                        </div>
                      );
                    }
                    return null;
                  }
                })
              : 
                <div className="text-gray-400 text-sm text-center py-3">
                  No locations found
                </div>
              }
            </div>
          </div>

          <div className="border-t border-gray-800 p-3 flex justify-between">
            <button 
              className="text-sm text-gray-400 hover:text-white"
              onClick={() => onSelect([])}>
              Clear
            </button>
            <button 
              className="text-sm text-blue-500 hover:text-blue-400"
              onClick={() => setIsOpen(false)}>
              Apply
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default LocationFilterDropdown;
