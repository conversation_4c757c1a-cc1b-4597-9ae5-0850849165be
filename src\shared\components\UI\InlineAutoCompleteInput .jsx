import React, { useState, useEffect } from 'react';

const InlineAutoCompleteInput = ({ suggestions = [], value, setValue, onKeyDown, onBlur, inputRef }) => {
    const [match, setMatch] = useState('');

    useEffect(() => {
        if (!value) return setMatch('');
        const suggestion = suggestions.find(
            (s) =>
                s.toLowerCase().startsWith(value.toLowerCase()) &&
                s.toLowerCase() !== value.toLowerCase()
        );
        setMatch(suggestion || '');
    }, [value, suggestions]);

    const handleLocalKeyDown = (e) => {
        if ((e.key === 'Tab' || e.key === 'ArrowRight') && match) {
            e.preventDefault();
            setValue(match);
        } else {
            onKeyDown?.(e);
        }
    };

    return (
        <div className="relative w-30 font-mono">
            <div
                className="absolute top-0 left-0 w-30 h-full px-2 py-0 text-gray-400 pointer-events-none whitespace-pre text-14-regula"
                style={{ fontSize: '0.875rem', lineHeight: '1.25rem' }}
            >
                {match}
            </div>
            <input
                ref={inputRef}
                value={value}
                onClick={(e) => e.stopPropagation()} // ✅ Add this
                onChange={(e) => setValue(e.target.value)}
                onKeyDown={handleLocalKeyDown}
                onBlur={onBlur}
                placeholder="Enter label"
                className="relative px-2 bg-transparent border-none w-30 text-white z-10 outline-none text-14-regular"
                autoComplete="off"
            />
        </div>
    );
};

export default InlineAutoCompleteInput;
