import React, { useRef, useEffect, useState } from "react";
import PerformanceMetricsGrid from "./PerformanceMetricsGrid";
import PostCarousel from "./PostCarousel";
// eslint-disable-next-line no-unused-vars
import { motion } from "framer-motion";

const tabs = [
    "Performance Overview",
    "Audience Demographics",
    "Audience Interests & Behaviour",
    "Sponsored Content Insights",
    "Similar Creators",
];

const InstagramAnalytics = () => {
    const [activeTab, setActiveTab] = useState(tabs[0]);
    const [tabPositions, setTabPositions] = useState({});
    const tabContainerRef = useRef(null);

    const sectionRefs = useRef(
        tabs.reduce((acc, tab) => {
            acc[tab] = React.createRef();
            return acc;
        }, {})
    ).current;

    const handleTabClick = (tab) => {
        setActiveTab(tab);
        sectionRefs[tab].current?.scrollIntoView({ behavior: "smooth", block: "start" });
    };

    useEffect(() => {
        if (tabContainerRef.current) {
            const positions = {};
            Array.from(tabContainerRef.current.children).forEach((child) => {
                const tab = child.dataset.tab;
                positions[tab] = {
                    left: child.offsetLeft,
                    width: child.offsetWidth,
                };
            });
            setTabPositions(positions);
        }
    }, [activeTab]);

    return (
        <div className="w-full bg-primary/70">
            {/* Tab Navigation */}
            <div className="sticky -top-6 z-10 bg-primary pt-5">
                <div className="rounded-tr-lg rounded-tl-lg bg-gray-500 z-10">
                    <div className="flex justify-between w-full" ref={tabContainerRef}>
                        {tabs.map((tab) => (
                            <button
                                key={tab}
                                data-tab={tab}
                                onClick={() => handleTabClick(tab)}
                                className={`w-full relative px-4 py-2 text-sm font-medium transition-colors duration-300 ${activeTab === tab ? "text-brand-500" : "text-gray-200 hover:text-brand-500"
                                    }`}
                            >
                                {tab}
                            </button>
                        ))}
                    </div>

                    {/* Animated Indicator */}
                    {tabPositions[activeTab] && (
                        <motion.div
                            className="absolute bottom-0 h-0.5 bg-[#38BDF8] rounded-full"
                            layout
                            transition={{ type: "spring", stiffness: 500, damping: 30 }}
                            style={{
                                left: tabPositions[activeTab].left,
                                width: tabPositions[activeTab].width,
                            }}
                        />
                    )}
                </div>
            </div>

            {/* Sections */}
            <div className="space-y-12 mt-6 ">
                <section ref={sectionRefs["Performance Overview"]} className="bg-gray-600 rounded-xl px-5 py-2.5 scroll-mt-18">
                    <h2 className="text-xl font-semibold mb-4 mt-5">Performance Overview</h2>
                    <PerformanceMetricsGrid platform="instagram" />
                    <PostCarousel platform="instagram" postCount={5} />
                    {/* Add cards & charts */}
                </section>

                <section ref={sectionRefs["Audience Demographics"]} className="bg-gray-600 rounded-xl px-5 py-2.5 scroll-mt-18">
                    <h2 className="text-xl font-semibold mb-4">Audience Demographics</h2>
                    {/* Add demographic data */}
                </section>

                <section ref={sectionRefs["Audience Interests & Behaviour"]} className="bg-gray-600 rounded-xl px-5 py-2.5 scroll-mt-18">
                    <h2 className="text-xl font-semibold mb-4">Audience Interests & Behaviour</h2>
                    <p>h</p>
                    <p className="h-20">h</p>
                    <p className="h-20">h</p>
                    <p className="h-20">h</p>
                    <p className="h-20">h</p>
                    <p className="h-20">h</p>
                    <p className="h-20">h</p>
                    <p className="h-20">h</p>
                    <p className="h-20">h</p>
                    <p className="h-20">h</p>
                    <p className="h-20">h</p>
                    <p className="h-20">h</p>
                    <p className="h-20">h</p>
                    <p className="h-20">h</p>
                    <p className="h-20">h</p>
                    <p className="h-20">h</p>
                    <p className="h-20">h</p>
                    <p className="h-20">h</p>
                    <p className="h-20">h</p>
                    <p className="h-20">h</p>
                    <p className="h-20">h</p>
                    <p className="h-20">h</p>
                    <p className="h-20">h</p>
                    <p className="h-20">h</p>
                    <p className="h-20">h</p>
                    {/* Add interest insights */}
                </section>

                <section ref={sectionRefs["Sponsored Content Insights"]} className="bg-gray-600 rounded-xl px-5 py-2.5 scroll-mt-18">
                    <h2 className="text-xl font-semibold mb-4">Sponsored Content Insights</h2>
                    {/* Add insights */}
                </section>

                <section ref={sectionRefs["Similar Creators"]} className="bg-gray-600 rounded-xl px-5 py-2.5 scroll-mt-18">
                    <h2 className="text-xl font-semibold mb-4">Similar Creators</h2>
                    {/* Add creator list */}
                </section>
            </div>
        </div>
    );
};

export default InstagramAnalytics;
