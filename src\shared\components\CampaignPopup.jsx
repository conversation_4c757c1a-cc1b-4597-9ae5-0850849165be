import React, { useRef, useEffect, useState } from 'react';

const CampaignPopup = ({
  popupTitle,
  onClose,
  campaigns,
  setCampaigns,
  position = 'right-0 top-8',
  addNewEnabled = false,
  onCreateCampaign, // <-- New prop
  onSave // <-- NEW PROP
}) => {
  const popupRef = useRef(null);
  const [newCampaignName, setNewCampaignName] = useState('');
  const [error, setError] = useState('');
  const [isCreating, setIsCreating] = useState(false);

  useEffect(() => {
    setCampaigns((prev) =>
      prev.map((campaign) => ({ ...campaign, isSelected: false }))
    );
  }, []);

  useEffect(() => {
    const handleClickOutside = (e) => {
      if (popupRef.current && !popupRef.current.contains(e.target)) {
        onClose();
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [onClose]);

  const handleCheckboxToggle = (id) => {
    setError('');
    setCampaigns((prev) =>
      prev.map((campaign) =>
        campaign.id === id ? { ...campaign, isSelected: !campaign.isSelected } : campaign
      )
    );
  };

  const handleCreate = () => {
    const name = newCampaignName.trim();
    if (name === '') return;

    if (onCreateCampaign) {
      onCreateCampaign(name); // parent handles ID creation and state update
    }

    setNewCampaignName('');
    setIsCreating(false);
  };

  const handleSave = () => {
    const selectedCampaigns = campaigns.filter((c) => c.isSelected);
    if (selectedCampaigns.length === 0) {
      setError('Select at one option.');
      return;
    }

    setError(''); // clear any previous error
    if (onSave) {
      onSave(selectedCampaigns);
    }
    onClose();
  };


  return (
    <div
      ref={popupRef}
      className={`absolute ${position} w-64 bg-gray-500 text-white rounded-lg p-4 shadow-lg z-50`}
    >
      <h3 className="text-base font-semibold mb-5">{popupTitle}</h3>

      <div className="flex flex-col gap-3 max-h-40 overflow-y-auto pr-1 scrollbar-thin scrollbar-thumb-gray-200 scrollbar-track-gray-800">
        {campaigns.map((item) => (
          <label key={item.id} className="cursor-pointer flex justify-between items-center text-sm">
            <span className="hover:text-gray-200">{item.name}</span>
            <div
              className={`w-5 h-5 flex items-center justify-center rounded border cursor-pointer ${item.isSelected ? 'bg-green-500 border-green-500 text-white' : 'border-gray-200 bg-gray-300'
                }`}
              onClick={(e) => { e.stopPropagation(); handleCheckboxToggle(item.id);}}
            >
              {item.isSelected && <span className="text-xs font-bold">✔</span>}
            </div>
          </label>
        ))}
      </div>
      {
        addNewEnabled && (<hr className="my-3 border-gray-200/30" />)
      }

      {isCreating ? (
        <input
          type="text"
          autoFocus
          className="w-full px-2 py-1 text-sm text-gray-50 border border-gray-500 rounded bg-gray-800"
          placeholder="Enter list name"
          value={newCampaignName}
          onChange={(e) => setNewCampaignName(e.target.value)}
          onKeyDown={(e) => {
            if (e.key === 'Enter') handleCreate();
          }}
        />
      ) : (
        addNewEnabled && (
          <button
            className="text-sm text-white font-semibold flex items-center gap-1 cursor-pointer hover:text-gray-200"
            onClick={(e) => { e.stopPropagation(); setIsCreating(true); setError(''); }}
          >
            <span>+</span> Create New
          </button>
        )
      )}

      {error && (
        <div className="text-red-2 text-xs mb-2">
          {error}
        </div>
      )}

      <button
        className="mt-5 w-full py-2 bg-blue-100 text-black rounded-md text-sm font-semibold"
        onClick={(e)=>{handleSave(); e.stopPropagation();}}
      >
        Save
      </button>
    </div>
  );
};

export default CampaignPopup;

