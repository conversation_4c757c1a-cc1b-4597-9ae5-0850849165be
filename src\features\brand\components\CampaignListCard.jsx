import React from 'react'
import { HiOutlineTrash } from "react-icons/hi2";
import AvatarStack from '@shared/components/AvatarStack';


const CampaignListCard = ({
    campaign,
    onClick,
    onDelete
}) => {
    const { name, createdOn, createdBy, liveDate, status, creators, progress, budget = 10} = campaign;

    const formatDateRangeShort = (start, end) => {
        if (!start || !end) return '';

        const startDate = new Date(start);
        const endDate = new Date(end);

        if (isNaN(startDate) || isNaN(endDate)) return '';

        const getDay = (date) => date.getDate(); // no leading zero
        const getMonthShort = (date) =>
            date.toLocaleString('en-GB', { month: 'short' });
        const getYearShort = (date) =>
            `’ ${date.getFullYear().toString().slice(-2)}`; // curly apostrophe

        return `${getDay(startDate)} ${getMonthShort(startDate)} - ${getDay(endDate)} ${getMonthShort(endDate)}${getYearShort(endDate)}`;
    };



    const formatStatusLabel = (status) => {
        return status
            .replace(/_/g, ' ')
            .replace(/\w\S*/g, (txt) => txt.charAt(0).toUpperCase() + txt.slice(1).toLowerCase());
    };

    const getColorClasses = () => {
        switch (status.toLowerCase()) {
            case 'active':
                return {
                    bg: 'bg-purple-1',
                    border: 'border-l-purple-1'
                };
            case 'upcoming':
                return {
                    bg: 'bg-yellow-500',
                    border: 'border-l-yellow-500'
                };
            case 'completed':
                return {
                    bg: 'bg-green-2',
                    border: 'border-l-green-2'
                };
            case 'paused':
            case 'draft':
                return {
                    bg: 'bg-orange',
                    border: 'border-l-orange'
                };
            case 'planned':
                return {
                    bg: 'bg-blue',
                    border: 'border-l-blue'
                };
            default:
                return {
                    bg: 'bg-gray-500',
                    border: 'border-l-gray-500'
                };
        }
    };


    const { bg, border } = getColorClasses();

    return (
        <div
            // ref={wrapperRef}
            onClick={(onClick)} // ✅ add click handler
            className={`group relative w-full border-l-3 hover:border-l-0 ${border} p-5 bg-gray-600 rounded-lg shadow-md text-white  hover:scale-101 hover:transform-gpu transition pointer-events-auto`}
        >
            <button
                onClick={(e) => {
                    e.stopPropagation(); // 🛑 prevent card click
                    onDelete?.();
                }}
                className="absolute z-10 top-5 right-5 text-gray-50 opacity-0 group-hover:opacity-100 transition hover:text-red-2 hover:cursor-pointer"
            >
                <HiOutlineTrash className='h-5 w-5' />
            </button>
            <div className='w-full flex flex-col gap-5'>
                <div className={`flex flex-col gap-2.5 rounded-[10px] text-14-medium text-white `}>
                    <span className={`py-1.5 px-2.5 rounded-[10px] w-fit ${bg}`}>
                        {formatStatusLabel(status)}
                    </span>
                    <h1 className='text-20-medium text-gray-50'>{name}</h1>
                </div>
                <div className="flex justify-between gap-5">
                    <div className="flex flex-col justify-between flex-1 gap-5">
                        <div className="flex flex-col gap-2 items-start">
                            <span className='text-14-medium text-gray-200'>Creators</span>
                            <AvatarStack avatars={creators} maxAvatars={3} count={creators.length - 3} className="" />
                        </div>
                        <div className='flex flex-col gap-2'>
                            <div className='text-14-medium text-gray-200'>Campaign Date</div>
                            <div className="text-16-semibold text-white">{formatDateRangeShort(createdOn, liveDate)}</div>
                        </div>
                    </div>

                    <div className="flex flex-col justify-between flex-1">
                        <div className='flex flex-col gap-2'>
                            <div className='text-14-medium text-gray-200'>Created by</div>
                            <div className="text-16-semibold text-white">{createdBy}</div>
                        </div>
                        <div className='flex flex-col gap-2'>
                            <div className='text-14-medium text-gray-200'>Budget</div>
                            <div className="text-16-semibold text-white">₹{budget}</div>
                        </div>

                    </div>
                </div>
                <div className="text-white w-full max-w-md rounded-md">
                    <div className="flex justify-between text-sm mb-1">
                        <span>Progress</span>
                        <span>{progress} %</span>
                    </div>
                    <div className="w-full h-3 rounded-full bg-gray-800">
                        <div className="h-full bg-sky-blue rounded-full" style={{ width: `${progress}%` }}></div>
                    </div>
                </div>
            </div>

        </div >
    )
}

export default CampaignListCard
