import React, { useState } from 'react';
import LocationFilterDropdown from './LocationFilterDropdown';

/**
 * Example usage of the LocationFilterDropdown component
 * This demonstrates how to integrate the component with parent components
 */
const LocationFilterExample = () => {
  const [selectedLocation, setSelectedLocation] = useState(null);
  const [selectedLocations, setSelectedLocations] = useState([]);

  // Handle single location selection
  const handleLocationSelect = (locationData) => {
    console.log('Selected location:', locationData);
    setSelectedLocation(locationData);
    
    // Example of how you might use this data in a filter system
    if (locationData) {
      // Add to filters or trigger search
      console.log(`Filtering by: ${locationData.fullLocation}`);
      if (locationData.group) {
        console.log(`Group: ${locationData.group}`);
      }
    }
  };

  // Handle multiple location selections (if implementing multi-select)
  const handleMultiLocationSelect = (locationData) => {
    if (!locationData) {
      setSelectedLocations([]);
      return;
    }

    setSelectedLocations(prev => {
      const exists = prev.find(loc => 
        loc.country === locationData.country && loc.city === locationData.city
      );
      
      if (exists) {
        // Remove if already selected
        return prev.filter(loc => 
          !(loc.country === locationData.country && loc.city === locationData.city)
        );
      } else {
        // Add new selection
        return [...prev, locationData];
      }
    });
  };

  // Clear all selections
  const handleClearAll = () => {
    setSelectedLocation(null);
    setSelectedLocations([]);
  };

  return (
    <div className="p-6 bg-gray-900 min-h-screen">
      <h1 className="text-white text-2xl font-bold mb-6">
        LocationFilterDropdown Examples
      </h1>

      {/* Single Selection Example */}
      <div className="mb-8">
        <h2 className="text-white text-lg font-semibold mb-4">
          Single Location Selection
        </h2>
        
        <div className="flex items-center gap-4 mb-4">
          <LocationFilterDropdown
            onSelect={handleLocationSelect}
            selectedLocations={selectedLocation ? [selectedLocation] : []}
            placeholder="Select Location"
            className="min-w-[200px]"
          />
          
          <button
            onClick={handleClearAll}
            className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
          >
            Clear
          </button>
        </div>

        {selectedLocation && (
          <div className="bg-gray-800 p-4 rounded-lg">
            <h3 className="text-white font-medium mb-2">Selected Location:</h3>
            <div className="text-gray-300 space-y-1">
              <p><strong>Full Location:</strong> {selectedLocation.fullLocation}</p>
              <p><strong>Country:</strong> {selectedLocation.country}</p>
              <p><strong>City:</strong> {selectedLocation.city}</p>
              {selectedLocation.group && (
                <p><strong>Group:</strong> {selectedLocation.group}</p>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Multiple Selection Example */}
      <div className="mb-8">
        <h2 className="text-white text-lg font-semibold mb-4">
          Multiple Location Selection (Custom Implementation)
        </h2>
        
        <div className="flex items-center gap-4 mb-4">
          <LocationFilterDropdown
            onSelect={handleMultiLocationSelect}
            selectedLocations={selectedLocations}
            placeholder="Add Location"
            className="min-w-[200px]"
          />
          
          <span className="text-gray-400">
            {selectedLocations.length} location(s) selected
          </span>
        </div>

        {selectedLocations.length > 0 && (
          <div className="bg-gray-800 p-4 rounded-lg">
            <h3 className="text-white font-medium mb-2">Selected Locations:</h3>
            <div className="space-y-2">
              {selectedLocations.map((location, index) => (
                <div key={index} className="flex items-center justify-between bg-gray-700 p-2 rounded">
                  <div className="text-gray-300">
                    <span className="font-medium">{location.fullLocation}</span>
                    {location.group && (
                      <span className="text-gray-400 ml-2">({location.group})</span>
                    )}
                  </div>
                  <button
                    onClick={() => handleMultiLocationSelect(location)}
                    className="text-red-400 hover:text-red-300"
                  >
                    Remove
                  </button>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Integration with Filter System Example */}
      <div className="mb-8">
        <h2 className="text-white text-lg font-semibold mb-4">
          Integration with Filter System
        </h2>
        
        <div className="bg-gray-800 p-4 rounded-lg">
          <h3 className="text-white font-medium mb-2">Filter Configuration:</h3>
          <pre className="text-gray-300 text-sm overflow-x-auto">
{`// Example of how to integrate with existing filter system
const handleLocationFilter = (locationData) => {
  if (!locationData) {
    // Clear location filter
    setActiveFilters(prev => ({
      ...prev,
      location: null
    }));
    return;
  }

  // Add location to active filters
  setActiveFilters(prev => ({
    ...prev,
    location: {
      country: locationData.country,
      city: locationData.city,
      group: locationData.group,
      displayName: locationData.fullLocation
    }
  }));

  // Trigger search with new filters
  searchCreators({
    query: searchQuery,
    filters: {
      ...activeFilters,
      location: locationData
    }
  });
};`}
          </pre>
        </div>
      </div>

      {/* API Integration Notes */}
      <div className="bg-gray-800 p-4 rounded-lg">
        <h3 className="text-white font-medium mb-2">Real API Integration Notes:</h3>
        <div className="text-gray-300 space-y-2 text-sm">
          <p>• Replace <code className="bg-gray-700 px-1 rounded">mockLocationApi</code> with actual API service calls</p>
          <p>• Update API endpoints in the component to match your backend</p>
          <p>• Consider adding caching for frequently accessed countries/cities</p>
          <p>• Implement proper error handling and retry logic</p>
          <p>• Add loading states for better UX during API calls</p>
          <p>• Consider implementing infinite scroll for large city lists</p>
        </div>
      </div>
    </div>
  );
};

export default LocationFilterExample;
