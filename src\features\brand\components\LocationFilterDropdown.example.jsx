import React, { useState } from 'react';
import LocationFilterDropdown from './LocationFilterDropdown';

/**
 * Example usage of the LocationFilterDropdown component
 * This demonstrates how to integrate the component with parent components
 */
const LocationFilterExample = () => {
  const [selectedLocation, setSelectedLocation] = useState(null);
  const [selectedLocations, setSelectedLocations] = useState([]);

  // Handle single location selection
  const handleLocationSelect = (locationData) => {
    console.log('Selected location:', locationData);
    setSelectedLocation(locationData);

    // Example of how you might use this data in a filter system
    if (locationData) {
      if (locationData.selectionType === 'individual') {
        console.log(`Individual selection: ${locationData.fullLocation}`);
        console.log(`Type: ${locationData.type}, Group: ${locationData.group}`);
      } else if (locationData.selectionType === 'group') {
        console.log(`Group selection: ${locationData.group} in ${locationData.country}`);
        console.log(`Selected items: ${locationData.selectedItems.length}`);
        console.log(`Excluded items: ${locationData.excludedItems.length}`);
      }
    }
  };

  // Handle multiple location selections (if implementing multi-select)
  const handleMultiLocationSelect = (locationData) => {
    if (!locationData) {
      setSelectedLocations([]);
      return;
    }

    setSelectedLocations(prev => {
      const exists = prev.find(loc => 
        loc.country === locationData.country && loc.city === locationData.city
      );
      
      if (exists) {
        // Remove if already selected
        return prev.filter(loc => 
          !(loc.country === locationData.country && loc.city === locationData.city)
        );
      } else {
        // Add new selection
        return [...prev, locationData];
      }
    });
  };

  // Clear all selections
  const handleClearAll = () => {
    setSelectedLocation(null);
    setSelectedLocations([]);
  };

  return (
    <div className="p-6 bg-gray-900 min-h-screen">
      <h1 className="text-white text-2xl font-bold mb-6">
        LocationFilterDropdown Examples
      </h1>

      {/* Single Selection Example */}
      <div className="mb-8">
        <h2 className="text-white text-lg font-semibold mb-4">
          Single Location Selection with State Persistence
        </h2>

        <div className="bg-gray-800 p-3 rounded mb-4">
          <p className="text-gray-300 text-sm mb-2">
            <strong>Test State Persistence:</strong> Select a location, close the dropdown, then reopen it.
            The component should remember your selection and show it as active.
          </p>
          <p className="text-gray-300 text-sm mb-2">
            <strong>Test Cross-Selection Recognition:</strong> Select items through group selection, then search for those same items individually.
            They should appear highlighted with selection indicators showing their source (Individual/Group/Pending).
          </p>
          <p className="text-gray-300 text-sm mb-2">
            <strong>Test Country Visual Indicators:</strong> Select locations within a country, then navigate back to country selection.
            The country should show selection badges, counts, and summaries of what's selected within it.
          </p>
          <p className="text-gray-300 text-sm mb-2">
            <strong>Test Multi-Group Selection:</strong> Select items from multiple groups (e.g., Tier 1 + Tier 2).
            Each group selection returns to Phase 2, showing cumulative pending selections. Use "Apply All Selections" to commit all at once.
          </p>
          <p className="text-gray-300 text-sm">
            <strong>Test Master Checkbox:</strong> In group detail view (Phase 3), use the "Select All" checkbox to quickly select/deselect all items.
            The checkbox shows three states: unchecked (none selected), indeterminate/dash (some selected), and checked (all selected).
          </p>
        </div>

        <div className="flex items-center gap-4 mb-4">
          <LocationFilterDropdown
            onSelect={handleLocationSelect}
            selectedLocations={selectedLocation ? [selectedLocation] : []}
            placeholder="Select Location"
            className="min-w-[200px]"
          />

          <button
            onClick={handleClearAll}
            className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
          >
            Clear
          </button>
        </div>

        {selectedLocation && (
          <div className="bg-gray-800 p-4 rounded-lg">
            <h3 className="text-white font-medium mb-2">Selected Location:</h3>
            <div className="text-gray-300 space-y-1">
              <p><strong>Selection Type:</strong> {selectedLocation.selectionType}</p>
              <p><strong>Country:</strong> {selectedLocation.country}</p>

              {selectedLocation.selectionType === 'individual' && (
                <>
                  <p><strong>Location:</strong> {selectedLocation.city}</p>
                  <p><strong>Type:</strong> {selectedLocation.type}</p>
                  <p><strong>Full Location:</strong> {selectedLocation.fullLocation}</p>
                  {selectedLocation.group && (
                    <p><strong>Group:</strong> {selectedLocation.group}</p>
                  )}
                </>
              )}

              {selectedLocation.selectionType === 'group' && (
                <>
                  <p><strong>Group:</strong> {selectedLocation.group}</p>
                  <p><strong>Selected Items:</strong> {selectedLocation.selectedItems.length}</p>
                  <p><strong>Excluded Items:</strong> {selectedLocation.excludedItems.length}</p>

                  {selectedLocation.selectedItems.length > 0 && (
                    <div className="mt-2">
                      <p className="font-medium">Selected Locations:</p>
                      <ul className="list-disc list-inside ml-4 text-sm">
                        {selectedLocation.selectedItems.map((item, index) => (
                          <li key={index}>
                            {item.name} ({item.type})
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}

                  {selectedLocation.excludedItems.length > 0 && (
                    <div className="mt-2">
                      <p className="font-medium text-red-400">Excluded Locations:</p>
                      <ul className="list-disc list-inside ml-4 text-sm">
                        {selectedLocation.excludedItems.map((item, index) => (
                          <li key={index}>
                            {item.name} ({item.type})
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Multiple Selection Example */}
      <div className="mb-8">
        <h2 className="text-white text-lg font-semibold mb-4">
          Multiple Location Selection (Custom Implementation)
        </h2>
        
        <div className="flex items-center gap-4 mb-4">
          <LocationFilterDropdown
            onSelect={handleMultiLocationSelect}
            selectedLocations={selectedLocations}
            placeholder="Add Location"
            className="min-w-[200px]"
          />
          
          <span className="text-gray-400">
            {selectedLocations.length} location(s) selected
          </span>
        </div>

        {selectedLocations.length > 0 && (
          <div className="bg-gray-800 p-4 rounded-lg">
            <h3 className="text-white font-medium mb-2">Selected Locations:</h3>
            <div className="space-y-2">
              {selectedLocations.map((location, index) => (
                <div key={index} className="flex items-center justify-between bg-gray-700 p-2 rounded">
                  <div className="text-gray-300">
                    <span className="font-medium">{location.fullLocation}</span>
                    {location.group && (
                      <span className="text-gray-400 ml-2">({location.group})</span>
                    )}
                  </div>
                  <button
                    onClick={() => handleMultiLocationSelect(location)}
                    className="text-red-400 hover:text-red-300"
                  >
                    Remove
                  </button>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Integration with Filter System Example */}
      <div className="mb-8">
        <h2 className="text-white text-lg font-semibold mb-4">
          Integration with Filter System
        </h2>
        
        <div className="bg-gray-800 p-4 rounded-lg">
          <h3 className="text-white font-medium mb-2">Filter Configuration:</h3>
          <pre className="text-gray-300 text-sm overflow-x-auto">
{`// Example of how to integrate with existing filter system
const handleLocationFilter = (locationData) => {
  if (!locationData) {
    // Clear location filter
    setActiveFilters(prev => ({
      ...prev,
      location: null
    }));
    return;
  }

  if (locationData.selectionType === 'individual') {
    // Handle individual location selection
    setActiveFilters(prev => ({
      ...prev,
      location: {
        type: 'individual',
        country: locationData.country,
        location: locationData.city,
        locationType: locationData.type,
        group: locationData.group,
        displayName: locationData.fullLocation
      }
    }));
  } else if (locationData.selectionType === 'group') {
    // Handle group selection with exclusions
    setActiveFilters(prev => ({
      ...prev,
      location: {
        type: 'group',
        country: locationData.country,
        group: locationData.group,
        selectedItems: locationData.selectedItems,
        excludedItems: locationData.excludedItems,
        displayName: \`\${locationData.group} (\${locationData.selectedItems.length} locations)\`
      }
    }));
  }

  // Trigger search with new filters
  searchCreators({
    query: searchQuery,
    filters: {
      ...activeFilters,
      location: locationData
    }
  });
};`}
          </pre>
        </div>
      </div>

      {/* State Persistence Demo */}
      <div className="mb-8">
        <h2 className="text-white text-lg font-semibold mb-4">
          State Persistence Behavior
        </h2>

        <div className="bg-gray-800 p-4 rounded-lg">
          <h3 className="text-white font-medium mb-2">How State Persistence Works:</h3>
          <div className="text-gray-300 space-y-2 text-sm">
            <p><strong>Individual Selections:</strong></p>
            <ul className="list-disc list-inside ml-4 space-y-1">
              <li>Select a city/state → Close dropdown → Reopen</li>
              <li>Component navigates directly to Phase 2 (location selection)</li>
              <li>Previously selected item is highlighted with checkmark</li>
              <li>Button text shows the selected location name</li>
            </ul>

            <p className="mt-3"><strong>Group Selections:</strong></p>
            <ul className="list-disc list-inside ml-4 space-y-1">
              <li>Select a group with specific items → Close dropdown → Reopen</li>
              <li>Component navigates to Phase 2 showing the selected group</li>
              <li>Group shows selection summary (e.g., "5 of 8 selected, 2 excluded")</li>
              <li>Group is visually highlighted with blue border and checkmark</li>
              <li>Clicking "Edit Selection" shows Phase 3 with preserved selections</li>
            </ul>

            <p className="mt-3"><strong>Master Checkbox Functionality (Phase 3):</strong></p>
            <ul className="list-disc list-inside ml-4 space-y-1">
              <li><strong>Unchecked state</strong>: No items selected - click to select all</li>
              <li><strong>Indeterminate state</strong>: Some items selected - shows dash icon, click to select all</li>
              <li><strong>Checked state</strong>: All items selected - click to deselect all</li>
              <li>Master checkbox updates automatically when individual items are toggled</li>
              <li>Provides quick way to select/deselect entire groups</li>
              <li>Maintains all existing individual selection functionality</li>
            </ul>

            <p className="mt-3"><strong>Multi-Group Selection Workflow:</strong></p>
            <ul className="list-disc list-inside ml-4 space-y-1">
              <li>Select items from first group (e.g., Tier 1) → Returns to Phase 2</li>
              <li>Select items from second group (e.g., Tier 2) → Returns to Phase 2</li>
              <li>Phase 2 shows cumulative pending selections from all groups</li>
              <li>"Apply All Selections" button commits all pending selections at once</li>
              <li>Pending selection count updates in real-time across all interfaces</li>
              <li>Can mix individual selections with multiple group selections</li>
              <li>Dropdown only closes when user explicitly applies or cancels</li>
            </ul>

            <p className="mt-3"><strong>Country Selection Visual Indicators:</strong></p>
            <ul className="list-disc list-inside ml-4 space-y-1">
              <li>Countries with selected locations show visual indicators in Phase 1</li>
              <li>Selection count badges display total selected locations per country</li>
              <li>Selection summaries show what's selected within each country:</li>
              <li className="ml-4">Individual: "Mumbai, Delhi + 3 more"</li>
              <li className="ml-4">Groups: "Tier 1 group, Tier 2: 3 locations"</li>
              <li className="ml-4">Mixed: Shows both individual and group selections</li>
              <li>Countries with selections have blue borders and highlighting</li>
            </ul>

            <p className="mt-3"><strong>Cross-Selection Recognition:</strong></p>
            <ul className="list-disc list-inside ml-4 space-y-1">
              <li>Items selected via group selection appear highlighted in individual search</li>
              <li>Items selected individually show as selected in group previews</li>
              <li>Selection source indicators show how each item was selected:</li>
              <li className="ml-4">
                <span className="bg-green-600 text-green-100 px-1.5 py-0.5 rounded text-xs mr-2">Individual</span>
                Selected through individual search
              </li>
              <li className="ml-4">
                <span className="bg-blue-600 text-blue-100 px-1.5 py-0.5 rounded text-xs mr-2">Group</span>
                Selected through committed group selection
              </li>
              <li className="ml-4">
                <span className="bg-yellow-600 text-yellow-100 px-1.5 py-0.5 rounded text-xs mr-2">Pending</span>
                Selected in current group session (not yet committed)
              </li>
            </ul>

            <p className="mt-3"><strong>Technical Implementation:</strong></p>
            <ul className="list-disc list-inside ml-4 space-y-1">
              <li>Uses <code className="bg-gray-700 px-1 rounded">selectedLocations</code> prop to restore state</li>
              <li>Cross-references <code className="bg-gray-700 px-1 rounded">groupSelections</code> state for pending selections</li>
              <li>Helper functions ensure consistent selection detection across all interfaces</li>
              <li>Visual indicators update in real-time across all selection methods</li>
            </ul>
          </div>
        </div>
      </div>

      {/* API Integration Notes */}
      <div className="bg-gray-800 p-4 rounded-lg">
        <h3 className="text-white font-medium mb-2">Real API Integration Notes:</h3>
        <div className="text-gray-300 space-y-2 text-sm">
          <p>• Replace <code className="bg-gray-700 px-1 rounded">mockLocationApi</code> with actual API service calls</p>
          <p>• Update API endpoints in the component to match your backend</p>
          <p>• Consider adding caching for frequently accessed countries/cities</p>
          <p>• Implement proper error handling and retry logic</p>
          <p>• Add loading states for better UX during API calls</p>
          <p>• Consider implementing infinite scroll for large city lists</p>
          <p>• <strong>State persistence works automatically</strong> - no additional API calls needed for restoration</p>
        </div>
      </div>
    </div>
  );
};

export default LocationFilterExample;
