import React, { useState } from 'react';

const HashtagInput = ({ value = [], onChange, placeholder = 'Enter hashtags, separate with comma' ,prefix = '#' }) => {
  const [inputValue, setInputValue] = useState('');

  const addTags = (raw) => {
    const newTags = raw
      .split(',')
      .map(tag => tag.trim().replace(/^#/, ''))
      .filter(tag => tag.length > 0 && !value.includes(tag));

    if (newTags.length > 0) {
      onChange([...value, ...newTags]);
    }
  };

  const handleKeyDown = (e) => {
    if (e.key === 'Enter' || e.key === ',') {
      e.preventDefault();
      addTags(inputValue);
      setInputValue('');
    }
  };

  const handlePaste = (e) => {
    e.preventDefault();
    const paste = e.clipboardData.getData('text');
    addTags(paste);
    setInputValue('');
  };

  const removeTag = (tagToRemove) => {
    onChange(value.filter(tag => tag !== tagToRemove));
  };

  return (
    <div className="flex items-start max-h-19 flex-wrap gap-2 border border-gray-600 line-clamp-2 rounded-lg px-3 py-2 bg-transparent w-full overflow-y-auto scrollbar-thumb-gray-400 scrollbar-track-transparent scrollbar-thin scrollbar-hidden-unless-hover">
      {value.map((tag, i) => (
        <div
          key={i}
          className="border-1 border-gray-400 text-white px-2 py-0.5 rounded-xl text-sm flex items-center gap-1 whitespace-nowrap"
        >
          <span>{prefix}{tag}</span>
          <button
            type="button"
            onClick={() => removeTag(tag)}
            className="text-white hover:text-gray-200 text-sm leading-none cursor-pointer"
          >
            ×
          </button>
        </div>
      ))}
      <input
        type="text"
        placeholder={placeholder}
        value={inputValue}
        onChange={(e) => setInputValue(e.target.value)}
        onKeyDown={handleKeyDown}
        onPaste={handlePaste}
        className="bg-transparent outline-none text-white placeholder-gray-400 flex-1 min-w-[100px]"
      />
    </div>
  );
};

export default HashtagInput;
