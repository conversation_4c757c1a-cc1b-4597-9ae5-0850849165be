import React from "react";
import InstagramCircle from "@assets/icon/instagram-circle.svg";
import YoutubeCircle from "@assets/icon/youtube-circle.svg";
import ContentCopy from "@assets/icon/content_copy.svg";
import Error from "@assets/icon/error.svg";
import SocialButton from "@shared/components/UI/button";

const SideProfile = ({ data }) => {
    // const avgFollowers = (data.profiles.reduce((sum, profile) => sum + profile.followers, 0) / data.profiles.length)/ 1000; // Average followers in thousands
    return (
        <aside className="flex flex-col gap-5 p-2.5 rounded-lg w-full bg-gray-600">
            {/* Profiles List */}
            <div className="py-5 px-2.5 flex flex-col gap-5">
                {data.profiles.map((profile, index) => (
                    <React.Fragment key={index}>
                        <div className="flex gap-5 items-center ">
                            <div className="flex w-20 relative items-center justify-center">
                                <img
                                    src={profile.avatarUrl}
                                    alt={profile.username}
                                    className="w-14 h-14 rounded-full object-cover"
                                />
                                <img src={profile.channel === 1 ? InstagramCircle : YoutubeCircle} alt={profile.channel === 1 ? "Instagram-icon" : "YouTube-icon"} className="absolute bottom-0 right-0 w-4.5" />
                            </div>
                            <div className="w-full">
                                <div className="text-white text-14-regular mb-1">{profile.username}</div>
                                <div className="text-gray-300 text-12-regular text-left mb-2.5">
                                    {profile.title}
                                </div>
                                <div className="flex justify-between gap-4 text-white text-sm w-full">
                                    <div className="flex flex-col items-left gap-0.5 w-1/2">
                                        <span className="text-14-bold">{profile.posts}</span>
                                        <span className="text-gray-400 text-10-regular">
                                            {profile.channel === 1 ? "Posts" : "Videos"}
                                        </span>
                                    </div>
                                    <div className="flex flex-col items-left gap-0.5 w-1/2">
                                        <span className="text-14-bold">{profile.followers}</span>
                                        <span className="text-gray-400 text-10-regular">
                                            {profile.channel === 1 ? "Followers" : "Subscribers"}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {/* Render divider if more than 1 profile and not last item */}
                        {data.profiles.length > 1 && index < data.profiles.length - 1 && (
                            <div className="border-t border-gray-500 my-2 mx-2" />
                        )}
                    </React.Fragment>
                ))}
            </div>

            {/* Profile Progress */}
            <div className="flex flex-col gap-3.75 p-2.5 border-1 border-gray-400 rounded-lg text-gray-50">
                <div className="text-white text-20-semibold mb-2">Make Your Profile Shine 🌟</div>
                <div className="flex w-full gap-3.5 items-center">
                    <div className="w-full bg-gray-400 rounded-full h-2 mb-0 flex items-center">
                        <div
                            className="bg-green h-2 rounded-full"
                            style={{ width: `${(data.profileProgress.stepsDone / data.profileProgress.totalSteps) * 100}%` }}
                        ></div>
                    </div>
                    <div className="text-14-regular w-fit text-gray-200 whitespace-nowrap flex items-center h-full">
                        {data.profileProgress.stepsDone}/{data.profileProgress.totalSteps} Steps Done
                    </div>
                </div>

                {data.profileProgress.missingSteps.filter(step => step.isCompleted === false).map((step, i) => (
                    <div key={i} className="text-gray-300 flex gap-1.5">
                        <div>
                            <img src={Error} alt="Error" className="w-5" />
                        </div>
                        <div className="flex flex-col gap-1.5">
                            <span className="text-14-regular text-gray-50">{step.title}</span>
                            <span className="text-12-regular text-gray-300">
                                {step.description}
                            </span>
                        </div>
                    </div>
                ))}

                {data.profileProgress.missingSteps.filter(step => step.isCompleted === false).length > 0 && (
                    <SocialButton label="Update Profile" type="text" className="text-white bg-transparent border-1 border-gray-300 hover:bg-gray-500 hover:text-gray-200" />
                )}
            </div>

            {/* Achievements */}
            {data.achievements && data.achievements.length > 0 && (
                <div className="bg-teal rounded-lg p-2.5 flex flex-col gap-3.75">
                    <h3 className="text-white text-20-semibold">Your Achievements</h3>
                    <ul className="list-none text-gray-100 text-14-regular space-y-2.5">
                        {data.achievements.map((achievement, index) => (
                            <li key={index}>{achievement.emoji} {achievement.title}</li>
                        ))}
                    </ul>
                </div>
            )}
            
            {/* Brand Collaborations */}
            {data.brandCollaborations && data.brandCollaborations.length > 0 && (
                <div className="bg-light-1 rounded-lg p-2.5 flex flex-col gap-3.75">
                    <h3 className="text-gray-900 text-20-semibold">Brand Collaborations ({data.brandCollaborations.length})</h3>
                    <ul className="flex flex-row flex-wrap gap-x-6 gap-y-2.5 text-gray-900 text-14-regular w-full">
                        {data.brandCollaborations.map((collaboration, index) => (
                            <li key={index} className="flex items-center w-full">
                                <img src={collaboration.icon} alt={collaboration.title} className="inline-block mr-2.5 rounded-full object-cover h-5" />
                                <span className="mr-1.5">{collaboration.title}</span>
                                <span className="ml-auto text-gray-400 text-12-regular">
                                    {(() => {
                                        if (!collaboration.date) return null;
                                        const d = new Date(collaboration.date);
                                        if (isNaN(d)) return collaboration.date;
                                        const day = d.getDate();
                                        const month = d.toLocaleString('default', { month: 'short' });
                                        return `${day} ${month}`;
                                    })()}
                                </span>
                            </li>
                        ))}
                    </ul>
                </div>
            )}

            {/* Categories */}
            {data.categories && data.categories.primary.length > 0 && (
                <div className="bg-blue rounded-lg p-3.75">
                    <h3 className="text-white text-20-semibold mb-2">Your Categories</h3>
                    <div className="mb-2 text-gray-100 text-14-regular">Primary</div>
                    <div className="flex flex-wrap gap-2 mb-2">
                        {data.categories.primary.map((cat, i) => (
                            <span
                                key={i}
                                className="text-gray-50 rounded-full px-3 py-1 h-6 text-14-regular border-1 border-gray-50"
                            >
                                {cat}
                            </span>
                        ))}

                    </div>
                    {/* Secondary Categories */}
                    {data.categories.secondary && data.categories.secondary.length > 0 && (
                        <>
                            <div className="mb-2 text-gray-100 text-14-regular">Secondary</div>
                            <div className="flex flex-wrap gap-2">
                                {data.categories.secondary.map((cat, i) => (
                                    <span
                                        key={i}
                                        className="text-gray-50 rounded-full px-3 py-1 h-6 text-14-regular border-1 border-gray-50">
                                        {cat}
                                    </span>
                                ))}
                                <span className="text-[#2F76D5] bg-[#DEF8FF80] rounded-full px-3 py-1 h-6 text-14-regular border-1 border-[#2F76D5]"                    >
                                    + Category
                                </span>
                            </div>
                        </>
                    )}
                </div>
            )}

            {/* Languages */}
            { data.languages && data.languages.length > 0 && (
                <div className="bg-light-4 rounded-lg p-2.5">
                    <h3 className="text-gray-800 text-20-semibold mb-2">Content Language(s)</h3>
                    <div className="flex flex-wrap gap-2">
                        {data.languages.map((lang, i) => (
                            <span key={i} className="border-1 rounded-full border-gray-300 text-gray-900 px-2 py-1 text-14-regular">{lang}</span>
                        ))}
                    </div>
                </div>
            )}

            {/* Portfolio Links */}
            { data.profileProgress.missingSteps.filter(step => step.id === "profile-link" && step.isCompleted === true).length > 0 && (
                <div className="bg-purple text-white rounded-lg p-2.5">
                    <h3 className=" text-20-semibold mb-2">Portfolio Links</h3>
                    <div className="flex border-1 border-brand-200 rounded-2xl px-2.75 py-1 items-center max-w-full">
                        <span className="truncate max-w-[250px] block">{data.portfolioLink}</span>
                        <img 
                            src={ContentCopy} 
                            alt="Copy Icon" 
                            className="ml-2 cursor-pointer" 
                            onClick={() => {
                                if (navigator.clipboard) {
                                    navigator.clipboard.writeText(data.portfolioLink);
                                } else {
                                    const textarea = document.createElement('textarea');
                                    textarea.value = data.portfolioLink;
                                    document.body.appendChild(textarea);
                                    textarea.select();
                                    document.execCommand('copy');
                                    document.body.removeChild(textarea);
                                }
                            }}
                        />
                    </div>
                </div>
            )}
            
            {/* Pro Tips */}
            {data.protips && data.protips.length > 0 && (
                <div className="bg-sky-blue text-gray-900 rounded-lg p-5 flex flex-col gap-5">
                    <h3 className="text-20-semibold">Pro Tips! 🚀</h3>
                    <ul className="list-none text-14-regular space-y-5">
                        {data.protips.map((tip, index) => (
                            <li key={index}>
                                <span className="leading-tight" dangerouslySetInnerHTML={{ __html: tip.title }} />
                            </li>
                        ))}
                    </ul>
                </div>
            )}

            {/* {Edit Profile Button} */}
            {data.profileProgress.missingSteps.filter(step => step.isCompleted === false).length == 0 && (
                    <SocialButton label="Edit Profile" type="text" className="text-white bg-transparent border-1 border-gray-300 hover:bg-gray-500 hover:text-gray-200" />
                )}
        </aside>
    );
};

export default SideProfile;
