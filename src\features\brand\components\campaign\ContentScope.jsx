import React, { useState, useMemo, useEffect } from 'react';
import { useForm<PERSON>ontext, Controller, useWatch } from 'react-hook-form';
import { useSelector } from 'react-redux';
import { selectAllChannelsWithIcons } from '@/app/store/slices/systemSlice';
import HashtagInput from '../../../../shared/components/UI/HashtagInput';

// eslint-disable-next-line no-unused-vars
import { motion } from 'framer-motion';

const formatDeliverableText = (items) => {
  return Object.entries(items)
    .filter(([_, value]) => value > 0)
    .map(([type, count]) => `${count} ${type.charAt(0).toUpperCase() + type.slice(1)}`)
    .join(', ');
};

const ContentScope = (steps) => {
  const { control, getValues, setValue, watch } = useFormContext();
  const channels = useSelector(selectAllChannelsWithIcons);
  const [selectedChannel, setSelectedChannel] = useState(channels[0]?.name || '');


  const creatorCount = useWatch({ control, name: 'creatorCount' }) || 1;
  const budget = useWatch({ control, name: 'budget' }) || '';

  const formatCurrency = (value) => {
    if (!value || isNaN(value)) return '0';
    return Number(value).toLocaleString('en-IN');
  };

  const handleBudgetChange = (value) => {
    const numeric = value.replace(/[^\d]/g, '');
    setValue('budget', numeric);
  };

  const handleCountChange = (type, delta) => {
    const current = getValues(`${selectedChannel}.${type}`) || 0;
    const updated = Math.max(0, current + delta);
    setValue(`${selectedChannel}.${type}`, updated);
  };

  const selectedChannelData = channels.find(c => c.name === selectedChannel);

  // 👇 Watch all values from the form
  const allValues = watch();

  // console.log('allValues', allValues);

  const deliverables = useMemo(() => {
    const result = {};
    const validKeys = Object.keys(allValues).filter(key =>
      channels.some(channel => channel.name === key)
    );

    // console.log('validKeys', validKeys);

    validKeys.forEach((channelKey) => {
      const contentTypes = allValues[channelKey];
      const filteredTypes = Object.entries(contentTypes || {}).filter(([_, count]) => count > 0);
      if (filteredTypes.length > 0) {
        result[channelKey.toLowerCase()] = Object.fromEntries(filteredTypes);
      }
    });

    return result;
  }, [allValues, channels]);


  const hasAnySelection = Object.values(deliverables).some(platform =>
    Object.values(platform).some(count => count > 0)
  );

  // console.log('deliverables', deliverables);

  return (
    <div className='flex flex-col gap-5 p-7.5 rounded-3xl border-1 border-gray-300 bg-gray-900'>
      <div className='flex items-center gap-2'>
        <motion.div
          // whileHover={{ scale: 1.2, rotate: 3 }}
          // whileTap={{ scale: 0.95 }}
          className="text-xl p-2 text-amber-50 w-[36px] h-[36px] flex items-center justify-center"
          style={{
            borderRadius: '8px',
            background: 'var(--color-blue)',
            boxShadow: '0px 0px 8px 0px rgba(79, 152, 250, 0.50)',
            color: 'white',
          }}
        >
          {steps.stepConfig.icon}
        </motion.div>
        <div className="text-20-semibold text-center text-gray-50">{steps.stepConfig.title}</div>
      </div>
      {/* Form Step Content */}
      <div className="flex flex-col gap-7.5">
        <div className="flex gap-5">

          {/* Left Side */}
          <div className="flex w-1/2 flex-col justify-between gap-6">
            <div className="flex flex-col gap-6">
              {/* Channel Toggle */}
              <div className="flex flex-col gap-2.5">
                <label className="text-white text-14-medium">Channels</label>
                <div className="flex items-center gap-2.5 p-0.5 overflow-x-auto">
                  {channels.map((channel, index) => {
                    const isSelected = selectedChannel === channel.name;
                    const isInDeliverables = deliverables[channel.name.toLowerCase()];
                    return (
                      <button
                        key={index}
                        type="button"
                        onClick={() => setSelectedChannel(channel.name)}
                        className={`flex items-center gap-2.5 border-1 rounded-xl px-5 py-3.5 transition-colors cursor-pointer relative
              ${isSelected ? 'bg-gray-800/20 scale-102 border-gray-400' : 'border-gray-500'}
            `}
                      >
                        <img src={channel.icon.url} alt={channel.name} className="h-5 w-5" />
                        <span className="capitalize text-white text-16-semibold">{channel.name}</span>
                        {isInDeliverables && (
                          <i className="fi fi-bs-check text-green-2 text-sm"></i>
                        )}
                      </button>
                    );
                  })}
                </div>
              </div>

              {/* Content Type Counters */}
              {selectedChannelData?.contentType?.length > 0 && (
                <div className="flex flex-col gap-2.5">
                  <label className="text-white text-sm">Select Content Type</label>
                  <div className="flex gap-2 flex-wrap">
                    {selectedChannelData.contentType.map((type, index) => (
                      <div key={index} className={`flex flex-col items-start gap-2 text-white min-w-[100px] px-5 py-3.5 ${index < selectedChannelData.contentType.length - 1 ? 'border-r-1 border-gray-500' : ''}`}>
                        <span className='capitalize text-14-medium'>{type}</span>
                        <div className='flex items-center gap-2'>
                          <div className="flex items-center border-1 border-gray-500 rounded-lg px-4 py-1 gap-3">
                            <Controller
                              control={control}
                              name={`${selectedChannel}.${type.toLowerCase()}`}
                              rules={{ min: 0 }}
                              render={({ field }) => {
                                const platformKey = selectedChannel.toLowerCase();
                                const contentTypeKey = type.toLowerCase();
                                const value = deliverables[platformKey]?.[contentTypeKey];
                                return (
                                  <span className="w-8 text-16-medium text-left">{value ?? 0}</span>
                                );
                              }}
                            />
                          </div>
                          <div
                            className='border-1 border-gray-500 rounded-full h-8.5 w-8.5 flex items-center justify-center hover:scale-102 hover:bg-gray-500/50 cursor-pointer'
                            onClick={() => handleCountChange(type, -1)}
                          >
                            <button type="button" className="text-xl -mt-1 cursor-pointer">−</button>
                          </div>
                          <div
                            className='border-1 border-gray-500 rounded-full h-8.5 w-8.5 flex items-center justify-center hover:scale-102 hover:bg-gray-500/50 cursor-pointer'
                            onClick={() => handleCountChange(type, 1)}
                          >
                            <button type="button" className="text-xl -mt-1 cursor-pointer">+</button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* Validation for at least one contentType > 0 */}
                  <Controller
                    name="__contentTypeValidation"
                    control={control}
                    rules={{
                      validate: () => {
                        const values = getValues();
                        return Object.entries(values)
                          .filter(([key]) => channels.some(ch => ch.name === key))
                          .some(([_, types]) => Object.values(types || {}).some(val => parseInt(val) > 0))
                          || 'Select at least one content type in any channel';
                      }
                    }}
                    render={({ fieldState }) =>
                      fieldState.error ? (
                        <p className="text-red-2 text-xs -mt-2">{fieldState.error.message}</p>
                      ) : null
                    }
                  />
                </div>
              )}

              {/* ✅ Your Selection Summary */}
              {hasAnySelection && (
                <div className='flex flex-col gap-4 px-5 py-5 bg-primary rounded-2xl'>
                  <div className='text-gray-50 flex gap-2.5 items-center'>
                    <div className='bg-green-1 text-green w-8 h-8 flex items-center justify-center rounded-full pt-1'>
                      <i className="fi fi-bs-check-circle"></i>
                    </div>
                    <span className='text-16-medium'>Your Selection</span>
                  </div>
                  <div>
                    <div className="flex flex-wrap justify-start gap-2">
                      {Object.entries(deliverables).map(([platform, items]) => (
                        <div
                          key={platform}
                          className="flex items-center gap-2 w-fit px-5 py-2.5 rounded-full border border-gray-500 bg-transparent text-gray-200 text-16-medium"
                        >
                          {channels.find(channel => channel.name.toLowerCase() === platform)?.icon.url ? (
                            <img src={channels.find(channel => channel.name.toLowerCase() === platform)?.icon.url} className="h-4 w-4" alt={platform} />
                          ) : null}
                          <span>{formatDeliverableText(items)}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Hashtags */}
            <div className="flex flex-col gap-2.5">
              <label className="text-white text-sm">Hashtags</label>
              <Controller
                name="hashtags"
                control={control}
                defaultValue={[]}
                rules={{
                  required: 'At least one hashtag is required',
                  validate: (value) =>
                    Array.isArray(value) && value.length > 0 || 'At least one hashtag is required',
                }}
                render={({ field, fieldState }) => (
                  <>
                    <HashtagInput
                      value={field.value}
                      onChange={field.onChange}
                      placeholder="Enter hashtags, separate with comma"
                    />
                    {fieldState.error && (
                      <p className="text-red-2 text-xs -mt-2">{fieldState.error.message}</p>
                    )}
                  </>
                )}
              />
            </div>
          </div>

          {/* Right Side */}
          <div className="flex flex-col justify-between w-1/2 text-white gap-6">
            <div className='flex flex-col gap-6'>
              {/* Total Creators */}
              <div className="flex flex-col gap-2.5">
                <label className="text-white text-sm">
                  Total Creators to Onboard* <span className="text-xs text-gray-400">(Estimated)</span>
                </label>
                <Controller
                  name="creatorCount"
                  control={control}
                  defaultValue={1}
                  rules={{ required: true, min: 1 }}
                  render={({ field }) => (
                    <div className="flex items-center gap-2">
                      <input
                        type="number"
                        min={1}
                        value={field.value}
                        readOnly
                        className="w-[80px] text-left border outline-none border-gray-500 rounded-lg py-2 px-2 bg-transparent text-white"
                      />
                      <button
                        type="button"
                        onClick={() => field.onChange(Math.max(1, field.value - 1))}
                        className="w-8 h-8 flex items-center justify-center rounded-full border border-gray-500 hover:bg-gray-500/50"
                      >
                        −
                      </button>
                      <button
                        type="button"
                        onClick={() => field.onChange(field.value + 1)}
                        className="w-8 h-8 flex items-center justify-center rounded-full border border-gray-500 hover:bg-gray-500/50"
                      >
                        +
                      </button>
                    </div>
                  )}
                />
              </div>

              {/* Total Budget */}
              <div className="flex flex-col gap-2.5">
                <label className="text-white text-sm">Total Budget (₹)*</label>
                <Controller
                  name="budget"
                  control={control}
                  defaultValue=""
                  rules={{
                    required: 'Total budget is required',
                    validate: (val) =>
                      val && Number(val) > 0 || 'Budget must be greater than 0',
                  }}
                  render={({ field, fieldState }) => (
                    <>
                      <input
                        type="text"
                        placeholder="Enter Total Budget"
                        value={field.value}
                        onChange={(e) => handleBudgetChange(e.target.value)}
                        className="border border-gray-500 rounded-lg py-2.5 px-3.5 placeholder-gray-400 bg-transparent text-white"
                      />
                      {fieldState.error && (
                        <p className="text-red-2 text-xs -mt-1">{fieldState.error.message}</p>
                      )}
                    </>
                  )}
                />
              </div>

              {/* Budget Breakdown */}
              {budget > 0 && (
                < div className="bg-primary rounded-2xl px-5 py-4 flex flex-col gap-6">
                  <div className='flex gap-2.5 items-center'>
                    <div className='bg-orange-1 text-orange w-9 h-9 flex items-center justify-center rounded-full pt-1'>
                      <i class="fi fi-rr-career-growth"></i>
                    </div>
                    <div className='flex flex-col gap-0.5'>
                      <div className="text-16-regular text-white font-medium">Budget Breakdown</div>
                      <div className="text-12-regular text-gray-300">Real-time calculation based on your inputs</div>
                    </div>
                  </div>

                  <div className="flex">
                    {/* Total Budget */}
                    <div className="flex flex-col items-start gap-3 w-1/2 p-3">
                      <div className='flex gap-3'>
                        <div
                          className="bg-[#4F98FA] px-2 py-1 pt-2 rounded-lg"
                          style={{
                            boxShadow: '0px 0px 8px 0px rgba(79, 152, 250, 0.50)',
                          }}
                        >
                          <i class="fi fi-ss-coins mt-1"></i>
                        </div>
                        <div className="text-12-medium text-gray-200 flex items-center">Total Budget</div>
                      </div>
                      <div>
                        <div className="text-lg text-white font-semibold">
                          ₹ {formatCurrency(budget)}
                        </div>
                      </div>
                    </div>

                    {/* Per Influencer */}
                    <div className="flex flex-col items-start gap-3 w-1/2 p-3">
                      <div className='flex gap-3'>
                        <div
                          className="bg-[#4F98FA] px-2 py-1 pt-2 rounded-lg"
                          style={{
                            boxShadow: '0px 0px 8px 0px rgba(79, 152, 250, 0.50)',
                          }}
                        >
                          <i class="fi fi-rr-following"></i>
                        </div>
                        <div className="text-12-medium text-gray-200 flex items-center">Per Influencer</div>
                      </div>
                      <div className="text-lg text-white font-semibold">
                        ₹ {creatorCount > 0 ? formatCurrency(Number(budget) / creatorCount) : '0'}
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Mentions */}
            <div className="flex flex-col gap-2.5 ">
              <label className="text-white text-sm">Mentions</label>
              <Controller
                name="mentions"
                control={control}
                defaultValue={[]}
                rules={{
                  required: 'At least one mention is required',
                  validate: (value) =>
                    Array.isArray(value) && value.length > 0 || 'At least one mention is required',
                }}
                render={({ field, fieldState }) => (
                  <>
                    <HashtagInput
                      value={field.value}
                      onChange={field.onChange}
                      placeholder="Enter meniton, separate with comma"
                      prefix='@'
                    />
                    {fieldState.error && (
                      <p className="text-red-2 text-xs -mt-2">{fieldState.error.message}</p>
                    )}
                  </>
                )}
              />
            </div>
          </div>
        </div>
      </div >
    </div>

  );
};

export default ContentScope;
