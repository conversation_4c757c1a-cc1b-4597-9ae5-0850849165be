import React, { useState, useEffect } from "react";
import BookmarkOutlineIcon from "@assets/icon/bookmark_border-white.svg";
import Megaphone from "@assets/icon/megaphone-blue.svg";
import AllInclusive from "@assets/icon/all_inclusive-blue.svg";
import DownIcon from '@assets/icon/down.svg';
import UpIcon from '@assets/icon/up.svg';
import CheckIcon from '@assets/icon/check.svg';

const CreatorLists = [
    { id: 2, name: "Fashion Campaign", isSelected: false },
    { id: 3, name: "Tech Influencers", isSelected: false },
    { id: 4, name: "Beauty Creators", isSelected: false },
    { id: 5, name: "Travel Content", isSelected: false },
    { id: 6, name: "Gaming Influencers", isSelected: false },
    { id: 7, name: "Fitness Experts", isSelected: false }
];

const CreatorSuggestionCard = ({
    name,
    badge,
    profileLogo,
    subscribers,
    tags,
    impressions,
    engagementRate,
    isSaved: initialSaved,
    platform,
    requirements,
    bgColor = "bg-white",
}) => {
    // All existing state
    const [hovered, setHovered] = useState(false);
    const [isSaved, setIsSaved] = useState(initialSaved);
    const [bookmarkHover, setBookmarkHover] = useState(false);
    const [showDropdown, setShowDropdown] = useState(false);
    const [DropdownHover, setDropdownHover] = useState(false);
    
    // Store original lists and working copy
    const [originalLists, setOriginalLists] = useState(CreatorLists);
    const [workingLists, setWorkingLists] = useState(CreatorLists);
    
    // New state for creating lists
    const [showCreateNew, setShowCreateNew] = useState(false);
    const [newListName, setNewListName] = useState("");

    // Reset working copy when dropdown opens
    useEffect(() => {
        if (showDropdown) {
            setWorkingLists([...originalLists]);
        }
    }, [showDropdown, originalLists]);

    // Function to toggle selection status with radio button behavior
    const toggleList = (id) => {
        setWorkingLists(workingLists.map(list => 
            // If this is the clicked list, toggle it
            // If it's any other list, ensure it's deselected
            list.id === id 
                ? { ...list, isSelected: !list.isSelected } 
                : { ...list, isSelected: false }
        ));
    };
    
    // Handle saving changes - commits working copy to original
    const handleSave = () => {
        // If creating a new list, add it to working copy first
        if (showCreateNew && newListName.trim() !== "") {
            const newList = {
                id: Date.now(),
                name: newListName.trim(),
                isSelected: true
            };
            setWorkingLists([...workingLists, newList]);
            setNewListName("");
            setShowCreateNew(false);
        }
        
        // Save working copy to original lists
        setOriginalLists([...workingLists]);
        
        // Update the saved status
        setIsSaved(workingLists.some(list => list.isSelected));
        setShowDropdown(false);
    };

    return (
        <div className={`flex flex-col flex-shrink-0 min-w-[270px] bg-gray-600`}>
            {/* Match Score */}
            <span className="text-12-semibold mb-[5px]" role="img" aria-label="target">{badge}</span>

            <div
                className={` flex flex-col relative justify-between rounded-[20px] border-1 border-gray-800 p-2 transition-all duration-500  shadow-md group h-[294px]`}
                onMouseEnter={() => setHovered(true)}
                onMouseLeave={() => setHovered(false)}
            >

                <div className={`flex flex-col ${bgColor} rounded-[12px] p-3.5 gap-3 ${hovered ? "p-3.5 pb-0" : "p-3.5"} `}>
                    {/* Card Content */}
                    <div className="flex justify-between gap-2">
                        <div className="flex items-center gap-3">
                            <img src={profileLogo} alt={name} className="w-[30px] rounded-full" />
                            <div className="flex flex-col">
                                <span className="font-bold text-18-medium text-black">{name}</span>
                                <span className="font-bold text-12-medium text-black">Subscribers: {subscribers}</span>
                            </div>
                        </div>
                        <div className="flex rounded-full">
                            {platform.map((img, idx) => (
                                <img
                                    key={idx}
                                    src={img.src}
                                    alt={img.alt || ""}
                                    className={`h-7 ${img.className || ""}`}
                                />
                            ))}
                        </div>
                    </div>

                    {/* Tags */}
                    <div
                        className={`
                                    overflow-hidden
                                    flex flex-wrap gap-2 gap-y-1
                                    ${hovered ? "animate-collapse" : "animate-expand"}
                                `}
                    >
                        {tags.map((d, i) => (
                            <span
                                key={i}
                                className="text-gray-500 rounded-full px-3 py-1 h-6 text-14-regular border-1 border-gray-200"
                            >
                                {d}
                            </span>
                        ))}
                    </div>


                </div>

                {/* View details button */}
                <div className="relative mb-15  gap-3 min-h-[140px]">
                    {/* Always render both blocks for smooth transitions */}

                    {/* Non-hovered view */}
                    <div
                        className={`absolute top-4 inset-0 transition-opacity duration-500 ease-in-out ${hovered ? "opacity-0 pointer-events-none" : "opacity-100"} `}
                    >
                        <div className="flex flex-row justify-between items-start text-14-regular text-white">
                            <div className="flex flex-col gap-1">
                                <div className="flex items-center gap-1">
                                    <img src={Megaphone} alt="" /> Average Impressions : {impressions} %
                                </div>
                                <div className="flex items-center gap-1">
                                    <img src={AllInclusive} alt="" /> Engagement: {engagementRate} % 📈
                                </div>
                            </div>


                        </div>
                    </div>

                    {/* Hovered view */}
                    <div
                        className={`absolute inset-0 transition-opacity duration-500 ease-in-out ${hovered ? "opacity-100" : "opacity-0 pointer-events-none"} `}
                    >
                        <div className="flex flex-col gap-1 text-14-regular text-white">
                            <span className="mb-2">
                                🎖️ {requirements.filter((r) => r.isChecked).length} out of {requirements.length} Criteria Matched
                            </span>

                            {requirements.map((item) => (
                                <span key={item.label}>
                                    {item.isChecked ? `✅ ${item.label}` : `❌ ${item.label}`}
                                </span>
                            ))}
                        </div>
                    </div>
                </div>
                {/* View Details button (always visible) */}
                <div className="absolute bottom-3.5 right-0 px-3.5 flex justify-between w-full">
                    <div className="relative">
                        {/* Button container */}
                        <div className="flex items-center bg-gray-600 border text-white p-2.5 rounded-md">
                            {/* Bookmark */}
                            <div
                                className={` cursor-pointer rounded-full transition-all  duration-200 p-1 ${bookmarkHover ? "bg-white/10" : ""}`}
                                onClick={() => setIsSaved(!isSaved)}
                                onMouseEnter={() => setBookmarkHover(true)}
                                onMouseLeave={() => setBookmarkHover(false)}
                            >
                                <img
                                    src={BookmarkOutlineIcon}
                                    className="w-5 h-5"
                                    alt="bookmark"
                                />
                            </div>

                            {/* Divider */}
                            <div className="w-px h-5 bg-gray-500 mx-2" />

                            {/* Dropdown arrow */}
                            <div
                                className={`flex cursor-pointer rounded-full transition-all  duration-200 p-1 ${DropdownHover ? "bg-white/10" : ""}`}
                                onClick={() => setIsSaved(!isSaved)}
                                onMouseEnter={() => setDropdownHover(true)}
                                onMouseLeave={() => setDropdownHover(false)}
                            >
                                <button onClick={() => setShowDropdown(!showDropdown)} className="cursor-pointer ">
                                    <img
                                        src={showDropdown ? UpIcon : DownIcon}
                                        className="w-5 h-5 opacity-70"
                                        alt="arrow" />
                                </button>
                            </div>
                        </div>

                        {/* Dropdown */}
                        {showDropdown && (
                            <div className="absolute left-0 mt-2 w-64 bg-gray-900 text-white rounded-md shadow-lg p-4 z-50"
                                onMouseLeave={() => setShowDropdown(false)}
                            >
                                <div className="flex justify-between items-center mb-4">
                                    <span className="text-14-regular">My Creators</span>
                                    <div className="w-4 h-4 bg-green-500 rounded-sm flex items-center justify-center">
                                        <img src={CheckIcon} alt="CheckIcon" />
                                    </div>
                                </div>
                                <hr className="border-gray-400 my-2" />

                                {/* Creator Lists from working copy */}
                                <div className="max-h-15 overflow-y-auto mb-1">
                                    {workingLists.map((list) => (
                                        <div
                                            key={list.id}
                                            className="flex justify-between items-center py-1.5 hover:bg-gray-700/30 rounded px-2 cursor-pointer"
                                            onClick={() => toggleList(list.id)}
                                        >
                                            <span className="text-14-regular text-gray-200">{list.name}</span>
                                            <div className={`w-4 h-4 rounded-sm flex items-center justify-center ${list.isSelected
                                                    ? "bg-green-500"
                                                    : "border border-gray-400"
                                                }`}>
                                                {list.isSelected && (
                                                    <img src={CheckIcon} alt="Selected" className="w-3 h-3" />
                                                )}
                                            </div>
                                        </div>
                                    ))}
                                </div>

                                {/* Create new list form */}
                                {showCreateNew ? (
                                    <div className="flex items-center mb-3">
                                        <input
                                            type="text"
                                            value={newListName}
                                            onChange={(e) => setNewListName(e.target.value)}
                                            onKeyPress={(e) => {
                                                if (e.key === "Enter") {
                                                    handleSave();
                                                }
                                            }}
                                            placeholder="Enter list name"
                                            className="w-full px-2 py-1 bg-gray-700 text-white rounded border border-gray-600 focus:outline-none focus:border-blue-400 text-sm"
                                            autoFocus
                                        />
                                    </div>
                                ) : (
                                    <button 
                                        className="flex items-center h-7 text-sm text-gray-300 hover:text-white mb-3 w-full hover:bg-gray-700/30 rounded px-2 py-1.5 transition-colors"
                                        onClick={() => setShowCreateNew(true)}
                                    >
                                        <span className="mr-2 text-xl">+</span> Create New
                                    </button>
                                )}

                                <div className="flex justify-end">
                                    <button 
                                        onClick={handleSave}
                                        className="bg-blue-100 text-black px-4 py-2 rounded-md text-sm hover:bg-blue-200"
                                    >
                                        Save
                                    </button>
                                </div>
                            </div>
                        )}
                    </div>
                    <button className="bg-brand-500 text-white text-16-semibold px-5 py-2 h-12 rounded-lg transition-all duration-200 hover:bg-brand-600">
                        View Profile
                    </button>
                </div>

            </div>
        </div>
    );
};

export default CreatorSuggestionCard;