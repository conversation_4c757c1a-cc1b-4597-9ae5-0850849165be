import { useDispatch } from 'react-redux';
import { 
  loginThunk,
  registerThunk,
  getCurrentUserThunk,
  logoutThunk,
  setCredentials,
  clearAuth,
  clearError,
} from './authSlice';

/**
 * Custom hook for using auth-related actions in components
 * This provides a clean interface for components to interact with auth state
 */
const useAuthActions = () => {
  const dispatch = useDispatch();

  return {    // Async operations with API
    login: (credentials) => dispatch(loginThunk(credentials)),
    register: (userData) => dispatch(registerThunk(userData)),
    fetchCurrentUser: () => dispatch(getCurrentUserThunk()),
    logout: () => dispatch(logoutThunk()),
    
    // Synchronous state operations
    setCredentials: (data) => dispatch(setCredentials(data)),
    clearAuth: () => dispatch(clearAuth()),
    clearAuthError: () => dispatch(clearError()),
  };
};

export default useAuthActions;