import React, { useState, useEffect, useRef } from 'react';
import { useDispatch } from 'react-redux';
import { Toast } from '@shared/components/UI/Toast';
import { verifyOtpThunk, resendOtpThunk, brandVerifyOtpThunk } from '../service/authThunks';
import useRoles from '../../../shared/hooks/useRoles';

/**
 * OTP Verification Dialog component
 * @param {Object} props - Component props
 * @param {string} props.email - Email address to which <PERSON><PERSON> was sent
 * @param {string} props.type - Type of authentication ("signup" or "login")
 * @param {boolean} props.isOpen - Whether the dialog is open
 * @param {Function} props.onClose - Function to call when dialog is closed
 * @param {Function} props.onVerified - Function to call when OTP is verified
 * @param {Function} props.onUpdateEmail - Function to call when user wants to update email
 */
const OtpVerificationDialog = ({
    email,
    isOpen,
    onClose,
    type,
    isBrand = false,
    onVerified,
    onUpdateEmail
}) => {
    const [otp, setOtp] = useState(['', '', '', '']);
    const [isLoading, setIsLoading] = useState(false);
    const [errorMessage, setErrorMessage] = useState("");
    const [resendTimer, setResendTimer] = useState(40); // ⬅️ 40 sec resend timer
    const [otpValidityTimer, setOtpValidityTimer] = useState(120); // ⬅️ 2 min OTP validity

    const inputRefs = useRef([useRef(null), useRef(null), useRef(null), useRef(null)]);
    const { getRoleIdByName } = useRoles();
    const RoleId = getRoleIdByName(isBrand ? 'brand' : 'influencer');

    const formatTime = (seconds) => {
        const m = String(Math.floor(seconds / 60)).padStart(2, '0');
        const s = String(seconds % 60).padStart(2, '0');
        return `${m}:${s}`;
    };

    useEffect(() => {
        if (isOpen && inputRefs.current[0].current) {
            inputRefs.current[0].current.focus();
        }
    }, [isOpen]);

    useEffect(() => {
        if (isOpen) {
            setResendTimer(40);
            setOtpValidityTimer(120);
            setOtp(['', '', '', '']);
        }
    }, [isOpen]);

    useEffect(() => {
        if (!isOpen) return;

        const resendInterval = setInterval(() => {
            setResendTimer(prev => {
                if (prev <= 1) {
                    clearInterval(resendInterval);
                    return 0;
                }
                return prev - 1;
            });
        }, 1000);

        const otpValidityInterval = setInterval(() => {
            setOtpValidityTimer(prev => {
                if (prev <= 1) {
                    clearInterval(otpValidityInterval);
                    onClose(); // ⬅️ Automatically close dialog
                    return 0;
                }
                return prev - 1;
            });
        }, 1000);

        return () => {
            clearInterval(resendInterval);
            clearInterval(otpValidityInterval);
        };
    }, [isOpen, resendTimer, onClose]);


    const handleInputChange = (index, value) => {
        if (!/^\d*$/.test(value)) return;

        const newOtp = [...otp];
        newOtp[index] = value.slice(0, 1);
        setOtp(newOtp);

        if (value && index < 3) {
            inputRefs.current[index + 1].current.focus();
        }
    };

    const handleKeyDown = (index, e) => {
        if (e.key === 'Backspace') {
            if (!otp[index] && index > 0) {
                inputRefs.current[index - 1].current.focus();
                const newOtp = [...otp];
                newOtp[index - 1] = '';
                setOtp(newOtp);
            }
        }
    };

    const handlePaste = (e) => {
        e.preventDefault();
        const pastedData = e.clipboardData.getData('text').trim();

        if (/^\d{4}$/.test(pastedData)) {
            const digits = pastedData.split('');
            setOtp(digits);
            inputRefs.current[3].current.focus();
        }
    };

    const dispatch = useDispatch();

    const handleSubmit = async () => {
        const otpCode = otp.join('');
        console.log('Submitting OTP:', otpCode);

        if (otpCode.length !== 4) {
            Toast.error('Please enter the complete 4-digit OTP');
            return;
        }

        setIsLoading(true);

        if (isBrand) {
            if (type === 'signup') {
                try {
                    const result = await dispatch(brandVerifyOtpThunk({
                        email: email,
                        otp: otpCode,
                        role_uuid: RoleId,
                        flow: type
                    }));

                    console.log('OTP verification result:', result);

                    if (result.meta.requestStatus === 'fulfilled') {
                        const responseData = result.payload.data;
                        console.log('Authentication successful:', {
                            userId: responseData.access_token,
                            tokenExists: !!responseData.token
                        });
                        onVerified(responseData);
                        onClose();
                    } else {
                        const errorMessage = result?.payload?.message || 'OTP verification failed. Please try again.';
                        // Toast.error(errorMessage);
                        setErrorMessage(errorMessage);
                    }
                } catch (error) {
                    console.error('OTP verification error:', error);
                    // Toast.error(error.message || 'Failed to verify OTP. Please check your code and try again.');
                    setErrorMessage(error.message || 'Failed to verify OTP. Please check your code and try again.');
                } finally {
                    setIsLoading(false);
                    setOtp(['', '', '', '']);
                }
            } else {
                try {
                    const result = await dispatch(brandVerifyOtpThunk({
                        email: email,
                        otp: otpCode,
                        flow: type
                    }));

                    console.log('OTP verification result:', result);

                    if (result.meta.requestStatus === 'fulfilled') {
                        const responseData = result.payload.data;
                        console.log('Authentication successful:', {
                            userId: responseData.access_token,
                        });
                        onVerified(responseData);
                        onClose();
                    } else {
                        const errorMessage = result?.payload?.message || 'OTP verification failed. Please try again.';
                        // Toast.error(errorMessage);
                        setErrorMessage(errorMessage);
                    }
                } catch (error) {
                    console.error('OTP verification error:', error);
                    // Toast.error(error.message || 'Failed to verify OTP. Please check your code and try again.');
                    setErrorMessage(error.message || 'Failed to verify OTP. Please check your code and try again.');
                } finally {
                    setIsLoading(false);
                    setOtp(['', '', '', '']);
                    // onClose();
                }
            }
        } else {
            if (type === 'signup') {
                try {
                    const result = await dispatch(verifyOtpThunk({
                        email: email,
                        otp: otpCode,
                        role_uuid: RoleId,
                        flow: type
                    }));

                    console.log('OTP verification result:', result);

                    if (result.meta.requestStatus === 'fulfilled') {
                        const responseData = result.payload.data;
                        console.log('Authentication successful:', {
                            userId: responseData.access_token,
                            tokenExists: !!responseData.token
                        });
                        onVerified();
                        onClose();
                    } else {
                        const errorMessage = result?.payload?.message || 'OTP verification failed. Please try again.';
                        // Toast.error(errorMessage);
                        setErrorMessage(errorMessage);
                    }
                } catch (error) {
                    console.error('OTP verification error:', error);
                    // Toast.error(error.message || 'Failed to verify OTP. Please check your code and try again.');
                    setErrorMessage(error.message || 'Failed to verify OTP. Please check your code and try again.');
                } finally {
                    setIsLoading(false);
                    setOtp(['', '', '', '']);
                }
            } else {
                try {
                    const result = await dispatch(verifyOtpThunk({
                        email: email,
                        otp: otpCode,
                        flow: type
                    }));

                    console.log('OTP verification result:', result);

                    if (result.meta.requestStatus === 'fulfilled') {
                        const responseData = result.payload.data;
                        console.log('Authentication successful:', {
                            userId: responseData.access_token,
                        });
                        onVerified(responseData);
                        onClose();
                    } else {
                        const errorMessage = result?.payload?.message || 'OTP verification failed. Please try again.';
                        // Toast.error(errorMessage);
                        setErrorMessage(errorMessage);
                    }
                } catch (error) {
                    console.error('OTP verification error:', error);
                    // Toast.error(error.message || 'Failed to verify OTP. Please check your code and try again.');
                    setErrorMessage(error.message || 'Failed to verify OTP. Please check your code and try again.');
                } finally {
                    setIsLoading(false);
                    setOtp(['', '', '', '']);
                }
            }
        }
    };

    const handleResendOtp = async () => {
        try {
            setIsLoading(true);
            setResendTimer(40);
            setOtpValidityTimer(120);

            const result = await dispatch(resendOtpThunk({ email: email }));
            console.log('OTP resend result:', result);

            if (result.meta.requestStatus === 'fulfilled') {
                const responseData = result.payload;
                if (responseData && responseData.success) {
                    Toast.success('New OTP sent to your email');
                } else {
                    Toast.warning(responseData.message || 'Could not send new OTP');
                }
            } else {
                Toast.warning(result.payload || 'Could not send new OTP');
            }
        } catch (error) {
            console.error('Resend OTP error:', error);
            Toast.error(error.message || 'Failed to resend OTP');
        } finally {
            setIsLoading(false);
            setOtp(['', '', '', '']);
        }
    };

    const handleUpdateEmail = async () => {
        try {
            setOtp(['', '', '', '']);
            onUpdateEmail();
        } catch (error) {
            Toast.error(error.message || 'Failed to resend OTP');
        }
    };

    if (!isOpen) return null;

    return (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/80 p-4">
            <div className="flex flex-col gap-4.5 bg-[#212121] rounded-xl max-w-md w-full p-8 shadow-lg">
                <div className="flex flex-col gap-0 items-start text-left ">
                    <h2 className="text-18-medium text-gray-50">
                        We've sent a code to
                    </h2>
                    <span className="text-[#47C8EC]"> 📩 {email}</span> 
                </div>

                <div className='flex flex-col text-left'>
                    <p className="text-14-medium text-white mb-2">
                        Enter the OTP below to keep things moving
                    </p>
                    <div className="flex justify-between gap-x-1">
                        {otp.map((digit, index) => (
                            <input
                                key={index}
                                ref={inputRefs.current[index]}
                                type="text"
                                value={digit}
                                placeholder='0'
                                onChange={(e) => handleInputChange(index, e.target.value)}
                                onKeyDown={(e) => handleKeyDown(index, e)}
                                onPaste={index === 0 ? handlePaste : undefined}
                                className="w-22 h-12 text-center text-white bg-[#212121] border border-gray-800 rounded-lg text-20-semibold"
                                disabled={isLoading}
                                maxLength={1}
                            />
                        ))}
                    </div>
                </div>
                {
                    errorMessage !== "" && (
                        <p className="text-red-2 text-sm -mt-4">{errorMessage}</p>
                    )
                }
                <div className="text-right flex justify-between items-center">
                    <p className="text-14-regular text-gray-300">
                        OTP valid for: <span className="text-white">{formatTime(otpValidityTimer)}</span>
                    </p>
                    <button
                        onClick={handleResendOtp}
                        className={`text-14-regular ml-auto ${resendTimer > 0
                            ? 'text-gray-400 cursor-not-allowed'
                            : 'text-brand-500 hover:underline cursor-pointer'
                            }`}
                        disabled={isLoading || resendTimer > 0}
                    >
                        {resendTimer > 0 ? `Resend OTP in ${resendTimer}s` : 'Resend OTP'}
                    </button>
                </div>

                <p className="hidden text-left text-gray-200 mb-1">
                    Hang tight — we can't wait to have you on board!<br />
                    Build your audience. Land collabs. Let's go! <span role="img" aria-label="Sparkle">💫</span>
                </p>

                <div className="flex space-x-4 ">
                    <button
                        onClick={handleUpdateEmail}
                        className="flex-1 py-3 bg-transparent border border-[#333333] cursor-pointer text-gray-200 rounded-lg hover:text-white hover:bg-gray-900/50 hover:border-white hover:border-1 transition-colors"
                        disabled={isLoading}
                    >
                        Update Email
                    </button>
                    <button
                        onClick={handleSubmit}
                        // className="flex-1 py-3 px-7 bg-brand-500 text-white cursor-pointer rounded-lg hover:bg-brand-600 transition-colors"
                        className={`flex-1 py-3 px-7 rounded-lg transition-colors 
                            ${isLoading || otp.join('').length !== 4
                                ? 'bg-gray-500 text-gray-300 cursor-not-allowed'
                                : 'bg-brand-500 text-white hover:bg-brand-600 cursor-pointer'}
                        `}
                        disabled={isLoading || otp.join('').length !== 4}
                    >
                        Done!
                    </button>
                </div>
            </div>
        </div>
    );
};

export default OtpVerificationDialog;
