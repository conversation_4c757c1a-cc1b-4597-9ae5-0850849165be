import { defineConfig } from 'vite'
import tailwindcss from '@tailwindcss/vite'
import react from '@vitejs/plugin-react'
import path from 'path'
import { fileURLToPath } from 'url'
import { compression } from 'vite-plugin-compression2'; // Import the compression plugin


const __dirname = path.dirname(fileURLToPath(import.meta.url))

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    react(), 
    tailwindcss(),
    // Add compression plugins here
    compression({
      algorithm: 'gzip', // Generate .gz files
      threshold: 10240, // Only compress files larger than 10KB
      verbose: true,   // Optional: Log compression results
    }),
    compression({
      algorithm: 'brotliCompress', // Generate .br files
      threshold: 10240, // Only compress files larger than 10KB
      verbose: true,   // Optional: Log compression results
    }),
  ],
  resolve: {
    alias: {
      react: path.resolve('./node_modules/react'),
      'react-dom': path.resolve('./node_modules/react-dom'),
      "@": path.resolve(__dirname, "./src"),
      "@assets": path.resolve(__dirname, "./src/assets"),
      "@shared": path.resolve(__dirname, "./src/shared"),
      "@auth": path.resolve(__dirname, "./src/features/auth"),
      "@influencer": path.resolve(__dirname, "./src/features/influencer"),
      "@brand": path.resolve(__dirname, "./src/features/brand"),
      "@authApiService": path.resolve(__dirname, "./src/app/store/api/instances/authInstance.js"),
      "@discoveryApiService": path.resolve(__dirname, "./src/app/store/api/instances/discoveryInstance.js"),
    },
  },
  build: {
    minify: 'esbuild',
    reportCompressedSize: true,
  },
})
