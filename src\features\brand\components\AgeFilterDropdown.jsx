import React, { useState, useRef, useEffect } from 'react';
import DownIcon from '@assets/icon/down.svg';
import CalendarIcon from '@assets/icon/calendar.svg';

const AgeFilterDropdown = ({ onSelect, selectedAges = [] }) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef(null);

  // Define age range options as per design
  const ageOptions = [
    { label: "Teen", value: "teen", range: "13-19 Years" },
    { label: "Young Adult", value: "young-adult", range: "20-35 Years" },
    { label: "Adult", value: "adult", range: "36-55 Years" },
    { label: "Senior Citizen", value: "senior", range: "56+ Years" }
  ];

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const toggleDropdown = () => {
    setIsOpen(!isOpen);
  };

  const handleOptionSelect = (value) => {
    onSelect(value);
    // Don't close dropdown when selecting options to allow multiple selections
  };

  // Display selected count if any options are selected
  const selectedCount = selectedAges.length;
  const displaySelectedCount = selectedCount > 0 ? `(${selectedCount})` : '';

  return (
    <div className="relative" ref={dropdownRef}>
      <button
        className={`px-4 py-2.5 ${isOpen ? 'bg-gray-700' : 'bg-transparent'} border border-gray-500 rounded-lg flex items-center gap-1.5 text-sm text-gray-50 hover:text-gray-300 transition-colors cursor-pointer`}
        onClick={toggleDropdown}
      >
        <img src={CalendarIcon} alt="Age" className="w-3.5 h-3.5" />
        Audience Age {displaySelectedCount}
        <img
          src={DownIcon}
          alt="Dropdown"
          className={`w-3.5 h-3.5 ml-0.5 transition-transform duration-200 ${isOpen ? 'transform rotate-180' : ''}`}
        />
      </button>
      
      {isOpen && (
        <div className="absolute mt-1 left-0 w-64 bg-gray-900 border border-gray-800 rounded-md shadow-lg z-50 py-2">
          <div className="px-4 pb-2">
            <h3 className="text-white text-lg font-medium mb-2">Age</h3>
            
            {/* Min/Max Select Dropdowns - Not implemented in this version */}
            <div className="grid grid-cols-2 gap-2 mb-4">
              <div>
                <span className="text-gray-400 text-sm block mb-1">Min</span>
                <div className="bg-gray-800 border border-gray-700 rounded px-3 py-2 text-white flex justify-between items-center">
                  <span>Select</span>
                  <img src={DownIcon} alt="Down" className="w-3.5 h-3.5" />
                </div>
              </div>
              <div>
                <span className="text-gray-400 text-sm block mb-1">Max</span>
                <div className="bg-gray-800 border border-gray-700 rounded px-3 py-2 text-white flex justify-between items-center">
                  <span>Select</span>
                  <img src={DownIcon} alt="Down" className="w-3.5 h-3.5" />
                </div>
              </div>
            </div>

            {/* Age Range Options */}
            <div className="space-y-2">
              {ageOptions.map((option) => (
                <div
                  key={option.value}
                  className="flex items-center justify-between py-1.5 cursor-pointer"
                  onClick={() => handleOptionSelect(option.value)}
                >
                  <div className="flex items-center">
                    <div 
                      className={`w-5 h-5 rounded border ${
                        selectedAges.includes(option.value)
                          ? 'bg-blue-500 border-blue-500' 
                          : 'bg-gray-700 border-gray-600'
                      } flex items-center justify-center mr-2`}
                    >
                      {selectedAges.includes(option.value) && (
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5 text-white" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                      )}
                    </div>
                    <span className="text-white text-sm">{option.label}</span>
                  </div>
                  <span className="text-gray-400 text-sm">{option.range}</span>
                </div>
              ))}
            </div>
          </div>

          <div className="border-t border-gray-800 p-3 flex justify-between">
            <button 
              className="text-sm text-gray-400 hover:text-white"
              onClick={() => onSelect([])}>
              Clear
            </button>
            <button 
              className="text-sm text-blue-500 hover:text-blue-400"
              onClick={() => setIsOpen(false)}>
              Apply
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default AgeFilterDropdown;