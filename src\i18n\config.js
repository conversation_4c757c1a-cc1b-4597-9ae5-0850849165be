// src/i18n/config.js
import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';

import en from './language/en/translation.json';
import fr from './language/fr/translation.json';
import th from './language/th/translation.json'; // Import your Thai translation file

i18n
  .use(initReactI18next) // passes i18n to react-i18next
  .init({
    resources: {
      en: { translation: en },
      fr: { translation: fr },
      th: { translation: th }, // Add Thai resources here
    },
    lng: 'en', // default language
    fallbackLng: 'en', // Fallback to English if Thai translations are missing for a key
    interpolation: {
      escapeValue: false, // react already protects from xss
    },
  });

export default i18n;