// src/app/router.jsx
import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';

import CreatorAuthPage from '@auth/pages/CreatorAuth';
import RegisterPage from '@auth/pages/RegisterPage';
import SocialSignupPage from '@auth/pages/SocialSignupPage';
import SocialSignupFollowupPage from '@auth/pages/SocialSignupFollowupPage';
import DashboardHome from '@influencer/pages/CreatorDashboard';
import ProtectedRoute from '../shared/components/ProtectedRoute';
import PublicRoute from '../shared/components/PublicRoute';
import BrandAuthPage from '@auth/pages/BrandAuthPage';
import BrandSelectionPage from '@auth/pages/BrandSelectionPage';
import ProfileOnboarding from '@auth/pages/ProfileOnboarding';
import ProfileAnalyticsPage from '@influencer/pages/ProfileAnalyticsPage';
import Discovery from '@brand/pages/Discovery';
import BrandDashboard from '../features/brand/pages/BrandDashboard';
import ManageCreator from '../features/brand/pages/ManageCreator';
import LaunchScreen from '../features/auth/pages/LaunchScreen';
import DashboardLayout from '../shared/layout/DashboardLayout';
import RouteProtectionExample from '../shared/components/examples/RouteProtectionExample';
import CampaignManagement from '@brand/pages/CampaignManagement';
import MyContent from '../features/influencer/pages/MyContent';
import ContentCreations from '../features/influencer/pages/ContentCreations';
import CreatorListPage from '../features/brand/pages/CreatorListPage';
import CampaignPage from '../features/brand/pages/CampaignPage';
import CreatorsTab from '../features/brand/pages/campaign/CreatorsTab';
import ContentTab from '../features/brand/pages/campaign/ContentTab';
import ConversationTab from '../features/brand/pages/campaign/ConversationTab';
import AnalyticsTab from '../features/brand/pages/campaign/AnalyticsTab';
import DetailsTab from '../features/brand/pages/campaign/DetailsTab';
import CreateCampaign from '../features/brand/pages/CreateCampaign';


const Router = () => {
    return (
        <Routes>
            {/* Public Routes - Redirect authenticated users to dashboard */}
            <Route path="/influencer/:authType" element={
                <PublicRoute userType="influencer">
                    <CreatorAuthPage />
                </PublicRoute>
            } />
            <Route path="/brand/:authType" element={
                <PublicRoute userType="brand">
                    <BrandAuthPage />
                </PublicRoute>
            } />
            <Route path="/socialsignup" element={
                <PublicRoute>
                    <SocialSignupPage />
                </PublicRoute>
            } />
            <Route path="/socialsignup/profiles" element={
                <PublicRoute>
                    <SocialSignupPage />
                </PublicRoute>
            } />
            <Route path="/social-signup-followup/:platform" element={
                <PublicRoute>
                    <SocialSignupFollowupPage />
                </PublicRoute>
            } />
            <Route path="/influencer-signup/:version?" element={
                <PublicRoute userType="influencer">
                    <RegisterPage />
                </PublicRoute>
            } />
            <Route path="/profile-onboarding" element={
                <PublicRoute>
                    <ProfileOnboarding />
                </PublicRoute>
            } />
            <Route path="/brand/brand-selection/:organisationName" element={
                <PublicRoute userType="brand">
                    <BrandSelectionPage />
                </PublicRoute>
            } />
            <Route path="/brand/brand-selection/:organisationName/brands" element={
                // <PublicRoute userType="brand">
                <BrandSelectionPage />
                // </PublicRoute>
            } />
            {/* <Route path="/brand/brand-selection/:organisationName/onboarding" element={<BrandSelectionPage />} /> */}


            {/* Influencer Section */}
            <Route
                path="/influencer"
                element={
                    // <ProtectedRoute userType="influencer">
                    <DashboardLayout type="influencer" />
                    // </ProtectedRoute>
                }
            >
                <Route path="dashboard" element={<DashboardHome />} />
                <Route path="profile-analytics" element={<ProfileAnalyticsPage />} />
                <Route path="my-content" element={<MyContent />}>
                    <Route path="create-post" element={<ContentCreations />} />
                </Route>
            </Route>

            {/* Brand Section */}
            <Route
                path="/brand"
                element={
                    // <ProtectedRoute userType="brand">
                    <DashboardLayout type="brand" />
                    // </ProtectedRoute>
                }
            >
                <Route index element={<Navigate to="dashboard" replace />} />
                <Route path="dashboard" element={<BrandDashboard />} />
                <Route path="manage-creator" element={<ManageCreator />}>
                    <Route path=":listId" element={<CreatorListPage />} />
                </Route>
                <Route path="discovery" element={<Discovery />} />
                <Route path="discovery/:profileId" element={<ProfileAnalyticsPage />} />
                <Route path="campaigns" element={<CampaignManagement />} >
                    <Route path=":campaignId" element={<CampaignPage />} >
                        <Route index element={<Navigate to="creators" replace />} />
                        <Route path="creators" element={<CreatorsTab />} />
                        <Route path="content" element={<ContentTab />} />
                        <Route path="conversation" element={<ConversationTab />} />
                        <Route path="analytics" element={<AnalyticsTab />} />
                        <Route path="brief" element={<DetailsTab />} />
                    </Route>
                    <Route path="create" element={<CreateCampaign />} />
                </Route>
            </Route>



            {/* <Route path="/brand-signup/" element={<BrandSignUp />} /> */}
            <Route path="/example" element={<LaunchScreen />} />
            <Route path="/example-route" element={<RouteProtectionExample />} />

            {/* Protected Route */}
            {/* <Route
                path="/influencer/dashboard"
                element={
                    <ProtectedRoute>
                        <DashboardHome />
                    </ProtectedRoute> 
                }
            /> */}

            {/* Root redirect */}
            <Route path="/" element={<Navigate to="/influencer/login" replace />} />

            {/* Redirect unknown paths */}
            {/* <Route path="*" element={<Navigate to="/influencer/login" replace />} /> */}
        </Routes>
    );
};

export default Router;
