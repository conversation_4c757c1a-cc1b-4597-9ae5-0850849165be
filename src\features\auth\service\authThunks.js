// Auth async thunks for API operations
import { createAsyncThunk } from '@reduxjs/toolkit';
import authApi from '../../../app/store/api/authApi';
import brandManagementApi from '../../../app/store/api/brandManagementApi';

/**
 * Login user async thunk
 */
export const loginThunk = createAsyncThunk(
    'auth/login',
    async (credentials, { rejectWithValue }) => {
        try {
            const response = await authApi.login(credentials).send();
            if (response.status === 200 && response.data) {
                return {
                    message: response.data.data?.message || 'OTP Sent successfully',
                    success: response.status === 200,
                    data: response.data.data || null
                };
            } else if (response.status === 429) {
                return {
                    message: response.data?.message || 'Too Many Requests',
                    success: false,
                    data: null
                };
            }
            else {
                return {
                    message: response.data?.message || 'OTP sending failed',
                    success: false,
                    data: null
                };
            }
        } catch (error) {
            return rejectWithValue(
                error.response?.data?.message || 'Login failed. Please try again.'
            );
        }
    }
);

/**
 * Resend OTP async thunk
 */
export const resendOtpThunk = createAsyncThunk(
    'auth/resendOtp',
    async (data, { rejectWithValue }) => {
        try {
            const response = await authApi.resendOtp(data).send();
            if (response.status === 200 && response.data) {
                return {
                    message: response.data.data?.message || 'OTP Sent successfully',
                    success: response.status === 200,
                    data: response.data.data || null
                };
            } else if (response.status === 429) {
                return {
                    message: response.data?.message || 'Too Many Requests',
                    success: false,
                    data: null
                };
            }
            else {
                return {
                    message: response.data?.message || 'OTP Sending failed',
                    success: false,
                    data: null
                };
            }
        } catch (error) {
            return rejectWithValue(
                error.response?.data?.message || 'Registration failed. Please try again.'
            );
        }
    }
);

/**
 * Register user async thunk
 */
export const registerThunk = createAsyncThunk(
    'auth/register',
    async (userData, { rejectWithValue }) => {
        try {
            const response = await authApi.register(userData).send();
            if (response.status === 200 && response.data) {
                return {
                    message: response.data.data?.message || 'OTP Sent successfully',
                    success: response.status === 200,
                    data: response.data.data || null
                };
            } else if (response.status === 429) {
                return {
                    message: response.data?.message || 'Too Many Requests',
                    success: false,
                    data: null
                };
            } else if (response.status === 409) {
                return {
                    message: 'User already registered. Please sign in.',
                    success: false,
                    data: null
                };
            }
            else {
                return {
                    message: response.data?.message || 'OTP Sending failed',
                    success: false,
                    data: null
                };
            }
        } catch (error) {
            return rejectWithValue(
                error.response?.data?.message || 'Registration failed. Please try again.'
            );
        }
    }
);

/**
 * Get user profile async thunk
 */
export const getCurrentUserThunk = createAsyncThunk(
    'auth/getCurrentUser',
    async (_, { rejectWithValue }) => {
        try {
            const response = await authApi.getCurrentUser().send();
            return response;
        } catch (error) {
            return rejectWithValue('Failed to fetch user data' + error.message
            );
        }
    }
);

/**
 * Logout user async thunk
 */
export const logoutThunk = createAsyncThunk(
    'auth/logout',
    async (_, { rejectWithValue, dispatch }) => {
        try {
            // Get session_id from localStorage if available
            const sessionId = localStorage.getItem('session_id') || 'default_session';

            // Call logout API with session_id
            var response  = await authApi.logout({ session_id: sessionId }).send();
            console.log('Logout response:', response);

            // Clear all localStorage data
            const localStorageKeys = [
                'auth_token',
                'refresh_token',
                'user',
                'allocatedBrands',
                'organizationBrands',
                'organisationBrands', // Handle both spellings
                'organizationId',
                'organisationId', // Handle both spellings
                'socialProfiles',
                'session_id',
                'globalFilters'
            ];

            localStorageKeys.forEach(key => {
                localStorage.removeItem(key);
            });

            // Clear all sessionStorage data
            sessionStorage.clear();

            // Import and dispatch clear actions for other slices
            const { clearAllBrandState } = await import('../../brand/services/brandSlice');
            const { clearAllInfluencerState } = await import('../../influencer/services/influencerSlice');

            dispatch(clearAllBrandState());
            dispatch(clearAllInfluencerState());

            return {
                message: 'Logged out successfully',
                success: true
            };
        } catch (error) {
            // Even if API logout fails, we still clear local data
            const localStorageKeys = [
                'auth_token',
                'refresh_token',
                'user',
                'allocatedBrands',
                'organizationBrands',
                'organisationBrands',
                'organizationId',
                'organisationId',
                'socialProfiles',
                'session_id',
                'globalFilters'
            ];

            localStorageKeys.forEach(key => {
                localStorage.removeItem(key);
            });

            sessionStorage.clear();

            // Still dispatch clear actions even on API failure
            try {
                const { clearAllBrandState } = await import('../../brand/services/brandSlice');
                const { clearAllInfluencerState } = await import('../../influencer/services/influencerSlice');

                dispatch(clearAllBrandState());
                dispatch(clearAllInfluencerState());
            } catch (importError) {
                console.warn('Failed to clear slice states:', importError);
            }

            return rejectWithValue({
                message: error.response?.data?.message || 'Logout failed on server, but local data cleared',
                success: false
            });
        }
    }
);

/**
 * Verify OTP and complete registration process async thunk
 */

export const verifyOtpThunk = createAsyncThunk(
    'auth/verifyOtp',
    async (otpData, { rejectWithValue }) => {
        try {
            const { flow, ...rest } = otpData;

            // Choose the correct API based on flow
            let response;
            if (flow === 'signup') {
                response = await authApi.verifyOtp(rest).send();
            } else if (flow === 'login') {
                response = await authApi.loginVerifyOtp(rest).send();
            } else {
                return rejectWithValue({ message: 'Invalid OTP flow', success: false });
            }

            if (response.status === 200 && response.data.data) {
                const { access_token, refresh_token, user } = response.data.data;

                // Store tokens
                localStorage.setItem('auth_token', access_token);
                localStorage.setItem('refresh_token', refresh_token);
                localStorage.setItem('user', JSON.stringify(user));

                return {
                    message: response.data.data?.message || 'OTP verified successfully',
                    success: true,
                    data: response.data.data,
                    flow,
                };
            } else {
                return rejectWithValue({
                    message: response.data?.message || 'OTP verification failed',
                    success: false,
                    flow,
                });
            }
        } catch (error) {
            return rejectWithValue({
                message: 'Failed to verify OTP: ' + error.message,
                success: false,
                flow: otpData.flow,
            });
        }
    }
);



//Brand related thunks

/**
 * Brand register async thunk
 */
export const brandRegisterThunk = createAsyncThunk(
    'auth/brandRegister',
    async (brandData, { rejectWithValue }) => {
        try {
            const response = await authApi.brandRegister(brandData).send();
            if (response.status === 200 && response.data) {
                return {
                    message: response.data.data?.message || 'Brand registered successfully',
                    success: response.status === 200,
                    data: response.data.data || null
                };
            } else if (response.status === 429) {
                return {
                    message: response.data?.message || 'Too Many Requests',
                    success: false,
                    data: null
                };
            }
            else if (response.status === 409) {
                return {
                    message: 'User already registered. Please sign in.',
                    success: false,
                    data: null
                };
            }
            else {
                return {
                    message: response.data?.message || 'OTP sending failed',
                    success: false,
                    data: null
                };
            }
        } catch (error) {
            return rejectWithValue(
                error.response?.data?.message || 'Login failed. Please try again.'
            );
        }
    }
);

/**
 * Verify OTP and complete registration process async thunk
 */

export const brandVerifyOtpThunk = createAsyncThunk(
    'auth/brandVerifyOtp',
    async (otpData, { rejectWithValue }) => {
        try {
            const { flow, ...rest } = otpData;

            // Choose the correct API based on flow
            let response;
            if (flow === 'signup') {
                response = await authApi.brandVerifyOtp(rest).send();
            } else if (flow === 'login') {
                response = await authApi.brandLoginVerifyOtp(rest).send();
            } else {
                return rejectWithValue({ message: 'Invalid OTP flow', success: false });
            }

            if (response.status === 200 && response.data.data) {
                const { access_token, allocated_brands, refresh_token, user, organization_id, organization_brands } = response.data.data;

                // Store tokens
                localStorage.setItem('auth_token', access_token);
                localStorage.setItem('refresh_token', refresh_token);
                localStorage.setItem('user', JSON.stringify(user));
                localStorage.setItem('organisationBrands', JSON.stringify(organization_brands));
                localStorage.setItem('allocatedBrands', JSON.stringify(allocated_brands));
                localStorage.setItem('organisationId', JSON.stringify(organization_id));

                return {
                    message: response.data.data?.message || 'OTP verified successfully',
                    success: true,
                    data: response.data.data,
                    flow,
                };
            } else {
                return rejectWithValue({
                    message: response.data?.message || 'OTP verification failed',
                    success: false,
                    flow,
                });
            }
        } catch (error) {
            return rejectWithValue({
                message: 'Failed to verify OTP: ' + error.message,
                success: false,
                flow: otpData.flow,
            });
        }
    }
);

/**
 * Resend OTP async thunk
 */
export const brandResendOtpThunk = createAsyncThunk(
    'auth/brandResendOtp',
    async (data, { rejectWithValue }) => {
        try {
            const response = await authApi.brandResendOtp(data).send();
            if (response.status === 200 && response.data) {
                return {
                    message: response.data.data?.message || 'OTP Sent successfully',
                    success: response.status === 200,
                    data: response.data.data || null
                };
            } else if (response.status === 429) {
                return {
                    message: response.data?.message || 'Too Many Requests',
                    success: false,
                    data: null
                };
            }
            else {
                return {
                    message: response.data?.message || 'OTP Sending failed',
                    success: false,
                    data: null
                };
            }
        } catch (error) {
            return rejectWithValue(
                error.response?.data?.message || 'Registration failed. Please try again.'
            );
        }
    }
);

/**
 * Login user async thunk
 */
export const brandLoginThunk = createAsyncThunk(
    'auth/brand/login',
    async (credentials, { rejectWithValue }) => {
        try {
            const response = await authApi.brandLogin(credentials).send();
            if (response.status === 200 && response.data) {
                return {
                    message: response.data.data?.message || 'OTP Sent successfully',
                    success: response.status === 200,
                    data: response.data.data || null
                };
            } else if (response.status === 429) {
                return {
                    message: response.data?.message || 'Too Many Requests',
                    success: false,
                    data: null
                };
            }
            else if (response.status === 500) {
                return {
                    message: 'Internal Server Error',
                    success: false,
                    data: null
                };
            }
            else {
                return {
                    message: response.data?.message || 'OTP sending failed',
                    success: false,
                    data: null
                };
            }
        } catch (error) {
            return rejectWithValue(
                error.response?.data?.message || 'Login failed. Please try again.'
            );
        }
    }
);



/**
 * Oauth2 Login user async thunk
 */

export const initiateOAuthThunk = createAsyncThunk(
    'auth/initiate/oauth2',
    async (oauthData, { rejectWithValue }) => {
        try {
            const response = await authApi.initiateOAuth(oauthData).send();
            if (response.status === 200 && response.data) {
                return {
                    message: response.data?.message || 'Login Initiated successfully',
                    success: response.status === 200,
                    data: response.data || null
                };
            } else {
                return {
                    message: response.data?.message || 'Login failed',
                    success: false,
                    data: null
                };
            }
        } catch (error) {
            return rejectWithValue(
                error.response?.data?.message || 'Login failed. Please try again.'
            );
        }
    }
);

/**
 * Brand Management Thunks
 */

/**
 * Request Brand Access async thunk
 */
export const requestBrandAccessThunk = createAsyncThunk(
    'auth/requestBrandAccess',
    async (brandData, { rejectWithValue }) => {
        try {
            const response = await brandManagementApi.requestBrandAccess(brandData.brandId).send();
            if (response.status === 200 && response.data) {
                return {
                    message: response.data.data?.message || 'Brand access requested successfully',
                    success: response.status === 200,
                    data: response.data.data || null
                };
            } else {
                return {
                    message: response.data?.message || 'Brand access request failed',
                    success: false,
                    data: null
                };
            }
        } catch (error) {
            return rejectWithValue(
                error.response?.data?.message || 'Brand access request failed. Please try again.'
            );
        }
    }
);

/**
 * Create Brand async thunk
 */
export const createBrandThunk = createAsyncThunk(
    'auth/createBrand',
    async (brandData, { rejectWithValue }) => {
        try {
            const response = await brandManagementApi.createBrand(brandData).send();
            if (response.status === 200 && response.data) {
                return {
                    message: response.data.data?.message || 'Brand created successfully',
                    success: response.status === 200,
                    data: response.data.data || null
                };
            } else {
                return {
                    message: response.data?.message || 'Brand creation failed',
                    success: false,
                    data: null
                };
            }
        } catch (error) {
            return rejectWithValue(
                error.response?.data?.message || 'Brand creation failed. Please try again.'
            );
        }
    }
);

/**
 * Request User allotedBrand async thunk
 */
export const requestUserBrandInfoThunk = createAsyncThunk(
    'auth/requestUserBrandInfo',
    async (req, { rejectWithValue }) => {
        try {
            const response = await brandManagementApi.requestUserBrandInfo().send();
            console.log('requestUserBrandInfo response:', response);
            if (response.status === 200 && response.data) {
                const { allocated_brands, user, organization_id, organization_brands } = response.data.data;

                // Store tokens
                localStorage.setItem('user', JSON.stringify(user));
                localStorage.setItem('organisationBrands', JSON.stringify(organization_brands));
                localStorage.setItem('allocatedBrands', JSON.stringify(allocated_brands));
                localStorage.setItem('organisationId', JSON.stringify(organization_id));

                return {
                    message: response.data?.message || 'Brand access requested successfully',
                    success: response.status === 200,
                    data: response.data.data || null
                };
            } else {
                return {
                    message: response.data?.message || 'Brand access request failed',
                    success: false,
                    data: null
                };
            }
        } catch (error) {
            return rejectWithValue(
                error.response?.data?.message || 'Brand access request failed. Please try again.'
            );
        }
    }
);

/**
 * Request User allotedBrand async thunk
 */
export const requestUserCreatorInfoThunk = createAsyncThunk(
    'auth/requestUserCreatorInfo',
    async (req, { rejectWithValue }) => {
        try {
            const response = await brandManagementApi.requestUserCreatorInfo().send();
            console.log('requestUserCreatorInfo response:', response);
            if (response.status === 200 && response.data) {
                const { user, social_profiles } = response.data.data;

                // Store tokens
                localStorage.setItem('user', JSON.stringify(user));
                localStorage.setItem('socialProfiles', JSON.stringify(social_profiles));

                return {
                    message: response.data?.message || 'Creator user Info fetched successfully',
                    success: response.status === 200,
                    data: response.data.data || null
                };
            } else {
                return {
                    message: response.data?.message || 'Brand accesCreator user Info request failed',
                    success: false,
                    data: null
                };
            }
        } catch (error) {
            return rejectWithValue(
                error.response?.data?.message || 'Brand accesCreator user Info request failed. Please try again.'
            );
        }
    }
);


/**
 * get youtube channel async thunk
 */
export const getYoutubeChannelsThunk = createAsyncThunk(
    'auth/getYoutubeChannels',
    async (req, { rejectWithValue }) => {
        try {
            const response = await brandManagementApi.getYoutubeChannels().send();
            console.log('getYoutubeChannels response:', response);
            if (response.status === 200 && response.data) {
                return {
                    message: response.data?.message || 'Youtube channels fetched successfully',
                    success: response.status === 200,
                    data: response.data.data || null
                };
            } else {
                return {
                    message: response.data?.message || 'Youtube channels fetch failed',
                    success: false,
                    data: null
                };
            }
        } catch (error) {
            return rejectWithValue(
                error.response?.data?.message || 'Youtube channels fetch failed. Please try again.'
            );
        }
    }
);

/**
 * select youtube channels async thunk
 */
export const selectYoutubeChannelsThunk = createAsyncThunk(
    'auth/selectYoutubeChannels',
    async (channels, { rejectWithValue }) => {
        try {
            const response = await brandManagementApi
                .selectYoutubeChannels({ data: channels }) // assuming `channels` is an array
                .send();

            console.log('selectYoutubeChannels response:', response);

            if (response.status === 200 && response.data) {
                return {
                    message: response.data?.message || 'Youtube channels selected successfully',
                    success: true,
                    data: response.data.data || null
                };
            } else {
                return {
                    message: response.data?.message || 'Failed to select Youtube channels',
                    success: false,
                    data: null
                };
            }
        } catch (error) {
            return rejectWithValue(
                error.response?.data?.message || 'Youtube channels selection failed. Please try again.'
            );
        }
    }
);

