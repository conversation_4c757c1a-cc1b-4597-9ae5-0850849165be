import React, { useEffect, useRef, useState } from "react";
import PropTypes from "prop-types";
import ArrowUpRight from '@assets/icon/arrow-up-right.svg';
import ArrowDownLeft from '@assets/icon/arrow-down-left.svg';

const PerformanceCard = ({ icon, label, value, delta, isPositive, iconBg = "var(--color-blue)" }) => {
    // Animated value state
    const [displayedValue, setDisplayedValue] = useState(0);
    const valueRef = useRef(value);

    useEffect(() => {
        // Parse value as number (remove commas, handle %)
        let target = typeof value === 'string' ? parseFloat(value.replace(/,/g, '').replace('%', '')) : value;
        if (isNaN(target)) target = 0;
        let start = displayedValue;
        let startTime = null;
        const duration = 500; // ms
        function animateCountUp(timestamp) {
            if (!startTime) startTime = timestamp;
            const progress = Math.min((timestamp - startTime) / duration, 1);
            const current = start + (target - start) * progress;
            setDisplayedValue(progress === 1 ? target : current);
            if (progress < 1) {
                requestAnimationFrame(animateCountUp);
            }
        }
        requestAnimationFrame(animateCountUp);
        valueRef.current = value;
        // eslint-disable-next-line
    }, [value]);

    // Format value for display (keep % or commas if needed)
    function formatValue(val) {
        if (typeof value === 'string' && value.includes('%')) {
            return `${Math.round(val)}%`;
        }
        if (typeof value === 'string' && value.includes(',')) {
            return Math.round(val).toLocaleString();
        }
        if (typeof value === 'number') {
            return Math.round(val);
        }
        return value;
    }

    return (
        <div className="justify-between rounded-[8px] border border-[#656974] bg-[#484C55] p-3 flex flex-col gap-3 ">
            {/* Icon and Label */}
            <div className="flex items-center gap-3">
                <div
                    className="w-8 h-8 flex items-center justify-center rounded-lg"
                    style={{
                        background: iconBg,
                        boxShadow: `0px 4px 18px 0px ${iconBg}`,
                    }}
                >
                    <img src={icon} alt="" className={iconBg} />
                </div>
                <span className="text-14-medium text-gray-200">{label}</span>
            </div>
            {/* Value and Delta */}
            <div className="flex items-center justify-between">
                <span className="text-18-semibold text-white">{formatValue(displayedValue)}</span>
                <span className={`flex items-center text-18-semibold ${isPositive ? "text-green-2" : "text-red-2"}`}>
                    <img
                        src={isPositive ? ArrowUpRight : ArrowDownLeft}
                        width={20}
                        height={20}
                        alt={isPositive ? "Up" : "Down"}
                        className="mr-1"
                    />
                    {delta}
                </span>
            </div>
        </div>
    );
};

PerformanceCard.propTypes = {
    icon: PropTypes.string.isRequired,
    title: PropTypes.string.isRequired,
    value: PropTypes.string.isRequired,
    delta: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
    isPositive: PropTypes.bool.isRequired,
    iconBg: PropTypes.string,
};

export default PerformanceCard;
