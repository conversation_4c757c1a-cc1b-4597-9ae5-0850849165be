import React, { useEffect, useState } from "react";
import { cn } from "../../../lib/utils";

const notifications = [
  "🎉 Someone just landed a skincare collab!",
  "🔥 A creator got 50k views on a brand reel!",
  "💼 New fitness campaign just launched!",
  "📈 Creator earnings are up 40% this week!",
  "✨ 10 new deals signed today!",
  "🎬 Trending YouTube collab open now!",
  "🛍️ Fashion brand looking for Gen Z creators!",
  "🏆 Top creator earned $5k this week!",
  "🎯 Beauty brand wants micro-influencers!",
  "🚀 You’re next to land a brand deal!"
];

const NotificationStrip = () => {
  const [index, setIndex] = useState(0);
  const [prevIndex, setPrevIndex] = useState(null);
  const [animating, setAnimating] = useState(false);

  useEffect(() => {
    const interval = setInterval(() => {
      setPrevIndex(index);
      setIndex((prev) => (prev + 1) % notifications.length);
      setAnimating(true);

      setTimeout(() => {
        setAnimating(false);
      }, 250); // matches animation duration
    }, 2000);

    return () => clearInterval(interval);
  }, [index]);

  return (
    <div className="h-14 flex items-center justify-center w-full overflow-hidden">
      <div className="bg-indigo-100 text-indigo-800 text-16-regular py-2 px-4 rounded-lg w-full max-w-lg h-10 flex items-center justify-center relative">
        {prevIndex !== null && animating && (
          <span
            className={cn(
              "absolute w-full text-center animate-out"
            )}
            style={{ whiteSpace: "nowrap" }}
          >
            {notifications[prevIndex]}
          </span>
        )}

        <span
          className={cn(
            "absolute w-full text-center",
            animating ? "animate-in" : ""
          )}
          style={{ whiteSpace: "nowrap" }}
        >
          {notifications[index]}
        </span>
      </div>

      <style jsx>{`
        @keyframes slideUp {
          0% {
            transform: translateY(100%);
            opacity: 0;
          }
          40% {
            opacity: 0.3;
          }
          60% {
            opacity: 0.8;
          }
          100% {
            transform: translateY(0%);
            opacity: 1; /* ✅ Fix applied here */
          }
        }

        @keyframes slideOut {
          0% {
            transform: translateY(0%);
            opacity: 1;
          }
          40% {
            opacity: 0.6;
          }
          60% {
            opacity: 0.3;
          }
          100% {
            transform: translateY(-100%);
            opacity: 0;
          }
        }

        .animate-in {
          animation: slideUp 0.3s ease forwards;
        }

        .animate-out {
          animation: slideOut 0.3s ease forwards;
        }

      `}</style>
    </div>
  );
};

export default NotificationStrip;
