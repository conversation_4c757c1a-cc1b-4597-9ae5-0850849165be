import React, { useEffect } from 'react';
import Highcharts from "highcharts";
import HighchartsReact from "highcharts-react-official";
import PerformanceCard from '@influencer/components/PerformanceCard';

import { useParams } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { getCampaignAnalyticsThunk, getCampaignMetricsThunk } from '../../services/campaignThunks';
import { RequestStatus } from '../../../../app/store/enum';
import LoadingState from '../../components/LoadingState';
import ErrorState from '../../components/ErrorState';

import Heart from '@assets/icon/favorite_border.svg';
import UserAdd from '@assets/icon/userAdd.svg';
import UsersGroup from '@assets/icon/userGroup.svg';
import AllInclusive from '@assets/icon/all_inclusive.svg';
import TrendingUp from '@assets/icon/trending_up.svg';
import LightBulb from '@assets/icon/lightbulb1.svg';
import CommentDot from '@assets/icon/commentDot.svg';
import Eye from '@assets/icon/visibility.svg';

const data = {
    "creators": {
        "onboarded": 12,
        "required": 15,
        "status": "Almost There"
    },
    "content": {
        "posted": 128,
        "total": 140,
        "status": "Off to a Great Start"
    },
    "metrics": [
        { label: "Engagement Rate", value: "2.5%", delta: "-0.5%", isPositive: false, icon: AllInclusive, iconBg: "var(--color-blue)" },
        { label: "Average Reach", value: "1,200", delta: "+3.0%", isPositive: true, icon: TrendingUp, iconBg: "var(--color-green-2)" },
        { label: "Average Impressions", value: "10,000", delta: "+1.0%", isPositive: true, icon: LightBulb, iconBg: "var(--color-pink)" },
        { label: "Average Likes", value: "250", delta: "-2.0%", isPositive: false, icon: Heart, iconBg: "var(--color-teal)" },
        { label: "Average Comments", value: "409", delta: "+0.6%", isPositive: true, icon: CommentDot, iconBg: "var(--color-purple)" },
        { label: "Average Saves", value: "1.6M", delta: "-0.6%", isPositive: false, icon: Eye, iconBg: "var(--color-red-2)" },
    ],
    "audienceDemographics": {
        "ageGenderSplit": [
            { "ageGroup": "13-17", "female": 12, "male": 18 },
            { "ageGroup": "18-24", "female": 30, "male": 45 },
            { "ageGroup": "25-34", "female": 50, "male": 60 },
            { "ageGroup": "35-44", "female": 40, "male": 55 },
            { "ageGroup": "45-54", "female": 25, "male": 30 },
            { "ageGroup": "55+", "female": 10, "male": 20 }
        ],
        "genderSplit": { "female": 80, "male": 20 },
        "languageDistribution": [
            { "language": "Hindi", "percentage": 35 },
            { "language": "Bengali", "percentage": 25 },
            { "language": "Marathi", "percentage": 15 },
            { "language": "Tamil", "percentage": 25 }
        ],
        "locationDistribution": [
            { "location": "India", "percentage": 72.3 },
            { "location": "Brazil", "percentage": 45.1 },
            { "location": "China", "percentage": 56.5 },
            { "location": "USA", "percentage": 38.2 }
        ]
    },
    "topPerformingCreators": {
        "podium": [
            { "rank": 1, "name": "Rachel", "image": "https://randomuser.me/api/portraits/women/44.jpg" },
            { "rank": 2, "name": "Maria", "image": "https://randomuser.me/api/portraits/women/65.jpg" },
            { "rank": 3, "name": "Andrew", "image": "https://randomuser.me/api/portraits/men/22.jpg" }
        ],
        "others": [
            { "rank": 4, "name": "Olivia Rhye", "campaign": "The Buzz Bootcamp", "impactScore": 97, "image": "https://randomuser.me/api/portraits/women/12.jpg" },
            { "rank": 5, "name": "Olivia Rhye", "campaign": "The Buzz Bootcamp", "impactScore": 67, "image": "https://randomuser.me/api/portraits/women/12.jpg" },
            { "rank": 6, "name": "Olivia Rhye", "campaign": "The Buzz Bootcamp", "impactScore": 77, "image": "https://randomuser.me/api/portraits/women/12.jpg" },
            // { "rank": 7, "name": "Olivia Rhye", "campaign": "The Buzz Bootcamp", "impactScore": 87, "image": "https://randomuser.me/api/portraits/women/12.jpg" }
        ]
    },
    "spendAnalytics": {
        "walletBalance": 34000,
        "overspentBy": 66550,
        "monthlySpend": [
            { "month": "Jan", "spent": 50000, "budget": 30000 },
            { "month": "Feb", "spent": 22000, "budget": 31000 },
            { "month": "Mar", "spent": 53000, "budget": 72000 },
            { "month": "Apr", "spent": 54000, "budget": 33000 },
            { "month": "May", "spent": 35000, "budget": 34000 },
            { "month": "Jun", "spent": 56000, "budget": 35000 },
            { "month": "Jul", "spent": 47000, "budget": 36000 },
            { "month": "Aug", "spent": 58000, "budget": 97000 },
            { "month": "Sep", "spent": 59000, "budget": 38000 },
            { "month": "Oct", "spent": 30000, "budget": 79000 },
            { "month": "Nov", "spent": 69000, "budget": 60000 },
            { "month": "Dec", "spent": 64000, "budget": 41000 }
        ]
    },
    "impactTrend": {
        "creator": "Olivia Rhye",
        "campaigns": [
            { "month": "Jan", "impactScore": 480, "label": "Marketing Blitz", "avatarUrl": "https://randomuser.me/api/portraits/women/12.jpg" },
            { "month": "Feb", "impactScore": 500, "avatarUrl": "https://randomuser.me/api/portraits/women/12.jpg" },
            { "month": "Mar", "impactScore": 520, "label": "Marketing Blitz", "avatarUrl": "https://randomuser.me/api/portraits/women/12.jpg" },
            { "month": "Apr", "impactScore": 560, "avatarUrl": "https://randomuser.me/api/portraits/women/12.jpg" },
            { "month": "May", "impactScore": 600, "avatarUrl": "https://randomuser.me/api/portraits/women/12.jpg" },
            { "month": "Jun", "impactScore": 650, "avatarUrl": "https://randomuser.me/api/portraits/women/12.jpg" },
            { "month": "Jul", "impactScore": 800, "avatarUrl": "https://randomuser.me/api/portraits/women/12.jpg" },
            { "month": "Aug", "impactScore": 720, "avatarUrl": "https://randomuser.me/api/portraits/women/12.jpg" },
            { "month": "Sep", "impactScore": 760, "avatarUrl": "https://randomuser.me/api/portraits/women/12.jpg" },
            { "month": "Oct", "impactScore": 780, "avatarUrl": "https://randomuser.me/api/portraits/women/12.jpg" },
            { "month": "Nov", "impactScore": 790, "avatarUrl": "https://randomuser.me/api/portraits/women/12.jpg" },
            { "month": "Dec", "impactScore": 850, "avatarUrl": "https://randomuser.me/api/portraits/women/12.jpg" }
        ]
    }
}

const genderChartOptions = {
    chart: {
        type: "pie",
        backgroundColor: "transparent",
        height: 300
    },
    title: {
        text: ""
    },
    plotOptions: {
        pie: {
            innerSize: "50%",
            borderWidth: 2,
            borderColor: "#303239",
            showInLegend: true,
            dataLabels: {
                enabled: false,
                format: "{point.name}: ₹{point.y}",
                style: {
                    fontWeight: "bold",
                    color: "#000000"
                }
            },
            colors: ["var(--color-sky-blue)", "var(--color-green-2)"]
        }
    },
    tooltip: {
        pointFormat: '<b>{point.name}: {point.percentage:.1f}%</b>'
    },
    series: [
        {
            name: "Gender",
            data: [
                { name: "Female", y: data.audienceDemographics.genderSplit.female },
                { name: "Male", y: data.audienceDemographics.genderSplit.male }
            ]
        }
    ],
    credits: { enabled: false },
    legend: {
        align: "center",
        verticalAlign: "bottom",
        layout: "horizontal",
        itemStyle: {
            color: "#ffffff",
            fontWeight: "bold"
        },
        symbolRadius: 6,
        symbolHeight: 12
    }
};
const ageGenderChartOptions = {
    chart: {
        type: "column",
        backgroundColor: "#2b2d33", // or keep "" if you're applying it via container
        height: 300
    },
    title: {
        text: ""
    },
    plotOptions: {
        column: {
            borderWidth: 0,
            grouping: true,
            pointPadding: 0.05,   // Reduces space between bars in a group
            groupPadding: 0.10,   // Reduces space between groups
            dataLabels: {
                enabled: true,
                style: {
                    color: "#ffffff",
                    textOutline: "none",
                    fontWeight: "bold"
                }
            }
        }
    },
    xAxis: {
        categories: data.audienceDemographics.ageGenderSplit.map(item => item.ageGroup),
        labels: {
            style: { color: "#ffffff", fontWeight: "bold" }
        },
        lineColor: "#444"
    },
    yAxis: {
        min: 0,
        title: { text: "" },
        gridLineColor: "#444",
        labels: {
            style: { color: "#ffffff" }
        }
    },
    legend: {
        align: "center",
        verticalAlign: "bottom",
        itemStyle: {
            color: "#ffffff",
            fontWeight: "bold"
        },
        symbolRadius: 6,
        symbolHeight: 12
    },
    tooltip: {
        shared: true,
        backgroundColor: "#333",
        style: { color: "#fff" }
    },
    colors: ["#A6E8FC", "#12C9AC"], // Female (light blue), Male (teal)
    series: [
        {
            name: "Female",
            data: data.audienceDemographics.ageGenderSplit.map(item => item.female)
        },
        {
            name: "Male",
            data: data.audienceDemographics.ageGenderSplit.map(item => item.male)
        }
    ],
    credits: {
        enabled: false
    }
};
const languageChartOptions = {
    chart: {
        type: "pie",
        backgroundColor: "", // dark background
        height: 300
    },
    title: {
        text: ""
    },
    plotOptions: {
        pie: {
            innerSize: "40%",
            borderWidth: 3,
            borderColor: "#303239",
            allowPointSelect: true,
            cursor: "pointer",
            dataLabels: {
                enabled: false
            },
            showInLegend: true,
            startAngle: 0,
            endAngle: 360,
            slicedOffset: 5
        }
    },
    tooltip: {
        pointFormat: '<b>{point.percentage:.1f}%</b>'
    },
    colors: ["#1BC98E", "#5A9BFF", "#FF6B6B", "#A678F0"], // Hindi, Bengali, Marathi, Tamil
    series: [
        {
            name: "Languages",
            data: data.audienceDemographics.languageDistribution.map(item => ({
                name: item.language,
                y: item.percentage
            }))
        }
    ],
    legend: {
        align: "center",
        verticalAlign: "bottom",
        layout: "horizontal",
        itemStyle: {
            color: "#ffffff",
            fontWeight: "bold"
        },
        symbolRadius: 6,
        symbolHeight: 12
    },
    credits: {
        enabled: false
    }
};
const impactTrendOptions = {
    chart: {
        type: 'spline',
        backgroundColor: '#1e1f26',
        height: 360,
        spacing: [20, 10, 10, 10]
    },
    title: { text: '' },
    xAxis: {
        categories: data.impactTrend.campaigns.map(c => c.month),
        labels: {
            style: { color: '#ffffff' }
        },
        lineColor: '#444'
    },
    yAxis: {
        title: {
            text: 'Impact Scores',
            style: { color: '#ffffff' }
        },
        labels: {
            style: { color: '#ffffff' }
        },
        gridLineColor: '#333'
    },
    tooltip: {
        useHTML: true,
        formatter: function () {
            return `
                <div style="text-align:left">
                    <strong>${this.x}</strong><br/>
                    <img src="${this.point.avatar}" width="32" height="32" style="border-radius: 50%; margin-right: 5px;" />
                    ${this.series.name}<br/>
                    <b>Impact Score:</b> ${this.y}<br/>
                    ${this.point.label ? `<em>${this.point.label}</em>` : ''}
                </div>
            `;
        }
    },
    plotOptions: {
        spline: {
            lineWidth: 2,
            color: '#5A9BFF',
            marker: {
                enabled: true,
                radius: 16 // this is ignored if using symbol URL
            },
            dataLabels: {
                enabled: true,
                useHTML: true,
                formatter: function () {
                    return this.point.label
                        ? `<div style="margin-top: 4px; background: #3a3c4a; color: #fff; padding: 2px 8px; border-radius: 10px; font-size: 11px;">
                                ${this.point.label}
                            </div>`
                        : null;
                }
            }
        }
    },
    series: [
        {
            name: data.impactTrend.creator,
            data: data.impactTrend.campaigns.map(c => ({
                y: c.impactScore,
                label: c.label || null,
                avatar: c.avatarUrl, // you must include this in your `impactTrend` data
                marker: {
                    symbol: `url(${c.avatarUrl})`,
                    width: 32,
                    height: 32,
                }
            }))
        }
    ],
    credits: { enabled: false }
};







const AnalyticsTab = () => {
    const { campaignId } = useParams();
    const dispatch = useDispatch();

    const podiumOrder = [2, 1, 3]; // To match Figma layout: Maria, Rachel, Andrew
    const podiumMap = Object.fromEntries(data.topPerformingCreators.podium.map(p => [p.rank, p]));

    const {
        campaignMetrics,
        performanceData,
        analyticsStatus,
        analyticsError
    } = useSelector(state => state.campaign);

    useEffect(() => {
        if (campaignId) {
            dispatch(getCampaignAnalyticsThunk(campaignId));
            dispatch(getCampaignMetricsThunk({ campaignId, timeRange: '30d' }));
        }
    }, [dispatch, campaignId]);

    const formatNumber = (num) => {
        if (num >= 1000000) {
            return (num / 1000000).toFixed(1) + 'M';
        } else if (num >= 1000) {
            return (num / 1000).toFixed(1) + 'K';
        }
        return num?.toLocaleString() || '0';
    };

    const formatCurrency = (amount) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD'
        }).format(amount || 0);
    };

    if (analyticsStatus === RequestStatus.LOADING) {
        return <LoadingState message="Loading campaign analytics..." />;
    }

    if (analyticsStatus === RequestStatus.FAILED) {
        return <ErrorState message={analyticsError} />;
    }

    return (
        <div className="h-full w-full flex flex-col gap-5 ">
            {/* Progress */}
            <div className="flex items-center gap-4">
                <div className='flex flex-col rounded-xl border-1 border-gray-400 p-5 gap-4 flex-1'>
                    <div className='flex justify-between items-center'>
                        <div className='flex items-center gap-2'>
                            <i class="fi fi-rs-diamond pt-1 px-1.5 text-brand-600 bg-brand-200 drop-shadow-sm drop-shadow-brand-200 rounded-lg"></i>
                            <h2 className='text-16-medium text-gray-50'>Creators Onboarded / Required</h2>
                        </div>
                        <div className='flex flex-col items-end text-gray-500 text-12-semibold px-2 py-1 bg-light-8 rounded-full h-fit'>
                            🏁 Almost There!
                        </div>
                    </div>
                    <div className='flex justify-between items-center'>
                        <div className='flex gap-2.5 w-full items-center'>
                            <div className=''>
                                <span className='text-20-semibold text-sky-blue'>
                                    {data.creators.onboarded}
                                </span>
                                <span className='text-16-semibold text-gray-300'>
                                    /{data.creators.required}
                                </span>
                            </div>
                            <div className="w-full h-3 rounded-full bg-gray-800">
                                <div className="h-full bg-sky-blue rounded-full" style={{ width: `${data.creators.onboarded / data.creators.required * 100}%` }}></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div className='flex flex-col rounded-xl border-1 border-gray-400 p-5 gap-4 flex-1'>
                    <div className='flex justify-between items-center'>
                        <div className='flex items-center gap-2'>
                            <i class="fi  fi-rs-registration-paper pt-1 px-1.5 text-orange bg-light-4 drop-shadow-sm drop-shadow-light-4 rounded-lg"></i>
                            <h2 className='text-16-medium text-gray-50'>Content Posted / Total Content</h2>
                        </div>
                        <div className='flex flex-col items-end text-gray-500 text-12-semibold px-2 py-1 bg-light-6 rounded-full h-fit'>
                            🚀 Off to a Great Start
                        </div>
                    </div>
                    <div className='flex justify-between items-center'>
                        <div className='flex gap-2.5 w-full items-center'>
                            <div className=''>
                                <span className='text-20-semibold text-yellow'>
                                    {data.content.posted}
                                </span>
                                <span className='text-16-semibold text-gray-300'>
                                    /{data.content.total}
                                </span>
                            </div>
                            <div className="w-full h-3 rounded-full bg-gray-800">
                                <div className="h-full bg-yellow rounded-full" style={{ width: `${data.content.posted / data.content.total * 100}%` }}></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {/* Metrics Cards */}
            <div className={`flex flex-wrap gap-4 py-4.5`}>
                {data.metrics.map((item, index) => (
                    <div className="flex-grow" key={index}>
                        <PerformanceCard {...item} />
                    </div>
                ))}
            </div>

            {/* Audience Demographics */}
            <div className="flex flex-col bg-gray-600 rounded-lg p-6 gap-4">
                <h3 className="text-24-medium text-gray-50">Audience Demographics</h3>
                <div className="flex flex-col lg:flex-row gap-5">
                    <div className="flex flex-col lg:flex-2 border-1 border-gray-400 rounded-[10px] p-4.5 gap-4">
                        <h4 className="text-18-bold text-gray-50">Age & Gender Split</h4>
                        <div className="w-full">
                            <HighchartsReact highcharts={Highcharts} options={ageGenderChartOptions} />
                        </div>
                    </div>
                    <div className="flex flex-col lg:flex-1 border-1 border-gray-400 rounded-[10px] p-4.5 gap-4">
                        <h4 className="text-18-bold text-gray-50">Gender Split</h4>
                        <div className="w-full">
                            <HighchartsReact highcharts={Highcharts} options={genderChartOptions} />
                        </div>
                    </div>
                </div>
                <div className="flex flex-col lg:flex-row gap-5">
                    <div className="flex flex-col lg:flex-1 border-1 border-gray-400 rounded-[10px] p-4.5 gap-4">
                        <h4 className="text-18-bold text-gray-50">Language Split</h4>
                        <div className="w-full">
                            <HighchartsReact highcharts={Highcharts} options={languageChartOptions} />
                        </div>
                    </div>
                    <div className="flex flex-col lg:flex-1 border-1 border-gray-400 rounded-[10px] p-4.5 gap-4">
                        <h4 className="text-18-bold text-gray-50">Location</h4>

                        <div className="flex flex-col gap-7">
                            {data.audienceDemographics.locationDistribution.map((item, index) => (
                                <div key={index} className="flex flex-col gap-4">
                                    <div className="flex justify-between items-center">
                                        <span className="text-gray-100">{item.location}</span>
                                        <span className="text-gray-100">{item.percentage.toFixed(1)}%</span>
                                    </div>
                                    <div className="w-full h-3 rounded-full bg-gray-800">
                                        <div
                                            className="h-full bg-light-8 rounded-full"
                                            style={{ width: `${item.percentage}%` }}
                                        ></div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>
                </div>
            </div>

            {/* Performance Chart */}
            <div className="flex flex-col bg-gray-600 rounded-lg p-5 gap-5">
                <h3 className="text-18-semibold text-white">Top Performing Creators</h3>

                <div className="flex flex-col lg:flex-row gap-6">
                    {/* Podium */}
                    <div className="flex-1 rounded-[20px] p-4 flex justify-around items-end gap-6 border-1 border-gray-400">
                        {podiumOrder.map((rank) => {
                            const person = podiumMap[rank];
                            const isTop = person.rank === 1;

                            const blockHeight = isTop ? " rounded-t-[35px] h-44 bg-gray-500 text-20-medium" : person.rank === 2 ? "h-32 bg-gray-900 text-14-regular rounded-t-[15px]" : "h-28 bg-gray-900 text-14-regular rounded-t-[15px]";
                            const avatarSize = isTop ? "w-25 h-25" : "w-20 h-20";
                            const borderColor = isTop ? "border-orange-500" : "border-white";

                            return (
                                <div key={person.rank} className="flex flex-col items-center gap-1">
                                    {/* Crown only for top rank */}
                                    {isTop && <div className="-mb-3 mr-15 text-orange-400 -rotate-25 text-4xl z-11">👑</div>}

                                    <div className="flex flex-col items-center">
                                        {/* Avatar */}
                                        <div className={`relative ${avatarSize} rounded-full overflow-hidden border-4 ${borderColor} z-10`}>
                                            <img src={person.image} alt={person.name} className="object-cover w-full h-full" />
                                        </div>

                                        {/* Podium Block */}
                                        <div
                                            className={`w-30 ${blockHeight} flex flex-col justify-start items-center pt-4 pb-2 -mt-6`}
                                        >
                                            <div className="bg-gray-300 h-10 w-10 rounded-[7px] text-white rotate-45 z-10 -mt-2 flex items-center justify-center">
                                                <div className="-rotate-45">
                                                    #{person.rank}
                                                </div>
                                            </div>
                                            <p className="text-sm text-white">{person.name}</p>
                                        </div>
                                    </div>
                                </div>
                            );
                        })}
                    </div>

                    {/* Others */}
                    <div className="flex-2 flex flex-col gap-6">
                        {data.topPerformingCreators.others.map(({ rank, name, campaign, impactScore, image }) => (
                            <div key={rank} className="flex justify-between items-center border-1 border-gray-400 rounded-[20px] p-5">
                                <div className="flex items-center gap-5 text-20-medium w-full">
                                    <div>#{rank}</div>
                                    <div className="flex w-full">
                                        <div className="flex items-center gap-3 w-full">
                                            <img
                                                src={image}
                                                alt={name}
                                                className="w-10 h-10 rounded-full object-cover"
                                            />
                                            <p className="text-white text-14-semibold">{name}</p>
                                        </div>
                                        <div className="flex flex-col gap-3 w-full">
                                            <p className="text-14-medium text-gray-100">Campaign</p>
                                            <span className="text-16-medium text-gray-100">{campaign}</span>
                                        </div>
                                        <div className="flex flex-col gap-3 w-full">
                                            <p className="text-14-medium text-gray-100">Impact Score</p>
                                            <span className="text-16-medium text-gray-100">{impactScore}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            </div>

            <div className='flex flex-col lg:flex-row gap-5 w-full'>
                <div className='flex flex-col flex-1 rounded-xl border-1 border-gray-400 py-5 px-3'>
                </div>
                <div className='flex flex-col flex-2 rounded-xl border-1 border-gray-400 py-5 px-3'>
                    <div className='flex justify-between gap-9'>
                        <div className='flex flex-col gap-2.5'>
                            <div className='flex gap-2 items-center'>
                                {/* <i class="fi fi-br-wallet pt-1 px-1.5 bg-purple drop-shadow-sm drop-shadow-purple rounded-lg"></i> */}
                                <h2 className='text-24-medium text-gray-50'>Total Spent</h2>
                            </div>
                            <div className='flex gap-2 items-center'>
                                <p className='text-16-regular text-gray-200'>Wallet Balance <span className='text-18-medium text-gray-50'>₹{data.spendAnalytics.walletBalance}</span></p>
                            </div>
                        </div>
                        <div className='flex flex-col items-end text-gray-500 text-12-semibold px-2 py-1 bg-light-6 rounded-full h-fit'>
                            💸 Overspent by ₹{data.spendAnalytics.overspentBy}
                        </div>
                    </div>
                    {/* SpendChart here */}
                    <div className="w-full mt-4">
                        <HighchartsReact
                            highcharts={Highcharts}
                            options={{
                                chart: {
                                    type: 'spline',
                                    backgroundColor: 'transparent',
                                    height: 200,
                                },
                                title: { text: null },
                                xAxis: {
                                    categories: data.spendAnalytics.monthlySpend.map(item => item.month),
                                    labels: { style: { color: 'var(--color-gray-100)' } },
                                    gridLineWidth: 0,
                                    lineColor: '#444',
                                    tickColor: '#444',
                                },
                                yAxis: {
                                    title: { text: null },
                                    labels: { style: { color: 'var(--color-gray-100)' } },
                                    gridLineColor: '#333',
                                    tickAmount: 6,
                                },
                                legend: {
                                    align: 'right',
                                    verticalAlign: 'top',
                                    itemStyle: {
                                        color: 'var(--color-gray-100)',
                                        fontWeight: 'normal',
                                    },
                                    itemHoverStyle: {
                                        color: 'var(--color-gray-100)',
                                    },
                                },
                                tooltip: {
                                    shared: false,
                                    backgroundColor: 'var(--color-primary)',
                                    borderColor: '#333',
                                    style: { color: '#fff' },
                                },
                                series: [
                                    {
                                        name: 'Total Spent',
                                        type: 'areaspline',
                                        data: data.spendAnalytics.monthlySpend.map(item => item.spent),
                                        color: '#FF6B6B',
                                        fillColor: {
                                            linearGradient: [0, 0, 0, 100],
                                            stops: [
                                                [0, 'rgba(255, 107, 107, 0.2)'],
                                                [1, 'rgba(255, 107, 107, 0)'],
                                            ],
                                        },
                                        fillOpacity: 0.5,
                                        marker: { enabled: false },
                                    },
                                    {
                                        name: 'Total Budget',
                                        type: 'areaspline',
                                        data: data.spendAnalytics.monthlySpend.map(item => item.budget),
                                        color: '#12C9AC',
                                        fillColor: {
                                            linearGradient: [0, 0, 0, 110],
                                            stops: [
                                                [0, 'rgba(18, 201, 172, 0.1)'],
                                                [1, 'rgba(18, 201, 172, 0)'],
                                            ],
                                        },
                                        fillOpacity: 0.2,
                                        marker: { enabled: false },
                                    },
                                ],
                                credits: { enabled: false },
                            }}
                        />
                    </div>
                </div>
            </div>

            <div className='flex flex-col flex-1 rounded-xl border-1 border-gray-400 py-5 px-3'>
                <HighchartsReact
                    highcharts={Highcharts}
                    options={impactTrendOptions}
                />
            </div>

            {/* Additional Metrics */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-5">
                {/* Top Performing Content */}
                <div className="bg-gray-600 rounded-lg p-6">
                    <h3 className="text-18-semibold text-white mb-4">Top Performing Content</h3>
                    <div className="space-y-3">
                        {[
                            { title: "Summer Vibes Post #1", engagement: "12.5K", reach: "85K" },
                            { title: "Behind the Scenes Reel", engagement: "8.2K", reach: "62K" },
                            { title: "Product Showcase", engagement: "6.8K", reach: "45K" }
                        ].map((content, index) => (
                            <div key={index} className="flex items-center justify-between p-3 bg-gray-700 rounded-lg">
                                <div>
                                    <div className="text-14-semibold text-white">{content.title}</div>
                                    <div className="text-12-regular text-gray-300">
                                        {content.engagement} engagement • {content.reach} reach
                                    </div>
                                </div>
                                <div className="text-14-medium text-brand-400">#{index + 1}</div>
                            </div>
                        ))}
                    </div>
                </div>

                {/* Creator Performance */}
                <div className="bg-gray-600 rounded-lg p-6">
                    <h3 className="text-18-semibold text-white mb-4">Creator Performance</h3>
                    <div className="space-y-3">
                        {[
                            { name: "Sarah Johnson", engagement: "15.2K", posts: 3 },
                            { name: "Mike Chen", engagement: "12.8K", posts: 2 },
                            { name: "Emma Wilson", engagement: "9.5K", posts: 2 }
                        ].map((creator, index) => (
                            <div key={index} className="flex items-center justify-between p-3 bg-gray-700 rounded-lg">
                                <div>
                                    <div className="text-14-semibold text-white">{creator.name}</div>
                                    <div className="text-12-regular text-gray-300">
                                        {creator.engagement} total engagement • {creator.posts} posts
                                    </div>
                                </div>
                                <div className="text-14-medium text-green-400">
                                    {(parseFloat(creator.engagement.replace('K', '')) / creator.posts).toFixed(1)}K avg
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            </div>

            {/* ROI Summary */}
            <div className="bg-gray-600 rounded-lg p-6">
                <h3 className="text-18-semibold text-white mb-4">ROI Summary</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="text-center">
                        <div className="text-24-bold text-green-400">
                            {formatCurrency(campaignMetrics?.totalSpent * 3.2)}
                        </div>
                        <div className="text-14-medium text-gray-300">Estimated Value Generated</div>
                    </div>
                    <div className="text-center">
                        <div className="text-24-bold text-blue-400">3.2x</div>
                        <div className="text-14-medium text-gray-300">Return on Investment</div>
                    </div>
                    <div className="text-center">
                        <div className="text-24-bold text-purple-400">
                            {formatCurrency(campaignMetrics?.costPerEngagement)}
                        </div>
                        <div className="text-14-medium text-gray-300">Cost per Engagement</div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default AnalyticsTab;
