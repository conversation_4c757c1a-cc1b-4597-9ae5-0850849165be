import { useState, useCallback } from 'react';
import { Toast } from '@shared/components/UI/Toast';

/**
 * Custom hook for handling API requests with loading state and toast notifications
 * @returns {object} API request utilities
 */
const useApiRequest = () => {
  // Track loading state
  const [isLoading, setIsLoading] = useState(false);
  // Track error state
  const [error, setError] = useState(null);

  /**
   * Execute an API request with loading state and notifications
   * @param {function} apiFunction - The API function to call
   * @param {object} options - Options for the API call
   * @param {string} options.loadingMessage - Message to show while loading
   * @param {string} options.successMessage - Message to show on success
   * @param {string} options.errorMessage - Message to show on error
   * @param {boolean} options.showLoadingToast - Whether to show a loading toast
   * @param {boolean} options.showSuccessToast - Whether to show a success toast
   * @param {boolean} options.showErrorToast - Whether to show an error toast
   * @returns {Promise<any>} The result of the API call
   */
  const executeRequest = useCallback(async (
    apiFunction,
    {
      loadingMessage = 'Processing request...',
      successMessage = 'Operation completed successfully',
      errorMessage = 'An error occurred',
      showLoadingToast = true,
      showSuccessToast = true,
      showErrorToast = true,
      ...apiParams
    } = {}
  ) => {
    // Clear previous errors
    setError(null);
    
    // Set loading state
    setIsLoading(true);
    
    // Show loading toast if requested
    const toastId = showLoadingToast ? Toast.loading(loadingMessage) : null;
    
    try {
      // Execute the API call
      const response = await apiFunction(apiParams);
      
      // Show success toast if requested
      if (showSuccessToast) {
        if (toastId) {
          Toast.update(toastId, { 
            render: successMessage, 
            type: 'success',
            autoClose: 3000
          });
        } else {
          Toast.success(successMessage);
        }
      } else if (toastId) {
        // Dismiss toast if we're not showing a success toast
        Toast.update(toastId, { 
          render: '',
          type: 'default',
          isLoading: false,
          autoClose: 1
        });
      }

      // Reset loading state
      setIsLoading(false);
      
      // Return the response
      return response;
    } catch (err) {
      // Capture the error
      setError(err);
      
      // Show error toast if requested
      if (showErrorToast) {
        const errorMsg = err.message ? `${errorMessage}: ${err.message}` : errorMessage;
        
        if (toastId) {
          Toast.update(toastId, { 
            render: errorMsg, 
            type: 'error',
            autoClose: 5000
          });
        } else {
          Toast.error(errorMsg);
        }
      } else if (toastId) {
        // Dismiss toast if we're not showing an error toast
        Toast.update(toastId, { 
          render: '',
          type: 'default',
          isLoading: false,
          autoClose: 1
        });
      }
      
      // Reset loading state
      setIsLoading(false);
      
      // Re-throw the error
      throw err;
    }
  }, []);
  
  /**
   * Creates a wrapper for a Redux thunk that shows appropriate toasts based on request status
   * @param {function} thunkAction - The Redux thunk action creator to wrap
   * @param {object} options - Toast options
   * @returns {function} A function that dispatches the thunk with toast notifications
   */
  const wrapReduxThunk = useCallback((
    thunkAction,
    {
      loadingMessage = 'Processing request...',
      successMessage = 'Operation completed successfully',
      errorMessage = 'An error occurred',
      showLoadingToast = true,
      showSuccessToast = true,
      showErrorToast = true
    } = {}
  ) => {
    return async (dispatch, ...args) => {
      let toastId;
      
      if (showLoadingToast) {
        toastId = Toast.loading(loadingMessage);
      }
      
      const result = await dispatch(thunkAction(...args));
      
      if (result.meta?.requestStatus === 'fulfilled') {
        if (showSuccessToast) {
          if (toastId) {
            Toast.update(toastId, { 
              render: successMessage, 
              type: 'success',
              autoClose: 3000
            });
          } else {
            Toast.success(successMessage);
          }
        } else if (toastId) {
          Toast.update(toastId, { 
            render: '',
            type: 'default',
            isLoading: false,
            autoClose: 1
          });
        }
      } else if (result.meta?.requestStatus === 'rejected') {
        if (showErrorToast) {
          const errorMsg = result.payload ? `${errorMessage}: ${result.payload}` : errorMessage;
          
          if (toastId) {
            Toast.update(toastId, { 
              render: errorMsg, 
              type: 'error',
              autoClose: 5000
            });
          } else {
            Toast.error(errorMsg);
          }
        } else if (toastId) {
          Toast.update(toastId, { 
            render: '',
            type: 'default',
            isLoading: false,
            autoClose: 1
          });
        }
      }
      
      return result;
    };
  }, []);

  return {
    isLoading,
    error,
    executeRequest,
    wrapReduxThunk
  };
};

export default useApiRequest;
