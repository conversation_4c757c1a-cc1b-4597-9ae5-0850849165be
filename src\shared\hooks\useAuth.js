import useAuthSelectors from "@features/auth/service/authSelectors";

/**
 * Custom hook that provides authentication state and user data
 * This is a wrapper around the Redux auth selectors for easier component usage
 */
export function useAuth() {
    const authState = useAuthSelectors();

    return {
        user: authState.user,
        isAuthenticated: authState.isAuthenticated,
        token: authState.token,
        status: authState.status,
        error: authState.error,
        hasError: authState.hasError,
        organizationBrands: authState.organizationBrands,
        allocatedBrands: authState.allocatedBrands,
    };
}