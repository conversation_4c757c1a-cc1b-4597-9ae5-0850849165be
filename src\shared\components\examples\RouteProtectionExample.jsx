import React from 'react';
import { Link } from 'react-router-dom';
import useUserType from '../../hooks/useUserType';

/**
 * Example component demonstrating the route protection system
 * This shows how to use the useUserType hook and handle different user scenarios
 */
const RouteProtectionExample = () => {
  const {
    userType,
    isBrand,
    isInfluencer,
    isAuthenticated,
    getDashboardPath,
    // getLoginPath,
    canAccessBrandRoutes,
    canAccessInfluencerRoutes,
    user,
    allocatedBrands,
    organizationBrands,
    socialProfiles
  } = useUserType();

  if (!isAuthenticated) {
    return (
      <div className="p-6 bg-red-50 border border-red-200 rounded-lg">
        <h2 className="text-xl font-bold text-red-800 mb-4">Not Authenticated</h2>
        <p className="text-red-700 mb-4">
          You need to log in to access this content.
        </p>
        <div className="space-x-4">
          <Link 
            to="/influencer/login" 
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Influencer Login
          </Link>
          <Link 
            to="/brand/signin" 
            className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
          >
            Brand Login
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 bg-white border border-gray-200 rounded-lg">
      <h2 className="text-2xl font-bold text-gray-800 mb-6">Route Protection System Demo</h2>
      
      {/* User Type Information */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-3">User Information</h3>
        <div className="grid grid-cols-2 gap-4">
          <div className="p-4 bg-gray-500 rounded">
            <p><strong>User Type:</strong> {userType || 'Unknown'}</p>
            <p><strong>Is Brand:</strong> {isBrand ? 'Yes' : 'No'}</p>
            <p><strong>Is Influencer:</strong> {isInfluencer ? 'Yes' : 'No'}</p>
            <p><strong>Authenticated:</strong> {isAuthenticated ? 'Yes' : 'No'}</p>
          </div>
          <div className="p-4 bg-gray-500 rounded">
            <p><strong>User Name:</strong> {user?.name || 'N/A'}</p>
            <p><strong>User Email:</strong> {user?.email || 'N/A'}</p>
            <p><strong>Dashboard Path:</strong> {getDashboardPath()}</p>
          </div>
        </div>
      </div>

      {/* User Data */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold text-gray-700 mb-3">User Data</h3>
        <div className="grid grid-cols-3 gap-4">
          <div className="p-4 bg-blue-50 rounded">
            <h4 className="font-medium text-blue-800">Allocated Brands</h4>
            <p className="text-sm text-blue-600">
              {allocatedBrands?.length || 0} brands
            </p>
            {allocatedBrands?.length > 0 && (
              <ul className="text-xs text-blue-500 mt-2">
                {allocatedBrands.slice(0, 3).map((brand, index) => (
                  <li key={index}>{brand.name || `Brand ${index + 1}`}</li>
                ))}
              </ul>
            )}
          </div>
          <div className="p-4 bg-green-50 rounded">
            <h4 className="font-medium text-green-800">Organization Brands</h4>
            <p className="text-sm text-green-600">
              {organizationBrands?.length || 0} brands
            </p>
            {organizationBrands?.length > 0 && (
              <ul className="text-xs text-green-500 mt-2">
                {organizationBrands.slice(0, 3).map((brand, index) => (
                  <li key={index}>{brand.name || `Org Brand ${index + 1}`}</li>
                ))}
              </ul>
            )}
          </div>
          <div className="p-4 bg-purple-50 rounded">
            <h4 className="font-medium text-purple-800">Social Profiles</h4>
            <p className="text-sm text-purple-600">
              {socialProfiles?.length || 0} profiles
            </p>
            {socialProfiles?.length > 0 && (
              <ul className="text-xs text-purple-500 mt-2">
                {socialProfiles.slice(0, 3).map((profile, index) => (
                  <li key={index}>{profile.platform || `Profile ${index + 1}`}</li>
                ))}
              </ul>
            )}
          </div>
        </div>
      </div>

      {/* Route Access Information */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold text-gray-700 mb-3">Route Access</h3>
        <div className="grid grid-cols-2 gap-4">
          <div className={`p-4 rounded ${canAccessBrandRoutes() ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'} border`}>
            <h4 className={`font-medium ${canAccessBrandRoutes() ? 'text-green-800' : 'text-red-800'}`}>
              Brand Routes Access
            </h4>
            <p className={`text-sm ${canAccessBrandRoutes() ? 'text-green-600' : 'text-red-600'}`}>
              {canAccessBrandRoutes() ? 'Allowed' : 'Denied'}
            </p>
          </div>
          <div className={`p-4 rounded ${canAccessInfluencerRoutes() ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'} border`}>
            <h4 className={`font-medium ${canAccessInfluencerRoutes() ? 'text-green-800' : 'text-red-800'}`}>
              Influencer Routes Access
            </h4>
            <p className={`text-sm ${canAccessInfluencerRoutes() ? 'text-green-600' : 'text-red-600'}`}>
              {canAccessInfluencerRoutes() ? 'Allowed' : 'Denied'}
            </p>
          </div>
        </div>
      </div>

      {/* Navigation Links */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold text-gray-700 mb-3">Test Navigation</h3>
        <div className="grid grid-cols-2 gap-4">
          <div>
            <h4 className="font-medium text-gray-600 mb-2">Brand Routes</h4>
            <div className="space-y-2">
              <Link 
                to="/brand/dashboard" 
                className="block px-3 py-2 bg-blue-500 text-white rounded text-sm hover:bg-blue-600"
              >
                Brand Dashboard
              </Link>
              <Link 
                to="/brand/discovery" 
                className="block px-3 py-2 bg-blue-500 text-white rounded text-sm hover:bg-blue-600"
              >
                Brand Discovery
              </Link>
              <Link 
                to="/brand/signin" 
                className="block px-3 py-2 bg-gray-500 text-white rounded text-sm hover:bg-gray-600"
              >
                Brand Login (Public)
              </Link>
            </div>
          </div>
          <div>
            <h4 className="font-medium text-gray-600 mb-2">Influencer Routes</h4>
            <div className="space-y-2">
              <Link 
                to="/influencer/dashboard" 
                className="block px-3 py-2 bg-green-500 text-white rounded text-sm hover:bg-green-600"
              >
                Influencer Dashboard
              </Link>
              <Link 
                to="/influencer/profile-analytics" 
                className="block px-3 py-2 bg-green-500 text-white rounded text-sm hover:bg-green-600"
              >
                Profile Analytics
              </Link>
              <Link 
                to="/influencer/login" 
                className="block px-3 py-2 bg-gray-500 text-white rounded text-sm hover:bg-gray-600"
              >
                Influencer Login (Public)
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Expected Behavior */}
      <div className="bg-yellow-50 border border-yellow-200 rounded p-4">
        <h3 className="text-lg font-semibold text-yellow-800 mb-2">Expected Behavior</h3>
        <ul className="text-sm text-yellow-700 space-y-1">
          <li>• Clicking on routes you can't access will redirect you to your dashboard</li>
          <li>• Clicking on public routes (login pages) will redirect you to your dashboard</li>
          <li>• Only routes matching your user type will work normally</li>
          <li>• The route protection system handles all redirects automatically</li>
        </ul>
      </div>
    </div>
  );
};

export default RouteProtectionExample;
