import React, { useEffect, useRef, useState } from "react";
import PerformanceMetricsGrid from "./PerformanceMetricsGrid";
import PostCarousel from "./PostCarousel";
// eslint-disable-next-line no-unused-vars
import { motion } from "framer-motion";

const tabs = [
    "Performance Overview",
    "Audience Demographics",
    "Sponsored Content Insights",
];

const YouTubeAnalytics = () => {
    const [activeTab, setActiveTab] = useState(tabs[0]);
    const tabContainerRef = useRef(null);
    const [tabPositions, setTabPositions] = useState({});

    const sectionRefs = useRef(
        tabs.reduce((acc, tab) => {
            acc[tab] = React.createRef();
            return acc;
        }, {})
    );

    useEffect(() => {
        if (tabContainerRef.current) {
            const positions = {};
            Array.from(tabContainerRef.current.children).forEach((child) => {
                const tab = child.dataset.tab;
                positions[tab] = {
                    left: child.offsetLeft,
                    width: child.offsetWidth,
                };
            });
            setTabPositions(positions);
        }
    }, [activeTab]);

    const handleTabClick = (tab) => {
        setActiveTab(tab);
        sectionRefs.current[tab]?.current?.scrollIntoView({ behavior: "smooth" });
    };

    return (
        <div className="w-full">
            {/* Tab Navigation */}
            <div className="sticky -top-6 z-10 bg-primary pt-5">
                <div className="rounded-tr-lg rounded-tl-lg bg-gray-500 z-10">
                    <div className="z-100 flex justify-between w-full" ref={tabContainerRef}>
                        {tabs.map((tab) => (
                            <button
                                key={tab}
                                data-tab={tab}
                                onClick={() => handleTabClick(tab)}
                                className={`w-full relative px-4 py-2 text-sm font-medium transition-colors duration-300 ${activeTab === tab ? "text-brand-500" : "text-gray-200 hover:text-brand-500"
                                    }`}
                            >
                                {tab}
                            </button>
                        ))}
                    </div>

                    {/* Animated Indicator */}
                    {tabPositions[activeTab] && (
                        <motion.div
                            className="absolute bottom-0 h-0.5 bg-[#38BDF8] rounded-full"
                            layout
                            transition={{ type: "spring", stiffness: 500, damping: 30 }}
                            style={{
                                left: tabPositions[activeTab].left,
                                width: tabPositions[activeTab].width,
                            }}
                        />
                    )}
                </div>
            </div>

            {/* Tab Content */}
            <div className="space-y-12 mt-6">
                <section ref={sectionRefs.current["Performance Overview"]} className="bg-gray-600 rounded-xl px-5 py-2.5 scroll-mt-18">
                    <h2 className="text-xl font-semibold mb-4">Performance Overview</h2>
                    <PerformanceMetricsGrid platform="youtube"  />
                    <PostCarousel platform="youtube" postCount={5} />
                </section>

                <section ref={sectionRefs.current["Audience Demographics"]} className="bg-gray-600 rounded-xl px-5 py-2.5 scroll-mt-18">
                    <h2 className="text-xl font-semibold mb-4">Audience Demographics</h2>
                    <p className="text-gray-400">Demographics data goes here.</p>
                </section>

                <section ref={sectionRefs.current["Sponsored Content Insights"]} className="bg-gray-600 rounded-xl px-5 py-2.5 scroll-mt-18">
                    <h2 className="text-xl font-semibold mb-4">Sponsored Content Insights</h2>
                    <p className="text-gray-400">Sponsored content insights go here.</p>
                </section>
            </div>
        </div>
    );
};

export default YouTubeAnalytics;
