import React, { useEffect, useState } from 'react';
import Highcharts from 'highcharts';
import HighchartsReact from 'highcharts-react-official';
import { useNavigate, useLocation } from "react-router-dom";
import { useDispatch } from "react-redux";
import { useLoading } from '@shared/components/UI/LoadingContext';
import { useSnackbar } from '@shared/components/UI/SnackbarContext';
import { requestUserBrandInfoThunk } from '@auth/service/authThunks';
import { selectAllChannelsWithIcons } from '@/app/store/slices/systemSlice';



import useBrandSelectors from '@brand/services/brandSelectors';
import useBrandActions from '@brand/services/brandActions';

import DashboardLayout from '@shared/layout/DashboardLayout';
import PlatformToggle from '../../../shared/components/PlatformToggle';
import CreatorCategoryCard from '../components/CreatorCategoryCard';

// Import icons from assets instead of using React icons
import SearchIcon from '@assets/icon/ai-search.svg';
import MapPinIcon from '@assets/icon/location.svg';
import CalendarIcon from '@assets/icon/calendar.svg';
import ResourcesIcon from '@assets/icon/resources.svg';
import DownIcon from '@assets/icon/down.svg';
import Frame1 from '@assets/Frame1.svg';
import { useSelector } from 'react-redux';
import Frame2 from '@assets/Frame2.svg';
import Frame3 from '@assets/Frame3.svg';
import instagramIcon from "@assets/icon/instagram-circle.svg";
import youtubeIcon from "@assets/icon/youtube-circle.svg";
import CreatorSuggestionCard from '../components/CreatorSuggestionCard';
import { hover } from 'framer-motion';

// Sample avatar URLs (to simulate data that would come from an API)
const sampleAvatars = {
    avatar1: "https://images.unsplash.com/photo-1494790108377-be9c29b29330?w=150&h=150&fit=crop&crop=faces",
    avatar2: "https://images.unsplash.com/photo-1570295999919-56ceb5ecca61?w=150&h=150&fit=crop&crop=faces",
    avatar3: "https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150&h=150&fit=crop&crop=faces",
    avatar4: "https://images.unsplash.com/photo-1539571696357-5a69c17a67c6?w=150&h=150&fit=crop&crop=faces",
    avatar5: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=faces",
    avatar6: "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=faces",
    avatar7: "https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=faces",
    avatar8: "https://images.unsplash.com/photo-1524504388940-b1c1722653e1?w=150&h=150&fit=crop&crop=faces",
    avatar9: "https://images.unsplash.com/photo-1520813792240-56fc4a3765a7?w=150&h=150&fit=crop&crop=faces",
    avatar10: "https://images.unsplash.com/photo-1517841905240-472988babdf9?w=150&h=150&fit=crop&crop=faces",
    avatar11: "https://images.unsplash.com/photo-1546967191-fdfb13ed6b1e?w=150&h=150&fit=crop&crop=faces",
    avatar12: "https://images.unsplash.com/photo-1546456073-6712f79251bb?w=150&h=150&fit=crop&crop=faces",
    avatar13: "https://images.unsplash.com/photo-1520813792240-56fc4a3765a7?w=150&h=150&fit=crop&crop=faces",
    avatar14: "https://images.unsplash.com/photo-1529626455594-4ff0802cfb7e?w=150&h=150&fit=crop&crop=faces",
    avatar16: "https://images.unsplash.com/photo-1510414696678-2415ad8474aa?w=150&h=150&fit=crop&crop=faces",
    avatar17: "https://images.unsplash.com/photo-1531123897727-8f129e1688ce?w=150&h=150&fit=crop&crop=faces",
    avatar20: "https://images.unsplash.com/photo-1544723795-3fb6469f5b39?w=150&h=150&fit=crop&crop=faces"
};

const getRandomAvatars = (count = 4) => {
    const avatarValues = Object.values(sampleAvatars);
    const shuffled = avatarValues.sort(() => 0.5 - Math.random());
    return shuffled.slice(0, count);
};

const gradientIconCombos = [
    { gradient: "linear-gradient(110deg, #D7FBF6 1.65%, #ACE7DE 96.8%)", Icon: 1 },
    { gradient: "linear-gradient(109deg, #FFF6F6 2.59%, #FBCFCF 96.84%)", Icon: 2 },
    { gradient: "linear-gradient(110deg, #F7F0FF 3.42%, #D4BBEE 98.53%)", Icon: 3 },
    { gradient: "linear-gradient(110deg, #E2FDFD 5.14%, #BEEEF9 96.47%)", Icon: 4 },
    { gradient: "linear-gradient(109deg, #FEFCEA 2.85%, #F5EDAA 98.4%)", Icon: 5 },
    { gradient: "linear-gradient(110deg, #EDEAFF 1.87%, #C0B9EC 100%)", Icon: 6 },
    { gradient: "linear-gradient(109deg, #E8F6FF 1.16%, #8BC1E4 97.37%)", Icon: 7 },
    { gradient: "linear-gradient(109deg, #FFF3D8 0.85%, #E6D2A4 97.47%)", Icon: 8 },
    { gradient: "linear-gradient(110deg, #D7FBF6 1.65%, #ACE7DE 96.8%)", Icon: 1 },
    { gradient: "linear-gradient(109deg, #FFF6F6 2.59%, #FBCFCF 96.84%)", Icon: 2 },
    { gradient: "linear-gradient(110deg, #F7F0FF 3.42%, #D4BBEE 98.53%)", Icon: 3 },
    { gradient: "linear-gradient(110deg, #E2FDFD 5.14%, #BEEEF9 96.47%)", Icon: 4 },
];

// Featured creators data with external avatar URLs
const featuredCreators = [
    {
        name: "Ananya Pandey",
        subscribers: "120K",
        avatar: "https://images.unsplash.com/photo-1580489944761-15a19d654956?w=150&h=150&fit=crop&crop=faces",
        tags: ["AI & Future Tech", "Tech", "Apps & Software"],
        badge: "🏆 Best Engagement",
        bgColor: "bg-light-2",
        impressions: "65",
        engagementRate: "5.2",
        platform: [
            { src: youtubeIcon, className: "ml-0 " },
        ],
        requirements: [
            { isChecked: true, label: "Tech & Lifestyle" },
            { isChecked: true, label: "500k+ Audience" },
            { isChecked: true, label: "Audience Age: 25–34 Aligned" },
            { isChecked: false, label: "English Only" }
        ],
        profileLink: "https://creatorverse.com/creators/ananya-pandey",
        socialHandle: "@ananya_tech"
    },
    {
        name: "Shruti Sinha",
        subscribers: "120K",
        avatar: "https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?w=150&h=150&fit=crop&crop=faces",
        tags: ["AI & Future Tech", "Tech", "Apps & Software"],
        badge: "🔥 Fastest Growing",
        bgColor: "bg-light-3",
        impressions: "65",
        engagementRate: "5.2",
        platform: [
            { src: instagramIcon, className: "ml-0 " },
        ],
        requirements: [
            { isChecked: true, label: "Tech & Lifestyle" },
            { isChecked: true, label: "500k+ Audience" },
            { isChecked: true, label: "Audience Age: 25–34 Aligned" },
            { isChecked: false, label: "English Only" }
        ],
        profileLink: "https://creatorverse.com/creators/shruti-sinha",
        socialHandle: "@shruti.sinha"
    },
    {
        name: "Kriti Vyas",
        subscribers: "120K",
        avatar: "https://images.unsplash.com/photo-1499952127939-9bbf5af6c51c?w=150&h=150&fit=crop&crop=faces",
        tags: ["AI & Future Tech", "Tech", "Apps & Software"],
        badge: "🎯 Perfect Match (100% Match Score)",
        bgColor: "bg-light-5",
        impressions: "65",
        engagementRate: "5.2",
        platform: [
            { src: youtubeIcon, className: "ml-0 " },
        ],
        requirements: [
            { isChecked: true, label: "Tech & Lifestyle" },
            { isChecked: true, label: "500k+ Audience" },
            { isChecked: true, label: "Audience Age: 25–34 Aligned" },
            { isChecked: false, label: "English Only" }
        ],
        profileLink: "https://creatorverse.com/creators/kriti-vyas",
        socialHandle: "@kritivyas"
    },
    {
        name: "Sourya Singh",
        subscribers: "120K",
        avatar: "https://images.unsplash.com/photo-1531746020798-e6953c6e8e04?w=150&h=150&fit=crop&crop=faces",
        tags: ["AI & Future Tech", "Tech", "Apps & Software", "Apps & Software"],
        badge: "🏆 Best Engagement",
        bgColor: "bg-light-6",
        impressions: "85",
        engagementRate: "5.2",
        platform: [
            { src: youtubeIcon, className: "ml-0 " },
        ],
        requirements: [
            { isChecked: true, label: "Tech & Lifestyle" },
            { isChecked: true, label: "500k+ Audience" },
            { isChecked: true, label: "Audience Age: 25–34 Aligned" },
            { isChecked: false, label: "English Only" }
        ],
        profileLink: "https://creatorverse.com/creators/sourya-singh",
        socialHandle: "@sourya.singh"
    }
];


//campaigns Data
var data = {
    "walletBalance": 34000,
    "overspentAmount": 8660,
    "campaigns": {
        "active": 1,
        "planned": 2,
        "completed": 3,
        "draft": 1,
        "total": 1
    },
    "topCampaigns":
    {
        "id": "1",
        "name": "The Buzz Bootcamp",
        "platform": "instagram",
        "date": "12 June, 2025",
        "status": "Active",
        "spend": 15200,
        "engagement": "9.7",
        "impressions": "2.8M"
    },
    "creatorProgress": {
        "onboarded": 12,
        "required": 15,
        "label": "Almost There!"
    },
    "contentProgress": {
        "posted": 128,
        "total": 140,
        "label": "Off to a Great Start"
    },
    "spendChart": {
        "months": ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"],
        "totalSpent": [5000, 7000, 9000, 15000, 14000, 15200, 19000, 13500, 18000, 15500, 19000, 20000],
        "totalBudget": [6000, 8000, 10000, 12000, 15000, 17000, 18000, 18500, 19000, 19500, 20000, 21000]
    }
};


const BrandDashboard = () => {
    const [creatorCategories, setCreatorCategories] = useState([]);
    const navigate = useNavigate();
    const dispatch = useDispatch();
    const location = useLocation();
    const { setIsLoading } = useLoading();
    const { showSnackbar } = useSnackbar();
    const { getGlobalFilters, setCurrentFilterSet } = useBrandActions();
    const { selectedBrand } = useBrandSelectors();
    const channels = useSelector((state) => selectAllChannelsWithIcons(state));


    const { campaigns } = data;


    useEffect(() => {
        const runEffect = async () => {
            setIsLoading(true);

            try {
                const searchParams = new URLSearchParams(location.search);
                if (searchParams.get('accessToken')) {
                    const accessToken = searchParams.get('accessToken');
                    const refreshToken = searchParams.get('refreshToken');

                    localStorage.setItem('auth_token', accessToken);
                    localStorage.setItem('refresh_token', refreshToken);
                    getBrandUserData();

                    // Remove query params from URL
                    const newUrl = window.location.pathname;
                    window.history.replaceState({}, document.title, newUrl);
                }

                const result = await getGlobalFilters();

                if (result.meta.requestStatus === 'fulfilled') {
                    // Get the response data from the thunk
                    const responseData = result.payload.data;

                    const limited = getLimitedCategories(responseData, 4);
                    console.log('Limited Categories:', limited);

                    const transformed = limited.map((filter, index) => ({
                        id: filter.id,
                        title: filter.title,
                        description: filter.description,
                        platform: filter.platform,
                        filterData: filter.filters,
                        avatars: getRandomAvatars(),
                        count: filter?.count || Math.floor(Math.random() * (99 - 50 + 1)) + 50,
                        ...gradientIconCombos[index]
                    }));

                    if (transformed.length > 0) {
                        localStorage.setItem('globalFilters', JSON.stringify(responseData));
                        setCreatorCategories(transformed);
                        return;
                    } else {

                        showSnackbar('No global filters found', 'info');
                    }
                } else {
                    // Handle failure case
                    const errorMessage = result.payload.message || 'Get global fliter request failed. Please try again.';
                    showSnackbar(errorMessage, 'error');
                }
            } catch (error) {
                console.error('Get global fliter request error:', error);
                showSnackbar(error.message || 'Failed to request Get global fliter.', 'error');
            } finally {
                setIsLoading(false);
            }
        };
        runEffect();

    }, [dispatch]);

    const getBrandUserData = async () => {

        setIsLoading(true);

        try {
            const result = await dispatch(requestUserBrandInfoThunk());

            console.log('Brand User fetched result:', result);

            if (result.meta.requestStatus === 'fulfilled') {
                // Get the response data from the thunk
                const responseData = result.payload.data;
                if (responseData.allocated_brands.length > 0) {
                    localStorage.setItem('allocatedBrands', JSON.stringify(responseData.allocated_brands));
                    localStorage.setItem('organizationBrands', JSON.stringify(responseData.organization_brands));
                }
            } else {
                // Handle failure case
                const errorMessage = result.payload.message || 'Brand access request failed. Please try again.';
                showSnackbar(errorMessage);
            }
        } catch (error) {
            console.error('Brand access request error:', error);
            showSnackbar(error.message || 'Failed to request brand access.');
        } finally {
            setIsLoading(false);
        }
    }

    // Handle category card clicks
    const handleCategoryClick = (category) => {
        console.log('Category clicked:', category);
        setCurrentFilterSet(category.filterData);
        navigate('/brand/discovery', { state: { selectedCategory: category, channel: category.platform } });
        console.log('Navigating to discovery view with category:', category);
    };

    const getLimitedCategories = (categories, limitPerPlatform = 4) => {
        const platformMap = new Map();

        categories.forEach((category) => {
            const platform = category.platform;
            if (!platformMap.has(platform)) {
                platformMap.set(platform, []);
            }
            const group = platformMap.get(platform);
            if (group.length < limitPerPlatform) {
                group.push(category);
            }
        });

        // Flatten the grouped values back into a single array
        return Array.from(platformMap.values()).flat();
    };

    console.log('Creator asdfasdfasdfasd:', channels);

    return (
        <div className="h-full w-full bg-primary/70 flex flex-col gap-7.5 ">
            {/* Header */}
            <div className="flex items-center">
                <h1 className="text-24-bold text-gray-50">Hello, {selectedBrand?.name ? selectedBrand?.name : 'Brand'}!</h1>
            </div>

            <div className='flex flex-col items-start'>
                {campaigns?.total === 0 ? (
                    <h2 className="text-20-bold text-gray-50">
                        Discover the <span className="text-yellow">right creators</span> for your brand
                    </h2>
                ) : (
                    <h2 className="text-20-bold text-gray-50">
                        Here’s a quick look at how your <span className="text-yellow">
                            {campaigns.total === 1 ? 'campaign' : 'campaigns'}
                        </span> are doing! 📢
                    </h2>
                )}
            </div>


            {campaigns?.total == 0 && (

                <>
                    {/* Creator Categories Grid - Now using the Card component */}
                    <div className="grid grid-cols-4 gap-4 mb-8">
                        {creatorCategories.map((category, index) => (
                            <CreatorCategoryCard
                                key={index}
                                title={category.title}
                                description={category.description}
                                tag={category.tag}
                                tagColor={category.tagColor}
                                gradient={category.gradient}
                                avatars={category.avatars}
                                count={category.count}
                                platform={category.platform}
                                onClick={() => {
                                    handleCategoryClick(category);
                                }}
                                Icon={category.Icon}
                            />
                        ))}
                    </div>

                    {/* Sample Promotion */}
                    <div className='flex w-full justify-between'>
                        <div className="bg-transparent w-full p-4 rounded-lg flex flex-col gap-4 items-center text-center">
                            <img src={Frame3} alt="Frame 3" className=" rounded-md h-40" />
                            <h2 className="text-20-semibold mb-2 mt-7">Easily Plan Your Campaigns</h2>
                            <p className="text-gray-100 text-18-medium mb-4">Use the Creatorverse Calendar to view and plan your campaigns at a glance simple, clear, and stress-free.</p>
                        </div>
                        <div className="bg-transparent w-full p-4 rounded-lg flex flex-col gap-4 items-center text-center">
                            <img src={Frame1} alt="Frame 1" className=" rounded-md h-40" />
                            <h2 className="text-20-semibold mb-2 mt-7">Transparent Campaign Insights</h2>
                            <p className="text-gray-100 text-18-medium mb-4">Get clear, actionable campaign insights to fuel your next move</p>
                        </div>
                        <div className="bg-transparent w-full p-4 rounded-lg flex flex-col gap-4 items-center text-center">
                            <img src={Frame2} alt="Frame 2" className=" rounded-md h-40" />
                            <h2 className="text-20-semibold mb-2 mt-7">View Reports Instantly</h2>
                            <p className="text-gray-100 text-18-medium mb-4">Interpret the data, improvise your strategy, and drive real impact</p>
                        </div>
                    </div>

                    <div className='flex justify-center pb-10'>
                        <button
                            onClick={() => { }}
                            className="px-8 py-2.5 bg-brand-500 text-white rounded hover:bg-brand-600 transition"
                        >
                            + Let's Create Campaign
                        </button>
                    </div>
                </>

            )}

            {campaigns?.total > 0 && (
                <div className='flex flex-col gap-5'>
                    <div className='flex md:flex-col xl:flex-row gap-5'>
                        {/* Left Side */}
                        <div className='w-full xl:w-1/2'>
                            {campaigns?.total === 1 ? (
                                <div className='flex flex-col gap-5 h-full'>
                                    <div className='rounded-xl border-1 border-gray-400 p-5 h-1/2'>
                                        <div className='flex justify-between items-center h-1/2'>
                                            <div className='flex items-center gap-2'>
                                                <i class="fi fi-rs-diamond pt-1 px-1.5 text-brand-600 bg-brand-200 drop-shadow-sm drop-shadow-brand-200 rounded-lg"></i>
                                                <h2 className='text-16-medium text-gray-50'>Creators Onboarded / Required</h2>
                                            </div>
                                            <div className='flex flex-col items-end text-gray-500 text-12-semibold px-2 py-1 bg-light-8 rounded-full h-fit'>
                                                🏁 Almost There!
                                            </div>
                                        </div>
                                        <div className='flex justify-between items-center h-1/2'>
                                            <div className='flex gap-2.5 w-full items-center'>
                                                <div className=''>
                                                    <span className='text-20-semibold text-sky-blue'>
                                                        {data.creatorProgress.onboarded}
                                                    </span>
                                                    <span className='text-16-semibold text-gray-300'>
                                                        /{data.creatorProgress.required}
                                                    </span>
                                                </div>
                                                <div className="w-full h-3 rounded-full bg-gray-800">
                                                    <div className="h-full bg-sky-blue rounded-full" style={{ width: `${data.creatorProgress.onboarded / data.creatorProgress.required * 100}%` }}></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div className='rounded-xl border-1 border-gray-400 p-5 h-1/2'>
                                        <div className='flex justify-between items-center h-1/2'>
                                            <div className='flex items-center gap-2'>
                                                <i class="fi  fi-rs-registration-paper pt-1 px-1.5 text-orange bg-light-4 drop-shadow-sm drop-shadow-light-4 rounded-lg"></i>
                                                <h2 className='text-16-medium text-gray-50'>Content Posted / Total Content</h2>
                                            </div>
                                            <div className='flex flex-col items-end text-gray-500 text-12-semibold px-2 py-1 bg-light-6 rounded-full h-fit'>
                                                🚀 Off to a Great Start
                                            </div>
                                        </div>
                                        <div className='flex justify-between items-center h-1/2'>
                                            <div className='flex gap-2.5 w-full items-center'>
                                                <div className=''>
                                                    <span className='text-20-semibold text-yellow'>
                                                        {data.creatorProgress.onboarded}
                                                    </span>
                                                    <span className='text-16-semibold text-gray-300'>
                                                        /{data.creatorProgress.required}
                                                    </span>
                                                </div>
                                                <div className="w-full h-3 rounded-full bg-gray-800">
                                                    <div className="h-full bg-yellow rounded-full" style={{ width: `${data.contentProgress.posted / data.contentProgress.total * 100}%` }}></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                </div>
                            ) : (
                                <div className='flex flex-col gap-5 h-full'>
                                    <div className='flex flex-col rounded-xl border-1 border-gray-400 p-5 flex-1 gap-4'>
                                        <div className='flex items-center gap-2.5 h-fit'>
                                            <i class="fi fi-rs-megaphone pt-1 px-1.5 text-violet bg-light-1 drop-shadow-sm drop-shadow-light-1 rounded-lg"></i>
                                            <h2 className='text-20-semibold text-gray-200'>Total Campaigns: <span className='text-gray-50'>{data.campaigns.total}</span></h2>
                                        </div>
                                        <div className='flex items-center h-full gap-4'>
                                            <div className='flex flex-col gap-2.5 h-full w-full rounded-lg py-3.5 px-5.5 items-center bg-sky-blue'
                                                style={{
                                                    background: 'linear-gradient(110deg, #E2FDFD 5.14%, #BEEEF9 96.47%)'
                                                }}>
                                                <div className='flex items-center gap-2.5 h-fit'>
                                                    <i class="fi fi-rs-megaphone pt-1 px-1.5 text-brand-600 bg-white drop-shadow-sm drop-shadow-brand-200 rounded-lg"></i>
                                                    <h2 className='text-14-medium text-gray-900'>Active</h2>
                                                </div>
                                                <span className='text-30-bold text-brand-600'>{data.campaigns?.active || 0}</span>
                                            </div>
                                            <div className='flex flex-col gap-2.5 h-full w-full rounded-lg py-3.5 px-5.5 items-center'
                                                style={{
                                                    background: 'linear-gradient(109deg, #FFF6F6 2.59%, #FBCFCF 96.84%)'
                                                }}>
                                                <div className='flex items-center gap-2.5 h-fit'>
                                                    <i class="fi fi-br-hourglass-end pt-1 px-1.5 text-red-2 bg-white drop-shadow-sm drop-shadow-red-2/50 rounded-lg"></i>
                                                    <h2 className='text-14-medium text-gray-900'>Planned</h2>
                                                </div>
                                                <span className='text-30-bold text-red-2'>{data.campaigns?.planned || 0}</span>
                                            </div>
                                            <div className='flex flex-col gap-2.5 h-full w-full rounded-lg py-3.5 px-5.5 items-center'
                                                style={{
                                                    background: 'linear-gradient(110deg, #F6FFE3 0%, #CBE593 100%)'
                                                }}>
                                                <div className='flex items-center gap-2.5 h-fit'>
                                                    <i class="fi fi-ss-thumbtack pt-1 px-1.5 text-teal bg-white drop-shadow-sm drop-shadow-red-2/50 rounded-lg"></i>
                                                    <h2 className='text-14-medium text-gray-900'>Planned</h2>
                                                </div>
                                                <span className='text-30-bold text-teal'>{data.campaigns?.completed || 0}</span>
                                            </div>
                                            <div className='flex flex-col gap-2.5 h-full w-full rounded-lg py-3.5 px-5.5 items-center'
                                                style={{
                                                    background: 'linear-gradient(110deg, #EDEAFF 1.87%, #C0B9EC 100%)'
                                                }}>
                                                <div className='flex items-center gap-2.5 h-fit'>
                                                    <i class="fi fi-rs-time-quarter-past pt-1 px-1.5 text-purple bg-white drop-shadow-sm drop-shadow-purple/50 rounded-lg"></i>
                                                    <h2 className='text-14-medium text-gray-900'>Draft</h2>
                                                </div>
                                                <span className='text-30-bold text-purple'>{data.campaigns?.draft || 0}</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div className='flex flex-col rounded-xl border-1 border-gray-400 p-5 flex-1 gap-3'>
                                        <div className='flex justify-between items-center'>
                                            <div className='flex items-center gap-3'>
                                                <i class="fi fi-sr-crown pt-1 px-1.5 text-orange bg-light-4 drop-shadow-sm drop-shadow-light-4 rounded-full"></i>
                                                <span className='text-30-semibold text-gray-50'>#1</span>
                                                <div className='flex flex-col'>
                                                    <h2 className='text-18-medium text-gray-50'>The Buzz Bootcamp</h2>
                                                    <p className='text-14-regular text-gray-300'>Date: {data.topCampaigns.date}</p>
                                                </div>
                                            </div>
                                            <div className='flex items-center gap-2'>
                                                {channels
                                                    .filter(channel => channel.name.toLowerCase() === data.topCampaigns.platform.toLowerCase())
                                                    .map((channel, index) => (
                                                        <img
                                                            key={index}
                                                            src={channel.icon.url}
                                                            alt={channel.mname}
                                                            className="w-6 h-6 object-contain"
                                                        />
                                                    ))}
                                                <h2 className='text-18-semibold text-gray-100 capitalize'>{data.topCampaigns.platform}</h2>
                                            </div>
                                        </div>
                                        <div className='flex w-full rounded-lg py-3.5 px-10 items-center bg-sky-blue'
                                            style={{
                                                background: 'linear-gradient(109deg, #E8F6FF 1.16%, #8BC1E4 97.37%)'
                                            }}>
                                            <div className='flex flex-col justify-center items-center w-full gap-1'>
                                                <span className='text-[#1A237E] text-16-medium'>Spend</span>
                                                <span className='text-20-semibold text-gray-900'>₹{data.topCampaigns.spend}</span>
                                            </div>
                                            <div className='flex flex-col justify-center items-center w-full gap-1'>
                                                <span className='text-[#1A237E] text-16-medium'>Engagement</span>
                                                <span className='text-20-semibold text-gray-900'>{data.topCampaigns.engagement}%</span>
                                            </div>
                                            <div className='flex flex-col justify-center items-center w-full gap-1'>
                                                <span className='text-[#1A237E] text-16-medium'>Impression</span>
                                                <span className='text-20-semibold text-gray-900'>{data.topCampaigns.impressions}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            )}
                        </div>
                        {/* Right Side */}
                        <div className='w-full xl:w-1/2 rounded-xl border-1 border-gray-400 py-5 px-3'>
                            <div className='flex justify-between gap-9'>
                                <div className='flex flex-col gap-2.5'>
                                    <div className='flex gap-2 items-center'>
                                        <i class="fi fi-br-wallet pt-1 px-1.5 bg-purple drop-shadow-sm drop-shadow-purple rounded-lg"></i>
                                        <h2 className='text-16-medium text-gray-50'>Total Spent</h2>
                                    </div>
                                    <div className='flex gap-2 items-center'>
                                        <p className='text-16-regular text-gray-200'>Wallet Balance <span className='text-18-medium text-gray-50'>₹{data.walletBalance}</span></p>
                                    </div>
                                </div>
                                <div className='flex flex-col items-end text-gray-500 text-12-semibold px-2 py-1 bg-light-6 rounded-full h-fit'>
                                    💸 Overspent by ₹{data.overspentAmount}
                                </div>
                            </div>
                            {/* SpendChart here */}
                            <div className="w-full mt-4">
                                <HighchartsReact
                                    highcharts={Highcharts}
                                    options={{
                                        chart: {
                                            type: 'spline',
                                            backgroundColor: 'transparent',
                                            height: 200
                                        },
                                        title: { text: null },
                                        xAxis: {
                                            categories: data.spendChart.months,
                                            labels: { style: { color: 'var(--color-gray-100)' } },
                                            gridLineWidth: 0,
                                            lineColor: '#444',
                                            tickColor: '#444'
                                        },
                                        yAxis: {
                                            title: { text: null },
                                            labels: { style: { color: 'var(--color-gray-100)' } },
                                            gridLineColor: '#333',
                                            tickAmount: 6
                                        },
                                        legend: {
                                            align: 'right',
                                            verticalAlign: 'top',
                                            itemStyle: {
                                                color: 'var(--color-gray-100)',
                                                fontWeight: 'normal',
                                                itemHoverStyle: {
                                                    color: 'var(--color-gray-100)'
                                                }
                                            }
                                        },
                                        tooltip: {
                                            shared: false,
                                            backgroundColor: 'var(--color-primary)',
                                            borderColor: '#333',
                                            style: { color: '#fff' }
                                        },
                                        // series: [
                                        //     {
                                        //         name: 'Total Spent',
                                        //         data: data.spendChart.totalSpent,
                                        //         color: '#FF6B6B',
                                        //         marker: { enabled: false }
                                        //     },
                                        //     {
                                        //         name: 'Total Budget',
                                        //         data: data.spendChart.totalBudget,
                                        //         color: '#12C9AC',
                                        //         marker: { enabled: false }
                                        //     }
                                        // ],
                                        series: [
                                            {
                                                name: 'Total Spent',
                                                type: 'areaspline', // 👈 changed from 'spline' to 'areaspline'
                                                data: data.spendChart.totalSpent,
                                                color: '#FF6B6B', // Line color
                                                fillColor: {
                                                    linearGradient: [0, 0, 0, 100],
                                                    stops: [
                                                        [0, 'rgba(255, 107, 107, 0.2)'], // Top (near line)
                                                        [1, 'rgba(255, 107, 107, 0)']    // Bottom (transparent)
                                                    ]
                                                },
                                                fillOpacity: 0.5,
                                                marker: { enabled: false }
                                            },
                                            {
                                                name: 'Total Budget',
                                                type: 'areaspline',
                                                data: data.spendChart.totalBudget,
                                                color: '#12C9AC',
                                                fillColor: {
                                                    linearGradient: [0, 0, 0, 110],
                                                    stops: [
                                                        [0, 'rgba(18, 201, 172, 0.1)'],
                                                        [1, 'rgba(18, 201, 172, 0)']
                                                    ]
                                                },
                                                fillOpacity: 0.2,
                                                marker: { enabled: false }
                                            }
                                        ],
                                        credits: { enabled: false }
                                    }}
                                />
                            </div>
                        </div>
                    </div>

                    {/* Featured Creators */}
                    <div className="p-5 bg-gray-600 rounded-2xl">
                        <div className="flex justify-between items-center mb-4">
                            <h2 className="text-xl font-semibold text-white">Creators You'll Love</h2>
                            <button className="text-gray-200 hover:text-brand-500 font-medium cursor-pointer hover:underline">Find More Creators</button>
                        </div>

                        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                            {featuredCreators.map((creator, index) => (
                                <CreatorSuggestionCard
                                    profileLogo={creator.avatar}
                                    key={index}
                                    name={creator.name}
                                    badge={creator.badge}
                                    impressions={creator.impressions}
                                    engagementRate={creator.engagementRate}
                                    bgColor={creator.bgColor}
                                    avatar={creator.avatar}
                                    subscribers={creator.subscribers}
                                    profileLink={creator.profileLink}
                                    socialHandle={creator.socialHandle}
                                    platform={creator.platform}
                                    tags={creator.tags}
                                    requirements={creator.requirements}
                                />
                            ))}
                        </div>
                    </div>

                    <div className='h-40'></div>

                </div>
            )}








            {/* <div className='h-[1000px] text-gray-900'>.</div> */}

        </div>
    );
};

export default BrandDashboard;
