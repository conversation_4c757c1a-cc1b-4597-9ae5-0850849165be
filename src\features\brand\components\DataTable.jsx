import React, { useState, useEffect } from 'react';
import TooltipSpan from './TooltipSpan';
import UpIcon from '@assets/icon/up.svg';
import DownIcon from '@assets/icon/down.svg';


const DataTable = ({
    data,
    columns,
    sortConfig,
    onSort,
    selectable = true,
    rowKey = 'id',
    headerRowClass = "border-brand-500",
    onRowMouseLeave = () => { },
    onSelectionChange = () => { },
    onRowClick = () => { },
}) => {
    const [selectedItems, setSelectedItems] = useState(new Set());
    const [selectAll, setSelectAll] = useState(false);

    const updateParent = (selectedSet) => {
        const selectedRows = data.filter(row => selectedSet.has(row[rowKey]));
        onSelectionChange(selectedRows); // Notify parent
    };

    const toggleSelectItem = (id) => {
        setSelectedItems((prev) => {
            const newSet = new Set(prev);
            if (newSet.has(id)) {
                newSet.delete(id);
            } else {
                newSet.add(id);
            }
            updateParent(newSet); // Update here
            return newSet;
        });
    };

    const toggleSelectAll = () => {
        let newSet;
        if (selectAll) {
            newSet = new Set();
        } else {
            newSet = new Set(data.map((item) => item[rowKey]));
        }
        setSelectedItems(newSet);
        setSelectAll(!selectAll);
        updateParent(newSet); // Update here
    };

    // const getSortIcon = (key) => {
    //     if (sortConfig?.key !== key) return null;
    //     return sortConfig.direction === 'asc' ? UpIcon : DownIcon;
    // };

    useEffect(() => {
        // Sync "select all" checkbox state
        if (selectedItems.size === data.length && data.length > 0) {
            setSelectAll(true);
        } else {
            setSelectAll(false);
        }
    }, [selectedItems, data]);

    return (
        <div className="rounded-lg bg-gray-600">
            <table className="w-full text-left text-gray-300 border-collapse">
                <thead className=" text-gray-400 capitalize text-14-medium">
                    <tr className={`${headerRowClass} border-b-2 text-gray-50 text-14-medium`}>
                        {selectable ? (
                            <th className="px-4 py-3 w-10">
                                <input
                                    type="checkbox"
                                    checked={selectAll}
                                    onChange={(e) => {
                                        e.stopPropagation(); // Prevent triggering any parent click
                                        toggleSelectAll();
                                    }}
                                />
                            </th>
                        ) : (
                            // <td className='px-4 py-6 w-full h-full flex items-end justify-center bg-red'>s</td>
                            null
                        )}
                        {columns.map((col, idx) => (
                            <th
                                key={col.key}
                                style={{ width: col.width }}
                                className={`${!selectable && idx === 0 ? 'pl-6':''} px-4 py-5 ${col.sortable ? 'cursor-pointer' : ''}`}
                                onClick={() => col.sortable && onSort(col.key)}
                            >
                                <div className="flex items-center gap-1">
                                    {col.tooltip ? (
                                        <TooltipSpan text={col.header} tooltipText={col.tooltip} position={col.tooltipPosition} />
                                    ) : (
                                        <span>{col.header}</span>
                                    )}
                                    {col.sortable && (
                                        <div className="flex flex-col items-center ml-1">
                                            <img
                                                src={UpIcon}
                                                alt="Sort up"
                                                className={`w-4 h-4 -mb-1 ${sortConfig?.key === col.key && sortConfig.direction === 'asc' ? 'opacity-100' : 'opacity-30'}`}
                                            />
                                            <img
                                                src={DownIcon}
                                                alt="Sort down"
                                                className={`w-4 h-4 ${sortConfig?.key === col.key && sortConfig.direction === 'desc' ? 'opacity-100' : 'opacity-30'}`}
                                            />
                                        </div>
                                    )}

                                </div>
                            </th>
                        ))}
                    </tr>
                </thead>
                <tbody className="bg-primary/70">
                    {data.map((row) => (
                        <tr
                            key={row[rowKey]}
                            className="group hover:bg-gray-600 transition hover:cursor-pointer border-b border-gray-900"
                            onMouseLeave={() => onRowMouseLeave(row)}
                            onClick={(e) => {
                                e.stopPropagation(); // prevent parent click
                                onRowClick(row);
                            }}
                        >
                            {selectable ? (
                                <td className="px-4 py-2">
                                    <input
                                        type="checkbox"
                                        checked={selectedItems.has(row[rowKey])}
                                        onClick={(e) => e.stopPropagation()} // This prevents row click
                                        onChange={() => toggleSelectItem(row[rowKey])}
                                    />
                                </td>
                            ) : (
                                // <td className='px-4 py-2 text-center w-5'>s</td>
                                null
                            )}
                            {columns.map((col, idx) => (
                                <td key={col.key} className={`${!selectable && idx === 0 ? 'pl-6':''} px-4 py-2`}>
                                    {col.render ? col.render(row) : row[col.key]}
                                </td>
                            ))}
                        </tr>
                    ))}
                </tbody>
            </table>

            {data.length === 0 && (
                <div className="py-12 text-center">
                    <div className="text-gray-400 text-16-regular">
                        No data found matching your criteria.
                    </div>
                    <div className="text-gray-500 text-14-regular mt-1">
                        Try adjusting your search or filters.
                    </div>
                </div>
            )}
        </div>
    );
};

export default DataTable;
