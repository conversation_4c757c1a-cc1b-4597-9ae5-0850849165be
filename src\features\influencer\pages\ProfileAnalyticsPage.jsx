import React, { useState } from "react";
// eslint-disable-next-line no-unused-vars
import { AnimatePresence, motion } from "framer-motion"; // make sure it's imported at the top
import { useNavigate } from "react-router-dom";
import DashboardLayout from "@shared/layout/DashboardLayout";
import InstagramAnalytics from "@influencer/components/InstagramAnalytics";
import YouTubeAnalytics from "@influencer/components/YouTubeAnalytics";
import ArrowLeft from "@assets/icon/arrow-left.svg";
import instagramIcon from "@assets/icon/instagram-circle.svg";
import youtubeIcon from "@assets/icon/youtube-circle.svg";
import ContentCopy from "@assets/icon/content_copy.svg";
import PlatformToggle from "@shared/components/PlatformToggle";



const user = {
    name: "<PERSON><PERSON><PERSON>",
    title: "Product manager | Locobuzz | RCB",
    channelLinked: ["Instagram", "YouTube"],
    portfolioLink: "https://www.behance.net/snehajdedh46ff",
    languages: ["Hindi", "English", "Gujarati"],
    categories: ["Fashion", "Tech", "Lifestyle", "Sports", "Book Reading"],
    avatar: "https://randomuser.me/api/portraits/men/45.jpg"
};

const channelIcons = {
    Instagram: {
        src: instagramIcon,
        alt: "Instagram"
    },
    YouTube: {
        src: youtubeIcon,
        alt: "YouTube"
    }
    // Add more platforms here as needed
};

const ProfileAnalyticsPage = () => {
    const navigate = useNavigate();

    const [activeChannel, setActiveChannel] = useState("instagram");

    const handleBack = () => {
        navigate(-1);
    };

    return (
        <div className="h-full w-full bg-primary/70 max-w-[1670px] scrollbar-hide">
            <div className="flex items-center">
                <h1 className="mt-2.5 mb-5 text-24-bold text-gray-50">Hello, Creator</h1>
            </div>
            <div className="flex flex-col justify-between items-start rounded-xl gap-8 mb-4 w-full">
                <div className="flex items-center gap-x-2.5 w-full py-2 ">
                    <button
                        onClick={handleBack}
                        className="flex justify-center items-center bg-gray-500 rounded-full w-10 h-10 text-gray-300 hover:text-white hover:bg-gray-300 transition-colors cursor-pointer"
                    >
                        <img src={ArrowLeft} alt="Back" className="w-5 h-5" />
                    </button>
                    <span className="text-12-regular text-gray-200">Go Back</span>
                </div>
                <div className="flex justify-between gap-2.5 w-full ">
                    <div className="flex items-center space-x-10">
                        <div className="w-33 h-33 bg-gray-500 rounded-full relative">
                            <img
                                src={user.avatar}
                                alt={user.name}
                                className="rounded-full object-cover h-full"
                            />
                            <div className="flex absolute bottom-0 -right-5  z-10">
                                {user.channelLinked.map((channel) =>
                                    channelIcons[channel] ? (
                                        <img
                                            key={channel}
                                            src={channelIcons[channel].src}
                                            alt={channelIcons[channel].alt}
                                            className="w-9.5 h-9.5 rounded-full border-1 border-white bg-white -ml-4"
                                        />
                                    ) : null
                                )}
                            </div>
                        </div>
                        <div>
                            <h2 className="text-24-bold text-gray-50">{user.name}</h2>
                            <p className="text-18-medium text-gray-200 mt-1">{user.title}</p>
                            <a href={user.portfolio} className="text-sm text-blue-400" target="_blank" rel="noopener noreferrer">
                                {user.portfolio}
                            </a>
                            <div className="flex border-1 border-gray-500 rounded-2xl px-2.75 py-1 items-center max-w-full mt-3">
                                <span className="text-gray-50 ">{user.portfolioLink}</span>
                                <img
                                    src={ContentCopy}
                                    alt="Copy icon"
                                    className="ml-3 cursor-pointer"
                                    onClick={() => {
                                        if (navigator.clipboard) {
                                            navigator.clipboard.writeText(user.portfolioLink);
                                        } else {
                                            const textarea = document.createElement('textarea');
                                            textarea.value = user.portfolioLink;
                                            document.body.appendChild(textarea);
                                            textarea.select();
                                            document.execCommand('copy');
                                            document.body.removeChild(textarea);
                                        }
                                    }}
                                />
                            </div>
                        </div>
                    </div>
                    <div className="flex flex-col gap-5 mr-12">
                        <div className="flex flex-col gap-3">
                            <p className="text-18-semibold text-gray-200">Languages</p>
                            <div className="flex flex-wrap gap-2">
                                {user.languages.map((lang, i) => (
                                    <span key={i} className="border-1 rounded-full border-gray-500 text-gray-50 px-3 py-1 text-14-regular">{lang}</span>
                                ))}
                            </div>
                        </div>
                        <div className="flex flex-col gap-3">
                            <p className="text-18-semibold text-gray-200">Categories</p>
                            <div className="flex flex-wrap gap-2">
                                {user.categories.map((cat, i) => (
                                    <span key={i} className="border-1 rounded-full border-gray-500 text-gray-50 px-3 py-1 text-14-regular">{cat}</span>
                                ))}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <PlatformToggle
                activePlatform={activeChannel}
                onPlatformChange={setActiveChannel}
                className="my-4"
            />

            {/* Render analytics component based on selected channel */}
            <div className="mt-6 relative min-h-[300px]">
                {/* <AnimatePresence mode="wait">
                        <motion.div
                            key={activeChannel}
                            initial={{ opacity: 0, x: 10 }}
                            animate={{ opacity: 1, x: 0 }}
                            exit={{ opacity: 0, x: -10 }}
                            transition={{ duration: 0.3 }}
                            className="absolute w-full"
                        >
                            {activeChannel === "instagram" ? (
                                <InstagramAnalytics />
                            ) : (
                                <YouTubeAnalytics />
                            )}
                        </motion.div>
                    </AnimatePresence> */}
                {/* <AnimatePresence mode="wait">
                        <motion.div
                            key={activeChannel}
                            initial={{ rotateY: 90, opacity: 0 }}
                            animate={{ rotateY: 0, opacity: 1 }}
                            exit={{ rotateY: -90, opacity: 0 }}
                            transition={{ duration: 0.2 }}
                            className="absolute w-full"
                            style={{ perspective: 1000 }}
                        >
                            {activeChannel === "instagram" ? (
                                <InstagramAnalytics />
                            ) : (
                                <YouTubeAnalytics />
                            )}
                        </motion.div>
                    </AnimatePresence> */}
                <AnimatePresence mode="wait">
                    <motion.div
                        key={activeChannel}
                        initial={{ scale: 0.95, opacity: 0 }}
                        animate={{ scale: 1, opacity: 1 }}
                        exit={{ scale: 0.95, opacity: 0 }}
                        transition={{ duration: 0.2, ease: "easeInOut" }}
                        className="absolute w-full"
                    >
                        {activeChannel === "instagram" ? (
                            <InstagramAnalytics />
                        ) : (
                            <YouTubeAnalytics />
                        )}
                    </motion.div>
                </AnimatePresence>

            </div>
        </div>
    );
};

export default ProfileAnalyticsPage;
