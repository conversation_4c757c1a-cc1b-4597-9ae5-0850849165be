import React, { useState, useEffect, useRef } from 'react'
import { useParams, useNavigate } from 'react-router-dom';

import { useDispatch } from 'react-redux';
import { useLoading } from '@shared/components/UI/LoadingContext';
import { useSnackbar } from '@shared/components/UI/SnackbarContext';
import useBrandSelectors from '@brand/services/brandSelectors';
import {
    getListMembersThunk,
    deleteCreatorLabelThunk,
    updateCreatorListThunk,
    updateCreatorListMemberNotesThunk,
    fetchCreatorListLabelsThunk,
    createCreatorLabelThunk,
    deleteCreatorListMembersThunk
} from '@brand/services/brandThunks';

import LabelsComponent from '@brand/components/AddLabel';
import TextArea from '@shared/components/UI/TextArea';
import PopupLayout from '@shared/components/UI/PopupLayout';
import AvatarStack from '@shared/components/AvatarStack';
import Filter from '../components/FilterFixed';

// eslint-disable-next-line no-unused-vars
import { motion, AnimatePresence } from "framer-motion";
import { FiArrowLeft, FiEdit2 } from 'react-icons/fi';
import { Input } from "@shared/components/UI/input";
import DataTable from '../components/DataTable';
import TrashIcon from '@assets/icon/trash.svg';
import ArrowUpIcon from '@assets/icon/arrow_upward.svg';
import ArrowDownIcon from '@assets/icon/arrow_downward.svg';
import BookmarkIcon from '@assets/icon/bookmark_border-white.svg';
import DownloadIcon from '@assets/icon/download.svg';
import SearchIcon from "@assets/icon/nav/search.svg";
import InviteGif from "@assets/gif/invite.mp4";
import SettingSliderIcon from '@assets/icon/settings-sliders.svg'
import InstagramCircle from "@assets/icon/instagram-circle.svg";
import YoutubeCircle from "@assets/icon/youtube-circle.svg";
import EditIcon from '@assets/icon/edit.svg';
import SendIcon from '@assets/icon/paper-plane.svg';
import ErrorOutlineIcon from '@assets/icon/error_outline.svg';
import VerifiedIcon from '@assets/icon/blue_verified.svg';
import { IoChevronBack } from "react-icons/io5";


const sampleAvatars = {
    avatar1: "https://images.unsplash.com/photo-1494790108377-be9c29b29330?w=150&h=150&fit=crop&crop=faces",
    avatar2: "https://images.unsplash.com/photo-1570295999919-56ceb5ecca61?w=150&h=150&fit=crop&crop=faces",
    avatar3: "https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150&h=150&fit=crop&crop=faces",
    avatar4: "https://images.unsplash.com/photo-1539571696357-5a69c17a67c6?w=150&h=150&fit=crop&crop=faces",
    avatar5: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=faces"
};

const searchCreatorsData = [
    {
        id: 1,
        avatar: "https://images.unsplash.com/photo-1580489944761-15a19d654956?w=150&h=150&fit=crop&crop=faces",
        name: "Ananya Pandey",
        userName: "ananya_pandey",
        verified: true,
        age: 23,
        creatorverseScore: 2,
        followers: 12000,
        engagementRate: 3,
        audienceCredibility: 89,
        categories: {
            primary: ['Fashion', 'Lifestyle', 'Fitness'],
            secondary: ['Beauty', 'Health']
        },
        pastCollaborations: [sampleAvatars.avatar1, sampleAvatars.avatar2, sampleAvatars.avatar3, sampleAvatars.avatar4],
        platform: "instagram",
    },
    {
        id: 2,
        name: "Kriti Vyas",
        userName: "kriti_vyas",
        avatar: "https://images.unsplash.com/photo-1499952127939-9bbf5af6c51c?w=150&h=150&fit=crop&crop=faces",
        age: 27,
        creatorverseScore: 8,
        followers: 1800000,
        engagementRate: 3.5,
        audienceCredibility: 94,
        categories: {
            primary: ["Tech", "Reviews", "Gadgets"],
            secondary: ["Unboxing", "Streaming"]
        },
        pastCollaborations: [sampleAvatars.avatar1, sampleAvatars.avatar2, sampleAvatars.avatar3, sampleAvatars.avatar4],
        platform: "youtube",
        verified: true
    },
    {
        id: 3,
        name: "Sourya Singh",
        userName: "sourya_singh",
        avatar: "https://images.unsplash.com/photo-1531746020798-e6953c6e8e04?w=150&h=150&fit=crop&crop=faces",
        age: 24,
        creatorverseScore: 5,
        followers: 850000,
        engagementRate: 6.3,
        audienceCredibility: 91,
        categories: {
            primary: ["Fitness", "Health", "Nutrition"],
            secondary: ["Wellness", "Yoga"]
        },
        pastCollaborations: [sampleAvatars.avatar1, sampleAvatars.avatar2],
        platform: "instagram",
        verified: false
    },
    {
        id: 4,
        name: "Shruti Sinha",
        userName: "shruti_sinha",
        avatar: "https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?w=150&h=150&fit=crop&crop=faces",
        age: 25,
        creatorverseScore: 1,
        followers: 3200000,
        engagementRate: 3.8,
        audienceCredibility: 87,
        categories: {
            primary: ["Beauty", "Skincare", "Makeup"],
            secondary: ["Lifestyle", "Vlogs"]
        },
        pastCollaborations: [sampleAvatars.avatar1, sampleAvatars.avatar2, sampleAvatars.avatar3],
        platform: "instagram",
        verified: true
    },
    {
        id: 5,
        name: "Alex Rodriguez",
        userName: "alex_rodriguez",
        avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=faces",
        age: 30,
        creatorverseScore: 9,
        followers: 560000,
        engagementRate: 7.2,
        audienceCredibility: 88,
        categories: {
            primary: ["Gaming", "Tech", "Reviews"],
            secondary: ["Unboxing", "Streaming"]
        },
        pastCollaborations: [sampleAvatars.avatar1, sampleAvatars.avatar2],
        platform: "youtube",
        verified: false
    },
    {
        id: 6,
        name: "Maya Sharma",
        userName: "maya_sharma",
        avatar: "https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150&h=150&fit=crop&crop=faces",
        age: 26,
        creatorverseScore: 6,
        followers: 1200000,
        engagementRate: 4.9,
        audienceCredibility: 93,
        categories: {
            primary: ["Travel", "Photography", "Adventure"],
            secondary: ["Lifestyle"]
        },
        pastCollaborations: [sampleAvatars.avatar1, sampleAvatars.avatar2],
        platform: "instagram",
        verified: true
    },
    {
        id: 7,
        name: "Raj Mehta",
        userName: "raj_mehta",
        avatar: "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=faces",
        age: 29,
        creatorverseScore: 3,
        followers: 750000,
        engagementRate: 5.8,
        audienceCredibility: 87,
        categories: {
            primary: ["Comedy", "Entertainment", "Vlogs"],
            secondary: ["Acting"]
        },
        pastCollaborations: [sampleAvatars.avatar1, sampleAvatars.avatar2],
        platform: "instagram",
        verified: true
    },
    {
        id: 8,
        name: "Priya Kapoor",
        userName: "priya_kapoor",
        avatar: "https://images.unsplash.com/photo-1494790108377-be9c29b29330?w=150&h=150&fit=crop&crop=faces",
        age: 31,
        creatorverseScore: 9,
        followers: 1700000,
        engagementRate: 4.3,
        audienceCredibility: 92,
        categories: {
            primary: ["Food", "Cooking", "Recipes"],
            secondary: ["Culinary"]
        },
        pastCollaborations: [sampleAvatars.avatar1, sampleAvatars.avatar2],
        platform: "youtube",
        verified: true
    },
    {
        id: 9,
        name: "Dev Patel",
        userName: "dev_patel",
        avatar: "https://images.unsplash.com/photo-1539571696357-5a69c17a67c6?w=150&h=150&fit=crop&crop=faces",
        age: 22,
        creatorverseScore: 8,
        followers: 420000,
        engagementRate: 6.7,
        audienceCredibility: 85,
        categories: {
            primary: ["Finance", "Education", "Investing"],
            secondary: ["Crypto"]
        },
        pastCollaborations: [sampleAvatars.avatar1, sampleAvatars.avatar2, sampleAvatars.avatar3, sampleAvatars.avatar4],
        platform: "youtube",
        verified: false
    },
    // {
    //     id: 10,
    //     name: "Zara Ahmed",
    //     userName: "zara_ahmed",
    //     avatar: "https://images.unsplash.com/photo-1517841905240-472988babdf9?w=150&h=150&fit=crop&crop=faces",
    //     age: 28,
    //     creatorverseScore: 44,
    //     followers: 3500000,
    //     engagementRate: 5.2,
    //     audienceCredibility: 96,
    //     categories: {
    //         primary: ["Lifestyle", "Wellness", "Meditation"],
    //         secondary: ["Yoga", "Mental Health"]
    //     },
    //     pastCollaborations: [sampleAvatars.avatar1, sampleAvatars.avatar2, sampleAvatars.avatar3],
    //     platform: "instagram",
    //     verified: true
    // },
    // {
    //     id: 11,
    //     name: "Vikram Khanna",
    //     userName: "vikram_khanna",
    //     avatar: "https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=150&h=150&fit=crop&crop=faces",
    //     age: 33,
    //     creatorverseScore: 48,
    //     followers: 980000,
    //     engagementRate: 4.1,
    //     audienceCredibility: 89,
    //     categories: {
    //         primary: ["Sports", "Fitness", "Nutrition"],
    //         secondary: ["Training"]
    //     },
    //     pastCollaborations: [sampleAvatars.avatar1, sampleAvatars.avatar2, sampleAvatars.avatar3, sampleAvatars.avatar4],
    //     platform: "instagram",
    //     verified: false
    // },
    // {
    //     id: 12,
    //     name: "Neha Joshi",
    //     userName: "neha_joshi",
    //     avatar: "https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=faces",
    //     age: 26,
    //     creatorverseScore: 82,
    //     followers: 600000,
    //     engagementRate: 7.4,
    //     audienceCredibility: 84,
    //     categories: {
    //         primary: ["DIY", "Crafts", "Art"],
    //         secondary: ["Home Decor", "Sustainability"]
    //     },
    //     pastCollaborations: [sampleAvatars.avatar1, sampleAvatars.avatar2],
    //     platform: "youtube",
    //     verified: false
    // },
    // {
    //     id: 13,
    //     name: "Arjun Kumar",
    //     userName: "arjun_kumar",
    //     avatar: "https://images.unsplash.com/photo-1492562080023-ab3db95bfbce?w=150&h=150&fit=crop&crop=faces",
    //     age: 32,
    //     creatorverseScore: 77,
    //     followers: 1400000,
    //     engagementRate: 3.9,
    //     audienceCredibility: 90,
    //     categories: {
    //         primary: ["Tech", "Gadgets", "Reviews"],
    //         secondary: ["Unboxing"]
    //     },
    //     pastCollaborations: [sampleAvatars.avatar1, sampleAvatars.avatar2, sampleAvatars.avatar3],
    //     platform: "youtube",
    //     verified: true
    // },
    // {
    //     id: 14,
    //     name: "Leila Desai",
    //     userName: "leila_desai",
    //     avatar: "https://images.unsplash.com/photo-1488426862026-3ee34a7d66df?w=150&h=150&fit=crop&crop=faces",
    //     age: 27,
    //     creatorverseScore: 85,
    //     followers: 890000,
    //     engagementRate: 5.5,
    //     audienceCredibility: 88,
    //     categories: {
    //         primary: ["Beauty", "Skincare", "Self-care"],
    //         secondary: ["Lifestyle"]
    //     },
    //     pastCollaborations: [sampleAvatars.avatar1, sampleAvatars.avatar2],
    //     platform: "instagram",
    //     verified: true
    // },
    // {
    //     id: 15,
    //     name: "Rahul Shah",
    //     userName: "rahul_shah",
    //     avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=faces",
    //     age: 25,
    //     creatorverseScore: 71,
    //     followers: 675000,
    //     engagementRate: 6.1,
    //     audienceCredibility: 86,
    //     categories: {
    //         primary: ["Music", "Art", "Production"],
    //         secondary: ["Instruments"]
    //     },
    //     pastCollaborations: [sampleAvatars.avatar1, sampleAvatars.avatar2, sampleAvatars.avatar3, sampleAvatars.avatar4],
    //     platform: "instagram",
    //     verified: false
    // }
]


const CreatorListPage = () => {
    const dispatch = useDispatch();
    const navigate = useNavigate();
    const { listId } = useParams();
    const { setIsLoading } = useLoading();
    const { showSnackbar } = useSnackbar();
    const { selectedBrand } = useBrandSelectors();


    const [sortConfig, setSortConfig] = useState({ key: '', direction: 'asc' });

    const [openCreatorSearch, setOpenCreatorSearch] = useState(false);

    const [listName, setListName] = useState("")
    const [searchTerm, setSearchTerm] = useState("")
    const [showFilter, setShowFilter] = useState(false)
    const [isMultiSelected, setIsMultiSelected] = useState(false)
    const [savedFilters, setSavedFilters] = useState([])
    const [isEditing, setIsEditing] = useState(false)
    const [editedName, setEditedName] = useState(listName)
    const [isPopupOpen, setIsPopupOpen] = useState("");
    const [labels, setLabels] = useState([]);
    const [labelsNames, setLabelsNames] = useState([]);

    const [creatorListData, setCreatorListData] = useState([]);
    const [selectedCreatorData, setSelectedCreatorData] = useState([]);

    useEffect(() => {
        getCreatorsForList();
        getLabelsForBrand();
    }, [dispatch]);

    const prevBrandRef = useRef();

    useEffect(() => {
        console.log('Selected Brand changed:', selectedBrand, prevBrandRef.current);
        if (prevBrandRef.current && prevBrandRef.current !== selectedBrand) {
            navigate('/brand/manage-creator');
        }
        prevBrandRef.current = selectedBrand;
    }, [selectedBrand]);

    const inputRef = useRef(null);

    // Focus the input when entering edit mode
    useEffect(() => {
        if (isEditing && inputRef.current) {
            inputRef.current.focus();
            // Position cursor at the end of the text
            inputRef.current.setSelectionRange(
                editedName.length,
                editedName.length
            );
        }
    }, [isEditing, editedName]);

    const handleEditClick = () => {
        setIsEditing(true);
    };

    const handleKeyDown = (e) => {
        if (e.key === 'Enter') {
            e.preventDefault(); // 🚫 prevent newline
            saveListNameChanges();
        } else if (e.key === 'Escape') {
            e.preventDefault(); // optional
            cancelEdit();
        }
    };


    const handleBlur = () => {
        saveListNameChanges();
    };

    const saveListNameChanges = async () => {
        if (editedName.trim()) {
            try {
                const result = await dispatch(updateCreatorListThunk({
                    listId: listId,
                    brandId: selectedBrand.id,
                    body: {
                        name: editedName.trim(),
                    }
                }));
                if (result.meta.requestStatus === 'fulfilled') {
                    // Get the response data from the thunk
                    const responseData = result.payload;

                    if (responseData.success) {
                        showSnackbar('List Name Updated successfully', 'success');
                        setListName(editedName.trim());
                        setIsEditing(false);
                    }
                } else {
                    // Handle failure case
                    const errorMessage = result.payload.message || 'List Name Update request failed. Please try again.';
                    showSnackbar(errorMessage, 'error');
                }
            } catch (error) {
                console.error('List Name Update request error:', error);
                showSnackbar(error.message || 'Failed to request List Name Update.', 'error');
            } finally {
                setIsLoading(false);
            }
        } else {
            // Reset to original name if empty
            setEditedName(listName);
            setIsEditing(false);
        }
    };

    const cancelEdit = () => {
        setEditedName(listName);
        setIsEditing(false);
    };

    const handleBack = (navigateTo) => {
        // Your navigation logic here
        console.log('Navigating back to:', navigateTo);
        if (navigateTo === 'creatorList') {
            setOpenCreatorSearch(false);
            setIsMultiSelected(false);
            setSearchTerm('');
        }
        else if (navigateTo === 'savedList') {
            setOpenCreatorSearch(false);
            setIsMultiSelected(false);
            setSearchTerm('');
            navigate('/brand/manage-creator');
        }
    };


    const creatorListColumns = [
        {
            key: 'name',
            header: 'Name',
            width: '150px',
            render: (row) => (
                <div className="flex items-center gap-3">
                    <img src={row.avatar} className="w-10 h-10 rounded-full" alt={row.name} />
                    <div className="flex flex-col">
                        <div className="flex items-center gap-1">
                            <span className="font-semibold text-white">{row.name}</span>
                            {row.verified && <img src={VerifiedIcon} className="h-3 w-3" alt="Verified" />}
                        </div>
                    </div>
                </div>
            )
        },
        {
            key: 'status',
            header: 'Status',
            width: '10px',
            render: (row) => (renderStatus(row.status))
        },
        // {
        //     key: 'channels',
        //     header: 'Channel',
        //     width: '80px',
        //     render: (row) => (
        //         <div className="flex items-center gap-1">
        //             <div className="flex -space-x-2">
        //                 {row.channels && row.channels.map((channel, i) => (
        //                     <img
        //                         key={i}
        //                         src={channel}
        //                         alt="Creator"
        //                         className="w-6 h-6 rounded-full  bg-white   object-cover"
        //                     />
        //                 ))}
        //             </div>
        //         </div>
        //     )
        // },
        {
            key: 'audience',
            header: 'Audience',
            sortable: true,
            width: '50px',
            render: (row) => <span className="text-14-medium text-white">{row.followers ? formatNumber(row.followers) : 'N/A'}</span>,
        },
        {
            key: 'engagementRate',
            header: 'Eng. Rate',
            sortable: true,
            width: '80px',
            tooltip: 'Avg engagement (likes, comments, shares) divided by audience count',
            tooltipPosition: 'bottom',
            render: (row) => {
                const formattedRate = Number(row.engagementRate).toFixed(2); // Ensures 2 decimal places
                return <span className={`text-14-medium text-white`}>{formattedRate} %</span>;
            },
        },
        {
            key: 'campaign',
            header: 'Campaign',
            width: '100px',
            sortable: true,
            render: (row) => (
                <div className='flex items-center gap-2'>
                    {row.campaign === "" ? (
                        <div>
                            <span className="text-14-medium text-brand-500 hover:text-brand-600">+ Add to campaigns </span>
                        </div>
                    ) : (
                        <span className="text-14-medium text-white">{row.campaign}</span>
                    )}
                </div>
            )
        },
        {
            key: 'labels',
            header: 'Labels',
            width: '180px',
            render: (row) => {
                const handleAddLabel = async (newLabel) => {
                    setIsLoading(true);
                    const labelId = labels.find(label => label.name === newLabel)?.id;
                    console.log('Adding label:', newLabel, 'to creator:', row.id);
                    return await addLabelToList(row.id, newLabel, labelId); // Pass row.id
                };

                const handleDeleteLabel = async (labelToDelete) => {
                    setIsLoading(true);
                    return await deleteLabelFromList(row.id, labelToDelete.id); // Pass row.id
                };

                return (
                    <div className="flex flex-col gap-1 max-w-full truncate break-words overflow-hidden">
                        <LabelsComponent
                            initialLabels={row.labels}
                            onAddLabel={(newLabel) => handleAddLabel(newLabel)}
                            onDeleteLabel={(labelToDelete) => handleDeleteLabel(labelToDelete)}
                            suggestions={labelsNames}
                        />
                    </div>
                );
            },
        },
        {
            key: 'notes',
            header: 'Notes',
            width: '140px',
            // render: (row) => (
            //     <TextArea
            //         notes={row.notes}
            //         onUpdate={(newNote) => updateNotesForCreator(row.id, newNote)}
            //     />
            // )
            render: (row) => {
                const handleUpdateCreator = async (newNotes) => {
                    return await updateNotesForCreator(row.id, newNotes);
                };
                return (<div className='w-full'>
                    <TextArea notes={row.notes} onUpdate={handleUpdateCreator} />
                </div>)
            }
        },
    ]

    const searchCreatorColumns = [
        {
            key: 'name',
            header: 'Name',
            width: '180px',
            render: (row) => (
                <div className="flex items-center gap-3 py-3">
                    <img src={row.avatar} className="w-10 h-10 rounded-full" alt={row.name} />
                    <div className="flex flex-col">
                        <div className="flex items-center gap-1">
                            <span className="font-semibold text-white">{row.name}</span>
                            {row.verified && <img src={VerifiedIcon} className="h-3 w-3" alt="Verified" />}
                        </div>
                        <span className="text-sm text-gray-400">@{row.userName}</span>
                    </div>
                </div>
            )
        },
        {
            key: 'creatorverseScore',
            header: 'CV Score',
            sortable: true,
            width: '100px',
            tooltip: 'A combined metric of engagement quality, audience authenticity, and content performance',
            tooltipPosition: 'bottom',
            render: (row) => renderCreatorverseScore(row.creatorverseScore),
        },
        {
            key: 'followers',
            header: 'Followers',
            sortable: true,
            width: '110px',
            render: (row) => <span className="text-16-semibold text-white">{row.followers ? formatNumber(row.followers) : 'N/A'}</span>,
        },
        {
            key: 'engagementRate',
            header: 'Engagement Rate',
            sortable: true,
            width: '140px',
            tooltip: 'Avg engagement (likes, comments, shares) divided by follower count',
            tooltipPosition: 'bottom',
            render: (row) => {
                const color = row.engagementRate < 3 ? 'text-white' : 'text-white';
                return <span className={`text-16-semibold ${color}`}>{row.engagementRate} %</span>;
            },
        },
        // {
        //     key: 'audienceCredibility',
        //     header: 'Audience Credibility',
        //     sortable: true,
        //     width: '150px',
        //     tooltip: 'Percentage of real, active followers',
        //     tooltipPosition: 'bottom',
        //     render: (row) => (
        //         <span className={`font-medium ${row.audienceCredibility < 80 ? 'text-white' : 'text-white'}`}>
        //             {row.audienceCredibility}%
        //         </span>
        //     )
        // },
        {
            key: 'categories',
            header: 'Categories',
            width: '180px',
            render: (row) => (
                <div className='flex flex-col gap-1'>
                    <div className="flex flex-wrap gap-1">
                        {row.categories.primary.map((cat, idx) => (
                            <span
                                key={`first-${idx}`}
                                className="text-violet bg-light-1 rounded-full px-2 py-1 h-6 text-14-regular border-1 border-violet"
                            >
                                {cat}
                            </span>
                        ))}
                    </div>
                    <div className="flex flex-wrap gap-1">
                        {row.categories.secondary.map((cat, idx) => (
                            <span
                                key={`second-${idx}`}
                                className="text-teal bg-light-8 rounded-full px-2 py-1 h-6 text-14-regular border-1 border-teal"
                            >
                                {cat}
                            </span>
                        ))}
                    </div>
                </div>
            )
        },
        {
            key: 'pastCollaborations',
            header: 'Past Collaborations',
            width: '60px',
            render: (row) => (
                <AvatarStack
                    avatars={row.pastCollaborations}
                    maxAvatars={3}
                    count={row.pastCollaborations.length - 3}
                    className="ml-1"
                />
            )
        }
    ];

    // Filter options array - keeping the existing array as is
    const filterOptions = [
        {
            optionName: "Status",
            optionFor: "creator",
            channel: "instagram",
            filters: [
                {
                    name: "Status",
                    type: "checkbox",
                    icon: "gender-icon",
                    minmax: false,
                    enterValue: false,
                    placeholder: "Select Status", //only needed for enterValue
                    options: [
                        { label: "Shortlisted", value: "shortlisted", description: "" },
                        { label: "In Progress", value: "in_progress", description: "" },
                        { label: "Negotiation", value: "negotiation", description: "" },
                        { label: "Outreached", value: "outreached", description: "" },
                        { label: "Onboarded", value: "onboarded", description: "" },
                        { label: "Rejected", value: "rejected", description: "" },
                    ]
                },
            ]
        },
        {
            optionName: "Channels",
            optionFor: "creator",
            channel: "instagram",
            filters: [
                {
                    name: "channel",
                    type: "checkbox",
                    minmax: false,
                    icon: "follower-icon",
                    enterValue: false,
                    placeholder: "Select Channel", //only needed for enterValue
                    options: [
                        { label: "Instagram", value: "instagram", description: "Instagram" },
                        { label: "YouTube", value: "youtube", description: "YouTube" },
                    ]
                }
            ]
        },
        {
            optionName: "Audience",
            optionFor: "creator",
            channel: "instagram",
            filters: [
                {
                    name: "audience",
                    type: "checkbox",
                    minmax: true,
                    icon: "age-icon",
                    enterValue: false,
                    placeholder: "Select Age", //only needed for enterValue
                    options: [
                        { label: "Nano", value: "1000-10000", description: "1k-10k" },
                        { label: "Micro", value: "10001-50000", description: "10k-50k" },
                        { label: "Mid", value: "50001-500000", description: "50k-500k" },
                        { label: "Macro", value: "500001-1000000", description: "500k-1M" },
                        { label: "Mega", value: "1000001+", description: "1M+" }
                    ]
                },
            ]
        },
        {
            optionName: "Engagement Rate",
            optionFor: "creator",
            channel: "instagram",
            filters: [
                {
                    name: "Engagement Rate",
                    type: "checkbox",
                    minmax: false,
                    icon: "engagement-icon",
                    enterValue: false,
                    placeholder: "Select Engagement Rate", //only needed for enterValue
                    options: [
                        { label: "Very High", value: "10%+", description: "10%+" },
                        { label: "High", value: "5%-10%", description: "5%-10%" },
                        { label: "Medium", value: "2%-5%", description: "2%-5%" },
                        { label: "Low", value: "<2%", description: "<2%" },
                    ]
                },
            ]
        },
        {
            optionName: "Campaigns",
            optionFor: "creator",
            channel: "instagram",
            filters: [
                {
                    name: "Campaigns",
                    type: "checkbox",
                    searchBox: true,
                    minmax: false,
                    icon: "engagement-icon",
                    enterValue: false,
                    placeholder: "Select Campaigns",
                    options: [
                        { label: "Summer Surge", value: "Summer Surge", description: "" },
                        { label: "Engagement Explosion", value: "Engagement Explosion", description: "" },
                        { label: "Precision Targeting", value: "Precision Targeting", description: "" },
                        { label: "Brand Spotlight", value: "Brand Spotlight", description: "" },
                    ]
                },
            ]
        },
    ];

    const formatNumber = (num) => {
        if (num >= 1_000_000) {
            return (num / 1_000_000).toFixed(num % 1_000_000 === 0 ? 0 : 1) + 'M';
        } else if (num >= 1_000) {
            return (num / 1_000).toFixed(num % 1_000 === 0 ? 0 : 1) + 'K';
        }
        return num.toString();
    };

    const handleSort = (key) => {
        console.log("Sorting triggered for key:", key);
        setSortConfig((prev) => {
            const newDirection = prev.key === key && prev.direction === 'asc' ? 'desc' : 'asc';
            console.log("New sort config:", { key, direction: newDirection });
            return {
                key,
                direction: newDirection,
            };
        });
    };

    const renderStatus = (status) => {
        const getColor = () => {
            if (status === "shortlisted") return 'bg-teal text-white';
            if (status === "in_progress") return 'bg-yellow text-primary';
            if (status === "negotiation") return 'bg-orange text-white';
            if (status === "outreached") return 'bg-gray-400 text-white';
            if (status === "onboarded") return 'bg-sky-blue text-primary';
            if (status === "rejected") return 'bg-red-2 text-white';
            if (status === "no_status") return 'bg-transparent text-white';
            if (status === "completed") return 'bg-green-2 text-white';
            if (status === "") return 'bg-gray-400 text-white';
        };

        const formatStatusLabel = (status) => {
            return status
                .replace(/_/g, ' ')
                .replace(/\w\S*/g, (txt) => txt.charAt(0).toUpperCase() + txt.slice(1).toLowerCase());
        };

        return (
            <div className={`flex items-center rounded-full py-1 px-3 w-fit  ${getColor()}`}>
                <span className="text-14-regular">{formatStatusLabel(status)}</span>
            </div>
        );
    };

    const renderCreatorverseScore = (score) => {
        const getColor = () => {
            if (score >= 8) return 'bg-green-2';
            if (score >= 5) return 'bg-blue';
            if (score >= 2) return 'bg-orange';
            if (score >= 0) return 'bg-red-2';
            return 'bg-red-2';
        };
        return (
            <div className={`flex items-center justify-center rounded-md py-1 w-9 gap-1 ${getColor()}`}>
                <span className="text-14-semibold text-white">{score}</span>
                {/* <img src={getIcon()} alt="" /> */}
            </div>
        );
    };

    // Enhanced sort logic
    const getSortedData = (data, sortConfig) => {
        if (!sortConfig?.key || !sortConfig?.key.trim() || !data || data.length === 0) return data;

        console.log("Sorting by:", sortConfig.key, "Direction:", sortConfig.direction);

        return [...data].sort((a, b) => {
            let aVal = a[sortConfig.key];
            let bVal = b[sortConfig.key];

            // Handle undefined or null values
            if (aVal === undefined || aVal === null) return sortConfig.direction === 'asc' ? -1 : 1;
            if (bVal === undefined || bVal === null) return sortConfig.direction === 'asc' ? 1 : -1;

            // Special handling for specific fields
            if (sortConfig.key === 'followers' || sortConfig.key === 'audience') {
                // Convert from formatted strings (e.g., "120K") to numbers if needed
                aVal = typeof aVal === 'string' ? parseFloat(aVal.replace(/[^0-9.]/g, '')) : aVal;
                bVal = typeof bVal === 'string' ? parseFloat(bVal.replace(/[^0-9.]/g, '')) : bVal;
            } else if (sortConfig.key === 'engagementRate') {
                // Convert from percentage strings if needed
                aVal = typeof aVal === 'string' ? parseFloat(aVal.replace('%', '')) : aVal;
                bVal = typeof bVal === 'string' ? parseFloat(bVal.replace('%', '')) : bVal;
            }

            // String comparison
            if (typeof aVal === 'string') {
                return sortConfig.direction === 'asc'
                    ? aVal.localeCompare(bVal)
                    : bVal.localeCompare(aVal);
            }

            // Number comparison
            return sortConfig.direction === 'asc' ? aVal - bVal : bVal - aVal;
        });
    };

    // Filter data based on search term
    const getFilteredData = (data, searchTerm) => {
        if (!searchTerm || !searchTerm.trim()) return data;

        const normalizedSearchTerm = searchTerm.trim().toLowerCase();
        console.log("Filtering by search term:", normalizedSearchTerm);
        console.log("Data before filtering:", data.length, "items");

        return data.filter(item => {
            // Check if this is from mockList (creator lists) or creatorListData
            const isCreatorList = 'listName' in item;

            if (isCreatorList) {
                // For creator lists (mockList)
                // Search by list name
                if (item.listName && item.listName.toLowerCase().includes(normalizedSearchTerm)) {
                    return true;
                }

                // Search by creator name (if available)
                if (item.createdBy && item.createdBy.toLowerCase().includes(normalizedSearchTerm)) {
                    return true;
                }

                // Search by created date or last updated date
                if ((item.createdOn && item.createdOn.toLowerCase().includes(normalizedSearchTerm)) ||
                    (item.lastUpdated && item.lastUpdated.toLowerCase().includes(normalizedSearchTerm))) {
                    return true;
                }
            } else {
                // For creator data (creatorListData)
                // Search by creator name
                if (item.name && item.name.toLowerCase().includes(normalizedSearchTerm)) {
                    return true;
                }

                // Search by channel (if the channel is a string property)
                if (item.channels) {
                    // If channels is an array of objects or strings
                    if (Array.isArray(item.channels)) {
                        // For image URLs, we just check if there's something matching the search term
                        // This is a simplified approach since we can't easily search image content
                        const channelCount = item.channels.length;
                        if (normalizedSearchTerm.includes('instagram') && channelCount > 0) {
                            return true;
                        }
                        if (normalizedSearchTerm.includes('youtube') && channelCount > 1) {
                            return true;
                        }
                    }

                    // If channels is a string
                    if (typeof item.channels === 'string' &&
                        item.channels.toLowerCase().includes(normalizedSearchTerm)) {
                        return true;
                    }
                }

                // Search by status if available
                if (item.status && item.status.toLowerCase().includes(normalizedSearchTerm)) {
                    return true;
                }

                // Search by campaign if available
                if (item.campaign && item.campaign.toLowerCase().includes(normalizedSearchTerm)) {
                    return true;
                }

                // Search by labels if available
                if (item.labels && Array.isArray(item.labels) &&
                    item.labels.some(label => label.name.toLowerCase().includes(normalizedSearchTerm))) {
                    return true;
                }

                // Search by notes if available
                if (item.notes && item.notes.toLowerCase().includes(normalizedSearchTerm)) {
                    return true;
                }
            }
            return false;
        });
    };

    const getCreatorsForList = async () => {
        if (!selectedBrand?.id || !listId) return;
        setIsLoading(true);

        try {
            const result = await dispatch(getListMembersThunk({
                listId: listId,
                brandId: selectedBrand.id
            }));
            if (result.meta.requestStatus === 'fulfilled') {
                // Get the response data from the thunk
                const responseData = result.payload.data;

                // console.log('CreatorList Menbers:', responseData.members);

                setListName(responseData.list_info.name);
                setEditedName(responseData.list_info.name);

                const transformed = (responseData.members || []).map((member) => ({
                    id: member.id,
                    name: member.influencer_name,
                    avatar: member.avatar_url,
                    verified: member.verified,
                    status: member.status,
                    channels: member.channels,
                    campaign: member.campaign,
                    labels: member.labels,
                    notes: member.notes || '',
                    followers: member.followers,
                    engagementRate: member.engagement_rate,
                    created_at: member.created_at,
                    updated_at: member.updated_at,
                }));

                if (transformed.length > 0) {
                    setCreatorListData(transformed);
                    console.log('CreatorList :', transformed);
                } else {

                    showSnackbar('Get Creator List request found', 'info');
                }
            } else {
                // Handle failure case
                const errorMessage = result.payload.message || 'Get global fliter request failed. Please try again.';
                showSnackbar(errorMessage, 'error');
            }
        } catch (error) {
            console.error('Get global fliter request error:', error);
            showSnackbar(error.message || 'Failed to request Get global fliter.', 'error');
        } finally {
            setIsLoading(false);
        }
    }

    const addLabelToList = async (profileId, newLabel, labelId) => {
        try {
            setIsLoading(true);

            const result = await dispatch(createCreatorLabelThunk({
                name: newLabel,
                label_id: labelId,
                member_id: profileId,
                brand_id: selectedBrand.id,
            }));

            if (result.meta.requestStatus === 'fulfilled') {
                const responseData = result.payload;

                if (responseData.success) {
                    // showSnackbar('Label added successfully', 'success');
                    if (responseData.data) {
                        console.log('Label added to global response data:', responseData.data);
                        setLabelsNames((prevLabels) => [...prevLabels, responseData.data.name]);
                        setLabels((prevLabels) => [...prevLabels, responseData.data]);
                    }
                    await getCreatorsForList();
                    return true;
                }
            } else {
                const errorMessage =
                    result.payload?.message || 'Failed to add label. Please try again.';
                showSnackbar(errorMessage, 'error');
            }
            return false;
        } catch (error) {
            console.error('Add label request error:', error);
            showSnackbar(error.message || 'Failed to add label.', 'error');
            return false;
        } finally {
            setIsLoading(false);
        }
    };


    const deleteLabelFromList = async (profileId, labelId) => {
        console.log('Deleting label:', labelId, 'from creator:', profileId);
        try {
            const result = await dispatch(deleteCreatorLabelThunk({
                profileId: profileId,
                labelId: labelId,
                body: { brand_id: selectedBrand.id }
            }));

            if (result.meta.requestStatus === 'fulfilled') {
                const responseData = result.payload;

                if (responseData.success) {
                    showSnackbar('Label deleted successfully', 'success');
                    setLabels((prevLabels) => prevLabels.filter(label => label.id !== labelId));
                    setCreatorListData((prevData) =>
                        prevData.map((creator) => {
                            if (creator.id === profileId) {
                                return {
                                    ...creator,
                                    labels: creator.labels.filter((label) => label.id !== labelId),
                                };
                            }
                            return creator;
                        })
                    );
                    return true;
                } else {
                    showSnackbar(responseData.message || 'Failed to delete label', 'error');
                }
            } else {
                const errorMessage = result.payload?.message || 'Failed to delete label';
                showSnackbar(errorMessage, 'error');
            }
        } catch (error) {
            console.error('Delete label error:', error);
            showSnackbar(error.message || 'An error occurred while deleting the label', 'error');
        } finally {
            setIsLoading(false);
        }
    };



    const getLabelsForBrand = async () => {
        try {
            const result = await dispatch(fetchCreatorListLabelsThunk({ brandId: selectedBrand.id }));
            if (result.meta.requestStatus === 'fulfilled') {
                // Get the response data from the thunk
                const responseData = result.payload;

                if (responseData.success) {
                    setLabels(responseData.data.labels);
                    setLabelsNames(responseData.data.labels.map(label => label.name));

                    console.log('Labels Fetched :', responseData.data.labels);
                    // showSnackbar('Labels Fetched successfully', 'success');
                }
            } else {
                // Handle failure case
                const errorMessage = result.payload.message || 'Labels Fetching request failed. Please try again.';
                showSnackbar(errorMessage, 'error');
            }
        } catch (error) {
            console.error('Labels Fetching request error:', error);
            showSnackbar(error.message || 'Failed to request Labels Fetching.', 'error');
        } finally {
            setIsLoading(false);
        }
    };

    const updateNotesForCreator = async (profileId, newNotes) => {
        try {
            setIsLoading(true); // Start loading

            console.log('Updating notes for creator:', profileId, 'in list:', listId, "with notes:", newNotes);

            const result = await dispatch(
                updateCreatorListMemberNotesThunk({
                    listId,
                    profileId: profileId,
                    brandId: selectedBrand.id,
                    body: { notes: newNotes }
                })
            );

            if (result.meta.requestStatus === 'fulfilled') {
                const responseData = result.payload;

                if (responseData.success) {
                    await getCreatorsForList(listId);
                    showSnackbar('Notes updated successfully', 'success');
                }
            } else {
                const errorMessage =
                    result.payload?.message || 'Failed to update notes. Please try again.';
                showSnackbar(errorMessage, 'error');
            }
        } catch (error) {
            console.error('Update notes request error:', error);
            showSnackbar(error.message || 'Unexpected error occurred.', 'error');
        } finally {
            setIsLoading(false);
        }
    };

    const deleteSelectedCreators = async () => {
        try {
            setIsLoading(true);

            if(selectedCreatorData.length === 0) {
                showSnackbar('Please select at least one creator to delete', 'error');
                return;
            };

            const result = await dispatch(deleteCreatorListMembersThunk({
                listId: listId,
                body: {
                    brand_id: selectedBrand.id,
                    member_ids: selectedCreatorData.map(creator => creator.id),
                }
            }));

            if (result.meta.requestStatus === 'fulfilled') {
                const responseData = result.payload;

                if (responseData.success) {
                    showSnackbar('Creator deleted successfully', 'success');
                    await getCreatorsForList(listId);
                }
            } else {
                const errorMessage = result.payload?.message || 'Failed to delete creator';
                showSnackbar(errorMessage, 'error');
            }
        } catch (error) {
            console.error('Delete creator error:', error);
            showSnackbar(error.message || 'An error occurred while deleting the creator', 'error');
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <>
            <div className='flex flex-col gap-4.5 w-full'>
                {/* Header */}
                <div className="flex items-center">
                    <h1 className="text-30-semibold text-gray-50">Manage Creators</h1>
                </div>

                {/* List name header */}
                <div className='flex items-center'>
                    <div className="flex py-3 text-white">
                        {/* Back button */}
                        <button
                            onClick={(e) => {
                                e.stopPropagation(); // prevent parent click
                                openCreatorSearch ? handleBack('creatorList') : handleBack('savedList');
                            }}
                            className="mr-2 text-lg hover:text-gray-300 transition cursor-pointer"
                            aria-label="Back"
                        >
                            {/* <i className="fi fi-sr-angle-small-left text-2xl"></i> */}
                            {/* <FiArrowLeft /> */}
                            <IoChevronBack className='h-6 w-6' />
                        </button>

                        {/* List name - editable or static */}
                        {isEditing ? (
                            <div
                                ref={(el) => {
                                    if (el) {
                                        el.focus();
                                        const range = document.createRange();
                                        const sel = window.getSelection();
                                        range.selectNodeContents(el);
                                        range.collapse(false); // to the end
                                        sel.removeAllRanges();
                                        sel.addRange(range);
                                    }
                                }}
                                contentEditable
                                dir="ltr"
                                onInput={(e) => setEditedName(e.currentTarget.textContent)}
                                className="bg-[#262626] border border-[#444] h-7 text-18-bold rounded px-2 py-1 text-sm font-medium outline-none inline-block text-left w-full"
                                suppressContentEditableWarning={true}
                                onKeyDown={handleKeyDown}
                                onBlur={handleBlur}
                                style={{ direction: 'ltr', textAlign: 'left', unicodeBidi: 'embed' }}
                            >
                                {editedName}
                            </div>
                        )
                            :
                            (
                                <h1 className="flex-1 flex items-center text-sm h-7 w-fit px-2 text-18-bold truncate text-left">
                                    {listName}
                                </h1>
                            )}

                        {/* Edit button */}
                        {listName !== 'My Creators' && (
                            <button
                                onClick={handleEditClick}
                                className="ml-2 text-sm text-gray-400 hover:text-white transition w-7"
                                aria-label="Edit list name"
                            >
                                <img src={EditIcon} alt="Edit" className="w-5 h-5 cursor-pointer" />
                            </button>
                        )}
                    </div>
                </div>



                {openCreatorSearch ?
                    // Creator Search View
                    <div className='flex flex-col gap-3'>
                        <div className='flex justify-center items-center text-16-medium text-gray-50'>
                            <span>Add creators from Creatorverse Discovery Database</span>
                        </div>
                        <div className='relative flex items-center justify-center w-full'>
                            <div className='w-full flex justify-center'>
                                <Input
                                    type="text"
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                    icon={SearchIcon}
                                    placeholder="Enter by name..."
                                    className="px-3 py-1 h-10 w-120 bg-transparent text-sm placeholder-gray-400 focus:outline-none"
                                />
                            </div>
                            {isMultiSelected && (
                                <div className='absolute right-0 flex items-center justify-end '>
                                    {/* Add to List */}
                                    <button
                                        className={`relative px-10 py-3 bg-brand-500 rounded-lg flex items-center gap-1.5 text-16-semibold text-white hover:bg-brand-600 transition-colors cursor-pointer`}
                                        onClick={(e) => {
                                            e.stopPropagation();
                                            handleBack('creatorList');
                                            console.log('Add to list clicked');
                                        }}
                                        title="Import Creators"
                                    >
                                        Add to list
                                    </button>
                                </div>
                            )}
                        </div>

                        {/* Creator Table */}
                        <DataTable
                            data={getSortedData(getFilteredData(searchCreatorsData, searchTerm), sortConfig)}
                            columns={searchCreatorColumns}
                            sortConfig={sortConfig}
                            onSort={handleSort}
                            onRowMouseLeave={() => {
                                // setPopupVisibleId(null);
                                // setCampaignPopupVisibleId(null);
                            }}
                            headerRowClass="border-brand-500 rounded-t-lg"
                            onSelectionChange={(selectedRows) => {
                                if (selectedRows.length > 0) {
                                    setIsMultiSelected(true);
                                } else {
                                    setIsMultiSelected(false);
                                }
                            }}
                        />

                    </div>
                    :
                    // Creator List View
                    <div className='flex flex-col gap-5'>
                        {/* Action Buttons */}
                        <div className="flex justify-between items-center w-full">
                            <div className='flex items-center gap-4'>
                                {/* Search Box */}
                                <div className="flex items-center space-x-4">
                                    <Input
                                        type="text"
                                        value={searchTerm}
                                        onChange={(e) => setSearchTerm(e.target.value)}
                                        icon={SearchIcon}
                                        placeholder="Search by name, channel....."
                                        className="px-3 py-1 h-10 w-100 bg-transparent text-sm placeholder-gray-400 focus:outline-none"
                                    />
                                </div>
                                {/* Filter Buttons */}
                                <button
                                    className={`relative px-4 py-2.5 bg-transparent border border-gray-500 rounded-lg flex items-center gap-1.5 text-sm text-gray-50 hover:text-gray-300 transition-colors cursor-pointer`}
                                    onClick={() => {
                                        setShowFilter(!showFilter);
                                        console.log('Filter button clicked');
                                    }}
                                    title="Toggle Filters"
                                >
                                    <img src={SettingSliderIcon} alt="Filter" className="w-3.5 h-3.5" />
                                    Filters
                                    {savedFilters.length > 0 && (
                                        <span className="ml-2 bg-brand-200 text-brand-600 text-xs rounded-full w-5 h-5 flex items-center justify-center">
                                            {savedFilters.length}
                                        </span>
                                    )}

                                    {showFilter ? <Filter
                                        filterOptions={filterOptions}
                                        hidetab={true}
                                        savedFilters={savedFilters}
                                        onClose={() => setShowFilter(false)}
                                        onApplyFilters={(filters) => {
                                            setSavedFilters(filters);
                                            console.log(filters);
                                            // Process your filters here
                                        }}
                                    /> : null}

                                </button>
                            </div>
                            <div className='flex items-center gap-4'>
                                {/* Import List */}
                                {!(isMultiSelected > 0) && <button
                                    className={`relative px-4 py-2.5 bg-transparent border border-gray-400 rounded-lg flex items-center gap-1.5 text-16-semibold text-gray-50 hover:text-gray-300 transition-colors cursor-pointer`}
                                    onClick={() => {
                                        setIsPopupOpen("invite");
                                        console.log('BulkInvite Popup clicked');
                                    }}
                                    title="Toggle Filters"
                                >
                                    <img src={DownloadIcon} alt="Filter" className="w-5 h-5" />
                                    Bulk Outreach
                                </button>}
                                <button
                                    className={`px-4 py-2.5 bg-transparent border border-gray-400 rounded-lg flex items-center gap-1.5 text-16-semibold text-gray-50 hover:text-gray-300 transition-colors cursor-pointer`}
                                    onClick={() => {
                                        setIsPopupOpen("import")
                                        console.log('Popup clicked');
                                    }}
                                    title="Toggle Filters"
                                >
                                    <img src={DownloadIcon} alt="Filter" className="w-5 h-5" />
                                    Import
                                </button>
                                {/* Create List */}
                                <button
                                    className={`relative px-4 py-3 bg-brand-500 border border-gray-500 rounded-lg flex items-center gap-1.5 text-16-semibold text-white hover:bg-brand-600 transition-colors cursor-pointer`}
                                    onClick={() => {
                                        console.log('Popup clicked mera');
                                        setOpenCreatorSearch(true);
                                    }}
                                    title="Import Creators"
                                >
                                    + Add Creators
                                </button>
                            </div>
                        </div>
                        {/* Bulk Action Button */}
                        <div className={`-my-2 relative w-full ${isMultiSelected ? "h-12" : "h-0"} transition-all duration-300`}>
                            <AnimatePresence mode="wait">
                                {isMultiSelected && (
                                    <motion.div
                                        key="filters"
                                        initial={{ y: 40, rotateX: 10, opacity: 0 }}
                                        animate={{ y: 0, rotateX: 0, opacity: 1 }}
                                        exit={{ y: -40, rotateX: -10, opacity: 0 }}
                                        transition={{ duration: 0.2, ease: 'easeInOut' }}
                                        className="absolute inset-0 flex items-center justify-start gap-4 rounded-lg text-white"
                                    >
                                        {/* Outreach */}
                                        <button
                                            className={`relative px-4 py-2.5 bg-transparent border border-gray-400 rounded-lg flex items-center gap-1.5 text-14-medium text-gray-50 hover:text-gray-300 transition-colors cursor-pointer`}
                                            onClick={() => {
                                                setIsPopupOpen("invite");
                                                console.log('selected Outreach Popup clicked');
                                            }}
                                            title="Toggle Filters"
                                        >
                                            <img src={SendIcon} alt="Filter" className="w-4 h-4" />
                                            Outreach
                                        </button>
                                        {/* Delete */}
                                        <button
                                            className={`relative px-4 py-2.5 bg-transparent border border-gray-400 rounded-lg flex items-center gap-1.5 text-14-medium text-gray-50 hover:text-gray-300 transition-colors cursor-pointer`}
                                            onClick={async (e) => {
                                                e.stopPropagation();
                                                await deleteSelectedCreators();
                                                console.log('Popup clicked');
                                            }}
                                            title="Delete Selected Creators"
                                        >
                                            <img src={TrashIcon} alt="Filter" className="w-4 h-4" />
                                            Delete
                                        </button>
                                    </motion.div>
                                )}
                            </AnimatePresence>
                        </div>
                        {/* Creator List Table */}
                        <DataTable
                            data={getSortedData(getFilteredData(creatorListData, searchTerm), sortConfig)}
                            columns={creatorListColumns}
                            sortConfig={sortConfig}
                            onSort={handleSort}
                            onRowMouseLeave={() => { }}
                            onSelectionChange={(selectedRows) => {
                                if (selectedRows.length > 0) {
                                    setIsMultiSelected(true);
                                    setSelectedCreatorData(selectedRows);
                                } else {
                                    setIsMultiSelected(false);
                                    setSelectedCreatorData([]);
                                }
                                console.log('Selected rows:', selectedRows);
                                // Show sliding action panel here based on selectedRows.length
                            }}
                            onRowClick={(row) => {
                                console.log('Row clicked:', row);

                            }}
                            headerRowClass="border-violet-1 rounded-t-lg"
                        />
                    </div>
                }
            </div>
            {
                isPopupOpen === "import" ? (
                    <PopupLayout
                        title="Import Profiles"
                        onClose={() => setIsPopupOpen("")}
                        isAcceptButton={false}
                        isCancelButton={false}
                    >
                        <ImportProfiles
                            onClose={() => setIsPopupOpen("")}
                            onComplete={(results) => {
                                console.log('Import complete with results:', results);
                                // Here you would handle the imported profiles
                                // e.g., add them to creatorListData or handle accordingly
                            }}
                        />
                    </PopupLayout>
                ) : isPopupOpen === "invite" ? (
                    <PopupLayout
                        title="Invite Creators"
                        className="bg-[#292929]"
                        onClose={() => setIsPopupOpen("")}
                        isAcceptButton={true}
                        isCancelButton={false}
                        acceptText="Done"
                    >
                        <div className="flex flex-col gap-7">
                            <span className='text-24-semibold text-gray-50'>Sit tight! We’re sending the invites</span>
                            <div className='flex flex-col gap-12 mb-12'>
                                <div className='flex flex-col gap-2'>
                                    <span className='text-18-medium text-gray-50'>Your campaign is about to reach multiple creators.</span>
                                    <span className='text-14-semibold text-gray-300'>We'll keep you posted when they start responding.</span>
                                </div>
                                <div className='flex items-center justify-center'>
                                    {/* Gif */}
                                    <video
                                        src={InviteGif}
                                        autoPlay
                                        muted
                                        playsInline
                                        className="w-50 h-50 bg-transparent"
                                    >
                                        Your browser does not support the video tag.
                                    </video>
                                </div>
                            </div>
                        </div>
                    </PopupLayout>
                ) : null
            }
        </>
    )
}

export default CreatorListPage
