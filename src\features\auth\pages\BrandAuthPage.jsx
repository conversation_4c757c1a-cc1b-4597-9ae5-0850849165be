import React, { useState, useEffect, startTransition } from 'react';
import { Link, useNavigate, useParams, useLocation } from "react-router-dom";
import { useTranslation } from 'react-i18next';
import { useDispatch } from 'react-redux';
import { brandLoginThunk, brandRegisterThunk, initiateOAuthThunk } from '../service/authThunks';
import { useSelector } from 'react-redux';
import { selectChannelIconByKey, selectChannelIconByName } from '@/app/store/slices/systemSlice';
import { UserRole, RegisterSource } from '../../../app/store/enum';
import useRoles from '../../../shared/hooks/useRoles';



import { Toast } from '@shared/components/UI/Toast';
import { Input } from '@shared/components/UI/input';
import SocialButton from "@shared/components/UI/button";
import GradientSignupCard from "@auth/component/GradientSignupCard";
import AuthLayout from "@shared/layout/AuthLayout";
import { useLoading } from '@shared/components/UI/LoadingContext';
import { useSnackbar } from '@shared/components/UI/SnackbarContext';
import PopupLayout from '@shared/components/UI/PopupLayout';



import handwave from '@assets/emoji/Waving_Hand_Light_Skin_Tone.png';
import creator1 from '@assets/creator1.svg';
import creator2 from '@assets/creator2.svg';
import creator3 from '@assets/creator3.svg';
import mailIcon from "@assets/icon/mail-icon.svg";
import OtpVerificationDialog from '../component/OtpVerificationDialog';


const BrandAuthPage = () => {
    const navigate = useNavigate();
    const dispatch = useDispatch();
    const googleIcon = useSelector(() => selectChannelIconByKey('google'));
    const { showSnackbar } = useSnackbar();


    const { getRoleIdByName } = useRoles();

    const { t } = useTranslation(); // Initialize useTranslation
    const { authType } = useParams(); // "signin" or "signup"
    const isSignup = authType === "signup";

    const [email, setEmail] = useState("");
    const [brandName, setBrandName] = useState("");
    const [errorMessage, setErrorMessage] = useState("");
    const [isPopupOpen, setIsPopupOpen] = useState("");

    useEffect(() => {
        if (email.includes("@")) {
            const domainPart = email.split("@")[1];
            const companyName = domainPart.split(".")[0];
            setBrandName(companyName);
        } else {
            setBrandName("");
        }
        if (errorMessage) {
            setErrorMessage(""); // Clear error message when email changes
        }
    }, [email]);

    const location = useLocation();

    useEffect(() => {
        const searchParams = new URLSearchParams(location.search);
        const errorParam = searchParams.get('error');

        if (errorParam) {
            setIsPopupOpen("error");

            // Remove query params from URL
            const newUrl = window.location.pathname;
            window.history.replaceState({}, document.title, newUrl);
        }
    }, [location.search]);

    // const [isLoading, setIsLoading] = useState(false);
    const { isLoading, setIsLoading } = useLoading();

    const [isPending, setIsPending] = useState(false);
    const [showOtpDialog, setShowOtpDialog] = useState(false);
    const [touched, setTouched] = useState(false);

    // Get the brand role ID
    const brandRoleId = getRoleIdByName('brand');

    const publicEmailDomains = [
        "gmail.com",
        "yahoo.com",
        "hotmail.com",
        "outlook.com",
        "aol.com",
        "icloud.com",
        "mail.com"
    ];

    const emailParts = email.split("@");
    const domain = emailParts.length > 1 ? emailParts[1].toLowerCase() : "";

    const isEmailValid =
        /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email) &&                     // basic email format   
        /^[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(domain);                  // valid domain format   


    // const isBrandNameValid = brandName.trim().length > 0;
    // const isFormValid = isEmailValid && (!isSignup || (isSignup && isBrandNameValid));
    const isFormValid = isEmailValid && (!isSignup || (isSignup));

    const handleSubmit = async (e) => {
        console.log("Form submitted");
        e.preventDefault();
        setIsPending(true);
        startTransition(async () => {
            try {
                setIsLoading(true);

                if (isSignup) {
                    // Call the email registration endpoint
                    const response = await dispatch(brandRegisterThunk({
                        email: email,
                        role_uuid: brandRoleId,
                        register_source: RegisterSource.EMAIL_OTP
                    }));

                    var responseData = response.payload;

                    console.log("Email signup response:", responseData);

                    if (responseData.success) {
                        handleAuthSuccess('signup');
                    }
                    else {
                        if (responseData.message.includes("already registered")) {
                            // Toast.error('User already registered. Please sign in.');
                            setErrorMessage('User already registered. Please sign in.');
                        } else {
                            // Toast.error(responseData.message || 'Failed to send OTP. Please try again.');
                            setErrorMessage(responseData.message || 'Failed to send OTP. Please try again.');
                        }
                    }
                    // Simulate signup logic
                    console.log("Brand Name:", brandName);
                    console.log("Email:", email);


                }
                else {
                    // Call the email registration endpoint
                    const response = await dispatch(brandLoginThunk({
                        email: email,
                        role_uuid: brandRoleId,
                        register_source: RegisterSource.EMAIL_OTP
                    }));

                    var loginResponse = response.payload;

                    if (loginResponse.success) {
                        handleAuthSuccess('login');
                    } else {
                        if (loginResponse.message.includes("already registered")) {
                            Toast.error('User already registered. Please sign in.');
                        } else {
                            // Toast.error(loginResponse.message || 'Failed to send OTP. Please try again.');
                            setErrorMessage(loginResponse.message || 'Failed to send OTP. Please try again.');
                        }
                    }

                }
            } catch (err) {
                console.error(`${isSignup ? "Signup" : "Signin"} error:`, err);
            } finally {
                setIsLoading(false);
                setIsPending(false);
                setTouched(true);
            }
        });
    };

    const handleOtpVerified = (responseData = {}) => {
        // After OTP verification is complete
        setShowOtpDialog(false);
        if (responseData) {
            const allocatedBrands = responseData?.allocated_brands;
            if (allocatedBrands.length > 0) {
                localStorage.setItem('allocatedBrands', JSON.stringify(allocatedBrands));
                localStorage.setItem('selectedBrand', JSON.stringify(allocatedBrands[0]));

                Toast.success(isSignup ? "Registration successful! Welcome aboard!" : "Login successful! Welcome back!");
                setTimeout(() => navigate("/brand/dashboard"), 0);
            } else {
                if (responseData?.organization_brands && responseData?.organization_brands.length > 0) {
                    setTimeout(() => navigate(`/brand/brand-selection/${responseData?.organization_name}/brands`), 0);
                } else {
                    if (!isSignup) {
                        Toast.error('No brand allocated. Please Add Brand.');
                    }
                    setTimeout(() => navigate(`/brand/brand-selection/${responseData?.organization_name}`), 0);
                }
            }

        }
    };

    const handleUpdateEmail = () => {
        // Close the OTP dialog and let user update email
        setShowOtpDialog(false);
    };

    const handleAuthSuccess = (type) => {
        if (type === 'signup') {
            Toast.success("OTP sent successfully!");
            // Show OTP verification dialog instead of navigating away
            setShowOtpDialog(true);
        } else {
            Toast.success("OTP sent successfully!");

            setShowOtpDialog(true);
        }
        setIsLoading(false);
    };

    const handleOAuthInitiate = async (provider) => {
        try {
            setIsLoading(true);

            // showSnackbar("Hi my name is ankit")

            // Map provider names to their OAuth provider values
            const providerMap = {
                google: 'google',
                instagram: 'instagram',
                youtube: 'youtube'
            };

            // Make sure we have a valid role ID
            if (!brandRoleId) {
                Toast.error('(R) Internal Server Error');
                setIsLoading(false);
                return;
            }

            // Call the OAuth initiate endpoint
            const response = await dispatch(initiateOAuthThunk({
                provider: providerMap[provider],
                role_uuid: brandRoleId,
                error_path: `/brand/${authType}`
            }));

            var responseData = response.payload.data;

            console.log("Email signup response:", responseData);

            console.log(`${provider} OAuth initiation response:`, response);

            // Handle the OAuth response - typically redirects to the provider's auth page
            if (responseData?.auth_url) {
                window.location.href = responseData.auth_url;
            } else {
                showSnackbar(`Failed to initiate ${provider} authentication`, 'error');
                // Toast.error(`Failed to initiate ${provider} authentication`);
                setIsLoading(false);
            }
        } catch (error) {
            console.error(`${provider} OAuth initiation error:`, error);
            Toast.error(`Failed to initiate ${provider} authentication: ${error.message || 'Unknown error'}`);
            setIsLoading(false);
        }
    };

    return (
        <AuthLayout type='brand'>
            {/* OTP Verification Dialog */}
            <OtpVerificationDialog
                email={email}
                isBrand={true}
                type={isSignup ? 'signup' : 'login'}
                isOpen={showOtpDialog}
                onClose={() => setShowOtpDialog(false)}
                onVerified={handleOtpVerified}
                onUpdateEmail={handleUpdateEmail}
            />
            {
                isPopupOpen === "error" && (
                    <PopupLayout
                        title="Invite Creators"
                        className="bg-[#292929]"
                        onClose={() => setIsPopupOpen("")}
                        isAcceptButton={false}
                        isCancelButton={false}
                        acceptText="Done"
                    >
                        <div className="flex flex-col gap-7">
                            <span className='text-24-semibold text-gray-50'>⚠️ Invalid Email</span>
                            <div className='flex flex-col gap-12 mb-5'>
                                <div className='flex flex-col gap-2'>
                                    <span className='text-18-medium text-gray-300'>Please use your work email to {authType === 'signup' ? 'sign up' : 'sign in'} for Creatorverse Business.</span>
                                    {/* <span className='text-14-semibold text-gray-300'>We'll keep you posted when they start responding.</span> */}
                                </div>
                            </div>
                        </div>
                    </PopupLayout>
                )
            }
            {/* <div className="flex flex-col w-full lg:max-w-md md:max-w-md sm:max-w-sm max-h-[80vh] transition-all duration-500"> */}
            <div className="flex flex-col w-full md:max-w-md max-h-[90vh]  px-4 md:px-0">
                <div className='flex flex-col space-y-5'>
                    <div className="text-left">
                        <h1 className="text-36-semibold mb-1 whitespace-nowrap ">
                            {/* Conditional translation for "Hey there," / "Welcome back," */}
                            {isSignup ? t("brandAuth.greetingSignup") : t("brandAuth.greetingSignin")}
                            <span className="brand-gradient-text"> {t("brandAuth.brandPartner")}</span>
                            <div className="bg-[#D6F7EE] w-10 h-10 rounded-full p-1.5 ml-2 inline-flex items-center justify-center align-middle">
                                <img src={handwave} alt={t("alt.wavingHand")} className="w-6 h-6" />
                            </div>
                        </h1>
                        <p className="text-gray-300 text-18-regular">
                            {/* Conditional translation for description */}
                            {isSignup ? t("brandAuth.descriptionSignup") : t("brandAuth.descriptionSignin")}
                        </p>
                    </div>

                    <form onSubmit={handleSubmit} className="space-y-6">
                        <div className="space-y-2">
                            <div className="flex flex-col gap-1.5">
                                {/* Label */}
                                <label className="text-14-medium text-foreground">Work Email</label>

                                {/* Input Wrapper */}
                                <div
                                    className={`flex items-center h-[44px] w-full rounded-lg px-3 py-2 bg-background border border-input focus-within:ring-ring focus-within:ring-[0.5px] focus-within:ring-offset-1 transition-all duration-200 hover:shadow-[0px_0px_0px_3px_rgba(0,200,255,0.14)]`}
                                >
                                    {/* Optional icon */}
                                    <img
                                        src={mailIcon}
                                        alt="icon"
                                        className="mr-2 h-4 w-4 opacity-70"
                                    />

                                    {/* Input field */}
                                    <input
                                        type="email"
                                        onChange={(e) => {
                                            setEmail(e.target.value);
                                            setTouched(false); // Reset error while editing
                                        }}
                                        placeholder={t("brandAuth.emailPlaceholder")}
                                        required
                                        onBlur={() => setTouched(true)} // Mark as touched on blur
                                        className="w-full bg-transparent text-sm outline-none placeholder:text-muted-foreground focus:bg-transparent"
                                    />
                                </div>

                                {/* Error Message */}
                                {touched && (() => {
                                    let message = "";
                                    if (errorMessage) {
                                        message = errorMessage;
                                    } else if (!email) {
                                        message = t("brandAuth.emailRequired");
                                    } else if (publicEmailDomains.includes(domain)) {
                                        message = t("brandAuth.emailWorkEmail");
                                    } else if (!isEmailValid) {
                                        message = t("brandAuth.emailValid");
                                    }
                                    return message ? <p className="text-sm text-red-2 -mt-1">{message}</p> : null;
                                })()}
                            </div>
                        </div>
                        <button
                            type="submit"
                            className={`w-full h-[44px] px-[18px] py-[10px] rounded-[8px] transition duration-300 text-16-regular
                                ${isFormValid && !isLoading && !isPending && !publicEmailDomains.includes(domain)
                                    ? "bg-brand-500 hover:bg-brand-500 text-white cursor-pointer"
                                    : "bg-gray-500 text-gray-300"
                                }`}
                            disabled={!isFormValid || isLoading || isPending || publicEmailDomains.includes(domain)}
                        >
                            {/* Conditional translation for button text */}
                            {isSignup ? t("brandAuth.createAccountButton") : t("brandAuth.signInButton")}
                        </button>

                        <div className="relative flex items-center justify-center mt-5 mb-10">
                            <hr className="w-3/5 border-gray-700" />
                            <div className="px-4 text-16-regular text-gray-200 bg-primary absolute">{t("brandAuth.orContinueWith")}</div>
                        </div>

                        <div className="flex flex-row gap-4">
                            <SocialButton
                                icon={<img src={googleIcon.url} alt={t("alt.socialIcon")} className="w-5 h-5" />}
                                variant="default"
                                className="w-full px-6 text-16-semibold text-gray-900"
                                label={t("brandAuth.continueWithGoogle")}
                                onClick={() => handleOAuthInitiate('google')}
                            />
                        </div>
                    </form>

                    <div className="text-center text-18-regular text-gray-200">
                        <p>
                            {isSignup
                                ? <>{t("brandAuth.alreadyMember")} <Link to="/brand/signin" onClick={() => setErrorMessage("")} className="text-[rgba(71,200,236,1)] hover:underline">{t("brandAuth.logIn")}</Link></>
                                : <>{t("brandAuth.newHere")} <Link to="/brand/signup" onClick={() => setErrorMessage("")} className="text-[rgba(71,200,236,1)] hover:underline">{t("brandAuth.createAccount")}</Link></>
                            }
                        </p>
                    </div>
                </div>

                <div className="flex mt-5">
                    <GradientSignupCard
                        to="/influencer/login"
                        label={t("brandAuth.creatorCardLabel")}
                        description={t("brandAuth.creatorCardDescription")}
                        images={[
                            { src: creator3, className: "bg-blue-300 transition-all duration-500 ease-in-out ml-0 group-hover:ml-0" },
                            { src: creator2, className: "bg-green-300 transition-all duration-500 ease-in-out -ml-2 group-hover:-ml-1" },
                            { src: creator1, className: "bg-yellow-300 transition-all duration-500 ease-in-out -ml-2 group-hover:-ml-1" }
                        ]}
                        className="mt-10"
                    />
                </div>
            </div>
        </AuthLayout>
    );
};

export default BrandAuthPage;