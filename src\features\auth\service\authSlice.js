import { createSlice } from '@reduxjs/toolkit';
import { loginThunk, logoutThunk, getCurrentUserThunk, registerThunk, verifyOtpThunk, brandVerifyOtpThunk, createBrandThunk, requestUserBrandInfoThunk, requestUserCreatorInfoThunk } from './authThunks';
import { getInfluencerInfoThunk } from '@influencer/services/influencerThunks';
import { RequestStatus } from '../../../app/store/enum';

const getParsedItem = (key, fallback) => {
  try {
    const value = localStorage.getItem(key);
    if (!value || value === 'undefined') return fallback;
    return JSON.parse(value);
  } catch {
    return fallback;
  }
};

/**
 * Auth slice initial state
 */
const initialState = {
  isAuthenticated: !!localStorage.getItem('auth_token'),
  user: JSON.parse(localStorage.getItem('user')) || null,
  token: localStorage.getItem('auth_token') || null,
  status: RequestStatus.IDLE, // 'idle' | 'loading' | 'succeeded' | 'failed'
  error: null,
  allocatedBrands: getParsedItem('allocatedBrands', []),
  organizationBrands: getParsedItem('organizationBrands', []),
  socialProfiles: getParsedItem('socialProfiles', [])
};

/**
 * Auth slice with reducers and extra reducers for thunks
 */
const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    // Synchronous actions
    setCredentials(state, action) {
      state.user = action.payload.user;
      state.token = action.payload.token;
      state.isAuthenticated = true;
    },
    clearAuth(state) {
      state.user = null;
      state.token = null;
      state.isAuthenticated = false;
      localStorage.removeItem('auth_token');
    },
    clearError(state) {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder

      // Register thunk cases
      .addCase(registerThunk.pending, (state) => {
        state.status = 'loading';
        state.error = null;
      })
      .addCase(registerThunk.fulfilled, (state) => {
        state.status = 'succeeded';
      })
      .addCase(registerThunk.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.payload || 'Registration failed';
      })

      // Get current user thunk cases
      .addCase(getCurrentUserThunk.pending, (state) => {
        state.status = 'loading';
      })
      .addCase(getCurrentUserThunk.fulfilled, (state, action) => {
        state.status = 'succeeded';
        state.user = action.payload;
        state.isAuthenticated = true;
      })
      .addCase(getCurrentUserThunk.rejected, (state) => {
        state.status = 'failed';
        // If can't get user data, clear auth
        state.isAuthenticated = false;
        state.user = null;
        state.token = null;
      })


      // Logout thunk cases
      .addCase(logoutThunk.pending, (state) => {
        state.status = RequestStatus.LOADING;
        state.error = null;
      })
      .addCase(logoutThunk.fulfilled, (state) => {
        // Reset auth state to initial values
        state.isAuthenticated = false;
        state.user = null;
        state.token = null;
        state.allocatedBrands = [];
        state.organizationBrands = [];
        state.socialProfiles = [];
        state.status = RequestStatus.IDLE;
        state.error = null;
      })
      .addCase(logoutThunk.rejected, (state, action) => {
        // Even on logout failure, clear auth state for security
        state.isAuthenticated = false;
        state.user = null;
        state.token = null;
        state.allocatedBrands = [];
        state.organizationBrands = [];
        state.socialProfiles = [];
        state.status = RequestStatus.FAILED;
        state.error = action.payload?.message || 'Logout failed';
      })

      // Verify OTP thunk cases
      .addCase(verifyOtpThunk.pending, (state) => {
        state.status = RequestStatus.LOADING;
        state.error = null;
      })
      .addCase(verifyOtpThunk.fulfilled, (state, action) => {
        state.status = RequestStatus.SUCCEEDED;
        state.isAuthenticated = true;
        state.user = action.payload.data.user;
        state.token = action.payload.data.access_token;
        state.socialProfiles = action.payload.data.social_profiles;
      })
      .addCase(verifyOtpThunk.rejected, (state, action) => {
        state.status = RequestStatus.FAILED;
        state.error = action.payload || 'OTP verification failed';
      })

      // Brand Verify OTP thunk cases
      .addCase(brandVerifyOtpThunk.pending, (state) => {
        state.status = RequestStatus.LOADING;
        state.error = null;
      })
      .addCase(brandVerifyOtpThunk.fulfilled, (state, action) => {
        state.status = RequestStatus.SUCCEEDED;
        state.isAuthenticated = true;
        state.user = action.payload.data.user;
        state.token = action.payload.data.access_token;
        state.allocatedBrands = action.payload.data.allocated_brands;
        state.organizationBrands = action.payload.data.organization_brands;
      })
      .addCase(brandVerifyOtpThunk.rejected, (state, action) => {
        state.status = RequestStatus.FAILED;
        state.error = action.payload || 'OTP verification failed';
      })


      // Brand User thunk cases
      .addCase(requestUserBrandInfoThunk.pending, (state) => {
        state.status = RequestStatus.LOADING;
        state.error = null;
      })
      .addCase(requestUserBrandInfoThunk.fulfilled, (state, action) => {
        state.status = RequestStatus.SUCCEEDED;
        state.isAuthenticated = true;
        state.user = action.payload.data.user;
        state.token = action.payload.data.access_token;
        state.allocatedBrands = action.payload.data.allocated_brands;
        state.organizationBrands = action.payload.data.organization_brands;
      })
      .addCase(requestUserBrandInfoThunk.rejected, (state, action) => {
        state.status = RequestStatus.FAILED;
        state.error = action.payload || 'OTP verification failed';
      })

      // Creator User thunk cases
      .addCase(requestUserCreatorInfoThunk.pending, (state) => {
        state.status = RequestStatus.LOADING;
        state.error = null;
      })
      .addCase(requestUserCreatorInfoThunk.fulfilled, (state, action) => {
        state.status = RequestStatus.SUCCEEDED;
        state.isAuthenticated = true;
        state.user = action.payload.data.user;
        state.socialProfiles = action.payload.data.social_profiles;
      })
      .addCase(getInfluencerInfoThunk.fulfilled, (state, action) => {
        state.socialProfiles = action.payload.data.social_profiles;
        state.user = action.payload.data.user;
      })
      .addCase(requestUserCreatorInfoThunk.rejected, (state, action) => {
        state.status = RequestStatus.FAILED;
        state.error = action.payload || 'OTP verification failed';
      })

    //BrandManagement Add Brand
    .addCase(createBrandThunk.pending, (state) => {
      state.status = RequestStatus.LOADING;
      state.error = null;
    })
    .addCase(createBrandThunk.fulfilled, (state, action) => {
      state.status = RequestStatus.SUCCEEDED;
      state.allocatedBrands.push(action.payload.data);
    })
    .addCase(createBrandThunk.rejected, (state, action) => {
      state.status = RequestStatus.FAILED;
      state.error = action.payload || 'Failed to add brand';
    });

},
});

// Export synchronous actions
export const { setCredentials, clearAuth, clearError } = authSlice.actions;

// Export thunks
export { loginThunk, logoutThunk, getCurrentUserThunk, registerThunk, verifyOtpThunk };

// Export the reducer
export default authSlice.reducer;