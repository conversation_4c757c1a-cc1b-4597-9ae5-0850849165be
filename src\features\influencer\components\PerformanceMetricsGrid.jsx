import React from "react";
import PerformanceCard from "./PerformanceCard";

import { useSelector } from 'react-redux';
import { selectAllChannelsWithIcons } from '@/app/store/slices/systemSlice';

import Heart from '@assets/icon/favorite_border.svg';
import UserAdd from '@assets/icon/userAdd.svg';
import UsersGroup from '@assets/icon/userGroup.svg';
import AllInclusive from '@assets/icon/all_inclusive.svg';
import TrendingUp from '@assets/icon/trending_up.svg';
import LightBulb from '@assets/icon/lightbulb1.svg';
import CommentDot from '@assets/icon/commentDot.svg';
import Eye from '@assets/icon/visibility.svg';
import InstagramIcon from '@assets/icon/instagram-circle.svg';
import YoutubeIcon from '@assets/icon/youtube-icon.svg';

const PerformanceMetricsGrid = ({ platform, isConfigured = true }) => {
    const channels = useSelector((state) => selectAllChannelsWithIcons(state));
    const metricsMap = {
        instagram: [
            { label: "Audience", value: "7,000", delta: "+2.5%", isPositive: true, icon: UserAdd, iconBg: "var(--color-violet-1)" },
            { label: "Audience Credibility Score", value: "45%", delta: "+5%", isPositive: true, icon: UsersGroup, iconBg: "var(--color-red-2)" },
            { label: "Engagement Rate", value: "2.5%", delta: "-0.5%", isPositive: false, icon: AllInclusive, iconBg: "var(--color-blue)" },
            { label: "Average Reach", value: "1,200", delta: "+3.0%", isPositive: true, icon: TrendingUp, iconBg: "var(--color-green-2)" },
            { label: "Average Impressions", value: "10,000", delta: "+1.0%", isPositive: true, icon: LightBulb, iconBg: "var(--color-pink)" },
            { label: "Average Likes", value: "250", delta: "-2.0%", isPositive: false, icon: Heart, iconBg: "var(--color-teal)" },
            { label: "Average Comments", value: "409", delta: "+0.6%", isPositive: true, icon: CommentDot, iconBg: "var(--color-purple)" },
            { label: "Average Saves", value: "1.6M", delta: "-0.6%", isPositive: false, icon: Eye, iconBg: "var(--color-red-2)" },
        ],
        youtube: [
            { label: "Subscribers", value: "12,000", delta: "+1.2%", isPositive: true, icon: UserAdd, iconBg: "var(--color-brand-500)" },
            { label: "Channel Score", value: "78%", delta: "+2%", isPositive: true, icon: Heart, iconBg: "var(--color-green-1)" },
            { label: "Engagement Rate", value: "3.1%", delta: "-0.2%", isPositive: false, icon: AllInclusive, iconBg: "var(--color-red-1)" },
            { label: "Average Views", value: "3,500", delta: "+4.0%", isPositive: true, icon: Heart, iconBg: "var(--color-violet)" },
            { label: "Watch Time", value: "1,200h", delta: "+1.5%", isPositive: true, icon: TrendingUp, iconBg: "var(--color-blue)" },
            { label: "Average Likes", value: "320", delta: "-1.0%", isPositive: false, icon: Heart, iconBg: "var(--color-hot-pink)" },
            { label: "Average Comments", value: "120", delta: "+0.8%", isPositive: true, icon: AllInclusive, iconBg: "var(--color-green-2)" },
            { label: "Shares", value: "2,100", delta: "+2.2%", isPositive: true, icon: Eye, iconBg: "var(--color-red-2)" },
        ],
    };

    const defaultPlatform = Object.keys(metricsMap)[0];
    const metrics = metricsMap[platform] || metricsMap[defaultPlatform];

    const channel = channels.find((ch) => ch.name.toLowerCase() === platform);

    console.log('Metrics:', channel);

    return (
        <div className="relative">
            <div className={`flex flex-wrap gap-4 py-4.5 ${!isConfigured ? 'blur-sm pointer-events-none' : ''}`}>
                {metrics.map((item, index) => (
                    <div className="w-1/5 flex-grow" key={index}>
                        <PerformanceCard {...item} />
                    </div>
                ))}
            </div>

            {!isConfigured && (
                <div className="absolute inset-0 bg-black/0
                 bg-opacity-60 flex flex-col items-center justify-center text-gray-50 text-center rounded-xl p-6 z-5">
                    <p className="text-14-semibold mb-5">🔒 Unlock Campaign Insights</p>
                    <h3 className="text-20-bold text-gray-200 mb-4">🚫 You’re Missing Out on <span className="text-brand-500">Powerful Insights</span></h3>
                    <p className="text-18-medium text-brand-500 cursor-pointer mb-4">🔗 Uncover key campaign insights in under 60 seconds</p>
                    <ul className="text-14-semibold text-gray-200 mb-5 space-y-1 text-left">
                        <li>🚀 Track campaign impact live</li>
                        <li>🌟 Engagement, reach, and impressions in one place</li>
                    </ul>
                    <button
                        className={`${platform === 'instagram' ? 'bg-light-2' : 'bg-white'} bg-white text-14-semibold text-gray-900 px-7 py-2.5  rounded-md flex justify-center cursor-pointer items-center gap-3 shadow-md hover:scale-102 transition`}
                    >
                        <img src={channel?.icon.url} className="bg-red-100 rounded-full" alt="Instagram" />
                        <span className="flex justify-center items-center">{channel?.name}</span>
                    </button>
                </div>
            )}
        </div>
    );
};

export default PerformanceMetricsGrid;
