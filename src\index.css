@import "tailwindcss";
@import "@flaticon/flaticon-uicons/css/all/all";

/* @plugin 'tailwind-scrollbar'; */
@plugin 'tailwind-scrollbar' {
  nocompatible: true;
}

@layer base {
  :where(.css-dev-only-do-not-override-os2fvf).ant-select-outlined:not(.ant-select-customize-input) .ant-select-selector {
    background-color: var(--color-gray-900) !important;
    border: 1px solid var(--color-gray-900) !important;
    color: var(--color-gray-400) !important;
  }


  .ant-table-container {
    border: none !important;
  }

  .ant-table {
    /* background-color: var(--color-gray-900) !important; */
    background-color: transparent !important;
  }

  .ant-table-tbody>tr {
    border-bottom: 1px solid var(--color-gray-900) !important;
    border: none !important;

  }

  .ant-table-tbody>tr:hover>td {
    background-color: var(--color-gray-600) !important;
    border: none !important;

    transition: background-color 0.3s ease;
  }

  .ant-table-tbody>tr>td,
  .ant-table-thead>tr>th {
    padding: 4px;
    background-color: #252525 !important;
    border: none !important;
    border-bottom: 1px solid var(--color-gray-900) !important;

  }

  /* tr:nth-child(odd) {
    background: #F1E6FF;
  }

  tr:nth-child(even) {
    background: white;
  } */

  thead[class*="ant-table-thead"] th {
    background-color: var(--color-gray-600) !important;
    /* background-color: red !important; */
    color: white !important;
    font-weight: bold;
    border: none !important;
    text-align: center;
  }

  .table_btn {
    margin: 0 !important;
  }

  .ant-btn {
    margin: 0;
  }

  .ant-table-tbody>tr:hover>td {
    color: #FFF;
  }

  .ant-picker:focus,
  .ant-picker-focused {
    box-shadow: none !important;
    border-color: #4b5563 !important;
    /* optional: gray-600 for subtle border */
  }

  .ant-picker-input>input::placeholder {
    color: var(--color-gray-400) !important;
  }

  .ant-select-selector {
    background-color: transparent !important;
    border: 1px solid #4B5563 !important;
    box-shadow: none !important;
    color: #6B7280 !important;
    /* Tailwind gray-500 */
  }

  .ant-select-selection-placeholder {
    color: var(--color-gray-400) !important;
    /* Placeholder color */
  }

  .ant-select-arrow {
    color: var(--color-gray-400) !important;
  }

  .custom-ant-dropdown {
    background-color: #537099 !important;
    /* Tailwind gray-800 */
    color: #6B7280 !important;
    border-radius: 8px;
  }

  .ant-select-item {
    color: var(--color-gray-100) !important;
  }

  .ant-select-item-options,
  .ant-select-item-option-selected {
    background-color: var(--color-gray-600) !important;
    /* gray-700 */
    color: #E5E7EB !important;
    /* gray-200 */
  }

  .ant-select-selection-item {
    color: #E5E7EB !important;
  }

  .ant-switch-checked {
    background-color: #12C9AC !important;
    /* Replace with your custom color */
  }

}


@theme {
  --color-primary: #252525;

  /* Gray Colors */
  --color-gray-50: #F1F1F1;
  --color-gray-100: #E5E9ED;
  --color-gray-200: #C4C9CF;
  --color-gray-300: #8F939E;
  --color-gray-400: #656974;
  --color-gray-500: #484C55;
  --color-gray-600: #303239;
  --color-gray-800: #505050;
  --color-gray-900: #2E2E2E;

  --color-black: #000000;
  --color-white: #FFFFFF;
  --color-bg: #252525;

  /* Brand Colors */
  --color-brand-200: #DEF8FF;
  --color-brand-500: #47C8EC;
  --color-brand-600: #14A7CF;

  /* Additional Colors */
  --color-green: #51DC8E;
  --color-green-1: #1D4336;
  --color-green-2: #12C9AC;

  --color-blue: #4F98FA;
  --color-sky-blue: #A8EAFE;
  --color-persian-blue: #1A237E;

  --color-red: #FAA6B0;
  --color-red-1: #511213;
  --color-red-2: #FF7070;

  --color-violet: #9633EC;
  --color-violet-1: #B973FF;

  --color-purple: #696EFF;
  --color-purple-1: #794BFF;

  --color-pink: #E42E85;
  --color-hot-pink: #FEACFE;

  --color-yellow: #E8FD95;
  --color-orange: #FFAE4C;
  --color-orange-1: #43341D;

  --color-teal: #259091;

  /* Light Colors */
  --color-light-1: #E2DBF9;
  --color-light-2: #FFE8DA;
  --color-light-3: #F7FCEC;
  --color-light-4: #FDFCEA;
  --color-light-5: #DFEBFF;
  --color-light-6: #FFF1F1;
  --color-light-7: #ECF0F4;
  --color-light-8: #D6F7EE;

  --animate-collapse: collapse 0.5s ease-in-out forwards;
  --animate-expand: expand 0.5s ease-in-out forwards;

  /* Custom Heights */
  --height-15: 3.75rem;
}

@keyframes collapse {
  0% {
    height: 3.75rem;
  }

  100% {
    height: 0rem;
  }
}

@keyframes expand {
  0% {
    height: 0rem;
  }

  100% {
    height: 3.75rem;
  }
}


@layer utilities {
  .creator-gradient-text {
    background: linear-gradient(91.33deg, #FF915D 0.03%, #FF7FF0 50.75%, #FF915D 100.76%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
  }

  .brand-gradient-text {
    background: linear-gradient(91deg, #47C8EC 4.63%, #29EAB6 34.15%, #47C8EC 69.52%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
  }


  .text-14-bold {
    font-family: 'Inter', sans-serif;
    font-weight: 700;
    font-size: 14px;
    line-height: 100%;
    letter-spacing: 0%;
    vertical-align: middle;
  }

  .text-18-bold {
    font-family: 'Inter', sans-serif;
    font-weight: 700;
    font-size: 18px;
    line-height: 100%;
    letter-spacing: 0%;
    vertical-align: middle;
  }

  .text-20-bold {
    font-family: 'Inter', sans-serif;
    font-weight: 700;
    font-size: 20px;
    line-height: 100%;
    letter-spacing: 0%;
    vertical-align: middle;
  }

  .text-24-bold {
    font-family: 'Inter', sans-serif;
    font-weight: 700;
    font-size: 24px;
    line-height: 100%;
    letter-spacing: 0%;
    vertical-align: middle;
  }

  .text-30-bold {
    font-family: 'Inter', sans-serif;
    font-weight: 700;
    font-size: 30px;
    line-height: 100%;
    letter-spacing: 0%;
    vertical-align: middle;
  }

  .text-12-semibold {
    font-family: 'Inter', sans-serif;
    font-weight: 600;
    font-size: 12px;
    line-height: 100%;
    letter-spacing: 0%;
    vertical-align: middle;
  }

  .text-14-semibold {
    font-family: 'Inter', sans-serif;
    font-weight: 600;
    font-size: 14px;
    line-height: 100%;
    letter-spacing: 0%;
    vertical-align: middle;
  }

  .text-16-semibold {
    font-family: 'Inter', sans-serif;
    font-weight: 600;
    font-size: 16px;
    line-height: 100%;
    letter-spacing: 0%;
    vertical-align: middle;
  }

  .text-18-semibold {
    font-family: 'Inter', sans-serif;
    font-weight: 600;
    font-size: 18px;
    line-height: 100%;
    letter-spacing: 0%;
    vertical-align: middle;
  }

  .text-20-semibold {
    font-family: 'Inter', sans-serif;
    font-weight: 600;
    font-size: 20px;
    line-height: 100%;
    letter-spacing: 0%;
  }

  .text-22-semibold {
    font-family: 'Inter', sans-serif;
    font-weight: 600;
    font-size: 22px;
    line-height: 100%;
    letter-spacing: 0%;
  }

  .text-24-semibold {
    font-family: 'Inter', sans-serif;
    font-weight: 600;
    font-size: 24px;
    line-height: 100%;
    letter-spacing: 0%;
  }

  .text-28-semibold {
    font-family: 'Inter', sans-serif;
    font-weight: 600;
    font-size: 28px;
    line-height: 100%;
    letter-spacing: 0%;
  }

  .text-30-semibold {
    font-family: 'Inter', sans-serif;
    font-weight: 600;
    font-size: 1.875em;
    /* 30px */
    line-height: 1;
    /* 100% */
    letter-spacing: 0;
  }

  .text-36-semibold {
    font-family: 'Inter', sans-serif;
    font-weight: 600;
    font-size: clamp(1.75rem, 4vw, 2.25rem);
    /* scales between ~28px to 36px */
    line-height: clamp(2.25rem, 5vw, 2.75rem);
    /* scales line-height */
    letter-spacing: -0.02em;
  }

  .text-8-medium {
    font-family: 'Inter', sans-serif;
    font-weight: 500;
    font-size: clamp(0.5rem, 1.5vw, 0.53625rem);
    /* ~8px to 8.58px */
    line-height: 1;
    letter-spacing: 0;
  }

  .text-10-medium {
    font-family: 'Inter', sans-serif;
    font-weight: 500;
    font-size: 10px;
    /* ~13px to 14px */
    line-height: 1.2;
    letter-spacing: 0;
  }

  .text-12-medium {
    font-family: 'Inter', sans-serif;
    font-weight: 500;
    font-size: 12px;
    /* ~13px to 14px */
    line-height: 1.2;
    letter-spacing: 0;
  }

  .text-14-medium {
    font-family: 'Inter', sans-serif;
    font-weight: 500;
    font-size: clamp(0.8125rem, 2vw, 0.875rem);
    /* ~13px to 14px */
    line-height: 1.2;
    letter-spacing: 0;
  }

  .text-16-medium {
    font-family: 'Inter', sans-serif;
    font-weight: 500;
    font-size: 16px;
    line-height: 1.2;
    letter-spacing: 0;
  }

  .text-18-medium {
    font-family: 'Inter', sans-serif;
    font-weight: 500;
    font-size: 18px;
    line-height: 1.2;
    letter-spacing: 0;
  }

  .text-20-medium {
    font-family: 'Inter', sans-serif;
    font-weight: 500;
    font-size: 20px;
    line-height: 100%;
    letter-spacing: 0%;
  }

  .text-24-medium {
    font-family: 'Inter', sans-serif;
    font-weight: 500;
    font-size: 24px;
    line-height: 100%;
    letter-spacing: 0%;
  }

  .text-30-medium {
    font-family: 'Inter', sans-serif;
    font-weight: 500;
    font-size: 30px;
    line-height: 100%;
    letter-spacing: 0%;
  }

  .text-10-regular {
    font-family: 'Inter', sans-serif;
    font-weight: 400;
    font-size: 10px;
    line-height: 100%;
    letter-spacing: 0%;
  }

  .text-12-regular {
    font-family: 'Inter', sans-serif;
    font-weight: 400;
    font-size: 12px;
    line-height: 100%;
    letter-spacing: 0%;
  }

  .text-14-regular {
    font-family: 'Inter', sans-serif;
    font-weight: 400;
    font-size: 14px;
    line-height: 100%;
    letter-spacing: 0%;
  }

  .text-16-regular {
    font-family: 'Inter', sans-serif;
    font-weight: 400;
    font-size: 16px;
    line-height: 100%;
    letter-spacing: 0%;
  }

  .text-18-regular {
    font-family: 'Inter', sans-serif;
    font-weight: 400;
    font-size: clamp(0.95rem, 2.5vw, 1.125rem);
    /* scales from ~15px to 18px */
    line-height: 1.2;
    /* use a proportional line-height for better scaling */
    letter-spacing: 0;
  }

  .text-label-18 {
    font-family: 'Inter', sans-serif;
    font-weight: 400;
    font-size: 18px;
    line-height: 100%;
    letter-spacing: 0%;
  }

  .text-label-16 {
    font-family: 'Inter', sans-serif;
    font-weight: 600;
    font-size: 16px;
    line-height: 24px;
    letter-spacing: 0%;
  }

  .text-heading-36 {
    font-family: 'Inter', sans-serif;
    font-weight: 600;
    font-size: clamp(1.75rem, 4vw, 2.25rem);
    /* scales between ~28px to 36px */
    line-height: clamp(2.25rem, 5vw, 2.75rem);
    /* scales line-height */
    letter-spacing: -0.02em;
  }

  .text-roboto {
    font-family: 'Roboto', sans-serif;
    font-weight: 500;
    font-size: clamp(0.5rem, 1.5vw, 0.53625rem);
    /* ~8px to 8.58px */
    line-height: 1;
    letter-spacing: 0;
  }

  .text-roboto-light {
    font-family: 'Roboto', sans-serif;
    font-weight: 300;
    font-style: normal;
    font-size: clamp(0.5rem, 1.5vw, 0.53625rem);
    /* ~8px to 8.58px */
    line-height: 1;
    letter-spacing: 0;
  }


  .bg-background {
    background-color: hsl(var(--background));
  }

  .text-foreground {
    color: hsl(var(--foreground));
  }

  .border-input {
    border-color: hsl(var(--input));
  }

  .ring-offset-background {
    --tw-ring-offset-color: hsl(var(--ring-offset));
  }

  .text-muted-foreground {
    color: hsl(var(--muted-foreground));
  }

  .border {
    border-color: hsl(var(--border));
  }

  .no-spinner::-webkit-outer-spin-button,
  .no-spinner::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  .no-spinner {
    -moz-appearance: textfield;
  }

  .perspective-1000 {
    perspective: 1000px;
  }

  .backface-hidden {
    backface-visibility: hidden;
  }

  .scrollbar-hidden {
    &::-webkit-scrollbar {
      display: none;
    }
  }

  /* In your global CSS (e.g. styles.css, globals.css) */
  .scrollbar-hidden-unless-hover::-webkit-scrollbar {
    width: 0px;
    height: 0px;
    background: transparent;
  }

  .scrollbar-hidden-unless-hover:hover::-webkit-scrollbar {
    height: 6px;
  }

  .scrollbar-hidden-unless-hover:hover::-webkit-scrollbar-thumb {
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 4px;
  }

  .scrollbar-hidden-unless-hover:hover::-webkit-scrollbar-track {
    background: transparent;
  }

  /* Firefox support */
  .scrollbar-hidden-unless-hover {
    scrollbar-width: none;
    /* Hide */
  }

  .scrollbar-hidden-unless-hover:hover {
    scrollbar-width: thin;
    /* Show on hover */
  }

}


:root {
  /* Typography */
  font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  /* Color scheme */
  color-scheme: light dark;

  /* Global foreground and background colors */
  color: hsl(0 0% 87%);
  background-color: hsl(0 0% 15%);
  /* same as #252525 */

  /* Tailwind-compatible design tokens (HSL values) */
  --background: 0 0% 15%;
  /* #252525 */
  --foreground: 0 0% 87%;
  /* rgba(255,255,255,0.87) equivalent */
  --input: 240 5.9% 26.7%;
  --ring: 240 5.9% 26.7%;
  --ring-offset: 0 0% 15%;
  --muted-foreground: 240 5% 64%;
  --border: 240 4.8% 30%;

  /* Font rendering */
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}