import { useSelector } from 'react-redux';

/**
 * Custom hook for accessing auth-related state in components
 * This provides a clean interface for components to read auth state
 */
const useAuthSelectors = () => {
  // Get the auth state slice from the store
  const authState = useSelector((state) => state.auth);
  
  return {    // User data and authentication status
    user: authState.user,
    isAuthenticated: authState.isAuthenticated,
    token: authState.token,

    //Organization data
    organizationBrands: authState.organizationBrands,
    allocatedBrands: authState.allocatedBrands,
    
    // Request status
    status: authState.status,
    isLoading: authState.status === 'loading',
    isIdle: authState.status === 'idle',
    isSucceeded: authState.status === 'succeeded',
    isFailed: authState.status === 'failed',

    // Error information
    error: authState.error,
    hasError: !!authState.error,
  };
};
export const selectOrganizationBrands = (state) => state.auth.organizationBrands;

export default useAuthSelectors;

// Export selectors