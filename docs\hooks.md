# Custom Hooks

This document describes the custom React hooks used in the Creatorverse Frontend application, located in `src/shared/hooks/`.

- **`useApiRequest.js`**: A custom hook for making API requests, likely wrapping `alovaInstance` and providing loading, error, and data states.
- **`useApiService.js`**: A hook that might provide an interface to various API services (e.g., `authApi`, `brandManagementApi`), simplifying API calls within components.
- **`useAuth.js`**: A hook for managing authentication state, providing functions for login, logout, and access to user information.
- **`useContainerWidth.js`**: A hook to dynamically measure and provide the width of a containing element, useful for responsive designs.
- **`useRoles.js`**: A hook to manage and access user roles, potentially providing utilities to check user permissions.

