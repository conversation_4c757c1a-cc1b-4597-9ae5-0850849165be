// Brand Management API service - Updated for Multi-Backend Architecture
import authInstance from './instances/authInstance';

/**
 * Brand Management API service - Updated for Multi-Backend Architecture
 * Contains all endpoints related to brand management
 * Now uses dedicated auth service instance for better separation of concerns
 */
const brandManagementApi = {
  // Request brand access
  requestBrandAccess: (brandId) => {
    return authInstance.Post(`/brands/${brandId}/join`, {});
  },

  // Create a new brand
  createBrand: (brandData) => {
    return authInstance.Post('/brands', brandData);
  },

  // Get user brand info
  requestUserBrandInfo: () => {
    return authInstance.Get(`/auth/brand/userinfo`);
  },
 
  // Get user creator info
  requestUserCreatorInfo: () => {
    return authInstance.Get(`/auth/influencer/userinfo`);
  },

  // Get youtube channels
  getYoutubeChannels: () => {
    return authInstance.Get(`/youtube/channels`);
  },

  selectYoutubeChannels: (channels) => {
    return authInstance.Post(`/youtube/channels/select`, channels);
  }

};

export default brandManagementApi;