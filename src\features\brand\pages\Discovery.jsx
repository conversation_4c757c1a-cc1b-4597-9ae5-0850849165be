import React, { useState, useEffect, useRef, useMemo } from 'react';
// eslint-disable-next-line no-unused-vars
import { motion, AnimatePresence } from "framer-motion";
import { BsGenderMale, BsCalendarDate, BsTranslate } from 'react-icons/bs';
import { Tooltip } from 'antd';
import { useLocation, useNavigate } from 'react-router-dom';



import DashboardLayout from '@shared/layout/DashboardLayout'
import PlatformToggle from '@shared/components/PlatformToggle'
import RankIcon from '@shared/components/RankIcon';
import CreatorTable from '../components/CreatorTable'
import Pagination from '../components/Pagination'
import DataTable from '../components/DataTable';
import CreatorCategoryCard from '../components/CreatorCategoryCard';

import InterestFilterDropdown from '../components/InterestFilterDropdown'
import LoadingState from '../components/LoadingState'
import ErrorState from '../components/ErrorState'
import { FaTimes } from 'react-icons/fa';
import { useDispatch, useSelector } from "react-redux";
import { useSnackbar } from '@shared/components/UI/SnackbarContext';
import { useLoading } from '@shared/components/UI/LoadingContext';
import useBrandActions from '@brand/services/brandActions';
import { setCurrentFilterSet } from '@brand/services/brandSlice';
import { selectCurrentFilterSet, selectGlobalFilters } from '@brand/services/brandSelectors';

import { getCreatorListsThunk, createCreatorListThunk, addMemberToListThunk } from '@brand/services/brandThunks';
import useBrandSelectors from '@brand/services/brandSelectors';







// Import icons
import FilterIcon from '@assets/icon/filter.svg'
import MapPinIcon from '@assets/icon/location.svg';
import BlueCrossIcon from '@assets/icon/blue-cross.svg'
import SettingSliderIcon from '@assets/icon/settings-sliders.svg'
import SearchWithSuggestions from '../components/SearchWithSuggestions'
import Filter from '../components/FilterFixed'
import FilterComponent from '../../../shared/components/FilterComponent'
import VerifiedIcon from '@assets/icon/blue_verified.svg';
import ArrowUpIcon from '@assets/icon/arrow_upward.svg';
import ArrowDownIcon from '@assets/icon/arrow_downward.svg';
import BookmarkIcon from '@assets/icon/bookmark_border-white.svg';
import SendIcon from '@assets/icon/paper-plane.svg';
import ErrorOutlineIcon from '@assets/icon/error_outline.svg';
import AvatarStack from '@shared/components/AvatarStack';
import CampaignPopup from '../../../shared/components/CampaignPopup';
import TooltipSpan from '../components/TooltipSpan';
import PlatformSelector from '../../../shared/components/PlatformSelector';
import AppliedFiltersPills from '../components/AppliedFiltersPills';
import SavedFiltersPanel from '../components/SavedFiltersPanel';

// Sample creator data - moved outside component to avoid re-creation on each render
const sampleAvatars = {
    avatar1: "https://images.unsplash.com/photo-1494790108377-be9c29b29330?w=150&h=150&fit=crop&crop=faces",
    avatar2: "https://images.unsplash.com/photo-1570295999919-56ceb5ecca61?w=150&h=150&fit=crop&crop=faces",
    avatar3: "https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150&h=150&fit=crop&crop=faces",
    avatar4: "https://images.unsplash.com/photo-1539571696357-5a69c17a67c6?w=150&h=150&fit=crop&crop=faces",
    avatar5: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=faces"
};

const savedFiltersData = [
    {
        name: "filterSaved1",
        createdDate: "24 May 24",
        reultcount: 23,
        filters: [
            {
                channel: "instagram",
                filter: "Category",
                value: "beauty_cosmetics",
                filterFor: "creator"
            },
            {
                channel: "instagram",
                filter: "Location",
                value: [
                    {
                        filter: "Tier 1",
                        value: "ahmedabad, bangalore, chennai, delhi, hyderabad, kolkata, mumbai, pune"
                    }
                ],
                filterFor: "creator"
            },
            {
                channel: "instagram",
                filter: "follower count",
                value: "500000-1000000",
                filterFor: "creator"
            }
        ]
    }
];

const Discovery = () => {
    const filterSectionRef = useRef(null);
    const location = useLocation();
    const navigate = useNavigate();
    const dispatch = useDispatch();
    const { isLoading, setIsLoading } = useLoading();
    const { showSnackbar } = useSnackbar();
    const { getFilterMetadata, searchCreators, setCurrentFilterSet } = useBrandActions();
    const { selectedBrand } = useBrandSelectors();

    // Get search suggestions from Redux store (for 410 responses)
    const searchSuggestions = useSelector(state => state.brand.searchSuggestions);


    const selectedCategory = location.state?.selectedCategory;

    const [activePlatform, setActivePlatform] = useState(location.state?.channel ? location.state.channel : "instagram")

    const [searchQuery, setSearchQuery] = useState("")
    const [currentPage, setCurrentPage] = useState(1)

    // Loading and error states
    const [isTableVisible, setIsTableVisible] = useState(false)
    const [isFilterTabOpen, setIsFilterTabOpen] = useState(false);
    const [isCreating, setIsCreating] = useState(true);


    const [isMultiSelected, setIsMultiSelected] = useState(false)
    const [error, setError] = useState(null)
    const [creators, setCreators] = useState([])
    const [showFilter, setShowFilter] = useState(false)
    const [showLocationFilter, setShowLocationFilter] = useState(false)
    const [showAgeFilter, setShowAgeFilter] = useState(false)

    // Track if we're updating savedFilters from search results to prevent infinite loops
    const [isUpdatingFromSearch, setIsUpdatingFromSearch] = useState(false)

    const [sortConfig, setSortConfig] = useState({ key: '', direction: 'asc' });
    const [popupVisibleId, setPopupVisibleId] = useState(null);
    const [campaignPopupVisibleId, setCampaignPopupVisibleId] = useState(null);

    const [groupPopupVisibleId, setGroupPopupVisibleId] = useState(null);

    useEffect(() => {
        const runEffect = async () => {
            setMyList([]);
            setIsLoading(true);

            await getFiltersStructure();
            await getCreatorList();

        };
        runEffect();

    }, [dispatch, selectedBrand]);

    const getCreatorList = async () => {
        try {
            const result = await dispatch(getCreatorListsThunk({ brand_id: selectedBrand.id }));
            if (result.meta.requestStatus === 'fulfilled') {
                // Get the response data from the thunk
                const responseData = result.payload;

                if (responseData.success) {
                    console.log('CreatorList :', responseData.data.lists);

                    const transformed = (responseData.data.lists || []).map((creatorList) => ({
                        id: creatorList.id,
                        name: creatorList.name,
                        description: creatorList.description,
                        isSelected: false,
                    }));

                    if (transformed.length > 0) {
                        setMyList(transformed);
                        console.log('CreatorList :', transformed);
                        return;
                    } else {

                        // showSnackbar('No Creator filters List found', 'info');
                    }
                }
                else {
                    showSnackbar(responseData.message, 'info');
                }
            } else {
                // Handle failure case
                const errorMessage = result.payload.message || 'Get global fliter request failed. Please try again.';
                showSnackbar(errorMessage, 'error');
            }
        } catch (error) {
            console.error('Get global fliter request error:', error);
            showSnackbar(error.message || 'Failed to request Get global fliter.', 'error');
        } finally {
            setIsLoading(false);
        }
    }

    const getFiltersStructure = async () => {
        try {
            const result = await getFilterMetadata();
            if (result.meta.requestStatus === 'fulfilled') {
                // Get the response data from the thunk
                const responseData = result.payload;

                console.log('Filter metadata:', responseData);

                setFilterOptions(responseData.data);

                // const transformed = (responseData.all_filters || []).map((filter, index) => ({

                // }));

                // if (transformed.length > 0) {
                //     localStorage.setItem('globalFilters', JSON.stringify(responseData.all_filters));

                //     return;
                // } else {

                //     showSnackbar('No global filters found', 'info');
                // }
            } else {
                // Handle failure case
                const errorMessage = result.payload.message || 'Get global fliter request failed. Please try again.';
                showSnackbar(errorMessage, 'error');
            }
        } catch (error) {
            console.error('Get global fliter request error:', error);
            showSnackbar(error.message || 'Failed to request Get global fliter.', 'error');
        } finally {
            setIsLoading(false);
        }
    }

    const [campaigns, setCampaigns] = useState([
        {
            id: 1,
            name: "Spring Collection",
            description: "Promote the new spring collection with influencers.",
            isSelected: false
        },
        // {
        //     id: 2,
        //     name: "Summer Campaign",
        //     description: "Get ready for the summer with our latest campaign.",
        //     isSelected: false
        // },
        // {
        //     id: 3,
        //     name: "Sales Campaign",
        //     description: "Don't miss our exclusive sales campaign.",
        //     isSelected: false
        // },
        // {
        //     id: 4,
        //     name: "Trending",
        //     description: "Join the trending campaign and boost your visibility.",
        //     isSelected: false
        // }
    ]);

    const [myList, setMyList] = useState([]);


    const handleCreateList = async (name) => {
        const newList = {
            name: name,
            description: "",
            brand_id: selectedBrand.id,
        };

        console.log('Creating campaign:', newList);

        try {
            const result = await dispatch(createCreatorListThunk(newList));
            if (result.meta.requestStatus === 'fulfilled') {
                // Get the response data from the thunk
                const responseData = result.payload.data;

                newList.id = responseData.id;
                newList.isSelected = true;

                console.log('Create campaign response:', responseData);


                console.log('Creating campaign:', newList);
                setMyList((prev) => [...prev, newList]);
            } else {
                // Handle failure case
                const errorMessage = result.payload.message || 'Get global fliter request failed. Please try again.';
                showSnackbar(errorMessage, 'error');
            }
        } catch (error) {
            console.error('Get global fliter request error:', error);
            showSnackbar(error.message || 'Failed to request Get global fliter.', 'error');
        } finally {
            setIsLoading(false);
        }
    };

    const handleAddCreatorToList = async (selectedList, row) => {
        const listId = selectedList[0]?.id;

        console.log('Adding creator to List:', row);

        try {
            const result = await dispatch(addMemberToListThunk({
                listId: listId,
                brandId: selectedBrand.id,
                profileIds: [row.id]
            }));
            if (result.meta.requestStatus === 'fulfilled') {
                // Get the response data from the thunk
                const responseData = result.payload;

                if (responseData.success) {
                    showSnackbar('Creator added to list successfully', 'success');
                }
                else {
                    showSnackbar(responseData.message, 'error');
                }

            } else {
                // Handle failure case
                const errorMessage = result.payload.message || 'Adding creator to List: request failed. Please try again.';
                showSnackbar(errorMessage, 'error');
            }
        } catch (error) {
            console.error('Adding creator to List: request error:', error);
            showSnackbar(error.message || 'Failed to request Adding creator to List:', 'error');
        } finally {
            setIsLoading(false);
        }
    };

    const columns = [
        {
            key: 'name',
            header: 'Name',
            width: '160px',
            render: (row) => {
                // Handle both API response and mock data structures
                const name = row.full_name || row.name || 'Unknown';
                const username = row.platform_username || row.userName || '';
                const avatar = row.image_url || row.avatar || "https://images.unsplash.com/photo-1494790108377-be9c29b29330?w=150&h=150&fit=crop&crop=faces";
                const verified = row.is_verified || row.verified || false;

                return (
                    <div className="flex items-center gap-3">
                        <img src={avatar} className="w-10 h-10 rounded-full" alt={name} />
                        <div className="flex flex-col gap-1">
                            <div className="flex items-center gap-1">
                                <span className="text-14-semibold text-gray-50">{name}</span>
                                {verified && <img src={VerifiedIcon} className="h-3.5 w-3.5" alt="Verified" />}
                            </div>
                            <span className="text-12-regular text-gray-400">@{username}</span>
                        </div>
                    </div>
                );
            }
        },
        {
            key: 'creatorverseScore',
            header: 'CV Score',
            sortable: true,
            width: '60px',
            tooltip: 'A combined metric of engagement quality, audience authenticity, and content performance',
            tooltipPosition: 'top',
            render: (row) => {
                // API response might not have creatorverseScore, use a default or calculate from available data
                const score = row.creatorverseScore || row.creator_score || Math.floor(Math.random() * 100); // Temporary fallback
                return renderCreatorverseScore(score);
            },
        },
        {
            key: 'followers',
            header: 'Followers',
            sortable: true,
            width: '60px',
            render: (row) => {
                const followers = row.follower_count || row.followers || 0;
                return <span className="text-14-regular text-gray-50">{formatNumber(followers)}</span>;
            },
        },
        {
            key: 'engagementRate',
            header: 'Eng. Rate',
            sortable: true,
            width: '100px',
            tooltip: 'Avg engagement (likes, comments, shares) divided by follower count',
            tooltipPosition: 'top',
            render: (row) => {
                const engagementRate = row.engagement_rate || row.engagementRate || 0;
                const formattedRate = Number(engagementRate).toFixed(2); // Ensures 2 decimal places
                return <span className={`text-14-regular text-white`}>{formattedRate} %</span>;
            },
        },
        {
            key: 'categories',
            header: 'Categories',
            width: '230px',
            render: (row) => {
                const categories = row.categories || {};
                const primaryCategories = (categories.primary || []).slice(0, 3);     // ✅ limit to 3
                const secondaryCategories = (categories.secondary || []).slice(0, 2); // ✅ limit to 2

                if (primaryCategories.length === 0 && secondaryCategories.length === 0) {
                    return <span className="text-gray-400 text-sm">No categories</span>;
                }

                return (
                    <div className='flex flex-col gap-1'>
                        {primaryCategories.length > 0 && (
                            <div className="flex flex-wrap gap-1">
                                {primaryCategories.map((cat, idx) => (
                                    <div key={`cat-${idx}`} className="flex items-center gap-2 mb-2">
                                        <i className={cat.iconClass}></i>
                                        <span
                                            className="text-sky-blue rounded-full px-2 py-1 h-6 text-14-regular border border-gray-400"
                                        >
                                            {cat}
                                        </span>
                                    </div>
                                ))}
                            </div>
                        )}
                        {secondaryCategories.length > 0 && (
                            <div className="flex flex-wrap gap-1">
                                {secondaryCategories.map((cat, idx) => (
                                    <div key={`second-${idx}`} className="flex items-center gap-2 mb-2">
                                        <i className={cat.iconClass}></i>
                                        <span
                                            className="text-sky-blue rounded-full px-2 py-1 h-6 text-14-regular border border-gray-400"
                                        >
                                            {cat}
                                        </span>
                                    </div>
                                ))}

                            </div>
                        )}
                    </div>
                );
            }
        },
        {
            key: 'pastCollaborations',
            header: 'Past Collaborations',
            width: '60px',
            render: (row) => {
                // Handle null/undefined pastCollaborations
                const collaborations = row.pastCollaborations || [];

                if (collaborations.length === 0) {
                    return <span className="text-gray-400 text-sm">No collaborations</span>;
                }

                return (
                    <AvatarStack
                        avatars={collaborations}
                        maxAvatars={3}
                        count={Math.max(0, collaborations.length - 3)}
                        className="ml-1"
                    />
                );
            }
        },
        {
            key: 'actions',
            header: () => <span className="sr-only">Actions</span>,
            width: '30px',
            render: (row) => (
                <div className="w-full h-full group">
                    <div className="relative w-full h-full">
                        <div className="-ml-10  transition-opacity duration-200 flex items-center gap-3 absolute top-1/2 left-0 transform -translate-y-1/2">
                            {/* <img
                                src={SendIcon}
                                alt="Move Down"
                                className="cursor-pointer hover:scale-110 transition"
                                onClick={() => {
                                    setCampaignPopupVisibleId((prev) => {
                                        const newValue = prev === null ? row.id : row.id;
                                        return newValue;
                                    });
                                }}
                            /> */}
                            <i
                                className={`fi ${row.savedList ? 'fi-sr-megaphone text-gray-50' : 'fi-rr-megaphone text-gray-300'} cursor-pointer hover:scale-110 hover:text-gray-50 transition-all`}
                                onClick={(e) => {
                                    e.stopPropagation();
                                    setCampaignPopupVisibleId((prev) => {
                                        const newValue = prev === null ? row.id : row.id;
                                        return newValue;
                                    });
                                }}
                            ></i>
                            <i
                                className={` fi ${row.campaignList ? 'fi-sr-bookmark text-gray-50' : 'fi-rr-bookmark text-gray-300'} cursor-pointer  hover:scale-110 hover:text-gray-50 transition-all`}
                                onClick={(e) => {
                                    e.stopPropagation();
                                    setPopupVisibleId((prev) => {
                                        const newValue = prev === null ? row.id : row.id;
                                        return newValue;
                                    });
                                }}
                            ></i>
                            {/* <img
                                src={BookmarkIcon}
                                alt="Bookmark"
                                className="cursor-pointer hover:scale-110 transition"
                                onClick={() => {
                                    setPopupVisibleId((prev) => {
                                        const newValue = prev === null ? row.id : row.id;
                                        return newValue;
                                    });
                                }}
                            /> */}
                        </div>

                        {/* Controlled MySaved popup visibility */}
                        {popupVisibleId === row.id && (
                            <CampaignPopup
                                campaigns={myList}
                                popupTitle="My Saved Lists"
                                setCampaigns={setMyList}
                                addNewEnabled={true}
                                onClose={() => setPopupVisibleId(null)}
                                placeholder="List name"
                                onCreateCampaign={handleCreateList}
                                onSave={(selectedLists) => handleAddCreatorToList(selectedLists, row)}
                            />
                        )}
                        {/* Controlled AddToCampaignpopup visibility */}
                        {campaignPopupVisibleId === row.id && (
                            <CampaignPopup
                                campaigns={campaigns}
                                popupTitle="Add to Campaign"
                                setCampaigns={setCampaigns}
                                onClose={() => setCampaignPopupVisibleId(null)}
                                placeholder="Campaign name"
                            />
                        )}
                    </div>
                </div>
            )
        }
    ];


    // Creator category cards data
    // const creatorCategories = [
    //     {
    //         title: "Nano Creators",
    //         description: "1k-10k followers, hyper-niche engagement engagement engagement",
    //         gradient: "linear-gradient(110deg, #D7FBF6 1.65%, #ACE7DE 96.8%)",
    //         avatars: [sampleAvatars.avatar1, sampleAvatars.avatar2, sampleAvatars.avatar3, sampleAvatars.avatar4],
    //         count: 20,
    //         platform: "instagram",
    //         Icon: 1
    //     },
    //     {
    //         title: "Top creators in Tier 2",
    //         description: "200k+, mass appeal",
    //         gradient: "linear-gradient(109deg, #FFF6F6 2.59%, #FBCFCF 96.84%)",
    //         avatars: [sampleAvatars.avatar4, sampleAvatars.avatar5, sampleAvatars.avatar1, sampleAvatars.avatar2],
    //         count: 72,
    //         platform: "instagram",
    //         Icon: 2
    //     },
    //     {
    //         title: "Micro Creators",
    //         description: "10k-50k, often highly trusted by their audience",
    //         gradient: "linear-gradient(110deg, #F7F0FF 3.42%, #D4BBEE 98.53%)",
    //         avatars: [sampleAvatars.avatar2, sampleAvatars.avatar3, sampleAvatars.avatar4, sampleAvatars.avatar5],
    //         count: 99,
    //         platform: "instagram",
    //         Icon: 3
    //     },
    //     {
    //         title: "Top creators in Tier 1",
    //         description: "50k-200k, with polished content",
    //         gradient: "linear-gradient(110deg, #E2FDFD 5.14%, #BEEEF9 96.47%)",
    //         avatars: [sampleAvatars.avatar3, sampleAvatars.avatar4, sampleAvatars.avatar5, sampleAvatars.avatar1],
    //         count: 72,
    //         platform: "instagram",
    //         Icon: 4
    //     },
    //     {
    //         title: "Top Engagement Creators",
    //         description: "1k-10k followers, hyper-niche engagement",
    //         gradient: "linear-gradient(109deg, #FEFCEA 2.85%, #F5EDAA 98.4%)",
    //         avatars: [sampleAvatars.avatar5, sampleAvatars.avatar1, sampleAvatars.avatar2, sampleAvatars.avatar3],
    //         count: 20,
    //         platform: "youtube",
    //         Icon: 5
    //     },
    //     {
    //         title: "Top Fashion Creators",
    //         description: "10k-50k, often highly trusted by their audience",
    //         gradient: "linear-gradient(110deg, #EDEAFF 1.87%, #C0B9EC 100%)",
    //         avatars: [sampleAvatars.avatar1, sampleAvatars.avatar3, sampleAvatars.avatar5, sampleAvatars.avatar2],
    //         count: 99,
    //         platform: "youtube",
    //         Icon: 6
    //     },
    //     {
    //         title: "Top Travel Creators",
    //         description: "50k-200k, with polished content",
    //         gradient: "linear-gradient(109deg, #E8F6FF 1.16%, #8BC1E4 97.37%)",
    //         avatars: [sampleAvatars.avatar2, sampleAvatars.avatar4, sampleAvatars.avatar1, sampleAvatars.avatar3],
    //         count: 72,
    //         platform: "youtube",
    //         Icon: 7
    //     },
    //     {
    //         title: "Top Tech Creators",
    //         description: "200k+, mass appeal",
    //         gradient: "linear-gradient(109deg, #FFF3D8 0.85%, #E6D2A4 97.47%)",
    //         avatars: [sampleAvatars.avatar3, sampleAvatars.avatar5, sampleAvatars.avatar2, sampleAvatars.avatar4],
    //         count: 72,
    //         platform: "youtube",
    //         Icon: 8
    //     }
    // ];
    const getRandomAvatars = (count = 4) => {
        const avatarValues = Object.values(sampleAvatars);
        const shuffled = avatarValues.sort(() => 0.5 - Math.random());
        return shuffled.slice(0, count);
    };

    const gradientIconCombos = [
        { gradient: "linear-gradient(110deg, #D7FBF6 1.65%, #ACE7DE 96.8%)", Icon: 1 },
        { gradient: "linear-gradient(109deg, #FFF6F6 2.59%, #FBCFCF 96.84%)", Icon: 2 },
        { gradient: "linear-gradient(110deg, #F7F0FF 3.42%, #D4BBEE 98.53%)", Icon: 3 },
        { gradient: "linear-gradient(110deg, #E2FDFD 5.14%, #BEEEF9 96.47%)", Icon: 4 },
        { gradient: "linear-gradient(109deg, #FEFCEA 2.85%, #F5EDAA 98.4%)", Icon: 5 },
        { gradient: "linear-gradient(110deg, #EDEAFF 1.87%, #C0B9EC 100%)", Icon: 6 },
        { gradient: "linear-gradient(109deg, #E8F6FF 1.16%, #8BC1E4 97.37%)", Icon: 7 },
        { gradient: "linear-gradient(109deg, #FFF3D8 0.85%, #E6D2A4 97.47%)", Icon: 8 }
    ];

    // const shuffleArray = (arr) => [...arr].sort(() => 0.5 - Math.random());

    const creatorCategories = useSelector(selectGlobalFilters);
    const globalFilters = (creatorCategories || []).map((filter, index) => ({
        id: filter.id,
        title: filter.title,
        description: filter.description,
        platform: filter.platform,
        filterData: filter.filters,
        avatars: getRandomAvatars(),
        count: filter?.count || Math.floor(Math.random() * (99 - 50 + 1)) + 50,
        ...gradientIconCombos[index % gradientIconCombos.length]
    }));


    const currentFilterSet = useSelector(selectCurrentFilterSet);
    console.log('Current filter set:', currentFilterSet);

    const [savedFilters, setSavedFilters] = useState(
        selectedCategory?.filterData || currentFilterSet || []
    );

    const [filterOptions, setFilterOptions] = useState([]);

    const filterWrapperRef = useRef(null);
    const tagRefs = useRef([]);
    const [maxVisibleTags, setMaxVisibleTags] = useState(3);

    useEffect(() => {
        if (!filterWrapperRef.current) return;

        const calculateVisibleTags = () => {
            if (!filterWrapperRef.current || tagRefs.current.length === 0) return;

            const containerWidth = (filterWrapperRef.current.offsetWidth) - 80;

            const tagWidths = tagRefs.current
                .map((tag) => tag?.offsetWidth || 0)
                .filter((w) => w > 0);

            const averageWidth =
                tagWidths.length > 0
                    ? tagWidths.reduce((sum, w) => sum + w, 0) / tagWidths.length + 12 // include gap
                    : 160;

            const visibleCount = Math.floor(containerWidth / averageWidth);
            setMaxVisibleTags(visibleCount > 0 ? visibleCount : 1);
        };

        // Initial calculation
        calculateVisibleTags();

        // Resize observer for more accurate detection
        const resizeObserver = new ResizeObserver(calculateVisibleTags);
        resizeObserver.observe(filterWrapperRef.current);

        // Cleanup
        return () => resizeObserver.disconnect();
    }, [savedFilters, activePlatform]);

    useEffect(() => {
        if (savedFilters.length > 0) {
            setIsTableVisible(true);
        }
    }, [savedFilters]);

    // Handle search when savedFilters change (but not when updating from search results)
    useEffect(() => {
        // Only trigger search if:
        // 1. We have saved filters
        // 2. We're not currently updating from search results (to prevent infinite loops)
        // 3. We have a search query or existing filters to search with
        if (savedFilters.length >= 0 && !isUpdatingFromSearch && (searchQuery || savedFilters.length >= 0)) {
            getSearchData(searchQuery, savedFilters);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [savedFilters])

    const getSearchData = async (query, filters = savedFilters) => {
        setIsLoading(true);
        setError(null);
        if (query.length > 0) {
            setSavedFilters([]);
            filters = [];
        }
        try {
            const searchParams = {
                searchQuery: query || searchQuery,
                filters: filters,
                limit: 20,
                offset: 0
            };
            const result = await searchCreators(searchParams);
            if (result.meta.requestStatus === 'fulfilled') {
                const responseData = result.payload;
                if (responseData.success && responseData.data) {
                    console.log('CreatorList :', responseData.data);

                    // Transform API response data to expected table format
                    if (responseData.data.profiles) {
                        const transformedProfiles = responseData.data.profiles.map((profile) => ({
                            // Use profile_id if available, otherwise generate from index
                            id: profile.id,
                            // Name mapping
                            name: profile.full_name || profile.name || 'Unknown Creator',
                            userName: profile.user_name,

                            // Image mapping
                            avatar: profile.avatar_url || profile.avatar || 'https://via.placeholder.com/40x40/374151/9CA3AF?text=?',

                            // Basic info (may not be in API response, using defaults)
                            age: profile.age || null,

                            // Metrics mapping
                            creatorverseScore: profile.creatorverse_score || Math.floor(Math.random() * 100),
                            followers: profile.followers || 0,
                            engagementRate: profile.engagement_rate || 0,

                            // Verification status
                            verified: profile.verified || false,

                            // Platform (may need to derive from URL or other fields)
                            platform: profile.platform || activePlatform,

                            // Categories (API may not provide this, using defaults)
                            categories: profile.categories || {
                                primary: [],
                                secondary: []
                            },

                            // Past collaborations (API may not provide this, using sample data)
                            pastCollaborations: profile.pastCollaborations || [
                                sampleAvatars.avatar1,
                                sampleAvatars.avatar2,
                                sampleAvatars.avatar3
                            ]
                        }));

                        console.log('Transformed profiles:', transformedProfiles);
                        setCreators(transformedProfiles);
                    }

                    // If there are new filters from the search response, update savedFilters
                    if (searchQuery !== "" && responseData.data.filters && Array.isArray(responseData.data.filters)) {
                        setIsUpdatingFromSearch(true);
                        setSavedFilters(prevFilters => {
                            // Merge new filters with existing ones, avoiding duplicates
                            const newFilters = responseData.data.filters.filter(newFilter =>
                                !prevFilters.some(existingFilter =>
                                    existingFilter.filter === newFilter.filter &&
                                    existingFilter.channel === newFilter.channel &&
                                    existingFilter.filterFor === newFilter.filterFor &&
                                    existingFilter.value === newFilter.value
                                )
                            );
                            return [...prevFilters, ...newFilters];
                        });
                        // Reset the flag after a brief delay to allow state update
                        setTimeout(() => setIsUpdatingFromSearch(false), 100);
                    }

                    // Show table if we have results
                    // if (responseData.data.profiles && responseData.data.profiles.length > 0) {
                    //    setIsTableVisible(true);
                    // }
                } else if (result.payload?.status === 410) {
                    // Handle 410 status - show suggestions message but don't show as error
                    const message = result.payload?.message || 'Query not related to influencer search. Please try the suggestions below.';
                    showSnackbar(message, 'info');
                    setCreators([]);
                    setError(null); // Don't set as error since suggestions are available
                } else {
                    showSnackbar(responseData.message || 'No creators found', 'info');
                    setCreators([]);
                }
            } else {
                // Check if this is a 410 status (suggestions available)
                if (result.payload?.status === 410) {
                    // Handle 410 status - show suggestions message but don't show as error
                    const message = result.payload?.message || 'Query not related to influencer search. Please try the suggestions below.';
                    showSnackbar(message, 'info');
                    setCreators([]);
                    setError(null); // Don't set as error since suggestions are available
                } else if (result.payload?.status === 408) {
                    // Handle 410 status - show suggestions message but don't show as error
                    const message = result.payload?.message || 'API Request Timeout. Please try again with filter.';
                    showSnackbar(message, 'info');
                    setCreators([]);
                    setSearchQuery("")
                    setError(null); // Don't set as error since suggestions are available
                }
                else {
                    // Handle other error cases
                    const errorMessage = result.payload?.message || 'Creator search failed. Please try again.';
                    showSnackbar(errorMessage, 'error');
                    setError(errorMessage);
                    setCreators([]);
                }
            }
        } catch (error) {
            console.error('Creator search error:', error);
            const errorMessage = error.message || 'Failed to search creators. Please try again.';
            showSnackbar(errorMessage, 'error');
            setError(errorMessage);
            setCreators([]);
        } finally {
            setIsLoading(false);
        }
    };

    // Handle retry on error
    const handleRetry = () => {
        // Re-fetch creators using the current search query and filters
        setCreators([])
        setError(null)
        getSearchData(searchQuery, savedFilters);
    }

    // Since API data is final and already filtered, just pass it through to the table
    // The API handles filtering based on search query and filters
    const filteredCreators = creators;

    // Sort creators - improved implementation with useMemo for better performance
    const sortedCreators = useMemo(() => {
        if (!sortConfig.key) return filteredCreators;

        return [...filteredCreators].sort((a, b) => {
            let aValue, bValue;

            // Handle different field names for API response vs mock data
            if (sortConfig.key === 'followers') {
                aValue = a.follower_count || a.followers || 0;
                bValue = b.follower_count || b.followers || 0;
                aValue = parseFloat(aValue.toString().replace(/[^\d.]/g, ''));
                bValue = parseFloat(bValue.toString().replace(/[^\d.]/g, ''));
            } else if (sortConfig.key === 'engagementRate') {
                aValue = a.engagement_rate || a.engagementRate || 0;
                bValue = b.engagement_rate || b.engagementRate || 0;
                aValue = parseFloat(aValue.toString().replace('%', ''));
                bValue = parseFloat(bValue.toString().replace('%', ''));
            } else if (sortConfig.key === 'creatorverseScore') {
                aValue = a.creatorverseScore || a.creator_score || 0;
                bValue = b.creatorverseScore || b.creator_score || 0;
                aValue = parseFloat(aValue);
                bValue = parseFloat(bValue);
            } else if (sortConfig.key === 'name') {
                aValue = a.full_name || a.name || '';
                bValue = b.full_name || b.name || '';
            } else {
                // Default fallback for other fields
                aValue = a[sortConfig.key] || '';
                bValue = b[sortConfig.key] || '';
            }

            // Handle string comparison
            if (typeof aValue === 'string') {
                return sortConfig.direction === 'asc'
                    ? aValue.localeCompare(bValue)
                    : bValue.localeCompare(aValue);
            }

            // Handle numeric comparison
            if (aValue < bValue) return sortConfig.direction === 'asc' ? -1 : 1;
            if (aValue > bValue) return sortConfig.direction === 'asc' ? 1 : -1;
            return 0;
        });
    }, [filteredCreators, sortConfig]);

    const renderCreatorverseScore = (score) => {
        const getColor = () => {
            if (score >= 80) return 'text-green-2';
            if (score >= 50) return 'text-sky-blue';
            if (score >= 20) return 'text-orange';
            if (score >= 0) return 'text-red-2';
            return 'bg-red-2';
        };
        return (
            <div className={`flex items-center justify-center rounded-md py-1 w-9 gap-1 ${getColor()}`}>
                <span className="text-14-semibold">{score}</span>
                <RankIcon type="star" percentage={score} fillDirection="bt" fillColor="var(--color-yellow)" />

                {/* <img src={getIcon()} alt="" /> */}
            </div>
        );
    };

    const formatNumber = (num) => {
        if (num >= 1_000_000) {
            return (num / 1_000_000).toFixed(num % 1_000_000 === 0 ? 0 : 1) + 'M';
        } else if (num >= 1_000) {
            return (num / 1_000).toFixed(num % 1_000 === 0 ? 0 : 1) + 'K';
        }
        return num.toString();
    };

    // Pagination    
    const itemsPerPage = 10
    const totalPages = Math.ceil(sortedCreators.length / itemsPerPage)
    const startIndex = (currentPage - 1) * itemsPerPage
    const paginatedCreators = sortedCreators.slice(startIndex, startIndex + itemsPerPage)

    const handleSort = (key) => {
        setSortConfig(prevConfig => ({
            key,
            direction: prevConfig.key === key && prevConfig.direction === 'asc' ? 'desc' : 'asc'
        }))
    }

    const removeFilter = (removeIndex) => {
        console.log('Removing filter at index:', removeIndex);
        setSavedFilters((prevFilters) =>
            prevFilters.filter((_, index) => index !== removeIndex)
        );
    };

    const addToAppliedFilters = (filter, filterFor) => {

        if (filter === null || filter === undefined || filter === '') {
            return;
        }

        const enhancedFilter = {
            channel: activePlatform,
            ...filter,
            filterFor: filterFor,
        };

        //console.log('Adding attribute filter:', enhancedFilter);

        setSavedFilters((prevFilters) => {
            const exists = prevFilters.some((f) =>
                f.filter === enhancedFilter.filter &&
                f.channel === enhancedFilter.channel &&
                f.filterFor === enhancedFilter.filterFor
            );

            let updatedFilters;

            if (exists) {
                // Replace the existing filter instead of keeping it
                updatedFilters = prevFilters.map(f =>
                    (f.filter === enhancedFilter.filter &&
                        f.channel === enhancedFilter.channel &&
                        f.filterFor === enhancedFilter.filterFor)
                        ? enhancedFilter
                        : f
                );
            } else {
                // Add the new filter
                updatedFilters = [...prevFilters, enhancedFilter];
            }

            return updatedFilters;
        });
    };
    // We don't need sortedData as we're already sorting in sortedCreators
    // Just use paginatedCreators directly in the table

    // useEffect(() => {
    //     console.log('Saved filters updated:', savedFilters);
    //     dispatch(setCurrentFilterSet(savedFilters));
    // }, [savedFilters, dispatch])



    //saved filters
    // 🔧 Add new filter (empty template)
    const handleCreate = (newName) => {
        const newFilter = {
            id: "74132",
            name: newName,
            createdDate: new Date().toLocaleDateString("en-GB", {
                day: "2-digit",
                month: "short",
                year: "2-digit",
            }),
            resultCount: 0,
            filters: [],
        };

        setSavedFilters((prev) => [newFilter, ...prev]);
        setIsCreating(false);
    };

    // 🗑️ Delete a saved filter
    const handleDelete = (index) => {
        const updated = [...savedFilters];
        updated.splice(index, 1);
        setSavedFilters(updated);
    };

    // ✏️ Update filter name
    const handleRename = (index, newName) => {
        const updated = [...savedFilters];
        updated[index].name = newName;
        setSavedFilters(updated);
    };

    // ✅ Click on a saved filter
    const handleSelect = (filterItem) => {
        console.log("Selected filter:", filterItem);
        // Optionally apply filters to UI
    };

    return (
        <div className="w-full flex flex-col gap-6 h-full bg-[#252525]/70">
            {/* Header */}

            <div className="flex items-center">
                <h1 className="text-24-bold text-gray-50">Discover</h1>
            </div>
            <div className='flex flex-col items-center'>
                <h2 className="text-20-bold text-gray-50">
                    Discover the <span className="text-yellow">right creators</span> for your brand
                </h2>
            </div>
            {/* Platform Toggle
            <AnimatePresence mode="wait" >
                {!isTableVisible && (
                    <motion.div
                        key="platform-toggle"
                        initial={{ opacity: 0, y: -50 }}
                        animate={{ opacity: 1.5, y: 0 }}
                        exit={{ opacity: 0, y: -50 }}
                        transition={{ duration: 0.40, ease: 'easeInOut' }}
                        className="flex justify-center"
                    >
                        <PlatformToggle
                            activePlatform={activePlatform}
                            onPlatformChange={setActivePlatform}
                            width="300"
                            height="50"
                        />
                    </motion.div>
                )}
            </AnimatePresence> */}


            {/* Search Bar */}
            <div className=' flex items-center justify-center gap-2'>
                <div className='cursor-pointer z-50 h-full'>
                    <PlatformSelector
                        value={activePlatform}
                        onChange={(newPlatform) => setActivePlatform(newPlatform)}
                        className='h-14'
                    />
                </div>
                <div className='flex justify-start items-start w-200'>
                    <SearchWithSuggestions
                        suggestionVisible={!isTableVisible}
                        onSearch={(query) => {
                            setSearchQuery(query);
                            getSearchData(query, savedFilters);
                        }}
                        searchQuery={searchQuery}
                        onSearchQueryChange={setSearchQuery}
                        externalSuggestions={searchSuggestions}
                    // onFocus={() => setIsTableVisible(true)}
                    />
                </div>
            </div>


            {/* Creator Table with Filters */}
            <div className="flex-1 relative w-full ">

                <AnimatePresence mode="wait">
                    {isTableVisible || savedFilters.length > 0 ? (
                        <motion.div
                            key="actions"
                            initial={{ y: 90, rotateX: 10, opacity: 0 }}
                            animate={{ y: 0, rotateX: 0, opacity: 1 }}
                            exit={{ y: -90, rotateX: -50, opacity: 0 }}
                            transition={{ duration: 0.7, ease: 'easeInOut' }}
                            className="flex items-center justify-center gap-4 rounded-lg text-white"
                        >
                            <div className="w-full flex flex-col gap-5">
                                {/* Filter Bar - Positioned right above table */}
                                <div ref={filterSectionRef} className="relative w-full min-h-[60px]">
                                    <AnimatePresence mode="wait">
                                        {!isMultiSelected ? (
                                            <motion.div
                                                key="actions"
                                                initial={{ y: 40, rotateX: 10, opacity: 0 }}
                                                animate={{ y: 0, rotateX: 0, opacity: 1 }}
                                                exit={{ y: -40, rotateX: -10, opacity: 0 }}
                                                transition={{ duration: 0.2, ease: 'easeIn' }}
                                                className="flex flex-col w-full items-center justify-center gap-4 rounded-lg text-white z-10 "
                                            >
                                                <div className='flex flex-col gap-3 w-full h-fit'>
                                                    <div className="flex justify-between w-full items-start gap-4 flex-wrap md:flex-nowrap">
                                                        {/* Left: Filter buttons */}
                                                        <div className="flex flex-shrink-0 gap-3 items-center">
                                                            {/* Filter Buttons */}
                                                            <button
                                                                className={`relative px-4 py-2.5 bg-transparent border border-gray-500 rounded-lg flex items-center gap-1.5 text-sm text-gray-50 hover:text-gray-300 transition-colors cursor-pointer`}
                                                                onClick={() => {
                                                                    setShowFilter(!showFilter);
                                                                    if (!showFilter && filterSectionRef.current) {
                                                                        filterSectionRef.current.scrollIntoView({ behavior: 'smooth', block: 'start' });
                                                                    }
                                                                    console.log('Filter button clicked');
                                                                }}
                                                                title="Filter"
                                                            >
                                                                <img src={SettingSliderIcon} alt="Filter" className="w-3.5 h-3.5" />
                                                                Filters
                                                                {savedFilters.filter(filter => filter.channel === activePlatform).length > 0 && (
                                                                    <span className="ml-2 bg-brand-200 text-brand-600 text-xs rounded-full w-5 h-5 flex items-center justify-center">
                                                                        {savedFilters.filter(filter => filter.channel === activePlatform).length}
                                                                    </span>
                                                                )}

                                                                {showFilter ? <Filter
                                                                    filterOptions={filterOptions}
                                                                    savedFilters={savedFilters}
                                                                    onClose={() => setShowFilter(false)}
                                                                    onApplyFilters={(filters) => {
                                                                        setSavedFilters(filters);
                                                                        console.log(filters);
                                                                        // Process your filters here
                                                                    }}
                                                                    selectedChannel={activePlatform}
                                                                /> : null}

                                                            </button>
                                                            {/* Location Filter Buttons */}
                                                            <button
                                                                className={`relative whitespace-nowrap px-4 py-2.5 bg-transparent border border-gray-500 rounded-lg flex items-center gap-1.5 text-sm text-gray-50 hover:text-gray-300 transition-colors cursor-pointer`}
                                                                onClick={() => {
                                                                    setShowLocationFilter(!showLocationFilter);
                                                                    //console.log('Location filter button clicked');
                                                                }}
                                                                title="Location Filter"
                                                            >
                                                                <img src={MapPinIcon} alt="Filter" className="w-3.5 h-3.5" />
                                                                Audience Location
                                                                {savedFilters.find(filter => filter.channel === activePlatform && filter.filterFor === 'audience' && filter.filter === 'Location')?.value?.reduce((acc, item) => {
                                                                    const count = item.value.split(',').filter(Boolean).length;
                                                                    return acc + count;
                                                                }, 0) > 0 && (
                                                                        <span className="ml-2 bg-brand-200 text-brand-600 text-xs rounded-full w-5 h-5 flex items-center justify-center">
                                                                            {savedFilters.find(filter => filter.channel === activePlatform && filter.filterFor === 'audience' && filter.filter === 'Location')?.value?.length}
                                                                        </span>
                                                                    )}
                                                                <svg
                                                                    className={`h-5 w-8 ml-2 text-gray-400 transform transition-transform ${!showLocationFilter ? '' : 'rotate-180'}`}
                                                                    fill="none"
                                                                    viewBox="0 0 24 24"
                                                                    stroke="currentColor"
                                                                >
                                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                                                                </svg>
                                                                {showLocationFilter ? <FilterComponent
                                                                    filter="Location"
                                                                    savedFilter={savedFilters.find(filter => filter.channel === activePlatform && filter.filterFor === 'audience' && filter.filter === 'Location')}
                                                                    filterConfig={filterOptions.find(option => option.optionFor === 'audience' && option.channel === activePlatform)?.filters.find(filter => filter.name.toLowerCase() === 'location')}
                                                                    onClose={() => setShowLocationFilter(false)}
                                                                    onApplyFilter={(filterData) => {
                                                                        if (filterData === null || filterData === undefined || filterData.value === '') {
                                                                            setShowLocationFilter(false);
                                                                            return;
                                                                        }
                                                                        addToAppliedFilters(filterData, 'audience');
                                                                        setShowLocationFilter(false);
                                                                    }}
                                                                /> : null}

                                                            </button>
                                                            {/* Age Filter Buttons */}
                                                            <button
                                                                className={`relative whitespace-nowrap px-4 py-2.5 bg-transparent border border-gray-500 rounded-lg flex items-center gap-1.5 text-sm text-gray-50 hover:text-gray-300 transition-colors cursor-pointer`}
                                                                onClick={() => {
                                                                    setShowAgeFilter(!showAgeFilter);
                                                                    //console.log('Age filter button clicked');
                                                                }}
                                                                title="Age Filters"
                                                            >
                                                                <BsCalendarDate />
                                                                Audience Age
                                                                {savedFilters.find(filter => filter.channel === activePlatform && filter.filterFor === 'audience' && filter.filter === 'age')?.value?.split(',').filter(Boolean).length > 0 && (
                                                                    <span className="ml-2 bg-brand-200 text-brand-600 text-xs rounded-full w-5 h-5 flex items-center justify-center">
                                                                        {savedFilters.find(filter => filter.channel === activePlatform && filter.filterFor === 'audience' && filter.filter === 'age')?.value?.split(',').filter(Boolean).length}
                                                                    </span>
                                                                )}
                                                                <svg
                                                                    className={`h-5 w-8 ml-2 text-gray-400 transform transition-transform ${!showAgeFilter ? '' : 'rotate-180'}`}
                                                                    fill="none"
                                                                    viewBox="0 0 24 24"
                                                                    stroke="currentColor"
                                                                >
                                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                                                                </svg>
                                                                {showAgeFilter ? <FilterComponent
                                                                    filter="age"
                                                                    savedFilter={savedFilters.find(filter => filter.channel === activePlatform && filter.filterFor === 'audience' && filter.filter === 'age')}
                                                                    filterConfig={filterOptions.find(option => option.optionFor === 'audience' && option.channel === activePlatform)?.filters.find(filter => filter.name.toLowerCase() === 'age')}
                                                                    onClose={() => setShowAgeFilter(false)}
                                                                    onApplyFilter={(filterData) => {
                                                                        if (filterData === null || filterData === undefined || filterData.value === '') {
                                                                            setShowLocationFilter(false);
                                                                            return;
                                                                        }
                                                                        addToAppliedFilters(filterData, 'audience');
                                                                        setShowAgeFilter(false);
                                                                    }}
                                                                /> : null}

                                                            </button>
                                                            {/* Clear All */}
                                                            {savedFilters.some(f => f.channel === activePlatform) && (
                                                                <div
                                                                    className="flex-shrink-0 flex gap-1 whitespace-nowrap text-brand-500 text-14-medium cursor-pointer hover:text-brand-600 hover:underline ml-2"
                                                                    onClick={() => setSavedFilters([])}
                                                                >
                                                                    <img src={BlueCrossIcon} alt="" />
                                                                    Clear all
                                                                </div>
                                                            )}
                                                        </div>

                                                        {/* Right: Action buttons */}
                                                        <div className="flex flex-shrink-0 gap-3 justify-end items-center">
                                                            {savedFilters.some(f => f.channel === activePlatform) && (
                                                                <button
                                                                    className="px-4 py-2.5 bg-transparent border border-gray-500 rounded-lg flex items-center gap-1.5 text-sm text-gray-50 hover:text-gray-300 whitespace-nowrap"
                                                                >
                                                                    Save as quick filter
                                                                </button>
                                                            )}
                                                            <button
                                                                onClick={() => setIsFilterTabOpen(true)}
                                                                className="relative px-4 w-12 py-2.5 bg-transparent border border-gray-500 rounded-lg flex items-center justify-center text-sm text-gray-50 hover:text-gray-300"
                                                            >
                                                                <img src={FilterIcon} alt="Filter" className="w-3.5 h-4.5" />
                                                            </button>
                                                            {/* Tab Panel */}
                                                            {isFilterTabOpen && (
                                                                <SavedFiltersPanel
                                                                    isOpen={isFilterTabOpen}
                                                                    onClose={() => setIsFilterTabOpen(false)}
                                                                    initialData={savedFiltersData}
                                                                />
                                                            )}
                                                        </div>
                                                    </div>
                                                    <div className="flex w-full items-start gap-4 flex-wrap md:flex-nowrap">
                                                        {/* {renderAppliedFiltersPills(savedFilters.filter(f => f.channel === activePlatform))} */}
                                                        {/* <AppliedFiltersPills filters={savedFilters.filter(f => f.channel === activePlatform)} /> */}
                                                        <AppliedFiltersPills
                                                            filters={savedFilters
                                                                .filter(f => f.channel === activePlatform)
                                                                .map((f, i) => ({ ...f, index: i }))}
                                                            removeFilter={removeFilter}
                                                        />
                                                    </div>
                                                </div>
                                            </motion.div>
                                        ) : (
                                            <motion.div
                                                key="filters"
                                                initial={{ y: 40, rotateX: 10, opacity: 0 }}
                                                animate={{ y: 0, rotateX: 0, opacity: 1 }}
                                                exit={{ y: -40, rotateX: -10, opacity: 0 }}
                                                transition={{ duration: 0.2, ease: 'easeInOut' }}
                                                className="flex items-center justify-center gap-4 rounded-lg text-white"
                                            >
                                                <div className="flex w-full justify-between items-start gap-4 flex-wrap md:flex-nowrap">
                                                    <div className="flex flex-shrink-0 gap-3 items-center">
                                                        {/* Add to Campaign */}
                                                        <button
                                                            className={`relative px-4 py-2.5 bg-transparent border border-gray-500 rounded-lg flex items-center gap-1.5 text-sm text-gray-50 hover:text-gray-300 transition-colors cursor-pointer`}
                                                            onClick={() => {
                                                                setGroupPopupVisibleId('campaign');
                                                                console.log('Popup clicked');
                                                            }}
                                                            title="Toggle Filters"
                                                        >
                                                            <img src={SendIcon} alt="Filter" className="w-3.5 h-3.5" />
                                                            Add to campaign

                                                            {groupPopupVisibleId === 'campaign' ? <CampaignPopup
                                                                campaigns={myList}
                                                                setCampaigns={setMyList}
                                                                onClose={() => setGroupPopupVisibleId(null)}
                                                                placeholder="List name"
                                                                position='left-0 top-11'

                                                            /> : null}

                                                        </button>
                                                        {/* Add to List */}
                                                        <button
                                                            className={`relative px-4 py-2.5 bg-transparent border border-gray-500 rounded-lg flex items-center gap-1.5 text-sm text-gray-50 hover:text-gray-300 transition-colors cursor-pointer`}
                                                            onClick={() => {
                                                                setGroupPopupVisibleId('list');
                                                                console.log('Popup clicked');
                                                            }}
                                                            title="Toggle Filters"
                                                        >
                                                            <img src={BookmarkIcon} alt="Filter" className="w-3.5 h-4.5" />
                                                            Add to List

                                                            {groupPopupVisibleId === 'list' ? <CampaignPopup
                                                                campaigns={campaigns}
                                                                setCampaigns={setCampaigns}
                                                                onClose={() => setGroupPopupVisibleId(null)}
                                                                placeholder="List name"
                                                                position='left-0 top-11'
                                                            /> : null}

                                                        </button>
                                                    </div>
                                                </div>
                                            </motion.div>
                                        )}
                                    </AnimatePresence>
                                </div>

                                {/* Creator Table */}
                                {error ? (
                                    <ErrorState message={error} onRetry={handleRetry} />
                                ) : (
                                    <>
                                        <DataTable
                                            data={paginatedCreators}
                                            columns={columns}
                                            sortConfig={sortConfig}
                                            onSort={handleSort}
                                            onRowMouseLeave={() => {
                                                setPopupVisibleId(null);
                                                setCampaignPopupVisibleId(null);
                                            }}
                                            headerRowClass="border-brand-500 rounded-t-lg"
                                            onSelectionChange={(selectedRows) => {
                                                if (selectedRows.length > 1) {
                                                    setIsMultiSelected(true);
                                                } else {
                                                    setIsMultiSelected(false);
                                                }
                                                console.log('Selected rows:', selectedRows);
                                                // Show sliding action panel here based on selectedRows.length
                                            }}
                                            onRowClick={(row) => {
                                                console.log('Row clicked:', row);
                                                navigate(`/brand/discovery/${row.id}`);
                                            }}
                                        />

                                        {sortedCreators.length > 0 && (
                                            <div className="border-t border-gray-800 py-3 px-4 flex flex-col items-center space-y-2">
                                                {totalPages > 1 && (
                                                    <Pagination
                                                        currentPage={currentPage}
                                                        totalPages={totalPages}
                                                        onPageChange={setCurrentPage}
                                                    />
                                                )}
                                                <div className="text-xs text-gray-400">
                                                    Showing {startIndex + 1}-{Math.min(startIndex + itemsPerPage, sortedCreators.length)} of {sortedCreators.length} creators
                                                </div>
                                            </div>
                                        )}
                                    </>
                                )}
                            </div>
                        </motion.div>
                    ) : (
                        <motion.div
                            key="filters"
                            initial={{ y: 90, rotateX: 10, opacity: 0 }}
                            animate={{ y: 0, rotateX: 0, opacity: 1 }}
                            exit={{ y: -10, rotateX: -10, opacity: 0 }}
                            transition={{ duration: 0.2, ease: 'easeInOut' }}
                            className="absolute inset-0 flex items-start justify-center gap-4 rounded-lg text-white"
                        >
                            <div className="grid grid-cols-4 gap-4 mb-8 w-full">
                                {globalFilters
                                    .filter((category) => category.platform === activePlatform)
                                    .map((category, index) => (
                                        <CreatorCategoryCard
                                            key={index}
                                            title={category.title}
                                            description={category.description}
                                            tag={category.tag}
                                            tagColor={category.tagColor}
                                            gradient={category.gradient}
                                            avatars={category.avatars}
                                            count={category.count}
                                            platform={category.platform}
                                            onClick={() => {
                                                console.log('Category clicked:', category);
                                                // setSearchQuery("");
                                                setSavedFilters(category.filterData);
                                                setIsTableVisible(true);
                                            }}
                                            Icon={category.Icon}
                                        />
                                    ))}
                            </div>
                        </motion.div>
                    )}
                </AnimatePresence>
            </div>
        </div >
    )
}

export default Discovery
