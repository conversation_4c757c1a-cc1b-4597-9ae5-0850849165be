import React from 'react';

const LoadingState = (text = 'Loading creators...') => {
  return (
    <div className="w-full flex flex-col items-center justify-center py-12">
      <div className="flex flex-col items-center gap-4">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-brand-500"></div>
        {
          text && text.length > 0 && <p className="text-gray-400 text-lg">{text}</p>
        }
      </div>
    </div>
  );
};

export default LoadingState;
