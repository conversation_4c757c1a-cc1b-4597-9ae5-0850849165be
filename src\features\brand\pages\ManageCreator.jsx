import React, { useState, useEffect } from 'react';
import { Outlet, useNavigate, useLocation, useParams } from "react-router-dom";

import DownloadIcon from '@assets/icon/download.svg';
import SearchIcon from "@assets/icon/nav/search.svg";

import { Input } from "@shared/components/UI/input";
import PopupLayout from '@shared/components/UI/PopupLayout';
import ImportProfiles from '../components/ImportProfiles';

import useBrandSelectors from '@brand/services/brandSelectors';
import { useDispatch } from 'react-redux';
import { useLoading } from '@shared/components/UI/LoadingContext';
import { useSnackbar } from '@shared/components/UI/SnackbarContext';
import {
    getCreatorListsThunk,
    deleteCreatorListThunk,
    createCreatorListThunk
} from '@brand/services/brandThunks';

import dayjs from 'dayjs';
import CreatorListCard from '../components/CreatorListCard ';

const ManageCreator = () => {
    const dispatch = useDispatch();
    const navigate = useNavigate();
    const location = useLocation();
    const params = useParams();
    const isSubRoute = !!params.listId;
    const { setIsLoading } = useLoading();
    const { showSnackbar } = useSnackbar();

    const { selectedBrand } = useBrandSelectors();
    const [localCreatorLists, setLocalCreatorLists] = useState([]);
    const [searchTerm, setSearchTerm] = useState("");
    const [isPopupOpen, setIsPopupOpen] = useState("");

    // ✅ Common function to fetch creator lists
    const fetchCreatorLists = async () => {
        try {
            setIsLoading(true);
            const result = await dispatch(getCreatorListsThunk({ brand_id: selectedBrand.id }));
            if (result.meta.requestStatus === 'fulfilled') {
                const responseData = result.payload.data;
                setLocalCreatorLists(responseData.lists);
            } else {
                showSnackbar(result.payload.message || 'Failed to load lists.', 'error');
            }
        } catch (error) {
            console.error('Error fetching lists:', error);
            showSnackbar(error.message || 'Failed to fetch creator lists.', 'error');
        } finally {
            setIsLoading(false);
        }
    };

    useEffect(() => {
        if(isSubRoute) return;
        fetchCreatorLists();
    }, [dispatch, selectedBrand, params]);

    // ✅ Navigate to detail page
    const handleCardClick = (listId) => {

        console.log('Navigating to detail page for list:', listId);
        navigate(`/brand/manage-creator/${listId}`);
    };

    const deleteCreatorList = async (creatorList) => {
        try {
            const result = await dispatch(deleteCreatorListThunk({
                listId: creatorList.id,
                body: { brand_id: selectedBrand.id }
            }));
            if (result.meta.requestStatus === 'fulfilled' && result.payload.success) {
                showSnackbar('List deleted successfully', 'success');
                fetchCreatorLists(); // ✅ refresh
            } else {
                showSnackbar(result.payload.message || 'Delete failed.', 'error');
            }
        } catch (error) {
            console.error('Delete error:', error);
            showSnackbar(error.message || 'Failed to delete creator list.', 'error');
        } finally {
            setIsLoading(false);
        }
    };

    const addNewList = () => {
        const today = dayjs().format('YYYY-MM-DD');
        const newId = `temp-${Date.now()}`;
        setLocalCreatorLists(prev => [
            ...prev,
            {
                id: newId,
                name: '',
                createdBy: 'CreatorVerse',
                createdOn: today,
                lastModifiedOn: today,
                creators: [],
                isNew: true,
            },
        ]);
    };

    const handleCreateList = async (name) => {
        const newList = {
            name,
            description: "",
            brand_id: selectedBrand.id,
        };

        try {
            const result = await dispatch(createCreatorListThunk(newList));
            if (result.meta.requestStatus === 'fulfilled') {
                showSnackbar('List created successfully', 'success');
                fetchCreatorLists(); // ✅ refresh
            } else {
                showSnackbar(result.payload.message || 'Creation failed.', 'error');
            }
        } catch (error) {
            console.error('Create error:', error);
            showSnackbar(error.message || 'Failed to create creator list.', 'error');
        } finally {
            setIsLoading(false);
        }
    };

    const removeTempList = (tempId) => {
        setLocalCreatorLists(prev => prev.filter(list => list.id !== tempId));
    };

    const updateListName = async (listId, newName) => {
        const list = localCreatorLists.find(l => l.id === listId);
        if (!list) return;

        if (list.isNew) {
            setLocalCreatorLists(prev => prev.filter(l => l.id !== listId));
            await handleCreateList(newName);
        } else {
            setLocalCreatorLists(prev =>
                prev.map(l => (l.id === listId ? { ...l, name: newName } : l))
            );
        }
    };

    const filteredLists = localCreatorLists.filter(list =>
        list.name?.toLowerCase().includes(searchTerm.toLowerCase())
    );

    return (
        <>
            {!isSubRoute && (
                <>
                    <div className="w-full flex flex-col gap-6 h-full bg-primary/70">
                        {/* Header */}
                        <div className="flex items-center">
                            <h1 className="text-30-semibold text-gray-50">Manage Creators</h1>
                        </div>

                        {/* Creator List View */}
                        <div className='flex flex-col gap-5'>
                            <div className='text-20-semibold text-gray-50'>
                                Organise your creator discovery with ease.
                            </div>
                            <div className='flex items-center justify-between'>
                                <div className="flex items-center gap-4">
                                    <Input
                                        type="text"
                                        value={searchTerm}
                                        onChange={(e) => setSearchTerm(e.target.value)}
                                        icon={SearchIcon}
                                        placeholder="Search lists..."
                                        className="px-3 py-1 h-10 w-94 bg-transparent text-sm placeholder-gray-400 focus:outline-none"
                                    />
                                </div>
                                <div className="flex items-center gap-4">
                                    {/* Import List */}
                                    <button
                                        className="relative px-4 py-2.5 bg-transparent border border-gray-400 rounded-lg flex items-center gap-1.5 text-16-semibold text-gray-50 hover:text-gray-300 transition-colors"
                                        onClick={() => setIsPopupOpen("import")}
                                    >
                                        <img src={DownloadIcon} alt="Import" className="w-5 h-5" />
                                        Import
                                    </button>

                                    {/* Create List */}
                                    <button
                                        className="relative px-4 py-3 bg-brand-500 rounded-lg text-16-semibold text-white hover:bg-brand-600 transition-colors"
                                        onClick={addNewList}
                                    >
                                        + Create new list
                                    </button>
                                </div>
                            </div>

                            {/* My Saved Lists */}
                            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-5 w-full">
                                {filteredLists.map((list) => (
                                    <CreatorListCard
                                        key={list.id}
                                        name={list.name}
                                        createdBy={list.created_by}
                                        createdOn={list.created_at}
                                        lastModifiedOn={list.updated_at}
                                        creators={list.creators}
                                        isNew={list.isNew}
                                        onClick={() => handleCardClick(list.id)}
                                        onDelete={() => deleteCreatorList(list)}
                                        onNameChange={(newName) => updateListName(list.id, newName)}
                                        onCancel={() => removeTempList(list.id)}
                                    />
                                ))}
                                <div
                                    onClick={addNewList}
                                    className="w-full h-[205px] border border-dashed border-gray-400 rounded-lg flex flex-col items-center justify-center cursor-pointer text-gray-400 hover:border-white hover:text-white transition"
                                >
                                    <div className="text-3xl">+</div>
                                    <div className="text-sm">Create New List</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {/* Popup */}
                    {isPopupOpen === "import" && (
                        <PopupLayout
                            title="Import Profiles"
                            onClose={() => setIsPopupOpen("")}
                            isAcceptButton={false}
                            isCancelButton={false}
                        >
                            <ImportProfiles
                                onClose={() => setIsPopupOpen("")}
                                onComplete={(results) => {
                                    console.log('Import complete with results:', results);
                                    // Optional: handle import result
                                }}
                            />
                        </PopupLayout>
                    )}
                </>
            )}

            {/* Render nested route content */}
            <Outlet />
        </>
    );
};

export default ManageCreator;
