/**
 * Authentication Service Alova Instance
 * 
 * This instance handles all authentication-related API calls including:
 * - User login/registration
 * - OTP verification
 * - Token refresh
 * - OAuth flows
 * - Brand authentication
 */

import { createAlova } from 'alova';
import FetchAdapter from 'alova/fetch';
import ReactHook from 'alova/react';
import { apiConfig, SERVICE_TYPES } from '../config/apiConfig';

/**
 * Token management utilities
 */
const getAuthToken = () => localStorage.getItem('auth_token');
const getRefreshToken = () => localStorage.getItem('refresh_token');
const setTokens = (accessToken, refreshToken) => {
  localStorage.setItem('auth_token', accessToken);
  if (refreshToken) {
    localStorage.setItem('refresh_token', refreshToken);
  }
};
const clearTokens = () => {
  localStorage.removeItem('auth_token');
  localStorage.removeItem('refresh_token');
  localStorage.removeItem('user');
  localStorage.removeItem('allocatedBrands');
  localStorage.removeItem('organizationBrands');
  localStorage.removeItem('organisationId');
};

/**
 * Redirect to appropriate login page based on context
 */
const redirectToLogin = (requestUrl = '') => {
  const isBrand = requestUrl.includes('/brand');
  if (isBrand) {
    window.location.href = '/brand/signin';
  } else {
    window.location.href = '/influencer/login';
  }
};

/**
 * Token refresh logic
 */
const refreshAuthToken = async (method) => {
  const refreshToken = getRefreshToken();
  if (!refreshToken) {
    console.error('No refresh token available');
    throw new Error('No refresh token available');
  }

  try {
    const refreshResponse = await fetch(`${apiConfig.authServiceURL}/auth/refresh-token`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...(apiConfig.environment === 'development' && {
          'ngrok-skip-browser-warning': 'true'
        })
      },
      body: JSON.stringify({ refresh_token: refreshToken }),
    });

    const refreshData = await refreshResponse.json();

    if (refreshResponse.ok && refreshData.data?.access_token) {
      const { access_token, refresh_token } = refreshData.data;
      setTokens(access_token, refresh_token);
      
      // Update the original request with new token
      method.config.headers['Authorization'] = `Bearer ${access_token}`;
      
      // Retry the original request
      return await method.send();
    } else {
      throw new Error(refreshData.message || 'Token refresh failed');
    }
  } catch (error) {
    console.error('Token refresh error:', error);
    clearTokens();
    redirectToLogin(method.url);
    throw error;
  }
};

/**
 * Create Authentication Service Alova Instance
 */
const authInstance = createAlova({
  baseURL: apiConfig.authServiceURL,
  requestAdapter: FetchAdapter(),
  statesHook: ReactHook,

  // Default request options
  defaultOptions: {
    headers: {
      ...apiConfig.common.headers,
      ...(apiConfig.environment === 'development' && {
        'ngrok-skip-browser-warning': 'true'
      })
    },
  },
  withCredentials: apiConfig.common.withCredentials,

  // Global response handling
  responded: {
    // Transform successful responses
    onSuccess: async (response, method) => {
      console.log(`[AUTH SERVICE] API response received:`, {
        url: method.url,
        status: response.status,
        timestamp: new Date().toISOString()
      });

      const json = await response.json();

      // Handle 401 Unauthorized - attempt token refresh
      if (response.status === 401) {
        console.warn('[AUTH SERVICE] Access token expired. Attempting token refresh...');
        
        // Don't attempt refresh for login/register endpoints
        const isAuthEndpoint = method.url.includes('/login') || 
                              method.url.includes('/register') || 
                              method.url.includes('/verify-otp') ||
                              method.url.includes('/refresh');
        
        if (!isAuthEndpoint) {
          try {
            return await refreshAuthToken(method);
          } catch (refreshError) {
            console.error('[AUTH SERVICE] Token refresh failed:', refreshError);
            // Let the error fall through to be handled by the component
          }
        }
      }

      return {
        data: json,
        status: response.status,
        service: SERVICE_TYPES.AUTH
      };
    },

    // Handle request errors
    onError: (error, method) => {
      console.error(`[AUTH SERVICE] API request failed:`, {
        url: method.url,
        error: error.message,
        timestamp: new Date().toISOString()
      });
      throw error;
    },
  },

  // Add authorization headers if user is authenticated
  beforeRequest(method) {
    console.log(`[AUTH SERVICE] Preparing request:`, {
      url: method.url,
      method: method.type,
      timestamp: new Date().toISOString()
    });

    // Add auth token for protected endpoints
    const token = getAuthToken();
    if (token) {
      // Don't add auth header for login/register endpoints
      const isPublicEndpoint = method.url.includes('/login') || 
                              method.url.includes('/register') || 
                              method.url.includes('/verify-otp');
      
      if (!isPublicEndpoint) {
        method.config.headers['Authorization'] = `Bearer ${token}`;
      }
    }

    // Add development headers
    if (apiConfig.environment === 'development') {
      method.config.headers['ngrok-skip-browser-warning'] = 'true';
    }
  }
});

/**
 * Auth instance utility functions
 */
export const authUtils = {
  getAuthToken,
  getRefreshToken,
  setTokens,
  clearTokens,
  redirectToLogin,
  refreshAuthToken,
  
  // Service health check
  checkHealth: () => apiConfig.checkServiceHealth(SERVICE_TYPES.AUTH),
  
  // Get service configuration
  getConfig: () => apiConfig.getServiceConfig(SERVICE_TYPES.AUTH)
};

export default authInstance;
