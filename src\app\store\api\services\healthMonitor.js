/**
 * Health Check and Service Monitoring
 * 
 * Provides comprehensive health monitoring for multi-backend services.
 * Features:
 * - Real-time health checks for all services
 * - Service availability monitoring
 * - Performance metrics collection
 * - Automatic failover detection
 * - Health status reporting
 * - Service dependency tracking
 */

import { apiConfig, SERVICE_TYPES } from '../config/apiConfig';
import envConfig from '../config/environmentConfig';
import errorHandler from './errorHandler';

/**
 * Health status constants
 */
export const HEALTH_STATUS = {
  HEALTHY: 'healthy',
  DEGRADED: 'degraded',
  UNHEALTHY: 'unhealthy',
  UNKNOWN: 'unknown'
};

/**
 * Service monitoring metrics
 */
const METRICS = {
  RESPONSE_TIME: 'response_time',
  SUCCESS_RATE: 'success_rate',
  ERROR_RATE: 'error_rate',
  AVAILABILITY: 'availability'
};

/**
 * Health Check Service
 */
class HealthMonitorService {
  constructor() {
    this.serviceHealth = new Map();
    this.metrics = new Map();
    this.monitoringInterval = null;
    this.isMonitoring = false;
    
    // Configuration
    this.config = {
      checkInterval: envConfig.get('monitoring.checkInterval', 30000), // 30 seconds
      timeout: envConfig.get('monitoring.timeout', 5000), // 5 seconds
      retryAttempts: envConfig.get('monitoring.retryAttempts', 2),
      degradedThreshold: envConfig.get('monitoring.degradedThreshold', 2000), // 2 seconds
      unhealthyThreshold: envConfig.get('monitoring.unhealthyThreshold', 5000), // 5 seconds
    };
    
    // Initialize service health tracking
    this.initializeServices();
  }

  /**
   * Initialize service health tracking
   */
  initializeServices() {
    Object.values(SERVICE_TYPES).forEach(service => {
      this.serviceHealth.set(service, {
        status: HEALTH_STATUS.UNKNOWN,
        lastCheck: null,
        lastSuccess: null,
        consecutiveFailures: 0,
        responseTime: null,
        error: null,
        metadata: {}
      });
      
      this.metrics.set(service, {
        responseTime: [],
        successCount: 0,
        errorCount: 0,
        totalRequests: 0,
        uptime: 0,
        lastReset: Date.now()
      });
    });
  }

  /**
   * Start health monitoring
   */
  startMonitoring() {
    if (this.isMonitoring) {
      console.warn('[HEALTH MONITOR] Monitoring already started');
      return;
    }
    
    this.isMonitoring = true;
    console.log('[HEALTH MONITOR] Starting health monitoring...');
    
    // Perform initial health check
    this.checkAllServices();
    
    // Set up periodic monitoring
    this.monitoringInterval = setInterval(() => {
      this.checkAllServices();
    }, this.config.checkInterval);
  }

  /**
   * Stop health monitoring
   */
  stopMonitoring() {
    if (!this.isMonitoring) {
      return;
    }
    
    this.isMonitoring = false;
    
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }
    
    console.log('[HEALTH MONITOR] Health monitoring stopped');
  }

  /**
   * Check health of all services
   */
  async checkAllServices() {
    const promises = Object.values(SERVICE_TYPES).map(service => 
      this.checkServiceHealth(service)
    );
    
    await Promise.allSettled(promises);
    
    // Log overall health status
    if (envConfig.isDebugMode()) {
      this.logHealthSummary();
    }
  }

  /**
   * Check health of a specific service
   * @param {string} service - Service name
   * @returns {Promise<Object>} Health check result
   */
  async checkServiceHealth(service) {
    const startTime = Date.now();
    const serviceConfig = envConfig.getServiceConfig(service);
    const healthEndpoint = `${serviceConfig.baseURL}/v1/health`;
    
    try {
      const response = await fetch(healthEndpoint, {
        method: 'GET',
        timeout: this.config.timeout,
        headers: {
          'Content-Type': 'application/json',
          ...envConfig.getHeaders()
        }
      });
      
      const responseTime = Date.now() - startTime;
      const data = await response.json();
      
      if (response.ok) {
        this.recordSuccess(service, responseTime, data);
        return this.getServiceHealth(service);
      } else {
        this.recordFailure(service, responseTime, `HTTP ${response.status}: ${response.statusText}`);
        return this.getServiceHealth(service);
      }
    } catch (error) {
      const responseTime = Date.now() - startTime;
      this.recordFailure(service, responseTime, error.message);
      return this.getServiceHealth(service);
    }
  }

  /**
   * Record successful health check
   * @param {string} service - Service name
   * @param {number} responseTime - Response time in ms
   * @param {Object} data - Response data
   */
  recordSuccess(service, responseTime, data) {
    const health = this.serviceHealth.get(service);
    const metrics = this.metrics.get(service);
    
    // Update health status
    health.status = this.determineHealthStatus(responseTime);
    health.lastCheck = Date.now();
    health.lastSuccess = Date.now();
    health.consecutiveFailures = 0;
    health.responseTime = responseTime;
    health.error = null;
    health.metadata = data?.data || {};
    
    // Update metrics
    metrics.responseTime.push(responseTime);
    metrics.successCount++;
    metrics.totalRequests++;
    
    // Keep only last 100 response times
    if (metrics.responseTime.length > 100) {
      metrics.responseTime.shift();
    }
    
    this.serviceHealth.set(service, health);
    this.metrics.set(service, metrics);
  }

  /**
   * Record failed health check
   * @param {string} service - Service name
   * @param {number} responseTime - Response time in ms
   * @param {string} error - Error message
   */
  recordFailure(service, responseTime, error) {
    const health = this.serviceHealth.get(service);
    const metrics = this.metrics.get(service);
    
    // Update health status
    health.status = HEALTH_STATUS.UNHEALTHY;
    health.lastCheck = Date.now();
    health.consecutiveFailures++;
    health.responseTime = responseTime;
    health.error = error;
    
    // Update metrics
    metrics.errorCount++;
    metrics.totalRequests++;
    
    this.serviceHealth.set(service, health);
    this.metrics.set(service, metrics);
  }

  /**
   * Determine health status based on response time
   * @param {number} responseTime - Response time in ms
   * @returns {string} Health status
   */
  determineHealthStatus(responseTime) {
    if (responseTime <= this.config.degradedThreshold) {
      return HEALTH_STATUS.HEALTHY;
    } else if (responseTime <= this.config.unhealthyThreshold) {
      return HEALTH_STATUS.DEGRADED;
    } else {
      return HEALTH_STATUS.UNHEALTHY;
    }
  }

  /**
   * Get health status for a specific service
   * @param {string} service - Service name
   * @returns {Object} Service health status
   */
  getServiceHealth(service) {
    return this.serviceHealth.get(service) || {
      status: HEALTH_STATUS.UNKNOWN,
      lastCheck: null,
      lastSuccess: null,
      consecutiveFailures: 0,
      responseTime: null,
      error: null,
      metadata: {}
    };
  }

  /**
   * Get health status for all services
   * @returns {Object} All services health status
   */
  getAllServicesHealth() {
    const health = {};
    
    this.serviceHealth.forEach((status, service) => {
      health[service] = { ...status };
    });
    
    return health;
  }

  /**
   * Get metrics for a specific service
   * @param {string} service - Service name
   * @returns {Object} Service metrics
   */
  getServiceMetrics(service) {
    const metrics = this.metrics.get(service);
    if (!metrics) return null;
    
    const avgResponseTime = metrics.responseTime.length > 0 
      ? metrics.responseTime.reduce((a, b) => a + b, 0) / metrics.responseTime.length 
      : 0;
    
    const successRate = metrics.totalRequests > 0 
      ? (metrics.successCount / metrics.totalRequests) * 100 
      : 0;
    
    const errorRate = metrics.totalRequests > 0 
      ? (metrics.errorCount / metrics.totalRequests) * 100 
      : 0;
    
    return {
      ...metrics,
      avgResponseTime: Math.round(avgResponseTime),
      successRate: Math.round(successRate * 100) / 100,
      errorRate: Math.round(errorRate * 100) / 100,
      uptime: this.calculateUptime(service)
    };
  }

  /**
   * Get metrics for all services
   * @returns {Object} All services metrics
   */
  getAllServicesMetrics() {
    const metrics = {};
    
    this.metrics.forEach((_, service) => {
      metrics[service] = this.getServiceMetrics(service);
    });
    
    return metrics;
  }

  /**
   * Calculate service uptime percentage
   * @param {string} service - Service name
   * @returns {number} Uptime percentage
   */
  calculateUptime(service) {
    const metrics = this.metrics.get(service);
    if (!metrics || metrics.totalRequests === 0) return 0;
    
    return Math.round((metrics.successCount / metrics.totalRequests) * 10000) / 100;
  }

  /**
   * Get overall system health
   * @returns {Object} Overall health status
   */
  getOverallHealth() {
    const services = this.getAllServicesHealth();
    const serviceStatuses = Object.values(services).map(s => s.status);
    
    let overallStatus = HEALTH_STATUS.HEALTHY;
    
    if (serviceStatuses.includes(HEALTH_STATUS.UNHEALTHY)) {
      overallStatus = HEALTH_STATUS.UNHEALTHY;
    } else if (serviceStatuses.includes(HEALTH_STATUS.DEGRADED)) {
      overallStatus = HEALTH_STATUS.DEGRADED;
    } else if (serviceStatuses.includes(HEALTH_STATUS.UNKNOWN)) {
      overallStatus = HEALTH_STATUS.UNKNOWN;
    }
    
    return {
      status: overallStatus,
      services: services,
      timestamp: Date.now(),
      monitoring: this.isMonitoring
    };
  }

  /**
   * Reset metrics for a service
   * @param {string} service - Service name
   */
  resetServiceMetrics(service) {
    this.metrics.set(service, {
      responseTime: [],
      successCount: 0,
      errorCount: 0,
      totalRequests: 0,
      uptime: 0,
      lastReset: Date.now()
    });
  }

  /**
   * Reset all metrics
   */
  resetAllMetrics() {
    Object.values(SERVICE_TYPES).forEach(service => {
      this.resetServiceMetrics(service);
    });
  }

  /**
   * Log health summary
   */
  logHealthSummary() {
    const overall = this.getOverallHealth();
    console.log(`[HEALTH MONITOR] Overall Status: ${overall.status}`);
    
    Object.entries(overall.services).forEach(([service, health]) => {
      const metrics = this.getServiceMetrics(service);
      console.log(`[HEALTH MONITOR] ${service}: ${health.status} (${metrics?.avgResponseTime}ms, ${metrics?.uptime}% uptime)`);
    });
  }

  /**
   * Check if service is healthy
   * @param {string} service - Service name
   * @returns {boolean} Whether service is healthy
   */
  isServiceHealthy(service) {
    const health = this.getServiceHealth(service);
    return health.status === HEALTH_STATUS.HEALTHY || health.status === HEALTH_STATUS.DEGRADED;
  }

  /**
   * Get service availability
   * @param {string} service - Service name
   * @returns {boolean} Whether service is available
   */
  isServiceAvailable(service) {
    const health = this.getServiceHealth(service);
    return health.status !== HEALTH_STATUS.UNHEALTHY;
  }
}

// Create singleton instance
const healthMonitor = new HealthMonitorService();

// Auto-start monitoring in development and staging
if (!envConfig.isProduction()) {
  healthMonitor.startMonitoring();
}

export { HEALTH_STATUS, METRICS };
export default healthMonitor;
