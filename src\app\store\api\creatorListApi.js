// Creatorverse API service - Updated for Multi-Backend Architecture
import authInstance from './instances/authInstance';

/**
 * Creator List API service - Updated for Multi-Backend Architecture
 * Contains all endpoints related to creator lists and their members
 */
const creatorListApi = {
  // Get all creator lists with optional query params
  // getCreatorList: (params = {}) => {
  //   return authInstance.Get('/creator-lists/lists', { params });
  // },
  // Disable caching by adding headers and a timestamp
  getCreatorList: (params = {}) => {
    return authInstance.Get('/creator-lists/lists', {
      params: { ...params, _t: new Date().getTime() }, // cache buster
      // headers: {
      //   'Cache-Control': 'no-cache',
      //   Pragma: 'no-cache',
      //   Expires: '0'
      // }
    });
  },

  // Create a new creator list
  createCreatorList: (payload) => {
    return authInstance.Post('/creator-lists/lists', payload);
  },

  // Add member to a creator list
  addMemberToList: (listId, payload) => {
    return authInstance.Post(`/creator-lists/lists/${listId}/members`, payload);
  },

  // Get members of a creator list
  getListMembers: (listId, params = {}) => {
    return authInstance.Get(`/creator-lists/lists/${listId}/members`, {
      params: {
        ...params,
        _cacheBuster: Date.now()
      }
    });
  },

  // Delete a creator list
  deleteList: (listId, body) => {
    return authInstance.Delete(`/creator-lists/lists/${listId}`, body);
  },

  patchCreatorList: (listId, body, params = {}) => {
    return authInstance.Patch(`/creator-lists/lists/${listId}`, body, { params });
  },

  patchCreatorListMemberNotes: (listId, profileId, body, params = {}) => {
    return authInstance.Patch(
      `/creator-lists/lists/${listId}/members/${profileId}/notes`,
      body,
      { params }
    );
  },

  // In your creatorListApi.js or similar API utility
  getLabels: (brandId, includeDetails = false) => {
    return authInstance.Get('/creator-lists/labels', {
      params: {
        brand_id: brandId,
        include_details: includeDetails,
      },
    });
  },

  createLabel: (body) => {
    return authInstance.Post('/creator-lists/labels', body);
  },

  deleteLabelFromProfile(profileId, labelId, body) {
    return authInstance.Delete(`/creator-lists/members/${profileId}/labels/${labelId}`, body);
  },

  deleteCreatorListMembers: (listId, body) => {
    return authInstance.Delete(
      `/creator-lists/lists/${listId}/members`,body
    );
  },




};

export default creatorListApi;
