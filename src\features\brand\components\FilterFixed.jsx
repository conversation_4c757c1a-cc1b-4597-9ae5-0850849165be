import React, { useState, useRef, useEffect } from 'react';
import { cn } from '../../../lib/utils';
import { FaTimes } from 'react-icons/fa';
import { BsGenderMale, BsCalendarDate, BsTranslate } from 'react-icons/bs';
import { FiUsers } from 'react-icons/fi';
import { IoLocationOutline, IoStatsChart } from 'react-icons/io5';
import { MdOutlineCategory, MdOutlineVerified } from 'react-icons/md'; 
import { BiHash, BiWorld, BiTrendingUp } from 'react-icons/bi';
import { FaRegKeyboard } from 'react-icons/fa6';
import { AiOutlineHeart } from 'react-icons/ai';
import { VscComment } from 'react-icons/vsc';

const Filter = ({ onClose, onApplyFilters, selectedChannel = "instagram", savedFilters = [], filterOptions = [], hidetab = false }) => {
    const popupRef = useRef(null);
    const [activeTab, setActiveTab] = useState('creator');
    //const [selectedChannel, setSelectedChannel] = useState(selectedChannel); // 'instagram' or 'youtube'
    const [selectedCategory, setSelectedCategory] = useState(filterOptions.filter(option => option.channel === selectedChannel)[0]?.optionName);
    console.log(filterOptions)
    // Track filters separately for creator and audience
    const [selectedFilters, setSelectedFilters] = useState({
        creator: {},
        audience: {}
    });
    const [searchTerm, setSearchTerm] = useState('');
    const [pillValues, setPillValues] = useState({
        creator: {},
        audience: {}
    });

    

    // Track min/max values for filters with minmax option
    const [minMaxValues, setMinMaxValues] = useState({
        creator: {},
        audience: {}
    });
    // Selected filters count by category, separated by tab
    const [filterCounts, setFilterCounts] = useState({
        creator: {},
        audience: {}
    });

    // Selected filters count by tab
    const [tabFilterCounts, setTabFilterCounts] = useState({
        creator: 0,
        audience: 0
    });

    // Track which filters are collapsed
    const [collapsedFilters, setCollapsedFilters] = useState({});

    // Track which multilevel sub-options are collapsed
    const [collapsedSubOptions, setCollapsedSubOptions] = useState({});

    // Track search terms for each multilevel filter
    const [multilevelSearch, setMultilevelSearch] = useState({
        creator: {},
        audience: {}
    });

    // Function to calculate the sum of all category counts for a given tab
    const calculateCategoryCountSum = React.useCallback((tab) => {
        if (!filterCounts[tab]) return 0;

        // Sum all category counts for this tab
        return Object.values(filterCounts[tab]).reduce((sum, count) => sum + count, 0);
    }, [filterCounts]);

    // Effect to update tab counts whenever filterCounts changes
    useEffect(() => {
        setTabFilterCounts(prev => ({
            ...prev,
            creator: calculateCategoryCountSum('creator'),
            audience: calculateCategoryCountSum('audience')
        }));
    }, [filterCounts, calculateCategoryCountSum]);

    // Initialize subOptions collapsed state based on their 'collapsed' property
    useEffect(() => {
        const initialCollapsedState = {};

        // Loop through all filter options for both tabs
        filterOptions.forEach(category => {
            category.filters.forEach(filter => {
                if (filter.type === 'multilevel-checkbox' && filter.options) {
                    filter.options.forEach(subOption => {
                        if (subOption.subOptionName) {
                            // Set collapse state based on the 'collapsed' property if present, otherwise default to true
                            const key = `${filter.name}-${subOption.subOptionName}`;
                            initialCollapsedState[key] = subOption.collapsed !== undefined ? subOption.collapsed : true;
                        }
                    });
                }
            });
        });

        setCollapsedSubOptions(initialCollapsedState);
    }, [filterOptions]);

    // Process saved filters when component initializes
    useEffect(() => {
        if (savedFilters && savedFilters.length > 0) {
            processSavedFilters();
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    // Process saved filters and update component state
    const processSavedFilters = () => {
        // Initialize temporary state objects
        const tempSelectedFilters = {
            creator: {},
            audience: {}
        };
        const tempPillValues = {
            creator: {},
            audience: {}
        };
        const tempMinMaxValues = {
            creator: {},
            audience: {}
        };
        // Initialize filter counts with tab structure
        const tempFilterCounts = {
            creator: {},
            audience: {}
        };

        // Group filters by filterFor (creator/audience) and then by category to track counts
        const filtersByCategory = {
            creator: {},
            audience: {}
        };

        // Process each saved filter
        savedFilters.forEach(filter => {
            //console.log(`Processing saved filter:`, filter);
            // Skip if filter doesn't match current channel
            if (filter.channel !== selectedChannel) {
                return;
            }

            const { filterFor, filter: filterName, value } = filter;
            //console.log(`Processing saved filter: ${filterName}, value:`, value);

            // Find filter details
            const allCategories = filterOptions.filter(
                option => option.optionFor === filterFor && option.channel === selectedChannel
            );

            let filterDetails = null;
            let categoryName = null;

            // Find the category and filter details
            for (const category of allCategories) {
                const found = category.filters.find(f => f.name === filterName);
                if (found) {
                    filterDetails = found;
                    categoryName = category.optionName;
                    break;
                }
            }

            if (!filterDetails || !categoryName) return;
            // Track categories for counting filters - separated by filterFor (creator/audience)
            if (!filtersByCategory[filterFor][categoryName]) {
                filtersByCategory[filterFor][categoryName] = new Set();
            }
            filtersByCategory[filterFor][categoryName].add(filterName);

            // Handle different filter types
            if (filterDetails.type === 'radio-button') {
                tempSelectedFilters[filterFor][filterName] = value;
            }

            // Special case for multilevel-checkbox with structured values
            else if (filterDetails.type === 'multilevel-checkbox' && Array.isArray(value)) {
                //console.log(`Processing structured multilevel filter: ${filterName} with structured values:`, value);

                // For each subOption group in the filter
                filterDetails.options.forEach(subOption => {
                    const subOptionName = subOption.subOptionName;
                    const subOptionType = subOption.subOptionType || 'checkbox';

                    // Find all values for this subOption group
                    const subOptionValues = value.filter(item => item.filter === subOptionName);

                    if (subOptionValues.length > 0) {
                        // Handle based on subOptionType
                        if (subOptionType === 'radio-button') {
                            // For radio buttons, store with special key
                            const radioKey = `${filterName}_radioGroup_${subOptionName}`;
                            // Take the first value for radio buttons (should only be one)
                            // Check if the value contains multiple values (shouldn't for radio buttons, but just in case)
                            const radioValue = subOptionValues[0].value;
                            if (radioValue.includes(',')) {
                                // Take the first value if there are multiple (shouldn't happen for radio buttons)
                                tempSelectedFilters[filterFor][radioKey] = radioValue.split(',')[0].trim();
                            } else {
                                tempSelectedFilters[filterFor][radioKey] = radioValue;
                            }
                        } else {
                            // For checkboxes, initialize array if needed
                            if (!tempSelectedFilters[filterFor][filterName]) {
                                tempSelectedFilters[filterFor][filterName] = [];
                            }

                            // Process each subOption item
                            subOptionValues.forEach(item => {
                                // Check if the value contains multiple comma-separated values
                                if (item.value.includes(',')) {
                                    // Split the comma-separated values and add each individually
                                    const splitValues = item.value.split(',').map(v => v.trim());
                                    splitValues.forEach(splitValue => {
                                        if (!tempSelectedFilters[filterFor][filterName].includes(splitValue)) {
                                            tempSelectedFilters[filterFor][filterName].push(splitValue);
                                        }
                                    });
                                } else {
                                    // Add single value
                                    if (!tempSelectedFilters[filterFor][filterName].includes(item.value)) {
                                        tempSelectedFilters[filterFor][filterName].push(item.value);
                                    }
                                }
                            });
                        }
                    }
                });
            }
            else if (filterDetails.type === 'checkbox' || filterDetails.type === 'multilevel-checkbox') {
                //console.log(`Processing regular checkbox/multilevel filter: ${filterName} with value:`, value);

                // Check if the value contains multiple comma-separated values (e.g., "fashion,travel")
                if (value && value.includes(',')) {
                    console.log(`Found comma-separated values: ${value}`);
                    // Split the comma-separated values and process each one
                    const values = value.split(',');
                    console.log(`Split values:`, values);

                    // Initialize the array if it doesn't exist
                    if (!tempSelectedFilters[filterFor][filterName]) {
                        tempSelectedFilters[filterFor][filterName] = [];
                    }

                    // Process each value
                    values.forEach(singleValue => {
                        const trimmedValue = singleValue.trim();

                        // For multilevel-checkbox, we need to check in all sub-options
                        let matchingOption = null;

                        if (filterDetails.type === 'multilevel-checkbox') {
                            // Search through all sub-options in all groups
                            for (const subOptionGroup of filterDetails.options) {
                                const found = subOptionGroup.subOptions?.find(opt =>
                                    opt.value === trimmedValue || opt.description === trimmedValue
                                );
                                if (found) {
                                    matchingOption = found;
                                    break;
                                }
                            }
                        } else {
                            // Regular checkbox
                            matchingOption = filterDetails.options?.find(opt =>
                                opt.value === trimmedValue || opt.description === trimmedValue
                            );
                        }

                        console.log(`Processing value: "${trimmedValue}", matching option:`, matchingOption);

                        if (matchingOption) {
                            // Add to the selected filters if it's not already there
                            if (!tempSelectedFilters[filterFor][filterName].includes(matchingOption.value)) {
                                tempSelectedFilters[filterFor][filterName].push(matchingOption.value);
                            }
                        } else if (filterDetails.enterValue) {
                            // If no matching option but filter allows enter values, add to pills
                            if (!tempPillValues[filterFor][filterName]) {
                                tempPillValues[filterFor][filterName] = [];
                            }
                            if (!tempPillValues[filterFor][filterName].includes(trimmedValue)) {
                                tempPillValues[filterFor][filterName].push(trimmedValue);
                            }
                        }
                    });

                    console.log(`Final selected filters for ${filterName}:`, tempSelectedFilters[filterFor][filterName]);
                }
                else {
                    // Single value processing
                    let matchingOption = null;

                    if (filterDetails.type === 'multilevel-checkbox') {
                        // Search through all sub-options in all groups
                        for (const subOptionGroup of filterDetails.options) {
                            const found = subOptionGroup.subOptions?.find(opt =>
                                opt.value === value || opt.description === value
                            );
                            if (found) {
                                matchingOption = found;
                                break;
                            }
                        }
                    } else {
                        // Regular checkbox
                        matchingOption = filterDetails.options?.find(opt =>
                            opt.value === value || opt.description === value
                        );
                    }
                    console.log(`Processing single value: "${value}", matching option:`, matchingOption);
                    if (matchingOption) {
                        // Check if the matched option is a range (like "13-19") and we have minmax capability
                        if(matchingOption) {
                            // It's a regular checkbox value
                            if (!tempSelectedFilters[filterFor][filterName]) {
                                tempSelectedFilters[filterFor][filterName] = [];
                            }
                            tempSelectedFilters[filterFor][filterName].push(matchingOption.value);
                        }
                        else if (filterDetails.minmax && (matchingOption.description.includes('-') || matchingOption.value.includes('-'))) {
                            // Parse the range from either value or description
                            let rangeStr = matchingOption.description.includes('-') ?
                                matchingOption.description : matchingOption.value;

                            // Extract min/max from the range
                            if (rangeStr.includes('-')) {
                                const [minVal, maxVal] = rangeStr.split('-');
                                const min = Number(minVal);
                                const max = Number(maxVal.replace('+', '')); // Handle cases like "56+"

                                tempMinMaxValues[filterFor][filterName] = {
                                    min: !isNaN(min) ? min : null,
                                    max: !isNaN(max) ? max : null
                                };
                            }
                        }
                    }
                    else if (filterDetails.minmax && (value.includes('-') || value.includes('+'))) {
                        // It's a min-max range value directly specified
                        let min = null;
                        let max = null;

                        if (value.includes('-')) {
                            const [minVal, maxVal] = value.split('-');
                            min = Number(minVal);
                            max = Number(maxVal);
                        } else if (value.includes('+')) {
                            min = Number(value.replace('+', ''));
                        } else {
                            // Just a number - treat as min
                            min = Number(value);
                        }

                        tempMinMaxValues[filterFor][filterName] = {
                            min: !isNaN(min) ? min : null,
                            max: !isNaN(max) ? max : null
                        };
                    }
                    // If no matching option or range, but filter allows enter values, create a pill
                    else if (filterDetails.enterValue) {
                        if (!tempPillValues[filterFor][filterName]) {
                            tempPillValues[filterFor][filterName] = [];
                        }
                        // Add as a pill value
                        if (!tempPillValues[filterFor][filterName].includes(value)) {
                            tempPillValues[filterFor][filterName].push(value);
                        }
                    }
                }
            }
            else if (filterDetails.type === 'enter-value' || filterDetails.enterValue) {
                // Handle comma-separated pill values
                if (value.includes(',')) {
                    const values = value.split(',');
                    tempPillValues[filterFor][filterName] = values;
                } else {
                    tempPillValues[filterFor][filterName] = [value];
                }
            }
        });        // Update filter counts based on the categories with filters
        // Process each filterFor (creator/audience) separately
        ['creator', 'audience'].forEach(filterFor => {
            Object.keys(filtersByCategory[filterFor]).forEach(categoryName => {
                // Store count with category name as key, separated by tab
                tempFilterCounts[filterFor][categoryName] = filtersByCategory[filterFor][categoryName].size;
            });
        });        // Update all state at once
        setSelectedFilters(tempSelectedFilters);
        setPillValues(tempPillValues);
        setMinMaxValues(tempMinMaxValues);
        setFilterCounts(tempFilterCounts);
    };


    // Close popup if clicked outside
    useEffect(() => {
        const handleClickOutside = (e) => {
            if (popupRef.current && !popupRef.current.contains(e.target)) {
                onClose();
            }
        };
        document.addEventListener('mousedown', handleClickOutside);
        return () => document.removeEventListener('mousedown', handleClickOutside);
    }, [onClose]);

    // // Update tab filter counts whenever filters change
    // useEffect(() => {
    //     const creatorCount = calculateTabFilterCount('creator');
    //     const audienceCount = calculateTabFilterCount('audience');

    //     setTabFilterCounts({
    //         creator: creatorCount,
    //         audience: audienceCount
    //     });
    // }, [calculateTabFilterCount]);

    // Function to toggle filter collapse state
    const toggleFilterCollapse = (filterName) => {
        setCollapsedFilters(prev => ({
            ...prev,
            [filterName]: !prev[filterName]
        }));
    };

    // Icon mapping function
    const getIcon = (iconName) => {
        switch (iconName) {
            case 'gender-icon': return <BsGenderMale className="w-5 h-5" />;
            case 'age-icon': return <BsCalendarDate className="w-5 h-5" />;
            case 'location-icon': return <IoLocationOutline className="w-5 h-5" />;
            case 'language-icon': return <BsTranslate className="w-5 h-5" />;
            case 'follower-icon': return <FiUsers className="w-5 h-5" />;
            case 'likes-icon': return <AiOutlineHeart className="w-5 h-5" />;
            case 'comments-icon': return <VscComment className="w-5 h-5" />;
            case 'engagement-icon': return <IoStatsChart className="w-5 h-5" />;
            case 'reel-icon': return <BiWorld className="w-5 h-5" />;
            case 'growth-icon': return <BiTrendingUp className="w-5 h-5" />;
            case 'sponsored-icon': return <FiUsers className="w-5 h-5" />;
            case 'category-icon': return <MdOutlineCategory className="w-5 h-5" />;
            case 'keywords-icon': return <FaRegKeyboard className="w-5 h-5" />;
            case 'hashtags-icon': return <BiHash className="w-5 h-5" />;
            case 'mentions-icon': return <FaRegKeyboard className="w-5 h-5" />;
            case 'verification-icon': return <MdOutlineVerified className="w-5 h-5" />;
            case 'creatorverse-icon': return <IoStatsChart className="w-5 h-5" />;
            case 'interests-icon': return <IoStatsChart className="w-5 h-5" />;
            case 'brand-icon': return <IoStatsChart className="w-5 h-5" />;
            case 'official-icon': return <MdOutlineVerified className="w-5 h-5" />;
            case 'subscribers-icon': return <FiUsers className="w-5 h-5" />;
            default: return <div className="w-5 h-5" />;
        }
    };

    // Handler functions
    function handleFilterChange(filterName, value) {
        // Check if there was a previous value
        const previousValue = selectedFilters[activeTab]?.[filterName];
        const hasExistingValue = previousValue !== undefined && previousValue !== value;

        // Clear any min/max values for this filter when selecting a radio button
        setMinMaxValues(prev => {
            const currentTabMinMax = prev[activeTab];
            if (currentTabMinMax && currentTabMinMax[filterName]) {
                const { [filterName]: _, ...rest } = currentTabMinMax;
                return {
                    ...prev,
                    [activeTab]: rest
                };
            }
            return prev;
        });
        setSelectedFilters(prev => {
            // Check if this is a special value like "any" that should clear the filter
            if (value === "any" || value === null || value === undefined) {
                const currentTabFilters = { ...prev[activeTab] };
                // Remove the filterName property entirely
                const { [filterName]: _, ...rest } = currentTabFilters;
                return {
                    ...prev,
                    [activeTab]: rest
                };
            } else {
                // Otherwise update with the new value
                return {
                    ...prev,
                    [activeTab]: {
                        ...prev[activeTab],
                        [filterName]: value
                    }
                };
            }
        });
        // Find the category that contains this filter
        const category = filterOptions.find(option =>
            option.optionFor === activeTab &&
            option.channel === selectedChannel &&
            option.filters.some(filter => filter.name === filterName)
        )?.optionName;

        if (category) {
            // Check if we have any existing values of any type for this filter
            const hasMinMaxValues = minMaxValues[activeTab]?.[filterName] !== undefined;
            // Check if we're selecting a value that clears the filter (like "any")
            const isClearingFilter = value === "any" || value === null || value === undefined;

            // If we're clearing a previously selected value, decrement the count
            if (isClearingFilter && hasExistingValue && !hasMinMaxValues) {
                setFilterCounts(prev => {
                    // Only decrease if we have a count in the current tab
                    if (prev[activeTab] && prev[activeTab][category]) {
                        const tabCounts = { ...prev[activeTab] };
                        const newCount = tabCounts[category] - 1;

                        if (newCount <= 0) {
                            const { [category]: _, ...rest } = tabCounts;
                            return { ...prev, [activeTab]: rest };
                        }
                        return {
                            ...prev,
                            [activeTab]: { ...tabCounts, [category]: newCount }
                        };
                    }
                    return prev;
                });

            }
            // For making a new selection when none was there before            
            else if (!hasExistingValue && !hasMinMaxValues && !isClearingFilter) {
                setFilterCounts(prev => ({
                    ...prev,
                    [activeTab]: {
                        ...prev[activeTab],
                        [category]: (prev[activeTab][category] || 0) + 1
                    }
                }));

            }
            // If we're just changing the selected radio value, count stays the same
        }
    }

    function handleCheckboxChange(filterName, value) {
        // Store whether min/max values existed before clearing them
        const hadMinMaxBefore = minMaxValues[activeTab]?.[filterName] !== undefined;

        // Clear any min/max values for this filter
        setMinMaxValues(prev => {
            const currentTabMinMax = prev[activeTab];

            if (currentTabMinMax && currentTabMinMax[filterName]) {
                const { [filterName]: _, ...rest } = currentTabMinMax;
                return {
                    ...prev,
                    [activeTab]: rest
                };
            }
            return prev;
        });

        // Check if the value is already selected before we update
        const isSelected = Array.isArray(selectedFilters[activeTab]?.[filterName]) &&
            selectedFilters[activeTab][filterName].includes(value);

        // Get current values for the active tab
        const currentValues = Array.isArray(selectedFilters[activeTab]?.[filterName])
            ? selectedFilters[activeTab][filterName]
            : [];

        // Calculate updated values
        const updatedValues = isSelected
            ? currentValues.filter(item => item !== value)
            : [...currentValues, value];
        // Will this be the last checkbox being unchecked?
        const willBeEmpty = isSelected && updatedValues.length === 0;

        setSelectedFilters(prev => {
            // If the resulting array will be empty, completely remove the property
            if (willBeEmpty) {
                const currentTabFilters = { ...prev[activeTab] };
                // Remove the filterName property entirely
                const { [filterName]: _, ...rest } = currentTabFilters;
                return {
                    ...prev,
                    [activeTab]: rest
                };
            } else {
                // Otherwise, update with the new values
                return {
                    ...prev,
                    [activeTab]: {
                        ...prev[activeTab],
                        [filterName]: updatedValues
                    }
                };
            }
        });// Find the category for this filter
        const category = filterOptions.find(option =>
            option.optionFor === activeTab &&
            option.channel === selectedChannel &&
            option.filters.some(filter => filter.name === filterName)
        )?.optionName; if (category) {
            setFilterCounts(prev => {
                // If we're checking a box and there were no previous selections of any kind
                if (!isSelected && currentValues.length === 0 && !hadMinMaxBefore) {                    // First selection, increment count

                    return {
                        ...prev,
                        [activeTab]: {
                            ...prev[activeTab],
                            [category]: (prev[activeTab][category] || 0) + 1
                        }
                    };
                }
                // If we're unchecking the last box and there were no min/max values
                else if (willBeEmpty && !hadMinMaxBefore) {                    // Create a new copy of the tab's filter counts
                    const newTabCounts = { ...prev[activeTab] };

                    // If this is the last filter in the category, remove it
                    if (newTabCounts[category] === 1) {
                        delete newTabCounts[category];
                    } else if (newTabCounts[category] > 1) {
                        // Otherwise decrement the count
                        newTabCounts[category]--;
                    }

                    return {
                        ...prev,
                        [activeTab]: newTabCounts
                    };
                }
                // Otherwise keep the count the same
                return prev;
            });
        }
    }

    function handleKeyDown(e, filterName) {
        if (e.key === 'Enter' && e.target.value.trim()) {
            const newValue = e.target.value.trim();

            // Get current pill values for this filter
            const currentPillValues = pillValues[activeTab]?.[filterName] || [];

            // Check if this value already exists to avoid duplicates
            if (currentPillValues.includes(newValue)) {
                // Clear the input field if it's a duplicate
                e.target.value = '';
                return;
            }

            // Check if this is the first pill being added for this filter
            const isFirstPill = currentPillValues.length === 0;

            // Add the new pill value
            setPillValues(prev => {
                const currentTabPills = prev[activeTab] || {};
                return {
                    ...prev,
                    [activeTab]: {
                        ...currentTabPills,
                        [filterName]: [...(currentTabPills[filterName] || []), newValue]
                    }
                };
            });

            // Check if this filter has checkbox type - in this case we want to keep the checkbox values
            // Get the filter details
            const allCategories = filterOptions.filter(
                option => option.optionFor === activeTab && option.channel === selectedChannel
            );
            let filterDetails = null;
            for (const category of allCategories) {
                const found = category.filters.find(f => f.name === filterName);
                if (found) {
                    filterDetails = found;
                    break;
                }
            }

            // Only remove from selectedFilters if this is not a checkbox filter with enterValue
            if (!filterDetails || !(filterDetails.type === 'checkbox' && filterDetails.enterValue)) {
                // For non-checkbox filters or filters without enterValue, we want to remove from selectedFilters
                // since the pills are the source of truth. This prevents duplicates 
                // when we apply the filter.
                setSelectedFilters(prev => {
                    const currentTabFilters = { ...prev[activeTab] || {} };

                    // Remove the filter entry if it exists (since we're tracking in pillValues now)
                    if (currentTabFilters[filterName]) {
                        const { [filterName]: _, ...rest } = currentTabFilters;
                        return {
                            ...prev,
                            [activeTab]: rest
                        };
                    }
                    return prev;
                });
            }
            // For checkbox filters with enterValue, we keep both checkbox values and pill values

            // Update filter count - only increment for the first pill
            if (isFirstPill) {
                const category = filterOptions.find(option =>
                    option.optionFor === activeTab &&
                    option.channel === selectedChannel &&
                    option.filters.some(filter => filter.name === filterName)
                )?.optionName; if (category) {
                    setFilterCounts(prev => ({
                        ...prev,
                        [activeTab]: {
                            ...prev[activeTab],
                            [category]: (prev[activeTab][category] || 0) + 1
                        }
                    }));

                }
            }

            // Clear the input field
            e.target.value = '';
        }
    }

    function removePillValue(filterName, value) {
        // Get current pill values before removal to check if this is the last pill
        const currentPills = pillValues[activeTab]?.[filterName] || [];
        const willBeLastPill = currentPills.length === 1;

        // Remove the pill value
        setPillValues(prev => {
            // Make sure we're updating the correct tab's pills
            const currentTabPills = prev[activeTab] || {};
            const updatedValues = Array.isArray(currentTabPills[filterName])
                ? currentTabPills[filterName].filter(item => item !== value)
                : [];

            if (updatedValues.length === 0) {
                // If all pills are removed, remove the property entirely
                const { [filterName]: _, ...restFilters } = currentTabPills;
                return {
                    ...prev,
                    [activeTab]: restFilters
                };
            } else {
                // Otherwise keep the array with remaining values
                return {
                    ...prev,
                    [activeTab]: {
                        ...currentTabPills,
                        [filterName]: updatedValues
                    }
                };
            }
        });

        // Check if this filter has checkbox type - in this case we want to keep the checkbox values
        // Get the filter details
        const allCategories = filterOptions.filter(
            option => option.optionFor === activeTab && option.channel === selectedChannel
        );
        let filterDetails = null;
        for (const category of allCategories) {
            const found = category.filters.find(f => f.name === filterName);
            if (found) {
                filterDetails = found;
                break;
            }
        }

        // Only update selectedFilters if this is not a checkbox filter with enterValue
        if (!filterDetails || !(filterDetails.type === 'checkbox' && filterDetails.enterValue)) {
            // Update selected filters
            setSelectedFilters(prev => {
                // Make sure we're updating the correct tab's filters
                const currentTabFilters = prev[activeTab] || {};
                const updatedValues = Array.isArray(currentTabFilters[filterName])
                    ? currentTabFilters[filterName].filter(item => item !== value)
                    : [];

                if (updatedValues.length === 0) {
                    // If all values are removed, remove the property entirely
                    const { [filterName]: _, ...restFilters } = currentTabFilters;
                    return {
                        ...prev,
                        [activeTab]: restFilters
                    };
                } else {
                    // Otherwise keep the array with remaining values
                    return {
                        ...prev,
                        [activeTab]: {
                            ...currentTabFilters,
                            [filterName]: updatedValues
                        }
                    };
                }
            });
        }
        // For checkbox filters with enterValue, we keep the checkbox values

        // Only update filter count if this is the last pill being removed
        if (willBeLastPill) {
            // Get the number of checkbox values for this filter
            const hasCheckboxValues = selectedFilters[activeTab]?.[filterName] &&
                Array.isArray(selectedFilters[activeTab][filterName]) &&
                selectedFilters[activeTab][filterName].length > 0;

            // Only update filter count if there are no checkbox values
            if (!hasCheckboxValues) {
                const category = filterOptions.find(option =>
                    option.optionFor === activeTab &&
                    option.channel === selectedChannel &&
                    option.filters.some(filter => filter.name === filterName)
                )?.optionName; if (category) {
                    setFilterCounts(prev => {
                        // If we have a count in this tab, decrement it                        
                        if (prev[activeTab] && prev[activeTab][category]) {
                            const tabCounts = { ...prev[activeTab] };
                            const newCount = tabCounts[category] - 1;
                            if (newCount <= 0) {
                                const { [category]: _, ...rest } = tabCounts;
                                return { ...prev, [activeTab]: rest };
                            }
                            return { ...prev, [activeTab]: { ...tabCounts, [category]: newCount } };
                        }
                        return prev;
                    });

                }
            }
        }
    }
    
    function handleClear() {
        setSelectedFilters({
            creator: {},
            audience: {}
        });
        setFilterCounts({
            creator: {},
            audience: {}
        });
        setPillValues({
            creator: {},
            audience: {}
        });
        setMinMaxValues({
            creator: {},
            audience: {}
        });
    }

    function handleApply() {
        if (onApplyFilters) {
            // Format the selected filters as required
            const formattedFilters = [];

            // Process filters for both creator and audience tabs
            ['creator', 'audience'].forEach(filterFor => {
                // Get all filter options for this tab and channel
                const allCategories = filterOptions.filter(
                    option => option.optionFor === filterFor && option.channel === selectedChannel
                );

                // Collect all filters (from pillValues, selectedFilters, minMaxValues)
                // and combine their values before sending
                const combinedFilterValues = {};

                // 1. Process pill values first
                const tabPills = pillValues[filterFor];
                Object.entries(tabPills || {}).forEach(([filterName, values]) => {
                    if (Array.isArray(values) && values.length > 0) {
                        if (!combinedFilterValues[filterName]) {
                            combinedFilterValues[filterName] = {
                                pillValues: values,
                                checkboxValues: [],
                                minMaxValues: null
                            };
                        } else {
                            combinedFilterValues[filterName].pillValues = values;
                        }
                    }
                });

                // 2. Process checkbox/radio button selections
                const tabFilters = selectedFilters[filterFor];
                Object.entries(tabFilters || {}).forEach(([key, values]) => {
                    // Check if this is a radio button key (with format filterName_radioGroup_subOptionName)
                    const isRadioKey = key.includes('_radioGroup_');

                    // Extract the actual filterName without the radio group suffix
                    const filterName = isRadioKey ? key.split('_radioGroup_')[0] : key;

                    // Find the filter details
                    let filter = null;
                    for (const category of allCategories) {
                        const foundFilter = category.filters.find(f => f.name === filterName);
                        if (foundFilter) {
                            filter = foundFilter;
                            break;
                        }
                    }

                    if (filter) {
                        if (!combinedFilterValues[filterName]) {
                            combinedFilterValues[filterName] = {
                                pillValues: [],
                                checkboxValues: isRadioKey ? [values] : (Array.isArray(values) ? [...values] : values),
                                minMaxValues: null
                            };
                        } else {
                            // For radio buttons, add to the existing array
                            if (isRadioKey) {
                                const currentValues = Array.isArray(combinedFilterValues[filterName].checkboxValues)
                                    ? combinedFilterValues[filterName].checkboxValues
                                    : [];
                                combinedFilterValues[filterName].checkboxValues = [...currentValues, values];
                            }
                            // For normal checkboxes, replace or update
                            else {
                                combinedFilterValues[filterName].checkboxValues = Array.isArray(values) ? [...values] : values;
                            }
                        }
                    }
                });

                // 3. Process min-max values
                const tabMinMax = minMaxValues[filterFor];
                Object.entries(tabMinMax || {}).forEach(([filterName, values]) => {
                    if (values && (values.min !== undefined || values.max !== undefined)) {
                        if (!combinedFilterValues[filterName]) {
                            combinedFilterValues[filterName] = {
                                pillValues: [],
                                checkboxValues: [],
                                minMaxValues: values
                            };
                        } else {
                            combinedFilterValues[filterName].minMaxValues = values;
                        }
                    }
                });

                // Now format each filter for output
                Object.entries(combinedFilterValues).forEach(([filterName, values]) => {
                    // Find the filter details again
                    let filter = null;
                    for (const category of allCategories) {
                        const foundFilter = category.filters.find(f => f.name === filterName);
                        if (foundFilter) {
                            filter = foundFilter;
                            break;
                        }
                    }

                    if (!filter) return;

                    // For special case where filter type is checkbox AND enterValue is true,
                    // we need to combine both checkbox values and pill values
                    if ((filter.type === 'checkbox' || filter.type === 'multilevel-checkbox') && filter.enterValue &&
                        values.checkboxValues && values.pillValues &&
                        (Array.isArray(values.checkboxValues) ? values.checkboxValues.length > 0 : true) &&
                        values.pillValues.length > 0) {

                        // Combine checkbox values and pill values into one string
                        const allValues = [
                            ...(Array.isArray(values.checkboxValues) ? values.checkboxValues : [values.checkboxValues]),
                            ...values.pillValues
                        ];

                        // Add as a single entry with all combined values
                        formattedFilters.push({
                            channel: selectedChannel,
                            filter: filterName,
                            value: allValues.join(','),
                            filterFor: filterFor,
                        });
                    } else {
                        // Handle each type of value separately

                        // Handle min-max values
                        if (values.minMaxValues && (values.minMaxValues.min !== undefined || values.minMaxValues.max !== undefined)) {
                            // Format the min-max value as "{min}-{max}"
                            let rangeValue = '';

                            if (values.minMaxValues.min !== undefined && values.minMaxValues.max !== undefined) {
                                rangeValue = `${values.minMaxValues.min}-${values.minMaxValues.max}`;
                            } else if (values.minMaxValues.min !== undefined) {
                                rangeValue = `${values.minMaxValues.min}+`;
                            } else if (values.minMaxValues.max !== undefined) {
                                rangeValue = `0-${values.minMaxValues.max}`;
                            }

                            if (rangeValue) {
                                formattedFilters.push({
                                    channel: selectedChannel,
                                    filter: filterName,
                                    value: rangeValue,
                                    filterFor: filterFor,
                                });
                            }
                        }
                        // Handle pill values
                        else if (values.pillValues && values.pillValues.length > 0) {
                            const combinedValue = values.pillValues.join(',');
                            formattedFilters.push({
                                channel: selectedChannel,
                                filter: filterName,
                                value: combinedValue,
                                filterFor: filterFor,
                            });
                        }
                        // Handle checkbox/radio values
                        else if (values.checkboxValues) {
                            // Special handling for multilevel-checkbox type
                            if (filter.type === 'multilevel-checkbox') {
                                // Get all the subOptions groups for this filter
                                const subOptionGroups = filter.options || [];

                                // Create a map to group values by subOptionName
                                const groupedValues = {};

                                // Process each subOption group
                                subOptionGroups.forEach(subOption => {
                                    const subOptionName = subOption.subOptionName;
                                    const subOptionType = subOption.subOptionType || 'checkbox';

                                    // Initialize the group if it doesn't exist
                                    if (!groupedValues[subOptionName]) {
                                        groupedValues[subOptionName] = [];
                                    }

                                    // Check if we have values for this subOption group
                                    if (Array.isArray(values.checkboxValues)) {
                                        // For checkboxes - find values that match this subOption group's options
                                        const subOptionValues = subOption.subOptions.map(opt => opt.value);
                                        const matchingValues = values.checkboxValues.filter(val =>
                                            subOptionValues.includes(val)
                                        );

                                        // Add all matching values to this group
                                        if (matchingValues.length > 0) {
                                            groupedValues[subOptionName].push(...matchingValues);
                                        }
                                    } else if (subOptionType === 'radio-button') {
                                        // For radio-buttons
                                        // Check if the radio button key is in the selectedFilters
                                        const radioKey = `${filterName}_radioGroup_${subOptionName}`;
                                        const radioValue = selectedFilters[filterFor]?.[radioKey];

                                        if (radioValue) {
                                            groupedValues[subOptionName].push(radioValue);
                                        }
                                    }
                                });
                                // Convert the grouped map to the structured array format with comma-separated values
                                const structuredValues = Object.entries(groupedValues)
                                    .filter(([, values]) => values.length > 0) // Only include groups with values
                                    .map(([subOptionName, values]) => ({
                                        filter: subOptionName,
                                        value: values.join(', ') // Join multiple values with comma and space
                                    }));

                                // Only add filter if we have some structured values
                                if (structuredValues.length > 0) {
                                    formattedFilters.push({
                                        channel: selectedChannel,
                                        filter: filterName,
                                        value: structuredValues,
                                        filterFor: filterFor,
                                    });
                                }
                            }
                            // Original handling for regular checkbox and radio button filters
                            else if (Array.isArray(values.checkboxValues) && values.checkboxValues.length > 0) {
                                // For checkbox filters, combine all values into a comma-separated string
                                const combinedValue = values.checkboxValues.join(',');

                                // Add as a single entry with the combined value
                                formattedFilters.push({
                                    channel: selectedChannel,
                                    filter: filterName,
                                    value: combinedValue,
                                    filterFor: filterFor,
                                });
                            } else if (values.checkboxValues !== null && values.checkboxValues !== undefined && !Array.isArray(values.checkboxValues)) {
                                // For radio button selections (single value)
                                formattedFilters.push({
                                    channel: selectedChannel,
                                    filter: filterName,
                                    value: values.checkboxValues,
                                    filterFor: filterFor,
                                });
                            }
                        }
                    }
                });
            });

            onApplyFilters(formattedFilters);
        }

        if (onClose) {
            onClose();
        }
    }

    function handleMinMaxChange(filterName, type, value) {
        const numValue = value === '' ? null : Number(value);

        // Update the min/max value for the filter
        setMinMaxValues(prev => {
            const currentValues = prev[activeTab]?.[filterName] || {};

            // If both values will be empty after this update, remove the entire entry
            const otherType = type === 'min' ? 'max' : 'min';
            const otherValue = currentValues[otherType];

            if (numValue === null && (otherValue === null || otherValue === undefined)) {
                // Both values are empty, remove the entire filter entry
                const updated = { ...prev };
                if (updated[activeTab]) {
                    const { [filterName]: _, ...rest } = updated[activeTab];
                    updated[activeTab] = rest;
                }
                return updated;
            }

            // Update the min or max value
            return {
                ...prev,
                [activeTab]: {
                    ...prev[activeTab],
                    [filterName]: {
                        ...currentValues,
                        [type]: numValue
                    }
                }
            };
        });

        // If there's a valid min or max value, clear any checkbox selections for this filter
        if (numValue !== null) {
            // Clear checkbox selections for this filter
            setSelectedFilters(prev => {
                const currentTabFilters = { ...prev[activeTab] };

                // Remove the checkbox selections for this filter
                if (currentTabFilters[filterName]) {
                    const { [filterName]: _, ...rest } = currentTabFilters;
                    return {
                        ...prev,
                        [activeTab]: rest
                    };
                }
                return prev;
            });            // Update filter count - we only need one count for min-max
            const category = filterOptions.find(option =>
                option.optionFor === activeTab &&
                option.channel === selectedChannel &&
                option.filters.some(filter => filter.name === filterName)
            )?.optionName;

            if (category) {
                // Check if this is the first value being set for this filter
                // We need to check both min-max and regular filter values
                const hasMinMaxValue = minMaxValues[activeTab]?.[filterName]?.min !== undefined ||
                    minMaxValues[activeTab]?.[filterName]?.max !== undefined;
                const hasCheckboxValue = selectedFilters[activeTab]?.[filterName] !== undefined;
                // Only increment if this is the first value of any type for this filter                
                if (!hasMinMaxValue && !hasCheckboxValue) {
                    setFilterCounts(prev => ({
                        ...prev,
                        [activeTab]: {
                            ...prev[activeTab],
                            [category]: (prev[activeTab][category] || 0) + 1
                        }
                    }));

                }
            }
        }
        else {
            // When clearing a value, check if both min and max are now empty
            const currentMinMax = minMaxValues[activeTab]?.[filterName] || {};
            const otherType = type === 'min' ? 'max' : 'min';
            const otherValue = currentMinMax[otherType];

            // If both values are now empty (or null), we should check whether to decrement the count
            if (otherValue === null || otherValue === undefined) {
                const category = filterOptions.find(option =>
                    option.optionFor === activeTab &&
                    option.channel === selectedChannel &&
                    option.filters.some(filter => filter.name === filterName)
                )?.optionName;

                // Check if we have checkbox values for this filter - if we do, don't decrement the count
                const hasCheckboxValues = selectedFilters[activeTab]?.[filterName] &&
                    Array.isArray(selectedFilters[activeTab][filterName]) &&
                    selectedFilters[activeTab][filterName].length > 0; if (category && !hasCheckboxValues) {
                        setFilterCounts(prev => {
                            // If we have a count in this tab, decrement it                        
                            if (prev[activeTab] && prev[activeTab][category]) {
                                const tabCounts = { ...prev[activeTab] };
                                const newCount = tabCounts[category] - 1;
                                if (newCount <= 0) {
                                    const { [category]: _, ...rest } = tabCounts;
                                    return { ...prev, [activeTab]: rest };
                                }
                                return { ...prev, [activeTab]: { ...tabCounts, [category]: newCount } };
                            }
                            return prev;
                        });
                    }
            }
        }
    }

    // Handle multilevel checkbox search
    function handleMultilevelSearch(filterName, searchText) {
        setMultilevelSearch(prev => ({
            ...prev,
            [activeTab]: {
                ...prev[activeTab],
                [filterName]: searchText
            }
        }));
    }

    // Toggle sub-option collapse state
    function toggleSubOptionCollapse(filterName, subOptionName) {
        const key = `${filterName}-${subOptionName}`;
        setCollapsedSubOptions(prev => ({
            ...prev,
            [key]: !prev[key]
        }));
    }

    // Handle multilevel checkbox change
    function handleMultilevelCheckboxChange(filterName, value) {
        // Similar to regular checkbox handling but for multilevel
        const isSelected = Array.isArray(selectedFilters[activeTab]?.[filterName]) &&
            selectedFilters[activeTab][filterName].includes(value);

        // Get current values for the active tab
        const currentValues = Array.isArray(selectedFilters[activeTab]?.[filterName])
            ? selectedFilters[activeTab][filterName]
            : [];

        // Check if any radio button groups for this filter have selections
        // This is to prevent double counting when both checkbox and radio selections exist
        const hasRadioSelections = Object.keys(selectedFilters[activeTab] || {}).some(key =>
            key.startsWith(`${filterName}_radioGroup_`)
        );

        // Calculate updated values
        const updatedValues = isSelected
            ? currentValues.filter(item => item !== value)
            : [...currentValues, value];

        // Will this be the last checkbox being unchecked?
        const willBeEmpty = isSelected && updatedValues.length === 0;

        // Store whether min/max values existed
        const hadMinMaxBefore = minMaxValues[activeTab]?.[filterName] !== undefined;

        setSelectedFilters(prev => {
            // If the resulting array will be empty, completely remove the property
            if (willBeEmpty) {
                const currentTabFilters = { ...prev[activeTab] };
                // Remove the filterName property entirely
                const { [filterName]: _, ...rest } = currentTabFilters;
                return {
                    ...prev,
                    [activeTab]: rest
                };
            } else {
                // Otherwise, update with the new values
                return {
                    ...prev,
                    [activeTab]: {
                        ...prev[activeTab],
                        [filterName]: updatedValues
                    }
                };
            }
        });

        // Find the category for this filter
        const category = filterOptions.find(option =>
            option.optionFor === activeTab &&
            option.channel === selectedChannel &&
            option.filters.some(filter => filter.name === filterName)
        )?.optionName;

        if (category) {
            setFilterCounts(prev => {                // If we're checking a box and there were no previous checkbox selections
                // AND no radio button selections for this filter AND no min/max values
                if (!isSelected && currentValues.length === 0 && !hadMinMaxBefore && !hasRadioSelections) {
                    console.log("ye wlaa hai for multilevelcheckbox", currentValues)
                    // First selection for this filter, increment count
                    const newCounts = {
                        ...prev,
                        [activeTab]: {
                            ...prev[activeTab],
                            [category]: (prev[activeTab][category] || 0) + 1
                        }
                    };

                    return newCounts;
                }                // If we're unchecking the last checkbox and there were no min/max values
                // AND no radio button selections for this filter
                else if (willBeEmpty && !hadMinMaxBefore && !hasRadioSelections) {
                    // Last value removed for this filter, remove the category count
                    const newCounts = { ...prev };
                    if (newCounts[activeTab] && newCounts[activeTab][category] === 1) {
                        const tabCounts = { ...newCounts[activeTab] };
                        delete tabCounts[category];
                        newCounts[activeTab] = tabCounts;

                    } else if (newCounts[activeTab] && newCounts[activeTab][category] > 1) {
                        newCounts[activeTab] = {
                            ...newCounts[activeTab],
                            [category]: newCounts[activeTab][category] - 1
                        };
                    }
                    return newCounts;
                }
                // No change to counts for other scenarios
                return prev;
            });
        }
    }    
    
    // Function to handle radio button changes for multilevel filters    
    function handleMultilevelRadioChange(filterName, value, subOptionName) {
        // For radio buttons, we need to store them with a modified key to separate
        // them from checkbox values for the same filter
        // We'll use the format: filterName_radioGroup_subOptionName
        const radioKey = `${filterName}_radioGroup_${subOptionName}`;

        // Check if we're changing an existing value or adding a new one
        const previousValue = selectedFilters[activeTab]?.[radioKey];
        const isNewSelection = previousValue === undefined;
        const isClearing = value === "any" || value === null || value === undefined;

        // Check if any other subOptions of this filter are already selected
        // This is important to prevent double counting when multiple subOptions are selected
        const hasOtherSelections = Object.keys(selectedFilters[activeTab] || {}).some(key =>
            // Check for regular filter entry
            (key === filterName && Array.isArray(selectedFilters[activeTab][key]) && selectedFilters[activeTab][key].length > 0) ||
            // Check for other radio groups from same filter
            (key !== radioKey && key.startsWith(`${filterName}_radioGroup_`))
        );

        // Clear any min/max values for this filter when selecting a radio button
        setMinMaxValues(prev => {
            const currentTabMinMax = prev[activeTab];
            if (currentTabMinMax && currentTabMinMax[radioKey]) {
                const { [radioKey]: _, ...rest } = currentTabMinMax;
                return {
                    ...prev,
                    [activeTab]: rest
                };
            }
            return prev;
        });

        setSelectedFilters(prev => {
            // Check if this is a special value like "any" that should clear the filter
            if (isClearing) {
                const currentTabFilters = { ...prev[activeTab] };
                // Remove the radioKey property entirely
                const { [radioKey]: _, ...rest } = currentTabFilters;
                return {
                    ...prev,
                    [activeTab]: rest
                };
            } else {
                // Otherwise update with the new value (not in an array as it's a radio button)
                return {
                    ...prev,
                    [activeTab]: {
                        ...prev[activeTab],
                        [radioKey]: value
                    }
                };
            }
        });

        // Find the category for this filter
        const category = filterOptions.find(option =>
            option.optionFor === activeTab &&
            option.channel === selectedChannel &&
            option.filters.some(filter => filter.name === filterName)
        )?.optionName;

        if (category) {
            setFilterCounts(prev => {
                // If this is a new selection (not changing existing selection) AND no other subOptions are selected
                if (isNewSelection && !isClearing && !hasOtherSelections) {
                    // This is the first selection for this filter, increment count
                    const newCounts = {
                        ...prev,
                        [activeTab]: {
                            ...prev[activeTab],
                            [category]: (prev[activeTab][category] || 0) + 1
                        }
                    };

                    return newCounts;
                }                // If we're clearing a selection AND no checkbox selections exist for this filter
                else if (isClearing && previousValue !== undefined && !Object.keys(selectedFilters[activeTab] || {}).some(key =>
                    key === filterName && Array.isArray(selectedFilters[activeTab][key]) && selectedFilters[activeTab][key].length > 0)) {
                    // Value removed and no other selections for this filter, decrease the category count
                    const newCounts = { ...prev };
                    if (newCounts[activeTab] && newCounts[activeTab][category] === 1) {
                        const tabCounts = { ...newCounts[activeTab] };
                        delete tabCounts[category];
                        newCounts[activeTab] = tabCounts;

                    } else if (newCounts[activeTab] && newCounts[activeTab][category] > 1) {
                        newCounts[activeTab] = {
                            ...newCounts[activeTab],
                            [category]: newCounts[activeTab][category] - 1
                        };
                    }
                    return newCounts;
                }
                // No change to counts for changing existing selection
                return prev;
            });
        }
    }

    // Helper function to check if a filter has options matching the search term
    // eslint-disable-next-line no-unused-vars
    function hasMatchingOptions(filter, searchTerm) {
        // Check checkbox and radio options
        if ((filter.type === 'checkbox' || filter.type === 'radio-button') && filter.options) {
            return filter.options.some(opt =>
                (opt.label && opt.label.toLowerCase().includes(searchTerm)) ||
                (opt.value && opt.value.toLowerCase().includes(searchTerm)) ||
                (opt.description && opt.description.toLowerCase().includes(searchTerm))
            );
        }

        // Check multilevel options
        if (filter.type === 'multilevel-checkbox' && filter.options) {
            return filter.options.some(subOption => {
                // Check subOption name
                if (subOption.subOptionName && subOption.subOptionName.toLowerCase().includes(searchTerm)) {
                    return true;
                }

                // Check subOptions
                if (subOption.subOptions) {
                    return subOption.subOptions.some(opt =>
                        (opt.label && opt.label.toLowerCase().includes(searchTerm)) ||
                        (opt.value && opt.value.toLowerCase().includes(searchTerm)) ||
                        (opt.description && opt.description.toLowerCase().includes(searchTerm))
                    );
                }
                return false;
            });
        }

        return false;
    }

    // New function for checkbox search
    function handleCheckboxSearch(filterName, searchText) {
        // Store the search text in multilevelSearch state for this specific filter
        setMultilevelSearch(prev => ({
            ...prev,
            [activeTab]: {
                ...prev[activeTab],
                [filterName]: searchText
            }
        }));
    }

    // Function to handle "Select All" checkbox for sub-options
    function handleSelectAllSubOptions(filterName, subOption, isChecked) {
        // Get the search term for filtering visible options
        const searchTermValue = multilevelSearch[activeTab]?.[filterName]?.toLowerCase() || '';

        // Get all visible sub-options based on search
        const visibleSubOptions = subOption.subOptions.filter(opt => {
            if (searchTermValue === '') return true;
            return (
                opt.label.toLowerCase().includes(searchTermValue) ||
                opt.value.toLowerCase().includes(searchTermValue) ||
                (opt.description && opt.description.toLowerCase().includes(searchTermValue))
            );
        });

        // Get values of all visible sub-options
        const visibleValues = visibleSubOptions.map(opt => opt.value);

        // Get current selected values
        const currentValues = Array.isArray(selectedFilters[activeTab]?.[filterName])
            ? [...selectedFilters[activeTab][filterName]]
            : [];

        // Store whether min/max values existed
        const hadMinMaxBefore = minMaxValues[activeTab]?.[filterName] !== undefined;

        // If radio buttons, select first option only if checked
        if (subOption.subOptionType === "radio-button") {
            if (isChecked && visibleValues.length > 0) {
                handleMultilevelRadioChange(filterName, visibleValues[0], subOption.subOptionName);
            } else if (!isChecked) {
                // Uncheck the radio button
                const radioKey = `${filterName}_radioGroup_${subOption.subOptionName}`;

                // Check if we had a value before
                const hadValueBefore = selectedFilters[activeTab]?.[radioKey] !== undefined;

                setSelectedFilters(prev => {
                    const currentTabFilters = { ...prev[activeTab] };
                    const { [radioKey]: _, ...rest } = currentTabFilters;
                    return {
                        ...prev,
                        [activeTab]: rest
                    };
                });

                // Handle filter count update if needed
                if (hadValueBefore) {
                    // Find the category for this filter
                    const category = filterOptions.find(option =>
                        option.optionFor === activeTab &&
                        option.channel === selectedChannel &&
                        option.filters.some(filter => filter.name === filterName)
                    )?.optionName;

                    if (category) {
                        setFilterCounts(prev => {
                            const newCounts = { ...prev };
                            if (newCounts[category] === 1) {
                                delete newCounts[category];
                            } else if (newCounts[category] > 1) {
                                newCounts[category]--;
                            }
                            return newCounts;
                        });
                    }
                }
            }
            // For radio buttons we return early
            if (subOption.subOptionType === "radio-button") {
                return;
            }
        }

        // For checkboxes, update based on check state
        let updatedValues;
        // Flag if this is the first selection or if all items are being removed
        const isFirstSelection = currentValues.length === 0 && isChecked;
        const willBeEmpty = !isChecked && currentValues.length > 0 &&
            currentValues.every(val => visibleValues.includes(val));

        if (isChecked) {
            // Add all visible values that aren't already selected
            updatedValues = [
                ...currentValues,
                ...visibleValues.filter(value => !currentValues.includes(value))
            ];
        } else {
            // Remove all visible values
            updatedValues = currentValues.filter(value => !visibleValues.includes(value));
        }

        // Update state
        setSelectedFilters(prev => {
            if (updatedValues.length === 0) {
                // Remove property if no values are selected
                const currentTabFilters = { ...prev[activeTab] };
                const { [filterName]: _, ...rest } = currentTabFilters;
                return {
                    ...prev,
                    [activeTab]: rest
                };
            } else {
                // Update with new values
                return {
                    ...prev,
                    [activeTab]: {
                        ...prev[activeTab],
                        [filterName]: updatedValues
                    }
                };
            }
        });
        // Find the category for this filter
        const filterCategory = filterOptions.find(option =>
            option.optionFor === activeTab &&
            option.channel === selectedChannel &&
            option.filters.some(filter => filter.name === filterName)
        )?.optionName;

        if (filterCategory) {
            setFilterCounts(prev => {
                // If this is the first selection and no min/max values                
                if (isFirstSelection && !hadMinMaxBefore) {
                    // New selection, increment count
                    const newCounts = {
                        ...prev,
                        [activeTab]: {
                            ...prev[activeTab],
                            [filterCategory]: (prev[activeTab][filterCategory] || 0) + 1
                        }
                    };

                    return newCounts;
                }
                // If all items are being removed and no min/max values                
                else if (willBeEmpty && !hadMinMaxBefore) {
                    // Value removed, decrease the category count
                    const newCounts = { ...prev };
                    if (newCounts[activeTab] && newCounts[activeTab][filterCategory] === 1) {
                        const tabCounts = { ...newCounts[activeTab] };
                        delete tabCounts[filterCategory];
                        newCounts[activeTab] = tabCounts;

                    } else if (newCounts[activeTab] && newCounts[activeTab][filterCategory] > 1) {
                        newCounts[activeTab] = {
                            ...newCounts[activeTab],
                            [filterCategory]: newCounts[activeTab][filterCategory] - 1
                        };
                    }
                    return newCounts;
                }
                // No change to counts for other scenarios
                return prev;
            });
        }
    }

    return (
        <div className=" absolute left-0 top-11.5 w-150 flex items-center justify-center bg-transparent bg-opacity-50 z-50"
            onClick={(e) => {
                e.stopPropagation();
            }}
            ref={popupRef}>
            <div className="bg-gray-900 rounded-lg w-full max-w-4xl h-fit max-h-[75vh] flex flex-col">
                {/* Header */}
                <div className="flex-shrink-0 flex justify-between items-center p-4 border-b border-gray-500">
                    <h2 className="text-white text-lg font-medium">Filters</h2>
                    <button
                        onClick={onClose}
                        className="text-gray-400 hover:text-white"
                    >
                        <FaTimes />
                    </button>
                </div>

                {/* Main content - with flex-grow to take available space */}
                <div className="flex-grow flex flex-col overflow-hidden">
                    {/* Tabs */}
                    {!hidetab &&
                        <div className="flex bg-gray-500 p-1.5 mx-4 mt-4 rounded-md">
                            <button
                                className={cn(
                                    "flex-1 py-2 px-4 rounded-md text-sm font-medium flex items-center justify-center",
                                    activeTab === 'creator'
                                        ? "bg-gray-400 text-gray-50"
                                        : "text-gray-300 hover:text-gray-100"
                                )} onClick={() => {
                                    setActiveTab('creator');
                                    // Forcing re-render of filter counts specific to this tab
                                    setSelectedCategory(selectedCategory);
                                }}
                            >
                                <span>Creator</span>
                                {tabFilterCounts.creator > 0 && (
                                    <span className="ml-2 text-brand-600 bg-brand-200 text-xs rounded-full w-5 h-5 flex items-center justify-center">
                                        {tabFilterCounts.creator}
                                    </span>
                                )}
                            </button>
                            <button
                                className={cn(
                                    "flex-1 py-2 px-4 rounded-md text-sm font-medium flex items-center justify-center",
                                    activeTab === 'audience'
                                        ? "bg-gray-400 text-gray-50"
                                        : "text-gray-300 hover:text-gray-100"
                                )} onClick={() => {
                                    setActiveTab('audience');
                                    // Forcing re-render of filter counts specific to this tab
                                    setSelectedCategory(selectedCategory);
                                }}
                            >
                                <span>Audience</span>
                                {tabFilterCounts.audience > 0 && (
                                    <span className="ml-2 text-brand-600 bg-brand-200 text-xs rounded-full w-5 h-5 flex items-center justify-center">
                                        {tabFilterCounts.audience}
                                    </span>
                                )}
                            </button>
                        </div>}

                    {/* Search */}
                    <div className="px-4 mt-4">
                        <div className="relative">
                            <input
                                type="text"
                                placeholder="Search"
                                value={searchTerm}
                                onChange={(e) => {
                                    const newSearchTerm = e.target.value;
                                    setSearchTerm(newSearchTerm);

                                    // If search term is being entered, we need to make sure the selected category
                                    // is still visible after filtering. If not, select a visible category.
                                    if (newSearchTerm) {
                                        const searchLower = newSearchTerm.toLowerCase();

                                        // Find visible categories after filtering
                                        const visibleCategories = filterOptions
                                            .filter(option => option.optionFor === activeTab && option.channel === selectedChannel)
                                            .filter(option => {
                                                // Check category name
                                                if (option.optionName.toLowerCase().includes(searchLower)) {
                                                    return true;
                                                }

                                                // Check filters
                                                return option.filters.some(filter =>
                                                    filter.name.toLowerCase().includes(searchLower)
                                                    // || hasMatchingOptions(filter, searchLower)
                                                );
                                            });

                                        // If current selected category is not in visible categories
                                        const currentCategoryIsVisible = visibleCategories.some(
                                            option => option.optionName === selectedCategory
                                        );

                                        if (!currentCategoryIsVisible && visibleCategories.length > 0) {
                                            setSelectedCategory(visibleCategories[0].optionName);
                                        }
                                        else if (visibleCategories.length > 0) {
                                            setSelectedCategory(visibleCategories[0].optionName);
                                        }
                                        else {
                                            setSelectedCategory()
                                        }
                                    }
                                }}
                                className="w-full bg-transparent border  rounded-md py-2 pl-10 pr-4 text-sm text-white placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-gray-200"
                            />
                            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <svg className="h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                                </svg>
                            </div>
                        </div>
                    </div>

                    {/* Filter content - Using flex-grow and overflow-hidden for scrolling */}
                    <div className="flex flex-grow overflow-hidden mt-4 pb-2"
                        onClick={(e) => {
                            e.stopPropagation();
                        }}>
                        {/* Filter categories */}
                        <div className="w-64 border-r border-gray-500 overflow-y-auto">
                            <div className="px-2">
                                {(() => {
                                    // Get filtered categories based on search and tab/channel
                                    const filteredCategories = filterOptions
                                        .filter(option => option.optionFor === activeTab && option.channel === selectedChannel)
                                        // Filter categories based on search term to show only those with matching filters
                                        .filter(option => {
                                            // If no search term, show all categories
                                            if (!searchTerm) return true;

                                            const searchLower = searchTerm.toLowerCase();

                                            // Check if category name matches
                                            if (option.optionName.toLowerCase().includes(searchLower)) {
                                                return true;
                                            }

                                            // Check if any filters within this category match the search term
                                            return option.filters.some(filter => {
                                                // Check if filter name matches or has matching options
                                                return filter.name.toLowerCase().includes(searchLower)
                                                // || hasMatchingOptions(filter, searchLower);
                                            });
                                        });

                                    // If no categories match the search term, show a message
                                    if (filteredCategories.length === 0 && searchTerm) {
                                        return (
                                            <div className="text-gray-400 p-4 text-center">
                                                No matching filters found for "{searchTerm}"
                                            </div>
                                        );
                                    }

                                    // Otherwise show the filtered categories
                                    return filteredCategories.map((option, index) => (
                                        <button
                                            key={index}
                                            onClick={() => { setSelectedCategory(option.optionName) }}
                                            className={cn(
                                                "flex items-center justify-between w-full text-left px-4 py-3 rounded-md mb-1",
                                                selectedCategory === option.optionName
                                                    ? "bg-gray-500 text-white"
                                                    : "text-gray-200 hover:bg-gray-500 hover:text-gray-50"
                                            )}
                                        >
                                            <div className="flex items-center space-x-2">
                                                <span>{option.optionName}</span>
                                                {filterCounts[activeTab] && filterCounts[activeTab][option.optionName] && (
                                                    <span className="text-brand-600 bg-brand-200 text-xs rounded-full w-5 h-5 flex items-center justify-center">
                                                        {filterCounts[activeTab][option.optionName]}
                                                    </span>
                                                )}
                                            </div>
                                            <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                                            </svg>
                                        </button>
                                    ));
                                })()}
                            </div>
                        </div>

                        {/* Filter options */}
                        <div className="flex-1 overflow-y-auto p-4">
                            {selectedCategory && filterOptions
                                .find(option => option.optionName === selectedCategory && option.optionFor === activeTab && option.channel === selectedChannel)?.filters
                                .map((filter, filterIndex) => (
                                    <div key={filterIndex} className="mb-6">
                                        <div className="flex items-center mb-3">
                                            {filter.icon && (
                                                <span className="mr-2 text-gray-400">
                                                    {getIcon(filter.icon)}
                                                </span>
                                            )}
                                            <div className="flex items-center">
                                                <h3 className="text-white text-sm font-medium capitalize">{filter.name}</h3>
                                                {/* Show indicator for applied filters even when collapsed */}
                                                {/* {((selectedFilters[activeTab]?.[filter.name] &&
                                                    (Array.isArray(selectedFilters[activeTab][filter.name]) ?
                                                        selectedFilters[activeTab][filter.name].length > 0 : true)) ||
                                                    (minMaxValues[activeTab]?.[filter.name]?.min !== undefined ||
                                                        minMaxValues[activeTab]?.[filter.name]?.max !== undefined) ||
                                                    (pillValues[activeTab]?.[filter.name]?.length > 0)) && (
                                                        <span className="ml-2 bg-brand-500 text-white text-xs rounded-full w-4 h-4 flex items-center justify-center">
                                                            {Array.isArray(selectedFilters[activeTab]?.[filter.name]) ? (
                                                                console.log("ye hai for multilevelcheckbox", activeTab, filter, selectedFilters[activeTab][filter.name]),
                                                                selectedFilters[activeTab][filter.name].length
                                                            ) : (
                                                                1
                                                            )}
                                                        </span>
                                                    )} */}
                                            </div>
                                            <button
                                                className="ml-auto"
                                                onClick={(e) => {
                                                    e.stopPropagation();
                                                    toggleFilterCollapse(filter.name);
                                                }}
                                            >
                                                <svg
                                                    className={`h-5 w-5 text-gray-400 transform transition-transform ${collapsedFilters[filter.name] ? '' : 'rotate-180'}`}
                                                    fill="none"
                                                    viewBox="0 0 24 24"
                                                    stroke="currentColor"
                                                >
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                                                </svg>
                                            </button>
                                        </div>

                                        {filter.type === 'radio-button' && !collapsedFilters[filter.name] && (
                                            <div className="space-y-2 pl-7">
                                                {filter.options.map((option, optionIndex) => (
                                                    <div key={optionIndex} className="flex items-center">                                                        <input
                                                        id={`${filter.name}-${option.value}`}
                                                        type="radio"
                                                        name={filter.name}
                                                        value={option.value}
                                                        checked={
                                                            // Only show checked if there are no min/max values for this filter
                                                            (!minMaxValues[activeTab]?.[filter.name]?.min && !minMaxValues[activeTab]?.[filter.name]?.max) &&
                                                            selectedFilters[activeTab]?.[filter.name] === option.value
                                                        }
                                                        onChange={() => handleFilterChange(filter.name, option.value)}
                                                        className="h-4 w-4 text-brand-500 focus:ring-brand-500 border-gray-600 bg-gray-700"
                                                    />
                                                        <label
                                                            htmlFor={`${filter.name}-${option.value}`}
                                                            className="ml-2 text-sm text-gray-200"
                                                        >
                                                            {option.label}
                                                        </label>
                                                    </div>
                                                ))}
                                            </div>
                                        )}
                                        {(filter.type === 'enter-value' || filter.enterValue) && !collapsedFilters[filter.name] && (
                                            <div className="pl-7">
                                                <input
                                                    type="text"
                                                    placeholder={filter.placeholder || `Enter ${filter.name}`}
                                                    onKeyDown={(e) => handleKeyDown(e, filter.name)}
                                                    className="mt-1 w-full border border-gray-600 rounded-md py-2 px-3 text-sm text-white placeholder-white focus:outline-none focus:ring-1 focus:ring-gray-300"
                                                />
                                                {/* Display pills */}
                                                <div className="flex flex-wrap gap-2 mt-2">
                                                    {pillValues[activeTab]?.[filter.name]?.map((value, idx) => (
                                                        <div
                                                            key={idx}
                                                            className="flex items-center bg-gray-500 text-white text-xs rounded-full px-3 py-1"
                                                        >
                                                            <span className='flex mb-0.5'>{value}</span>
                                                            <button
                                                                onClick={() => removePillValue(filter.name, value)}
                                                                className="ml-1 text-gray-400 hover:text-white"
                                                            >
                                                                <FaTimes size={10} />
                                                            </button>
                                                        </div>
                                                    ))}
                                                </div>
                                            </div>
                                        )}
                                        {filter.type === 'checkbox' && !collapsedFilters[filter.name] && (
                                            <div className="space-y-2 pl-7">
                                                {/* Search box for checkbox if searchBox property is true */}
                                                {filter.searchBox && (
                                                    <div className="relative mb-3">
                                                        <input
                                                            type="text"
                                                            placeholder={filter.placeholder || `Search ${filter.name}`}
                                                            value={multilevelSearch[activeTab]?.[filter.name] || ''}
                                                            onChange={(e) => handleCheckboxSearch(filter.name, e.target.value)}
                                                            className="w-full border border-gray-600 rounded-md py-1 px-3 text-sm text-white placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-gray-300"
                                                        />
                                                        <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                                                            <svg className="h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                                                            </svg>
                                                        </div>
                                                    </div>
                                                )}

                                                {filter.minmax && (
                                                    <div className="flex space-x-2 mb-2">
                                                        <div className="flex-1">
                                                            <input
                                                                className="w-full border border-gray-600 rounded text-sm text-white p-1 placeholder-gray-400  focus:ring-1 focus:ring-gray-300"
                                                                type="number"
                                                                placeholder="Min"
                                                                value={minMaxValues[activeTab]?.[filter.name]?.min || ''}
                                                                onChange={(e) => handleMinMaxChange(filter.name, 'min', e.target.value)}
                                                            />
                                                        </div>
                                                        <div className="flex-1">
                                                            <input
                                                                className="w-full border border-gray-600 rounded text-sm text-white p-1 placeholder-gray-400  focus:ring-1 focus:ring-gray-300"
                                                                type="number"
                                                                placeholder="Max"
                                                                value={minMaxValues[activeTab]?.[filter.name]?.max || ''}
                                                                onChange={(e) => handleMinMaxChange(filter.name, 'max', e.target.value)}
                                                            />
                                                        </div>
                                                    </div>
                                                )}
                                                {/* Filter options based on search term if present */}
                                                {filter.options
                                                    .filter(option => {
                                                        const searchTerm = multilevelSearch[activeTab]?.[filter.name]?.toLowerCase() || '';
                                                        if (searchTerm === '') return true;

                                                        return (
                                                            option.label.toLowerCase().includes(searchTerm) ||
                                                            option.value.toLowerCase().includes(searchTerm) ||
                                                            (option.description && option.description.toLowerCase().includes(searchTerm))
                                                        );
                                                    })
                                                    .map((option, optionIndex) => (
                                                        <div key={optionIndex} className="flex items-center">
                                                            <input
                                                                id={`${filter.name}-${option.value}`}
                                                                type="checkbox"
                                                                name={filter.name}
                                                                value={option.value} checked={
                                                                    // Only show checked if there are no min/max values for this filter
                                                                    (!minMaxValues[activeTab]?.[filter.name]?.min && !minMaxValues[activeTab]?.[filter.name]?.max) &&
                                                                    selectedFilters[activeTab]?.[filter.name] &&
                                                                    Array.isArray(selectedFilters[activeTab][filter.name]) &&
                                                                    selectedFilters[activeTab][filter.name].includes(option.value)
                                                                }
                                                                onChange={() => handleCheckboxChange(filter.name, option.value)}
                                                                className="h-4 w-4 text-brand-500 focus:ring-brand-500 border-gray-600 bg-gray-700 rounded"
                                                            />
                                                            <label
                                                                htmlFor={`${filter.name}-${option.value}`}
                                                                className="ml-2 text-sm text-gray-200 flex justify-between w-full"
                                                            >
                                                                <span>{option.label}</span>
                                                                {option.description && (
                                                                    <span className="text-xs text-gray-300">{option.description}</span>
                                                                )}
                                                            </label>
                                                        </div>
                                                    ))
                                                }
                                            </div>
                                        )}
                                        {filter.type === 'multilevel-checkbox' && !collapsedFilters[filter.name] && (
                                            <div className="space-y-3 pl-7">
                                                {/* Search box for multilevel checkbox */}
                                                {filter.searchBox && (
                                                    <div className="relative">
                                                        <input
                                                            type="text"
                                                            placeholder={filter.placeholder || `Search ${filter.name}`}
                                                            value={multilevelSearch[activeTab]?.[filter.name] || ''}
                                                            onChange={(e) => handleMultilevelSearch(filter.name, e.target.value)}
                                                            className="w-full border border-gray-600 rounded-md py-1 px-3 text-sm text-white placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-gray-300"
                                                        />
                                                        <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                                                            <svg className="h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                                                            </svg>
                                                        </div>
                                                    </div>
                                                )}

                                                {/* Sub-option containers */}
                                                {filter.options.map((subOption, subOptionIndex) => {
                                                    // Only show sub-options that match the search term (if any)
                                                    const searchTerm = multilevelSearch[activeTab]?.[filter.name]?.toLowerCase() || '';

                                                    // If search term exists, check if any sub-option matches
                                                    const hasMatchingOptions = searchTerm === '' ||
                                                        subOption.subOptions.some(opt =>
                                                            opt.label.toLowerCase().includes(searchTerm) ||
                                                            opt.value.toLowerCase().includes(searchTerm) ||
                                                            (opt.description && opt.description.toLowerCase().includes(searchTerm))
                                                        );

                                                    // Skip rendering this sub-option container if no matches
                                                    if (!hasMatchingOptions) return null;

                                                    // Get collapse state for this sub-option
                                                    const isSubOptionCollapsed = collapsedSubOptions[`${filter.name}-${subOption.subOptionName}`];

                                                    return (
                                                        <div key={subOptionIndex} className="border-0 rounded-md overflow-hidden">
                                                            {/* Sub-option header */}
                                                            <div className="p-1 bg-inherit">
                                                                <div className={`flex items-center justify-between ${isSubOptionCollapsed ? '' : 'border-b-2 pb-2'}`}>                                                                    <div className="flex items-center">
                                                                    {/* Select All Checkbox - only shown if checkboxEnabled is true */}
                                                                    {subOption.checkboxEnabled && (
                                                                        <input
                                                                            id={`${filter.name}-${subOption.subOptionName}-selectAll`}
                                                                            type="checkbox"
                                                                            className="h-4 w-4 text-brand-500 focus:ring-brand-500 border-gray-600 bg-gray-700 rounded mr-2"
                                                                            checked={
                                                                                // Check if all visible suboptions are selected
                                                                                (() => {
                                                                                    const searchTerm = multilevelSearch[activeTab]?.[filter.name]?.toLowerCase() || '';
                                                                                    const visibleSubOptions = subOption.subOptions.filter(opt => {
                                                                                        if (searchTerm === '') return true;
                                                                                        return (
                                                                                            opt.label.toLowerCase().includes(searchTerm) ||
                                                                                            opt.value.toLowerCase().includes(searchTerm) ||
                                                                                            (opt.description && opt.description.toLowerCase().includes(searchTerm))
                                                                                        );
                                                                                    });

                                                                                    if (visibleSubOptions.length === 0) return false;                                                                                    // Check differently based on subOptionType
                                                                                    if (subOption.subOptionType === "radio-button") {
                                                                                        // For radio buttons, check if any option is selected
                                                                                        const radioKey = `${filter.name}_radioGroup_${subOption.subOptionName}`;
                                                                                        return selectedFilters[activeTab]?.[radioKey] &&
                                                                                            visibleSubOptions.some(opt =>
                                                                                                selectedFilters[activeTab][radioKey] === opt.value
                                                                                            );
                                                                                    } else {
                                                                                        // For checkboxes, check if all options are selected
                                                                                        const selectedValuesArr = selectedFilters[activeTab]?.[filter.name] || [];
                                                                                        return Array.isArray(selectedValuesArr) &&
                                                                                            visibleSubOptions.every(opt =>
                                                                                                selectedValuesArr.includes(opt.value)
                                                                                            );
                                                                                    }
                                                                                })()
                                                                            }
                                                                            onChange={(e) => {
                                                                                // Select or deselect all visible suboptions
                                                                                handleSelectAllSubOptions(filter.name, subOption, e.target.checked);
                                                                            }}
                                                                        />
                                                                    )}
                                                                    <h4
                                                                        className="text-sm font-medium text-white cursor-pointer"
                                                                        onClick={() => toggleSubOptionCollapse(filter.name, subOption.subOptionName)}
                                                                    >
                                                                        {subOption.subOptionName}
                                                                    </h4>
                                                                </div>

                                                                    <svg
                                                                        className={`h-4 w-4 text-gray-400 transform transition-transform ${isSubOptionCollapsed ? '' : 'rotate-180'} cursor-pointer`}
                                                                        fill="none"
                                                                        viewBox="0 0 24 24"
                                                                        stroke="currentColor"
                                                                        onClick={() => toggleSubOptionCollapse(filter.name, subOption.subOptionName)}
                                                                    >
                                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                                                                    </svg>
                                                                </div>
                                                            </div>
                                                            {/* Sub-option checkboxes or radio buttons */}
                                                            {!isSubOptionCollapsed && (
                                                                <div className="p-2 bg-gray-900 space-y-2 max-h-40 overflow-y-auto">
                                                                    {subOption.subOptions
                                                                        .filter(opt => {
                                                                            // Filter options by search term if it exists
                                                                            if (searchTerm === '') return true;
                                                                            return (
                                                                                opt.label.toLowerCase().includes(searchTerm) ||
                                                                                opt.value.toLowerCase().includes(searchTerm) ||
                                                                                (opt.description && opt.description.toLowerCase().includes(searchTerm))
                                                                            );
                                                                        })
                                                                        .map((opt, optIndex) => (
                                                                            <div key={optIndex} className="flex items-center">                                                                                {subOption.subOptionType === "radio-button" ? (
                                                                                <input
                                                                                    id={`${filter.name}-${opt.value}`}
                                                                                    type="radio"
                                                                                    name={`${filter.name}-${subOption.subOptionName}`}
                                                                                    value={opt.value}
                                                                                    checked={
                                                                                        selectedFilters[activeTab]?.[`${filter.name}_radioGroup_${subOption.subOptionName}`] === opt.value
                                                                                    }
                                                                                    onChange={() => handleMultilevelRadioChange(filter.name, opt.value, subOption.subOptionName)}
                                                                                    className="h-4 w-4 text-brand-500 focus:ring-brand-500 border-gray-600 bg-gray-700"
                                                                                />
                                                                            ) : (
                                                                                <input
                                                                                    id={`${filter.name}-${opt.value}`}
                                                                                    type="checkbox"
                                                                                    name={filter.name}
                                                                                    value={opt.value}
                                                                                    checked={
                                                                                        selectedFilters[activeTab]?.[filter.name] &&
                                                                                        Array.isArray(selectedFilters[activeTab][filter.name]) &&
                                                                                        selectedFilters[activeTab][filter.name].includes(opt.value)
                                                                                    }
                                                                                    onChange={() => handleMultilevelCheckboxChange(filter.name, opt.value)}
                                                                                    className="h-4 w-4 text-brand-500 focus:ring-brand-500 border-gray-600 bg-gray-700 rounded"
                                                                                />)}
                                                                                <label
                                                                                    htmlFor={`${filter.name}-${opt.value}`}
                                                                                    className="ml-2 text-sm text-gray-200 flex justify-between w-full"
                                                                                >
                                                                                    <span>{opt.label}</span>
                                                                                    {opt.description && (
                                                                                        <span className="text-xs text-gray-300">{opt.description}</span>
                                                                                    )}
                                                                                </label>
                                                                            </div>
                                                                        ))
                                                                    }
                                                                </div>
                                                            )}
                                                        </div>
                                                    );
                                                })}
                                            </div>
                                        )}


                                    </div>
                                ))}
                        </div>
                    </div>
                </div>

                {/* Footer with action buttons - fixed at bottom with flex-shrink-0 */}
                <div className="flex-shrink-0 border-t border-gray-500 p-4 flex justify-between bg-gray-900">
                    <button
                        onClick={handleClear}
                        className="px-4 py-2 bg-transparent border border-gray-500 rounded-lg flex items-center gap-1.5 text-sm text-gray-50 hover:text-gray-300 transition-colors cursor-pointer"
                    >
                        Clear
                    </button>
                    <button
                        onClick={handleApply}
                        className="px-4 py-2 rounded-md text-sm font-medium text-white bg-brand-500 hover:bg-brand-600 hover:text-gray-100"
                    >
                        Apply Filters
                    </button>
                </div>
            </div>
        </div>
    );
};

export default Filter;