import React, { useState } from 'react';

// Inside ProfileSelector.jsx
export default function ProfileSelector({ onChange, profiles }) {
    const [selectedIds, setSelectedIds] = useState([]);

    const isAllSelected = selectedIds.length === profiles.length;

    const updateSelected = (newIds) => {
        setSelectedIds(newIds);
        const selectedProfiles = profiles.filter((p) => newIds.includes(p.id));
        onChange?.(selectedProfiles);
    };

    const toggleSelectAll = () => {
        if (isAllSelected) {
            updateSelected([]);
        } else {
            updateSelected(profiles.map((p) => p.id));
        }
    };

    const toggleProfile = (id) => {
        updateSelected(
            selectedIds.includes(id)
                ? selectedIds.filter((pid) => pid !== id)
                : [...selectedIds, id]
        );
    };

    return (
        <div className="flex flex-col text-white p-6 w-full ">
            {profiles.length > 1 ? (<label className="flex items-center mb-4 cursor-pointer gap-2">
                <input
                    type="checkbox"
                    checked={isAllSelected}
                    onChange={toggleSelectAll}
                    className="form-checkbox h-5 w-5 text-brand-500"
                />
                <span className="text-sm font-medium">Select All Profiles</span>
            </label>
            ) : null }


            <div className="grid grid-cols-1 pr-5 gap-4 overflow-y-auto max-h-[260px]">
                {profiles.map((profile, idx) => (
                    <div
                        key={`${profile.id}-${idx}`}
                        className={`flex items-center w-full justify-between p-4 border-b border-gray-700 ${selectedIds.includes(profile.id) ? '' : ''
                            }`}
                    >
                        <div className="flex items-center gap-3">
                            <img
                                src={profile.avatar}
                                alt={profile.name}
                                className="h-8 w-8 rounded-full object-cover"
                            />
                            <div>
                                <div className="font-semibold text-sm">{profile.name}</div>
                                <div className="text-gray-400 text-xs">{profile.profile}</div>
                            </div>
                        </div>
                        <input
                            type="radio"
                            checked={selectedIds.includes(profile.id)}
                            onChange={() => toggleProfile(profile.id)}
                            className="form-checkbox h-5 w-5 text-brand-500"
                        />
                    </div>
                ))}
            </div>
        </div>
    );
}

