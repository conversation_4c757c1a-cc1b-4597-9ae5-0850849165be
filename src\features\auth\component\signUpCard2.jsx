import React from 'react'
import handwave from '../../../assets/emoji/Waving_Hand_Light_Skin_Tone.png';
import cash from '../../../assets/cash.svg';
import briefcase from '../../../assets/briefcase.svg';
import rocket from '../../../assets/rocket.svg';
import { Link } from "react-router-dom";
import youtubeIcon from '../../../assets/icon/youtube-icon.svg';
import instagramIcon from '../../../assets/icon/instagram-icon.png';
import { useNavigate } from "react-router-dom";


import SocialButton from '../../../shared/components/UI/button';
import GradientSignupCard from '../../../features/auth/component/GradientSignupCard';


const SignUpCard2 = () => {

    const navigate = useNavigate();

    const handleConnectPlatform = async (platform) => {
        // Simulate some async operation like auth or API call
        await new Promise((resolve) => setTimeout(resolve, 500)); // simulate delay

        navigate(`/social-signup-followup/${platform}`);
    };

    return (
        <div className="flex flex-col w-full lg:max-w-lg md:max-w-md sm:max-w-sm max-h-[80vh] transition-all duration-500">
            <div className='flex flex-col space-y-10'>
                <div className="text-left">
                    <h1 className="text-36-semibold mb-1 whitespace-nowrap ">
                        Hey there, <span className="creator-gradient-text">Creator!</span>
                        <div className="bg-[#D6F7EE] w-10 h-10 rounded-full p-1.5 ml-2 inline-flex items-center justify-center align-middle">
                            <img src={handwave} alt="Waving Hand" className="w-6 h-6" />
                        </div>
                    </h1>
                    <p className="text-gray-300 text-18-regular">Land collabs. Get paid. Let's make waves.</p>
                </div>

                <div className="flex flex-col space-y-8 ">
                    <div className='text-18-semibold'>
                        Connect with
                    </div>
                    <SocialButton
                        icon={<img src={instagramIcon} alt="Instagram" />}
                        label="Instagram"
                        variant="default"
                        onClick={() => handleConnectPlatform("instagram")}
                    />
                    <SocialButton
                        icon={<img src={youtubeIcon} alt="YouTube" />}
                        label="YouTube"
                        variant="default"
                        onClick={() => handleConnectPlatform("youtube")}
                    />
                </div>

                <div className="text-center text-18-regular text-gray-200">
                    <p>
                        Already part of the fam? <Link to="/login" className="text-[rgba(71,200,236,1)] hover:underline">Sign in</Link>
                    </p>
                </div>
            </div>

            <div className="mt-10">
                <GradientSignupCard
                    to="/brand/signup"
                    label="Business"
                    description="Tap into influencer marketing—build real campaigns with real results"
                    images={[
                        { src: cash, className: "bg-blue-300 transition-all duration-500 ease-in-out ml-0 group-hover:ml-0" },
                        { src: briefcase, className: "bg-green-300 transition-all duration-500 ease-in-out -ml-2 group-hover:-ml-1" },
                        { src: rocket, className: "bg-yellow-300 transition-all duration-500 ease-in-out -ml-2 group-hover:-ml-1" }
                    ]}
                    className="mt-10"
                />
            </div>
        </div>
    )
}

export default SignUpCard2
