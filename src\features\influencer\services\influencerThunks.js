// Influencer async thunks for API operations
import { createAsyncThunk } from '@reduxjs/toolkit';
import brandManagementApi from '../../../app/store/api/brandManagementApi';
import analyticsApi from '../../../app/store/api/analyticsApi';

/**
 * Get influencer profile information async thunk
 */
export const getInfluencerInfoThunk = createAsyncThunk(
    'influencer/getInfluencerInfo',
    async (_, { rejectWithValue }) => {
        try {
            const response = await brandManagementApi.requestUserCreatorInfo().send();
            if (response.status === 200 && response.data) {
                const { user, social_profiles } = response.data.data;

                // Store influencer data in localStorage
                localStorage.setItem('socialProfiles', JSON.stringify(social_profiles));

                return {
                    message: response.data?.message || 'Influencer info retrieved successfully',
                    success: true,
                    data: response.data.data
                };
            } else {
                return rejectWithValue({
                    message: response.data?.message || 'Failed to retrieve influencer info',
                    success: false
                });
            }
        } catch (error) {
            return rejectWithValue({
                message: error.response?.data?.message || 'Failed to retrieve influencer info. Please try again.',
                success: false
            });
        }
    }
);

/**
 * Get YouTube channels async thunk
 */
export const getYoutubeChannelsThunk = createAsyncThunk(
    'influencer/getYoutubeChannels',
    async (_, { rejectWithValue }) => {
        try {
            const response = await brandManagementApi.getYoutubeChannels().send();
            if (response.status === 200 && response.data) {
                return {
                    message: response.data?.message || 'YouTube channels retrieved successfully',
                    success: true,
                    data: response.data.data
                };
            } else {
                return rejectWithValue({
                    message: response.data?.message || 'Failed to retrieve YouTube channels',
                    success: false
                });
            }
        } catch (error) {
            return rejectWithValue({
                message: error.response?.data?.message || 'Failed to retrieve YouTube channels. Please try again.',
                success: false
            });
        }
    }
);

/**
 * Select YouTube channels async thunk
 */
export const selectYoutubeChannelsThunk = createAsyncThunk(
    'influencer/selectYoutubeChannels',
    async (channels, { rejectWithValue }) => {
        try {
            const response = await brandManagementApi.selectYoutubeChannels({ data: channels }).send();
            if (response.status === 200 && response.data) {
                return {
                    message: response.data?.message || 'YouTube channels selected successfully',
                    success: true,
                    data: response.data.data
                };
            } else {
                return rejectWithValue({
                    message: response.data?.message || 'Failed to select YouTube channels',
                    success: false
                });
            }
        } catch (error) {
            return rejectWithValue({
                message: error.response?.data?.message || 'Failed to select YouTube channels. Please try again.',
                success: false
            });
        }
    }
);

/**
 * Get basic profile analytics async thunk
 */
export const getBasicProfileThunk = createAsyncThunk(
    'influencer/getBasicProfile',
    async (profileId, { rejectWithValue }) => {
        try {
            const response = await analyticsApi.getBasicProfile(profileId).send();
            if (response.status === 200 && response.data) {
                return {
                    message: 'Basic profile data retrieved successfully',
                    success: true,
                    data: response.data.data
                };
            } else {
                return rejectWithValue({
                    message: response.data?.message || 'Failed to retrieve basic profile data',
                    success: false
                });
            }
        } catch (error) {
            return rejectWithValue({
                message: error.response?.data?.message || 'Failed to retrieve basic profile data. Please try again.',
                success: false
            });
        }
    }
);

/**
 * Get detailed profile analytics async thunk
 */
export const getDetailedProfileThunk = createAsyncThunk(
    'influencer/getDetailedProfile',
    async ({ profileId, options = {} }, { rejectWithValue }) => {
        try {
            const response = await analyticsApi.getDetailedProfile(profileId, options).send();
            if (response.status === 200 && response.data) {
                return {
                    message: 'Detailed profile analytics retrieved successfully',
                    success: true,
                    data: response.data.data,
                    profileId
                };
            } else {
                return rejectWithValue({
                    message: response.data?.message || 'Failed to retrieve detailed profile analytics',
                    success: false
                });
            }
        } catch (error) {
            return rejectWithValue({
                message: error.response?.data?.message || 'Failed to retrieve detailed profile analytics. Please try again.',
                success: false
            });
        }
    }
);

/**
 * Trigger analytics fetch async thunk
 */
export const triggerAnalyticsFetchThunk = createAsyncThunk(
    'influencer/triggerAnalyticsFetch',
    async (profileId, { rejectWithValue }) => {
        try {
            const response = await analyticsApi.triggerAnalyticsFetch(profileId).send();
            if (response.status === 200) {
                return {
                    message: 'Analytics fetch triggered successfully',
                    success: true,
                    profileId
                };
            } else {
                return rejectWithValue({
                    message: response.data?.message || 'Failed to trigger analytics fetch',
                    success: false
                });
            }
        } catch (error) {
            return rejectWithValue({
                message: error.response?.data?.message || 'Failed to trigger analytics fetch. Please try again.',
                success: false
            });
        }
    }
);

/**
 * Get analytics status async thunk
 */
export const getAnalyticsStatusThunk = createAsyncThunk(
    'influencer/getAnalyticsStatus',
    async (profileId, { rejectWithValue }) => {
        try {
            const response = await analyticsApi.getAnalyticsStatus(profileId).send();
            if (response.status === 200 && response.data) {
                return {
                    message: 'Analytics status retrieved successfully',
                    success: true,
                    data: response.data.data,
                    profileId
                };
            } else {
                return rejectWithValue({
                    message: response.data?.message || 'Failed to retrieve analytics status',
                    success: false
                });
            }
        } catch (error) {
            return rejectWithValue({
                message: error.response?.data?.message || 'Failed to retrieve analytics status. Please try again.',
                success: false
            });
        }
    }
);

/**
 * Get dashboard KPIs async thunk
 */
export const getDashboardKPIsThunk = createAsyncThunk(
    'influencer/getDashboardKPIs',
    async (profileId, { rejectWithValue }) => {
        try {
            const response = await analyticsApi.getDashboardKPIs(profileId).send();
            if (response.status === 200 && response.data) {
                return {
                    message: 'Dashboard KPIs retrieved successfully',
                    success: true,
                    data: response.data.data,
                    profileId
                };
            } else {
                return rejectWithValue({
                    message: response.data?.message || 'Failed to retrieve dashboard KPIs',
                    success: false
                });
            }
        } catch (error) {
            return rejectWithValue({
                message: error.response?.data?.message || 'Failed to retrieve dashboard KPIs. Please try again.',
                success: false
            });
        }
    }
);

/**
 * Get top posts async thunk
 */
export const getTopPostsThunk = createAsyncThunk(
    'influencer/getTopPosts',
    async ({ profileId, postCount = 5 }, { rejectWithValue }) => {
        try {
            const response = await analyticsApi.getTopPosts(profileId, postCount).send();
            if (response.status === 200 && response.data) {
                return {
                    message: 'Top posts retrieved successfully',
                    success: true,
                    data: response.data.data,
                    profileId
                };
            } else {
                return rejectWithValue({
                    message: response.data?.message || 'Failed to retrieve top posts',
                    success: false
                });
            }
        } catch (error) {
            return rejectWithValue({
                message: error.response?.data?.message || 'Failed to retrieve top posts. Please try again.',
                success: false
            });
        }
    }
);

/**
 * Get monthly impressions async thunk
 */
export const getMonthlyImpressionsThunk = createAsyncThunk(
    'influencer/getMonthlyImpressions',
    async ({ profileId, period = 12 }, { rejectWithValue }) => {
        try {
            const response = await analyticsApi.getMonthlyImpressions(profileId, period).send();
            if (response.status === 200 && response.data) {
                return {
                    message: 'Monthly impressions retrieved successfully',
                    success: true,
                    data: response.data.data,
                    profileId
                };
            } else {
                return rejectWithValue({
                    message: response.data?.message || 'Failed to retrieve monthly impressions',
                    success: false
                });
            }
        } catch (error) {
            return rejectWithValue({
                message: error.response?.data?.message || 'Failed to retrieve monthly impressions. Please try again.',
                success: false
            });
        }
    }
);

/**
 * Get monthly likes by type async thunk
 */
export const getMonthlyLikesByTypeThunk = createAsyncThunk(
    'influencer/getMonthlyLikesByType',
    async ({ profileId, period = 12 }, { rejectWithValue }) => {
        try {
            const response = await analyticsApi.getMonthlyLikesByType(profileId, period).send();
            if (response.status === 200 && response.data) {
                return {
                    message: 'Monthly likes by type retrieved successfully',
                    success: true,
                    data: response.data.data,
                    profileId
                };
            } else {
                return rejectWithValue({
                    message: response.data?.message || 'Failed to retrieve monthly likes by type',
                    success: false
                });
            }
        } catch (error) {
            return rejectWithValue({
                message: error.response?.data?.message || 'Failed to retrieve monthly likes by type. Please try again.',
                success: false
            });
        }
    }
);

/**
 * Get content distribution async thunk
 */
export const getContentDistributionThunk = createAsyncThunk(
    'influencer/getContentDistribution',
    async (profileId, { rejectWithValue }) => {
        try {
            const response = await analyticsApi.getContentDistribution(profileId).send();
            if (response.status === 200 && response.data) {
                return {
                    message: 'Content distribution retrieved successfully',
                    success: true,
                    data: response.data.data,
                    profileId
                };
            } else {
                return rejectWithValue({
                    message: response.data?.message || 'Failed to retrieve content distribution',
                    success: false
                });
            }
        } catch (error) {
            return rejectWithValue({
                message: error.response?.data?.message || 'Failed to retrieve content distribution. Please try again.',
                success: false
            });
        }
    }
);

/**
 * List profiles async thunk
 */
export const listProfilesThunk = createAsyncThunk(
    'influencer/listProfiles',
    async (params = {}, { rejectWithValue }) => {
        try {
            const response = await analyticsApi.getProfiles(params).send();
            if (response.status === 200 && response.data) {
                return {
                    message: 'Profiles list retrieved successfully',
                    success: true,
                    data: response.data.data
                };
            } else {
                return rejectWithValue({
                    message: response.data?.message || 'Failed to retrieve profiles list',
                    success: false
                });
            }
        } catch (error) {
            return rejectWithValue({
                message: error.response?.data?.message || 'Failed to retrieve profiles list. Please try again.',
                success: false
            });
        }
    }
);

/**
 * Get monthly engagement rate async thunk
 */
export const getMonthlyEngagementThunk = createAsyncThunk(
    'influencer/getMonthlyEngagement',
    async ({ profileId, period = 12 }, { rejectWithValue }) => {
        try {
            const response = await analyticsApi.getMonthlyEngagementRate(profileId, period).send();
            if (response.status === 200 && response.data) {
                return {
                    message: 'Monthly engagement rate retrieved successfully',
                    success: true,
                    data: response.data.data,
                    profileId
                };
            } else {
                return rejectWithValue({
                    message: response.data?.message || 'Failed to retrieve monthly engagement rate',
                    success: false
                });
            }
        } catch (error) {
            return rejectWithValue({
                message: error.response?.data?.message || 'Failed to retrieve monthly engagement rate. Please try again.',
                success: false
            });
        }
    }
);
