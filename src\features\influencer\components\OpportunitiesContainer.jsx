import React from "react";
import profile from "@assets/icon/nav/profile.png";
import OpportunityCard from "./OpportunityCard";
import instagramIcon from "@assets/icon/instagram-circle.svg";
import youtubeIcon from "@assets/icon/youtube-circle.svg";

const opportunities = [
    {
        brand: "Netflix",
        logo: profile,
        title: "Summer Promotional Campaign",
        description: "Showcase your creativity and promote our latest summer product line",
        deliverables: ["1 Story", "1 Reel", "2 Videos", "1 Short", "1 Unboxing Video", "1 Story"],
        budget: "₹ 5,500 - 6,000",
        deadline: "June 30, 2025",
        isUrgent: true,
        isSaved: false,
        postDate: "2025-05-01T12:00:00Z",
        platform: [
            { src: instagramIcon, className: "ml-0 " },
            { src: youtubeIcon, className: "-ml-2 " },
        ],
        requirements: [
            { isChecked: true, label: "Tech & Lifestyle" },
            { isChecked: true, label: "500k+ Audience" },
            { isChecked: false, label: "Audience Age: 25–34 Aligned" },
            { isChecked: false, label: "English Only" },
        ]
    },
    {
        brand: "Spotify",
        logo: profile,
        title: "Music Moments Campaign",
        description: "Share your favorite music moments and win exciting rewards!",
        deliverables: ["1 Post", "1 Reel"],
        budget: "₹ 7,000 - 8,000",
        deadline: "June 30, 2025",
        deadlineColor: "text-[#1DE9B6]",
        isUrgent: false,
        isSaved: true,
        postDate: "2025-05-24T12:00:00Z",
        platform: [
            { src: instagramIcon, className: "ml-0 " },
        ],
        requirements: [
            { isChecked: true, label: "Tech & Lifestyle" },
            { isChecked: false, label: "500k+ Audience" },
            { isChecked: false, label: "Audience Age: 25–34 Aligned" },
            { isChecked: false, label: "English Only" },
        ]
    },
    {
        brand: "Amazon",
        logo: profile,
        timeAgo: "3d ago",
        title: "Prime Day Unboxing",
        description: "Unbox and review your Prime Day purchases for a chance to be featured.",
        deliverables: ["1 Unboxing Video", "1 Story"],
        budget: "₹ 4,000 - 5,000",
        deadline: "June 30, 2025",
        deadlineColor: "text-[#FFD600]",
        isUrgent: false,
        isSaved: false,
        postDate: "2025-05-10T12:00:00Z",
        platform: [
            { src: youtubeIcon, className: "ml-0 " },
        ],
        requirements: [
            { isChecked: true, label: "Tech & Lifestyle" },
            { isChecked: true, label: "500k+ Audience" },
            { isChecked: true, label: "Audience Age: 25–34 Aligned" },
            { isChecked: false, label: "English Only" }
        ]
    },
    // {
    //     matchScore: "88%",
    //     matchScoreColor: "text-[#47C8EC]",
    //     brand: "Nike",
    //     logo: profile,
    //     timeAgo: "5h ago",
    //     title: "Run With Us Challenge",
    //     description: "Join the Nike running challenge and inspire your followers to stay fit!",
    //     deliverables: ["1 Challenge Video", "1 Post", "1 Story"],
    //     budget: "₹ 6,500 - 7,500",
    //     deadline: "1 week remaining!",
    //     deadlineColor: "text-[#47C8EC]",
    //     isUrgent: true,
    //     isSaved: false,
    //     platform: [
    //         { src: instagramIcon, className: "ml-0 " },
    //         { src: youtubeIcon, className: "-ml-2 " },
    //     ],
    //     requirements: []
    // },
];

const OpportunitiesContainer = () => (
    <div className="bg-gray-600 rounded-xl p-6 ">
        <div className="flex justify-between items-center mb-4.5">
            <div className="flex flex-col gap-1">
                <h2 className="text-20-semibold text-gray-50">Opportunities Just for You 🎯</h2>
                <p className="text-12-regular text-gray-200">Brand Deals based on your category, audience likes, and past collaborations</p>
            </div>
            <button className="text-16-semibold text-gray-50">View More</button>
        </div>
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-5">
            {opportunities.map((op, idx) => (
                <OpportunityCard key={idx} {...op} />
            ))}
        </div>
    </div>
);

export default OpportunitiesContainer;