// src/shared/components/ProtectedRoute.jsx
import { useSelector } from 'react-redux';
import { Navigate, useLocation } from 'react-router-dom';

/**
 * ProtectedRoute component that ensures only authenticated users can access protected pages
 * Redirects unauthenticated users to appropriate login pages based on the route context
 * Also ensures users can only access routes appropriate for their user type
 */
const ProtectedRoute = ({ children, userType }) => {
  const {
    isAuthenticated,
    allocatedBrands,
    organizationBrands,
    socialProfiles,
    // user 
  } = useSelector(state => state.auth);
  const location = useLocation();
  const hasAccessToken = new URLSearchParams(location.search).has('accessToken');


  // If not authenticated, redirect to login
  if (!isAuthenticated && !hasAccessToken) {
    const redirectTo = userType === 'brand' ? '/brand/signin' : '/influencer/login';
    return <Navigate to={redirectTo} state={{ from: location }} replace />;
  }

  // If authenticated and userType is specified, check if user can access this route
  if (userType) {
    // Determine user type based on data
    const isBrandUser = (allocatedBrands && allocatedBrands.length > 0) ||
      (organizationBrands && organizationBrands.length > 0);
    const isInfluencerUser = (socialProfiles && socialProfiles.length > 0);

    // console.log('ProtectedRoute - Auth State:', { isBrandUser, isInfluencerUser, userType }, 'Location' , location.pathname);

    if (!hasAccessToken) {
      if (userType === 'brand' && !isBrandUser) {
        return <Navigate to="/influencer/dashboard" replace />;
      }

      if (userType === 'influencer' && !isInfluencerUser) {
        return <Navigate to="/brand/dashboard" replace />;
      }
    }
  }

  return children;
};

export default ProtectedRoute;
