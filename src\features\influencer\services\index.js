/**
 * Influencer Services Index
 * 
 * Central export point for all influencer-related services, actions, and selectors.
 * This provides a clean interface for consuming influencer functionality throughout the application.
 */

// === CORE SERVICES ===
export { default as influencerReducer } from './influencerSlice';
export { default as useInfluencerActions } from './influencerActions';
export { default as useInfluencerSelectors } from './influencerSelectors';

// === THUNKS ===
export {
    getInfluencerInfoThunk,
    getYoutubeChannelsThunk,
    selectYoutubeChannelsThunk,
    getBasicProfileThunk,
    getDetailedProfileThunk,
    triggerAnalyticsFetchThunk,
    getAnalyticsStatusThunk,
    getDashboardKPIsThunk,
    getTopPostsThunk,
    getMonthlyImpressionsThunk,
    getMonthlyEngagementThunk,
    getMonthlyLikesByTypeThunk,
    getContentDistributionThunk,
    listProfilesThunk
} from './influencerThunks';

// === SLICE ACTIONS ===
export {
    setSelectedProfile,
    setDashboardView,
    clearProfileData,
    clearDashboardData,
    clearErrors,
    clearInfluencerError,
    clearYoutubeError,
    clearProfileError,
    clearAnalyticsError,
    clearDashboardError,
    clearAllInfluencerState
} from './influencerSlice';

// === STANDALONE SELECTORS ===
export {
    selectSocialProfiles,
    selectYoutubeChannels,
    selectProfiles,
    selectCurrentProfile,
    selectBasicProfileData,
    selectDetailedProfileData,
    selectDashboardKPIs,
    selectInfluencerStatus,
    selectIsInfluencerLoading
} from './influencerSelectors';

/**
 * Influencer Service Factory
 * Provides a convenient way to access all influencer services
 */
export const influencerServices = {
    // Hooks
    useInfluencerActions,
    useInfluencerSelectors,
    
    // Profile Management
    getInfluencerInfo: getInfluencerInfoThunk,
    getYoutubeChannels: getYoutubeChannelsThunk,
    selectYoutubeChannels: selectYoutubeChannelsThunk,
    
    // Profile Analytics
    getBasicProfile: getBasicProfileThunk,
    getDetailedProfile: getDetailedProfileThunk,
    triggerAnalyticsFetch: triggerAnalyticsFetchThunk,
    getAnalyticsStatus: getAnalyticsStatusThunk,
    listProfiles: listProfilesThunk,
    
    // Dashboard Analytics
    getDashboardKPIs: getDashboardKPIsThunk,
    getTopPosts: getTopPostsThunk,
    getMonthlyImpressions: getMonthlyImpressionsThunk,
    getMonthlyEngagement: getMonthlyEngagementThunk,
    getMonthlyLikesByType: getMonthlyLikesByTypeThunk,
    getContentDistribution: getContentDistributionThunk,
};

/**
 * Default export for backward compatibility
 */
export default {
    // Services
    ...influencerServices,
    
    // Reducer
    influencerReducer,
};
