import React, { useState, useRef, useEffect } from 'react';
import DownIcon from '@assets/icon/down.svg';

const FilterDropdown = ({ icon, title, options, onSelect, selectedOptions = [] }) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const toggleDropdown = () => {
    setIsOpen(!isOpen);
  };

  const handleOptionSelect = (option) => {
    onSelect(option);
    // Don't close dropdown when selecting options to allow multiple selections
  };

  // Display selected count if any options are selected
  const selectedCount = selectedOptions.length;
  const displaySelectedCount = selectedCount > 0 ? `(${selectedCount})` : '';

  return (
    <div className="relative" ref={dropdownRef}>
      <button
        className={`px-3 py-1.5 ${isOpen ? 'bg-gray-700' : 'bg-gray-800'} rounded flex items-center gap-1.5 text-sm text-white hover:bg-gray-700 transition-colors`}
        onClick={toggleDropdown}
      >
        {icon && <img src={icon} alt={title} className="w-3.5 h-3.5" />}
        {title} {displaySelectedCount}
        <img
          src={DownIcon}
          alt="Dropdown"
          className={`w-3.5 h-3.5 ml-0.5 transition-transform duration-200 ${isOpen ? 'transform rotate-180' : ''}`}
        />
      </button>      {isOpen && (
        <div className="absolute mt-1 left-0 w-max min-w-full bg-gray-800 border border-gray-700 rounded-md shadow-lg z-50">
          <div className="py-1.5 max-h-60 overflow-y-auto">
            {options.map((option) => (
              <div
                key={option.value}
                className="px-3 py-1.5 flex items-center gap-2 hover:bg-gray-700 cursor-pointer"
                onClick={() => handleOptionSelect(option.value)}
              >
                <input
                  type="checkbox"
                  id={`option-${option.value}`}
                  checked={selectedOptions.includes(option.value)}
                  readOnly
                  className="rounded text-blue-500 focus:ring-blue-500 h-3.5 w-3.5 cursor-pointer"
                />
                <label
                  htmlFor={`option-${option.value}`}
                  className="cursor-pointer flex-grow text-white text-sm"
                >
                  {option.label}
                </label>
              </div>
            ))}
          </div>

          {options.length > 5 && (
            <div className="border-t border-gray-700 p-2 flex justify-between">
              <button 
                className="text-xs text-gray-400 hover:text-white"
                onClick={() => onSelect([])}>
                Clear
              </button>
              <button 
                className="text-xs text-blue-500 hover:text-blue-400"
                onClick={() => setIsOpen(false)}>
                Done
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default FilterDropdown;
