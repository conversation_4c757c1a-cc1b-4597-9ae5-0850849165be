/**
 * Environment Configuration Management
 * 
 * Manages environment-specific configurations for different deployment scenarios.
 * Supports:
 * - Development, staging, and production environments
 * - Environment variable overrides
 * - Feature flags per environment
 * - Service discovery and load balancing
 * - Security configurations
 */

/**
 * Environment detection and validation
 */
const detectEnvironment = () => {
  const env = import.meta.env.MODE || 'development';
  const validEnvironments = ['development', 'staging', 'production'];
  
  console.log('[ENV CONFIG] Detected environment:', env);

  if (!validEnvironments.includes(env)) {
    console.warn(`[ENV CONFIG] Invalid environment '${env}', defaulting to 'development'`);
    return 'development';
  }
  
  return env;
};

/**
 * Development environment configuration
 */
const developmentConfig = {
  environment: 'development',
  debug: true,
  
  // Service endpoints
  services: {
    auth: {
      baseURL: import.meta.env.VITE_AUTH_SERVICE_URL || 'https://a6a2-103-19-196-138.ngrok-free.app',
      timeout: 30000,
      retryAttempts: 3,
      retryDelay: 1000,
    },
    discovery: {
      baseURL: import.meta.env.VITE_DISCOVERY_SERVICE_URL || 'http://localhost:8000',
      timeout: 45000,
      retryAttempts: 3,
      retryDelay: 1000,
    }
  },
  
  // Feature flags
  features: {
    enableAnalytics: true,
    enableCaching: true,
    enableRetry: true,
    enableCircuitBreaker: true,
    enableDetailedLogging: true,
    enableMockData: false,
    enableServiceWorker: false,
  },
  
  // Security settings
  security: {
    enableCSP: false,
    enableHTTPS: false,
    tokenRefreshBuffer: 300, // 5 minutes
    maxTokenAge: 3600, // 1 hour
  },
  
  // Performance settings
  performance: {
    enableCompression: false,
    enableCDN: false,
    cacheTimeout: 300, // 5 minutes
    maxCacheSize: 50, // MB
  },
  
  // Development-specific headers
  headers: {
    'ngrok-skip-browser-warning': 'true',
    'X-Environment': 'development',
  }
};

/**
 * Staging environment configuration
 */
const stagingConfig = {
  environment: 'staging',
  debug: true,
  
  // Service endpoints
  services: {
    auth: {
      baseURL: import.meta.env.VITE_AUTH_SERVICE_URL || 'https://auth-staging.creatorverse.com',
      timeout: 30000,
      retryAttempts: 3,
      retryDelay: 1000,
    },
    discovery: {
      baseURL: import.meta.env.VITE_DISCOVERY_SERVICE_URL || 'https://discovery-staging.creatorverse.com',
      timeout: 45000,
      retryAttempts: 3,
      retryDelay: 1000,
    }
  },
  
  // Feature flags
  features: {
    enableAnalytics: true,
    enableCaching: true,
    enableRetry: true,
    enableCircuitBreaker: true,
    enableDetailedLogging: true,
    enableMockData: false,
    enableServiceWorker: true,
  },
  
  // Security settings
  security: {
    enableCSP: true,
    enableHTTPS: true,
    tokenRefreshBuffer: 300, // 5 minutes
    maxTokenAge: 3600, // 1 hour
  },
  
  // Performance settings
  performance: {
    enableCompression: true,
    enableCDN: false,
    cacheTimeout: 600, // 10 minutes
    maxCacheSize: 100, // MB
  },
  
  // Staging-specific headers
  headers: {
    'X-Environment': 'staging',
  }
};

/**
 * Production environment configuration
 */
const productionConfig = {
  environment: 'production',
  debug: false,
  
  // Service endpoints
  services: {
    auth: {
      baseURL: import.meta.env.VITE_AUTH_SERVICE_URL || 'https://auth.creatorverse.com',
      timeout: 30000,
      retryAttempts: 5,
      retryDelay: 2000,
    },
    discovery: {
      baseURL: import.meta.env.VITE_DISCOVERY_SERVICE_URL || 'https://discovery.creatorverse.com',
      timeout: 45000,
      retryAttempts: 5,
      retryDelay: 2000,
    }
  },
  
  // Feature flags
  features: {
    enableAnalytics: true,
    enableCaching: true,
    enableRetry: true,
    enableCircuitBreaker: true,
    enableDetailedLogging: false,
    enableMockData: false,
    enableServiceWorker: true,
  },
  
  // Security settings
  security: {
    enableCSP: true,
    enableHTTPS: true,
    tokenRefreshBuffer: 300, // 5 minutes
    maxTokenAge: 3600, // 1 hour
  },
  
  // Performance settings
  performance: {
    enableCompression: true,
    enableCDN: true,
    cacheTimeout: 1800, // 30 minutes
    maxCacheSize: 200, // MB
  },
  
  // Production-specific headers
  headers: {
    'X-Environment': 'production',
  }
};

/**
 * Environment configurations map
 */
const environmentConfigs = {
  development: developmentConfig,
  staging: stagingConfig,
  production: productionConfig,
};

/**
 * Get current environment configuration
 */
const getCurrentEnvironmentConfig = () => {
  const env = detectEnvironment();
  return environmentConfigs[env];
};

/**
 * Environment Configuration Manager
 */
class EnvironmentConfigManager {
  constructor() {
    this.currentEnv = detectEnvironment();
    this.config = getCurrentEnvironmentConfig();
    this.overrides = {};
  }

  /**
   * Get configuration value
   * @param {string} path - Configuration path (e.g., 'services.auth.baseURL')
   * @param {*} defaultValue - Default value if path not found
   * @returns {*} Configuration value
   */
  get(path, defaultValue = null) {
    const keys = path.split('.');
    let value = this.config;
    
    // Check overrides first
    let overrideValue = this.overrides;
    let hasOverride = true;
    
    for (const key of keys) {
      if (overrideValue && typeof overrideValue === 'object' && key in overrideValue) {
        overrideValue = overrideValue[key];
      } else {
        hasOverride = false;
        break;
      }
    }
    
    if (hasOverride) {
      return overrideValue;
    }
    
    // Get from main config
    for (const key of keys) {
      if (value && typeof value === 'object' && key in value) {
        value = value[key];
      } else {
        return defaultValue;
      }
    }
    
    return value;
  }

  /**
   * Set configuration override
   * @param {string} path - Configuration path
   * @param {*} value - Override value
   */
  set(path, value) {
    const keys = path.split('.');
    let current = this.overrides;
    
    for (let i = 0; i < keys.length - 1; i++) {
      const key = keys[i];
      if (!(key in current) || typeof current[key] !== 'object') {
        current[key] = {};
      }
      current = current[key];
    }
    
    current[keys[keys.length - 1]] = value;
  }

  /**
   * Check if feature is enabled
   * @param {string} featureName - Feature name
   * @returns {boolean} Whether feature is enabled
   */
  isFeatureEnabled(featureName) {
    return this.get(`features.${featureName}`, false);
  }

  /**
   * Get service configuration
   * @param {string} serviceName - Service name (auth, discovery)
   * @returns {Object} Service configuration
   */
  getServiceConfig(serviceName) {
    return this.get(`services.${serviceName}`, {});
  }

  /**
   * Get all service configurations
   * @returns {Object} All service configurations
   */
  getAllServiceConfigs() {
    return this.get('services', {});
  }

  /**
   * Get security configuration
   * @returns {Object} Security configuration
   */
  getSecurityConfig() {
    return this.get('security', {});
  }

  /**
   * Get performance configuration
   * @returns {Object} Performance configuration
   */
  getPerformanceConfig() {
    return this.get('performance', {});
  }

  /**
   * Get environment-specific headers
   * @returns {Object} Headers object
   */
  getHeaders() {
    return this.get('headers', {});
  }

  /**
   * Check if debug mode is enabled
   * @returns {boolean} Whether debug mode is enabled
   */
  isDebugMode() {
    return this.get('debug', false);
  }

  /**
   * Get current environment name
   * @returns {string} Environment name
   */
  getEnvironment() {
    return this.currentEnv;
  }

  /**
   * Check if current environment is production
   * @returns {boolean} Whether in production
   */
  isProduction() {
    return this.currentEnv === 'production';
  }

  /**
   * Check if current environment is development
   * @returns {boolean} Whether in development
   */
  isDevelopment() {
    return this.currentEnv === 'development';
  }

  /**
   * Check if current environment is staging
   * @returns {boolean} Whether in staging
   */
  isStaging() {
    return this.currentEnv === 'staging';
  }

  /**
   * Validate configuration
   * @returns {Object} Validation result
   */
  validateConfig() {
    const errors = [];
    const warnings = [];
    
    // Check required service URLs
    const authURL = this.get('services.auth.baseURL');
    const discoveryURL = this.get('services.discovery.baseURL');
    
    if (!authURL) {
      errors.push('Auth service URL is not configured');
    }
    
    if (!discoveryURL) {
      errors.push('Discovery service URL is not configured');
    }
    
    // Check HTTPS in production
    if (this.isProduction()) {
      if (authURL && !authURL.startsWith('https://')) {
        warnings.push('Auth service should use HTTPS in production');
      }
      
      if (discoveryURL && !discoveryURL.startsWith('https://')) {
        warnings.push('Discovery service should use HTTPS in production');
      }
    }
    
    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Get configuration summary
   * @returns {Object} Configuration summary
   */
  getConfigSummary() {
    return {
      environment: this.currentEnv,
      debug: this.isDebugMode(),
      services: Object.keys(this.getAllServiceConfigs()),
      features: Object.entries(this.get('features', {}))
        .filter(([, enabled]) => enabled)
        .map(([name]) => name),
      hasOverrides: Object.keys(this.overrides).length > 0
    };
  }
}

// Create singleton instance
const envConfig = new EnvironmentConfigManager();

// Validate configuration on startup
const validation = envConfig.validateConfig();
if (!validation.isValid) {
  console.error('[ENV CONFIG] Configuration errors:', validation.errors);
}
if (validation.warnings.length > 0) {
  console.warn('[ENV CONFIG] Configuration warnings:', validation.warnings);
}

// Log configuration summary in development
if (envConfig.isDevelopment()) {
  console.log('[ENV CONFIG] Configuration summary:', envConfig.getConfigSummary());
}

export { environmentConfigs, detectEnvironment };
export default envConfig;
