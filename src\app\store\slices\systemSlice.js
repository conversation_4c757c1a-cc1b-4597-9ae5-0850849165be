// Role management slice
import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import authApi from '../api/authApi';
import { RequestStatus } from '../enum';
import { channelIconMap } from '@shared/utils/channelIconMap';
import { createSelector } from '@reduxjs/toolkit';



/**
 * Fetch roles async thunk
 */
export const fetchRolesThunk = createAsyncThunk(
    'roles/fetchRoles',
    async (_, { rejectWithValue }) => {
        try {
            const response = await authApi.getRoles().send();

            // Try to fetch with a timeout
            // const response = await fetchWithTimeout(8000);
            console.log('Fetched roles from API:', response);
            return response;
        } catch (error) {
            console.error('Failed to fetch roles:', error.message);
            return rejectWithValue('Failed to fetch roles: ' + error.message);
        }
    }
);

/**
 * Initial state for roles slice
 */
const initialState = {
    roles: {
        data: {},
        status: RequestStatus.IDLE,
        error: null,
        lastFetched: null
    },
    channels: {
        data: [
            {
                name: 'Youtube',
                iconKey: 'youtube',
                contentType: ['video', 'shorts']
            },
            {
                name: 'Instagram',
                iconKey: 'instagram',
                contentType: ['post', 'reels', 'stories']
            },
            // {
            //     name: 'Tiktok',
            //     iconKey: 'tiktok'
            // }
        ],
        error: null,
        lastFetched: null
    }
};

/**
 * Roles slice definition
 */
const systemSlice = createSlice({
    name: 'system',
    initialState,
    reducers: {
        setRoleFromStorage(state, action) {
            state.roles.data = action.payload.data;
            state.roles.status = action.payload.status;
        },
        clearRolesError(state) {
            state.roles.error = null;
        },
        resetRolesStatus(state) {
            state.roles.status = RequestStatus.IDLE;
        }
    },
    extraReducers: (builder) => {
        builder
            .addCase(fetchRolesThunk.pending, (state) => {
                state.roles.status = RequestStatus.LOADING;
                state.roles.error = null;
            })
            .addCase(fetchRolesThunk.fulfilled, (state, action) => {
                state.roles.status = RequestStatus.SUCCEEDED;

                // Transform the roles data to a more usable format
                // Input format: { "role-id-1": "role-name-1", "role-id-2": "role-name-2" }
                // Output format: [ { id: "role-id-1", name: "role-name-1" }, { id: "role-id-2", name: "role-name-2" } ]

                if (action.payload && action.payload.data) {
                    const rolesData = action.payload.data.data;
                    // Create a dictionary: { name: id }
                    const nameToIdMap = Object.entries(rolesData).reduce((acc, [id, name]) => {
                        acc[name] = id;
                        return acc;
                    }, {});
                    state.roles.data = nameToIdMap;
                    // Store roles
                    localStorage.setItem('role', JSON.stringify(nameToIdMap));
                } else {
                    state.roles.data = {};
                }

                state.roles.lastFetched = new Date().toISOString();
            })
            .addCase(fetchRolesThunk.rejected, (state, action) => {
                state.roles.status = RequestStatus.FAILED;
                state.roles.error = action.payload || 'Failed to fetch roles';
            });
    },
});

// Export actions
export const { clearRolesError, resetRolesStatus, setRoleFromStorage } = systemSlice.actions;

// // Export selectors
// export const selectRoles = (state) => state.roles.data;
// export const selectRolesStatus = (state) => state.roles.status;
// export const selectRolesError = (state) => state.roles.error;
// export const selectRolesLastFetched = (state) => state.roles.lastFetched;
// export const selectIsRolesLoading = (state) => state.roles.status === RequestStatus.LOADING;

// Selectors - Roles
export const selectRoles = (state) => state.system.roles.data;
export const selectRolesStatus = (state) => state.system.roles.status;
export const selectRolesError = (state) => state.system.roles.error;
export const selectRolesLastFetched = (state) => state.system.roles.lastFetched;
export const selectIsRolesLoading = (state) => state.system.roles.status === RequestStatus.LOADING;

// Selectors - Channels
export const selectChannels = (state) => state.system.channels.data;
export const selectChannelsError = (state) => state.system.channels.error;
export const selectChannelsLastFetched = (state) => state.system.channels.lastFetched;

// Selector: get icon (React component or URL) by iconKey
export const selectChannelIconByKey = (iconKey) => {
    const entry = channelIconMap[iconKey?.toLowerCase()];
    if (!entry) return null;
    return entry;
};

export const selectChannelIconByName = (state, channelName) => {
    const channel = state.system.channels.data.find(
        (c) => c.name.toLowerCase() === channelName.toLowerCase()
    );
    if (!channel) return null;
    return selectChannelIconByKey(channel.iconKey);
};



export const selectRawChannels = (state) => state.system.channels.data;

export const selectAllChannelsWithIcons = createSelector(
    [selectRawChannels],
    (channels) =>
        channels.map((channel) => ({
            name: channel.name,
            icon: selectChannelIconByKey(channel.iconKey),
            contentType: channel.contentType // or whatever transformation you're applying
        }))
);

// Export reducer
export default systemSlice.reducer;
