import React, { useEffect } from 'react';
// eslint-disable-next-line no-unused-vars
import { motion } from "framer-motion";

import brandSelectionFrame from '@assets/brand-selection-frame.svg';
import ellipse1 from '@assets/Ellipse-1.svg';
import ellipse2 from '@assets/Ellipse-2.svg';
import ellipse3 from '@assets/Ellipse-3.svg';
import ellipse4 from '@assets/Ellipse-4.svg';
import ellipse5 from '@assets/Ellipse-5.svg';
import ellipse6 from '@assets/Ellipse-6.svg';
import ellipse7 from '@assets/Ellipse-7.svg';
import ellipse8 from '@assets/Ellipse-8.svg';
import ellipse9 from '@assets/Ellipse-9.svg';

// Add CSS for gradient border and floating animation
const gradientBorderStyles = `
.gradient-border-container {
  position: relative;
  border-radius: 0.375rem;
}

.gradient-border-container:focus-within .gradient-border {
  opacity: 1;
}

.gradient-border {
  position: absolute;
  inset: -3px;
  border-radius: 0.575rem;
  padding: 3px;
  background: linear-gradient(93deg, #FF905E 0.47%, #FF80E8 100.03%);
  -webkit-mask: 
    linear-gradient(#fff 0 0) content-box, 
    linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  opacity: 0;
  transition: opacity 0.2s ease;
  z-index: 1;
}
`;

const floatVariants = (delay = 0) => ({
    initial: { opacity: 0, y: 10 },
    animate: {
        opacity: 1,
        y: [0, -10, 0],
        transition: {
            opacity: { duration: 1.5, delay, ease: "easeInOut" },
            y: {
                duration: 4,
                delay: delay + 0.8,
                repeat: Infinity,
                repeatType: "loop",
                ease: "easeInOut",
            }
        },
    },
});

const BrandOnboardingLayout = ({ children }) => {
    // Inject the gradient border styles
    useEffect(() => {
        const styleElement = document.createElement('style');
        styleElement.textContent = gradientBorderStyles;
        document.head.appendChild(styleElement);

        return () => {
            document.head.removeChild(styleElement);
        };
    }, []);

    return (
        <div className="relative w-full h-screen flex items-center justify-center">
            {/* Background Frame */}
            <div className="absolute inset-0 overflow-hidden" style={{ zIndex: 0 }}>
                <img
                    src={brandSelectionFrame}
                    alt="Background Frame"
                    className="w-full h-full object-cover"
                />

                {/* Animated Ellipses */}
                <motion.img src={ellipse1} alt="CapBoy"
                    className="absolute"
                    style={{ top: '10%', left: '35%', width: '50px', height: '50px', transform: 'translateX(-50%)', zIndex: 2 }}
                    variants={floatVariants(0.2)}
                    initial="initial"
                    animate="animate"
                />

                <motion.img src={ellipse2} alt="CurlyHair"
                    className="absolute"
                    style={{ top: '6%', right: '30%', width: '55px', height: '55px', zIndex: 2 }}
                    variants={floatVariants(0.4)}
                    initial="initial"
                    animate="animate"
                />

                <motion.img src={ellipse3} alt="CoolBoy"
                    className="absolute"
                    style={{ top: '12%', right: '15%', width: '80px', height: '80px', zIndex: 2 }}
                    variants={floatVariants(0.6)}
                    initial="initial"
                    animate="animate"
                />

                <motion.img src={ellipse4} alt="BlondeHair"
                    className="absolute"
                    style={{ top: '10%', left: '22%', width: '55px', height: '55px', zIndex: 2 }}
                    variants={floatVariants(0.8)}
                    initial="initial"
                    animate="animate"
                />

                <motion.img src={ellipse5} alt="Nigerian"
                    className="absolute"
                    style={{ top: '35%', left: '30%', width: '50px', height: '50px', zIndex: 2 }}
                    variants={floatVariants(1.0)}
                    initial="initial"
                    animate="animate"
                />

                <motion.img src={ellipse6} alt="TechBoy"
                    className="absolute"
                    style={{ top: '45%', left: '15%', width: '70px', height: '70px', zIndex: 2 }}
                    variants={floatVariants(1.2)}
                    initial="initial"
                    animate="animate"
                />

                <motion.img src={ellipse7} alt="Goodluck"
                    className="absolute"
                    style={{ top: '35%', right: '25.5%', width: '55px', height: '55px', zIndex: 2 }}
                    variants={floatVariants(1.4)}
                    initial="initial"
                    animate="animate"
                />

                <motion.img src={ellipse8} alt="PurpleHair"
                    className="absolute"
                    style={{ top: '60%', left: '68%', width: '65px', height: '65px', transform: 'translateX(-50%)', zIndex: 2 }}
                    variants={floatVariants(1.6)}
                    initial="initial"
                    animate="animate"
                />
                
                <motion.img src={ellipse9} alt="Cowboy"
                    className="absolute"
                    style={{ top: '25%', left: '58%', width: '65px', height: '65px', transform: 'translateX(-50%)', zIndex: 2 }}
                    variants={floatVariants(1.6)}
                    initial="initial"
                    animate="animate"
                />
            </div>

            {/* Content */}
            {children}
        </div>
    );
};

export default BrandOnboardingLayout;