import React, { useState, useEffect, useRef } from 'react';
import { HiOutlineTrash } from "react-icons/hi2";
import AvatarStack from '@shared/components/AvatarStack';


const CreatorListCard = ({
    name,
    createdBy,
    createdOn,
    lastModifiedOn,
    creators = [],
    isNew = false,
    onDelete,
    onNameChange,
    onCancel,
    onClick, // ✅ added

}) => {
    const [editableName, setEditableName] = useState(name);
    const [isEditing, setIsEditing] = useState(isNew);
    const inputRef = useRef(null);
    const wrapperRef = useRef(null);

    // 🟡 Handle click outside
    useEffect(() => {
        function handleClickOutside(event) {
            if (wrapperRef.current && !wrapperRef.current.contains(event.target)) {
                if (isNew) {
                    onCancel?.(); // Remove new card
                } else {
                    setIsEditing(false); // Just close editing
                }
            }
        }

        function handleKeyDown(event) {
            if (event.key === 'Escape') {
                if (isNew) {
                    onCancel?.();
                } else {
                    setIsEditing(false);
                    setEditableName(name); // revert changes
                }
            }
        }

        document.addEventListener('mousedown', handleClickOutside);
        document.addEventListener('keydown', handleKeyDown);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
            document.removeEventListener('keydown', handleKeyDown);
        };
    }, [isNew, onCancel, name]);

    function formatDateToReadable(dateString) {
        if (!dateString) return '';

        const date = new Date(dateString);

        if (isNaN(date)) return ''; // handle invalid date

        return date.toLocaleDateString('en-GB', {
            day: '2-digit',
            month: 'long',
            year: 'numeric',
        });
    }


    return (
        <div
            ref={wrapperRef}
            onClick={onClick} // ✅ add click handler
            className="group relative w-full border-l-3 hover:border-l-0 border-purple p-5 bg-gray-600 rounded-lg shadow-md text-white  hover:scale-101 hover:transform-gpu transition pointer-events-auto"
        >
            {!isEditing && <button
                onClick={(e) => {
                    e.stopPropagation(); // 🛑 prevent card click
                    onDelete?.();
                }}
                className="absolute z-10 top-5 right-5 text-gray-50 opacity-0 group-hover:opacity-100 transition hover:text-red-2 hover:cursor-pointer"
            >
                {name !== 'My Creators' && (<HiOutlineTrash className='h-5 w-5' />)}

            </button>}


            <div className='flex flex-col gap-5 h-full'>
                {
                    isEditing ? (
                        <input
                            ref={inputRef}
                            autoFocus
                            placeholder='Enter new list name'
                            className="text-lg font-semibold bg-transparent border-b-1 border-gray-400 outline-none w-full"
                            value={editableName}
                            onChange={(e) => setEditableName(e.target.value)}
                            onKeyDown={(e) => {
                                if (e.key === 'Enter') {
                                    setIsEditing(false);
                                    onNameChange?.(editableName);
                                }
                            }}
                        />
                    ) : (
                        <div className="text-lg font-semibold">{editableName}</div>
                    )
                }

                <div className="flex gap-5 h-full">
                    <div className="flex flex-col justify-between w-1/2 gap-5">
                        <div className="flex flex-col gap-2 items-start">
                            <span className='text-14-medium text-gray-200'>Creators</span>
                            <AvatarStack avatars={creators} maxAvatars={3} count={creators.length - 3} className="" />
                        </div>
                        <div className='flex flex-col gap-2 '>
                            <div className='text-14-medium text-gray-200'>Created By</div>
                            <div className="text-16-semibold text-white">{createdBy}</div>
                        </div>

                    </div>

                    <div className="flex flex-col justify-between ml-5">
                        <div className='flex flex-col gap-2'>
                            <div className='text-14-medium text-gray-200'>Last Modified on</div>
                            <div className="text-16-semibold text-white">{formatDateToReadable(lastModifiedOn)}</div>
                        </div>
                        <div className='flex flex-col gap-2'>
                            <div className='text-14-medium text-gray-200'>Created On</div>
                            <div className="text-16-semibold text-white">{formatDateToReadable(createdOn)}</div>
                        </div>
                    </div>
                </div>
            </div>

        </div >
    );
};

export default CreatorListCard;
