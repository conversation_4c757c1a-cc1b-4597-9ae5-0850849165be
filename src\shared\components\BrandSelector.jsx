import React, { useState, useRef, useEffect } from 'react';
import { FaChevronDown, FaChevronUp, FaCheck } from 'react-icons/fa';
import useBrandSelectors from '@brand/services/brandSelectors';
import useBrandActions from '@brand/services/brandActions';
import { useDispatch } from 'react-redux';

export default function BrandSelector({ className }) {
    const [isOpen, setIsOpen] = useState(false);
    const ref = useRef(null);
    const { allocatedBrands, selectedBrand } = useBrandSelectors();
    const { setSelectedBrand } = useBrandActions();

    const dispatch = useDispatch();

    // Auto-select first brand only if none is selected
    useEffect(() => {
        if (allocatedBrands?.length > 0 && !selectedBrand) {

            const defaultBrand = allocatedBrands[0];
            dispatch(setSelectedBrand(defaultBrand));
        }
    }, [allocatedBrands, dispatch, selectedBrand]);

    const handleClickOutside = (e) => {
        if (ref.current && !ref.current.contains(e.target)) {
            setIsOpen(false);
        }
    };

    useEffect(() => {
        document.addEventListener('mousedown', handleClickOutside);
        return () => document.removeEventListener('mousedown', handleClickOutside);
    }, []);

    const handleBrandChange = (brand) => {
        dispatch(setSelectedBrand(brand));
        setIsOpen(false);
    };

    return (
        <div className={`relative ${className}`} ref={ref}>
            {/* Trigger */}
            <button
                className="cursor-pointer flex h-[42px] items-center gap-2 px-4 py-2 rounded-lg border border-gray-700 bg-[#0F0F0F] hover:border-gray-500 transition-colors"
                onClick={() => setIsOpen(!isOpen)}
            >
                {selectedBrand?.logo && <img src={selectedBrand.logo} alt={selectedBrand?.name} className="w-6 h-6" />}
                <span className="text-white text-sm font-medium">{selectedBrand?.name}</span>
                {isOpen ? (
                    <FaChevronUp className="text-white text-sm ml-1" />
                ) : (
                    <FaChevronDown className="text-white text-sm ml-1" />
                )}
            </button>

            {/* Dropdown */}
            {isOpen && (
                <div className="absolute right-0 mt-2 bg-[#0F0F0F] rounded-lg shadow-lg z-50 w-[212px] border border-gray-700 py-2 px-2">
                    {allocatedBrands.map((brand) => (
                        <div key={brand.id}>
                            <button
                                className={`cursor-pointer rounded-md w-full flex items-center justify-between px-3 py-2 hover:bg-gray-900 hover:scale-103 transition-colors ${selectedBrand?.id === brand.id ? 'bg-gray-800' : ''
                                    }`}
                                onClick={() => handleBrandChange(brand)}
                            >
                                <div className="flex items-center gap-2">
                                    {brand.logo && <img src={brand.logo} alt={brand.name} className="w-5 h-5" />}
                                    <span className="text-white text-sm">{brand.name}</span>
                                </div>
                                {selectedBrand?.id === brand.id && <FaCheck className="text-green-500 text-xs" />}
                            </button>
                            {/* {idx === 0 && <div className="border-t border-gray-700 my-2 mx-3" />} */}
                        </div>
                    ))}
                </div>
            )}
        </div>
    );
}
