import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import './index.css'
import App from './app/App.jsx'

// Prevent default browser behavior for file drops globally
// This prevents the browser from opening files when dropped outside valid drop zones
document.addEventListener('dragover', (event) => {
  event.preventDefault();
  event.stopPropagation();
}, false);

document.addEventListener('drop', (event) => {
  event.preventDefault();
  event.stopPropagation();
  console.log('Global drop event intercepted - preventing browser default');
}, false);

createRoot(document.getElementById('root')).render(
  <StrictMode>
    <App />
  </StrictMode>,
)
