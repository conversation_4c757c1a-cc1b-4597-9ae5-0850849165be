import React, { useState } from 'react';

// Import components
import AvatarStack from '@shared/components/AvatarStack';

// Import icons
import UpIcon from '@assets/icon/up.svg';
import DownIcon from '@assets/icon/down.svg';
import ArrowUpIcon from '@assets/icon/arrow_upward.svg';
import ArrowDownIcon from '@assets/icon/arrow_downward.svg';
import ErrorOutlineIcon from '@assets/icon/error_outline.svg';
import VerifiedIcon from '@assets/icon/blue_verified.svg';
import infoIcon from '@assets/icon/info.svg';
import BookmarkIcon from '@assets/icon/bookmark_border-white.svg';
import CheckIcon from "@assets/icon/check.svg";

const TooltipSpan = ({ text, tooltipText }) => {
    const [isTooltipVisible, setIsTooltipVisible] = useState(false);

    return (
        <div className="relative inline-flex items-center">
            {text}
            <div
                className="relative ml-1"
                onMouseEnter={() => setIsTooltipVisible(true)}
                onMouseLeave={() => setIsTooltipVisible(false)}
                onFocus={() => setIsTooltipVisible(true)}
                onBlur={() => setIsTooltipVisible(false)}
                tabIndex={0}
            >
                <img
                    src={infoIcon}
                    className="w-4 h-4 cursor-help"
                    alt="Info"
                />
                {isTooltipVisible && (
                    <div className="absolute z-50 bottom-full left-1/2 transform -translate-x-1/2 -translate-y-2 px-3 py-2 bg-gray-800 text-white text-sm rounded shadow-lg max-w-xs w-max">
                        {tooltipText}
                    </div>
                )}
            </div>
        </div>
    );
};


const CreatorTable = ({ creators, sortConfig, onSort }) => {
    // const [bookmarkedCreators, setBookmarkedCreators] = useState(new Set());
    const [selectedCreators, setSelectedCreators] = useState(new Set());
    const [selectAll, setSelectAll] = useState(false);

    // const toggleBookmark = (creatorId) => {
    //     setBookmarkedCreators(prev => {
    //         const newSet = new Set(prev);
    //         if (newSet.has(creatorId)) {
    //             newSet.delete(creatorId);
    //         } else {
    //             newSet.add(creatorId);
    //         }
    //         return newSet;
    //     });
    // };

    const toggleSelectCreator = (creatorId) => {
        setSelectedCreators(prev => {
            const newSet = new Set(prev);
            if (newSet.has(creatorId)) {
                newSet.delete(creatorId);
            } else {
                newSet.add(creatorId);
            }
            return newSet;
        });
    };

    const toggleSelectAll = () => {
        if (selectAll) {
            setSelectedCreators(new Set());
        } else {
            setSelectedCreators(new Set(creators.map(creator => creator.id)));
        }
        setSelectAll(!selectAll);
    };

    const getSortIcon = (columnKey) => {
        if (sortConfig.key !== columnKey) return null;
        return sortConfig.direction === 'asc' ? UpIcon : DownIcon;
    };

    const renderCreatorverseScore = (score) => {
        const getScoreColor = (score) => {
            if (score >= 80) return 'bg-green-2';
            if (score >= 40 && score <= 79) return 'bg-orange';
            if (score < 40) return 'bg-red-2';
            return 'text-red-400';
        };
        
        const getScoreIcon = (score) => {
            if (score >= 80) return ArrowUpIcon;
            if (score >= 40 && score <= 79) return ErrorOutlineIcon;
            if (score < 40) return ArrowDownIcon;
            return 'text-red-400';
        };

        return (
            <div className={`flex items-center rounded-md py-1 px-1.5 w-fit gap-1 ${getScoreColor(score)}`}>
                <span className={`font-medium text-white `}>{score}%</span>
                <img src={getScoreIcon(score)} alt="" />
            </div>
        );
    };

    const renderFollowers = (followers) => {
        return <span className="font-medium">{followers}</span>;
    };

    const renderEngagementRate = (rate) => {
        const numericRate = rate ? parseFloat(rate) : 0;
        const getEngagementColor = (rate) => {
            if (rate >= 5) return 'text-white';
            if (rate >= 3) return 'text-white';
            return 'text-red-400';
        };

        return (
            <span className={`font-medium ${getEngagementColor(numericRate)}`}>
                {rate}
            </span>
        );
    };

    const renderAudienceCredibility = (credibility) => {
        const getCredibilityColor = (cred) => {
            if (cred >= 90) return 'text-white';
            if (cred >= 80) return 'text-white';
            return 'text-orange-400';
        };

        return (
            <div className="flex items-center gap-1">
                <span className={`font-medium ${getCredibilityColor(credibility)}`}>{credibility}%</span>
            </div>
        );
    };
    const renderCategories = (categories) => {
        return (
            <div className="flex flex-col gap-1">
                {/* First row - violet pills */}
                <div className="flex flex-wrap gap-1">
                    {categories.primary.map((category, index) => (
                        <span
                            key={`first-${index}`}
                            className="text-violet bg-light-1 rounded-full px-3 py-1 h-6 text-14-regular border-1 border-violet"
                        >
                            {category}
                        </span>
                    ))}
                </div>

                {/* Second row - green pills */}
                {categories.secondary.length > 0 && (
                    <div className="flex flex-wrap gap-1">
                        {categories.secondary.map((category, index) => (
                            <span
                                key={`second-${index}`}
                                className="text-teal bg-light-8 rounded-full px-3 py-1 h-6 text-14-regular border-1 border-teal"
                            >
                                {category}
                            </span>
                        ))}
                    </div>
                )}
            </div>
        );
    };
    const renderPastCollaborations = (collaborations) => {
        // Create avatar-like URLs using first letter of each collaboration
        const maxVisible = 3;
        const remainingCount = collaborations.length > maxVisible ? collaborations.length - maxVisible : 0;

        return (
            <AvatarStack
                avatars={collaborations}
                count={remainingCount}
                maxAvatars={maxVisible}
                className="ml-1"
            />
        );
    };

    const SortableHeader = ({ children, sortKey, className = "" }) => (
        <th
            className={`px-4 py-3 text-left cursor-pointer hover:bg-gray-800 transition-colors ${className}`}
            onClick={() => onSort(sortKey)}
        >
            <div className="flex items-center gap-1">
                <span className="text-sm font-medium text-white">{children}</span>
                {getSortIcon(sortKey) && (
                    <img src={getSortIcon(sortKey)} alt="Sort" className="w-3 h-3" />
                )}
            </div>
        </th>
    ); return (
        <div className="rounded-lg ">
            <table className="w-full text-sm text-left text-gray-300 border-collapse">
                <thead className="text-xs capitalize bg-gray-600 text-gray-400">
                    <tr className="border-b-2 border-brand-500 text-gray-50 text-14-medium">
                        <th className="px-5 py-5 w-10">
                            <div className="flex items-center justify-center">
                                <input
                                    type="checkbox"
                                    checked={selectAll}
                                    onChange={toggleSelectAll}
                                    className="w-4 h-4 rounded border-gray-600 bg-gray-700 text-blue-500 focus:ring-blue-500 focus:ring-offset-gray-800"
                                />
                            </div>
                        </th>
                        <th className="px-5 py-5 w-[180px]">Name</th>
                        <th
                            className="px-5 py-5 cursor-pointer hover:text-white w-[150px]"
                            onClick={() => onSort('creatorverseScore')}
                        >
                            <div className="flex items-center gap-1.5">
                                {/* <img src={StarIcon} alt="Star" className="w-4 h-4" /> */}
                                {/* <span>CV Score</span> */}
                                <TooltipSpan
                                    text="Creatorverse Score"
                                    tooltipText="A combined metric of engagement quality, audience authenticity, and content performance"
                                />
                                {getSortIcon('creatorverseScore') && (
                                    <img
                                        src={getSortIcon('creatorverseScore')}
                                        alt="Sort"
                                        className="w-3 h-3 ml-1"
                                    />
                                )}
                            </div>
                        </th>
                        <th
                            className="px-5 py-5 cursor-pointer hover:text-white w-[110px]"
                            onClick={() => onSort('followers')}
                        >
                            <div className="flex items-center">
                                <span>Followers</span>
                                {getSortIcon('followers') && (
                                    <img
                                        src={getSortIcon('followers')}
                                        alt="Sort"
                                        className="w-3 h-3 ml-1"
                                    />
                                )}
                            </div>
                        </th>
                        <th
                            className="px-5 py-5 cursor-pointer hover:text-white w-[140px]"
                            onClick={() => onSort('engagementRate')}
                        >
                            <div className="flex items-center">
                                <TooltipSpan
                                    text=" engagement rate "
                                    tooltipText="Average engagement (likes, comments, shares) divided by follower count"
                                />
                                {getSortIcon('engagementRate') && (
                                    <img
                                        src={getSortIcon('engagementRate')}
                                        alt="Sort"
                                        className="w-3 h-3 ml-1"
                                    />
                                )}
                            </div>
                        </th>
                        <th
                            className="px-5 py-5 cursor-pointer hover:text-white w-[150px]"
                            onClick={() => onSort('audienceCredibility')}
                        >
                            <div className="flex items-center">
                                <TooltipSpan
                                    text="Audience Credibility"
                                    tooltipText="Percentage of real, active followers versus bots or inactive accounts"
                                />
                                {getSortIcon('audienceCredibility') && (
                                    <img
                                        src={getSortIcon('audienceCredibility')}
                                        alt="Sort"
                                        className="w-3 h-3 ml-1"
                                    />
                                )}
                            </div>
                        </th>
                        <th className="px-5 py-5 w-[180px]">Categories</th>
                        <th className="px-5 py-5 w-[150px]">Past Collaborations</th>
                        {/* <th className="px-5 py-5 w-[170px]">Action</th> */}
                    </tr>
                </thead>
                <tbody className='bg-transparent'>
                    {creators.map((creator) => (
                        <tr
                            key={creator.id}
                            className="border-b border-gray-800 hover:bg-gray-800/50 transition-colors"
                        >
                            {/* Checkbox */}
                            <td className="px-3 py-3 pl-4">
                                <div className="flex items-center justify-center">
                                    <input
                                        type="checkbox"
                                        checked={selectedCreators.has(creator.id)}
                                        onChange={() => toggleSelectCreator(creator.id)}
                                        className="w-4 h-4 rounded border-gray-600 bg-gray-700 text-blue-500 focus:ring-blue-500 focus:ring-offset-gray-800"
                                    />
                                </div>
                            </td>
                            {/* Creator Info */}
                            <td className="px-3 py-3">
                                <div className="flex items-center gap-3">
                                    <div className="relative">
                                        <img
                                            src={creator.avatar}
                                            alt={creator.name}
                                            className="w-10 h-10 rounded-full object-cover"
                                        />

                                    </div>
                                    <div className="flex flex-col">
                                        <div className="relative flex items-center gap-1">
                                            <span className="font-semibold text-white">{creator.name}</span>
                                            {creator.verified && (
                                                <img className="h-3 w-3" src={VerifiedIcon} alt="Verified" />
                                            )}
                                        </div >

                                        <div className="flex items-center gap-1">
                                            <span className="text-12-regular text-gray-300">
                                                @{creator.userName}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </td>
                            {/* Creatorverse Score */}
                            <td className="px-3 py-3">
                                {renderCreatorverseScore(creator.creatorverseScore)}
                            </td>

                            {/* Followers */}
                            <td className="px-3 py-3 text-white">
                                {renderFollowers(creator.followers)}
                            </td>

                            {/* Engagement Rate */}
                            <td className="px-3 py-3">
                                {renderEngagementRate(creator.engagementRate)}
                            </td>

                            {/* Audience Credibility */}
                            <td className="px-3 py-3">
                                {renderAudienceCredibility(creator.audienceCredibility)}
                            </td>

                            {/* Categories */}
                            <td className="px-3 py-3">
                                {renderCategories(creator.categories)}
                            </td>

                            {/* Past Collaborations */}
                            <td className="px-3 py-3">
                                {renderPastCollaborations(creator.pastCollaborations)}
                            </td>
                            {/* Actions */}
                            {/* <td className="px-3 py-3">
                                <div className="flex items-center gap-2 justify-end">
                                    <button
                                        onClick={() => toggleBookmark(creator.id)}
                                        className={`flex items-center justify-center w-7 h-7 rounded transition-colors ${bookmarkedCreators.has(creator.id)
                                            ? 'bg-blue-500 text-white'
                                            : 'bg-gray-800 hover:bg-gray-700'
                                            }`}
                                    >
                                        <img src={BookmarkIcon} alt="Bookmark" className="w-3.5 h-3.5" />
                                    </button>
                                    <button className="px-2.5 py-1 bg-blue-500 text-white rounded text-xs hover:bg-blue-600 transition-colors">
                                        View Profile
                                    </button>
                                </div>
                            </td> */}
                        </tr>
                    ))}
                </tbody>
            </table>

            {/* Empty State */}
            {creators.length === 0 && (
                <div className="py-12 text-center">
                    <div className="text-gray-400 text-16-regular">
                        No creators found matching your criteria.
                    </div>
                    <div className="text-gray-500 text-14-regular mt-1">
                        Try adjusting your search or filters.
                    </div>
                </div>
            )}
        </div>
    );
};

export default CreatorTable;
