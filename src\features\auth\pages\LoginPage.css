/* You can put this in your global CSS or Tailwind config */
.business-gradient-border {
  position: relative;
  z-index: 0;
  border-radius: 12px; /* Rounded corners */
  overflow: hidden;
}

.business-gradient-border::before {
  content: "";
  position: absolute;
  inset: 0;
  padding: 1.5px;
  background: linear-gradient(93.42deg, #48C9EC 0.47%, #2AE8B8 100.03%);
  border-radius: 12px;
  -webkit-mask:
    linear-gradient(#fff 0 0) content-box,
    linear-gradient(#fff 0 0);
  mask:
    linear-gradient(#fff 0 0) content-box,
    linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  z-index: -1;
}

.shadow-glow {
  box-shadow: 0px 0px 9px rgba(72, 200, 236, 0.8);
}


.creator-gradient-border {
  position: relative;
  z-index: 0;
  border-radius: 10px;
  overflow: hidden;
  background: var(--Gray-900, #2E2E2E);
}

.creator-gradient-border::before {
  content: "";
  position: absolute;
  inset: 0;
  padding: 1.5px;
  background: linear-gradient(93.42deg, #FF905E 0.47%, #FF80E8 100.03%);
  border-radius: 10px;
  -webkit-mask:
    linear-gradient(#fff 0 0) content-box,
    linear-gradient(#fff 0 0);
  mask:
    linear-gradient(#fff 0 0) content-box,
    linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  z-index: -1;
}

.shadow-creator-glow {
  box-shadow: 0px 0px 9px 0px #FF7850;
}