# Route Protection System

## Overview

The Creatorverse Frontend implements a comprehensive route protection system that handles authentication and user type-based access control. The system ensures that:

1. **Unauthenticated users** are redirected to appropriate login pages
2. **Authenticated users** are redirected away from auth pages to their dashboards
3. **User type mismatches** are handled by redirecting to the correct dashboard
4. **Post-login redirects** work correctly to return users to their intended destination

## Components

### ProtectedRoute

Protects routes that require authentication and optionally enforces user type restrictions.

```javascript
import ProtectedRoute from '../shared/components/ProtectedRoute';

// Basic protection (authentication only)
<ProtectedRoute>
  <SomeProtectedComponent />
</ProtectedRoute>

// User type-specific protection
<ProtectedRoute userType="brand">
  <BrandSpecificComponent />
</ProtectedRoute>

<ProtectedRoute userType="influencer">
  <InfluencerSpecificComponent />
</ProtectedRoute>
```

**Behavior**:
- **Unauthenticated users**: Redirected to login page with return path saved
- **Wrong user type**: Redirected to appropriate dashboard for their user type
- **Correct user type**: Renders children components

### PublicRoute

Protects public routes (like login/signup pages) from authenticated users.

```javascript
import PublicRoute from '../shared/components/PublicRoute';

// Basic public route
<PublicRoute>
  <LoginComponent />
</PublicRoute>

// User type-specific public route
<PublicRoute userType="brand">
  <BrandLoginComponent />
</PublicRoute>

<PublicRoute userType="influencer">
  <InfluencerLoginComponent />
</PublicRoute>
```

**Behavior**:
- **Unauthenticated users**: Renders children components
- **Authenticated users**: Redirected to appropriate dashboard
- **Post-login redirect**: Honors saved return path if available

## User Type Detection

The system automatically detects user type based on auth state data:

### Brand Users
- Have `allocatedBrands` array with data
- Have `organizationBrands` array with data

### Influencer Users
- Have `socialProfiles` array with data

### Utility Functions

```javascript
import { 
  getUserType, 
  isBrandUser, 
  isInfluencerUser,
  getUserDashboardPath,
  getLoginPath,
  canUserAccessRoute 
} from '../shared/utils/userTypeUtils';

// Get user type
const userType = getUserType(authState); // 'brand', 'influencer', or null

// Check user type
const isBrand = isBrandUser(authState);
const isInfluencer = isInfluencerUser(authState);

// Get appropriate paths
const dashboardPath = getUserDashboardPath(authState);
const loginPath = getLoginPath('brand'); // or 'influencer'

// Check route access
const canAccess = canUserAccessRoute(authState, 'brand');
```

### Custom Hook

```javascript
import useUserType from '../shared/hooks/useUserType';

const MyComponent = () => {
  const {
    userType,
    isBrand,
    isInfluencer,
    isAuthenticated,
    getDashboardPath,
    getLoginPath,
    canAccessRoute,
    canAccessBrandRoutes,
    canAccessInfluencerRoutes
  } = useUserType();

  // Use the data and helper functions
  if (!isAuthenticated) {
    // Handle unauthenticated state
  }

  if (isBrand) {
    // Handle brand user logic
  }

  if (isInfluencer) {
    // Handle influencer user logic
  }
};
```

## Router Configuration

### Public Routes (Auth Pages)

```javascript
// Influencer auth routes
<Route path="/influencer/:authType" element={
  <PublicRoute userType="influencer">
    <CreatorAuthPage />
  </PublicRoute>
} />

// Brand auth routes
<Route path="/brand/:authType" element={
  <PublicRoute userType="brand">
    <BrandAuthPage />
  </PublicRoute>
} />

// General public routes
<Route path="/profile-onboarding" element={
  <PublicRoute>
    <ProfileOnboarding />
  </PublicRoute>
} />
```

### Protected Routes (Dashboard Pages)

```javascript
// Influencer protected routes
<Route path="/influencer" element={
  <ProtectedRoute userType="influencer">
    <DashboardLayout type="influencer" />
  </ProtectedRoute>
}>
  <Route path="dashboard" element={<DashboardHome />} />
  <Route path="profile-analytics" element={<ProfileAnalyticsPage />} />
</Route>

// Brand protected routes
<Route path="/brand" element={
  <ProtectedRoute userType="brand">
    <DashboardLayout type="brand" />
  </ProtectedRoute>
}>
  <Route path="dashboard" element={<BrandDashboard />} />
  <Route path="discovery" element={<Discovery />} />
</Route>
```

## Authentication Flow Examples

### Scenario 1: Unauthenticated User Accessing Protected Route

1. User navigates to `/brand/dashboard`
2. `ProtectedRoute` detects no authentication
3. User redirected to `/brand/signin` with return path saved
4. After login, user redirected back to `/brand/dashboard`

### Scenario 2: Authenticated User Accessing Wrong Auth Page

1. Brand user navigates to `/influencer/login`
2. `PublicRoute` detects authentication
3. User redirected to `/brand/dashboard` (their appropriate dashboard)

### Scenario 3: Wrong User Type Accessing Protected Route

1. Influencer user navigates to `/brand/dashboard`
2. `ProtectedRoute` detects user type mismatch
3. User redirected to `/influencer/dashboard` (their appropriate dashboard)

### Scenario 4: Correct User Type Accessing Protected Route

1. Brand user navigates to `/brand/dashboard`
2. `ProtectedRoute` validates authentication and user type
3. User sees the brand dashboard

## Login Path Configuration

### Current Login Paths

- **Brand users**: `/brand/signin`
- **Influencer users**: `/influencer/login`

### Dashboard Paths

- **Brand users**: `/brand/dashboard`
- **Influencer users**: `/influencer/dashboard`

## Error Handling

The system gracefully handles edge cases:

- **Undetermined user type**: Falls back to influencer routes
- **Missing auth data**: Treats as unauthenticated
- **Invalid routes**: Redirects to default login page

## Security Features

1. **Complete Authentication Check**: Verifies both token and user data
2. **User Type Validation**: Ensures users can only access appropriate routes
3. **Return Path Security**: Validates return paths to prevent open redirects
4. **Graceful Degradation**: Handles missing or invalid data safely

## Testing Scenarios

### Authentication Tests
- [ ] Unauthenticated access to protected routes
- [ ] Authenticated access to public routes
- [ ] Post-login redirect functionality
- [ ] Logout and re-authentication flow

### User Type Tests
- [ ] Brand user accessing influencer routes
- [ ] Influencer user accessing brand routes
- [ ] User with mixed data (edge case)
- [ ] User with no type-specific data

### Edge Cases
- [ ] Corrupted auth state
- [ ] Missing localStorage data
- [ ] Network failures during auth check
- [ ] Concurrent login sessions

## Best Practices

1. **Always specify userType** for type-specific routes
2. **Use utility functions** for consistent user type detection
3. **Handle loading states** during authentication checks
4. **Provide fallback routes** for edge cases
5. **Test all authentication scenarios** thoroughly

## Migration Notes

### From Previous Implementation

The enhanced route protection system is backward compatible but provides additional features:

- **Automatic user type detection**
- **Improved redirect logic**
- **Better error handling**
- **Utility functions for common operations**

### Breaking Changes

None. All existing route configurations continue to work as before.
