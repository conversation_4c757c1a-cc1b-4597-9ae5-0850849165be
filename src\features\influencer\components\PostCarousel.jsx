import React, { useState } from "react";
import PostCard from "./PostCard";
import Image1 from "@assets/image/Image1.png";
import Image2 from "@assets/image/Image2.png";
import Image3 from "@assets/image/Image3.png";

// Helper to chunk array
const chunkArray = (arr, size) => {
    const result = [];
    for (let i = 0; i < arr.length; i += size) {
        result.push(arr.slice(i, i + size));
    }
    return result;
};

const PostCarousel = ({ platform, isConfigured = false, postCount = 4 }) => {
    const [currentPage, setCurrentPage] = useState(0);

    console.log('PostCarousel:', platform, isConfigured);

    if (isConfigured === false) {
        return null;
    }

    const posts = {
        instagram: [
            { image: Image1, likes: 1200, comments: 150, shares: 30, username: "aditya_jain", description:"A beautiful sunset", timeAgo: "2h ago" },
            { image: Image2, likes: 980, comments: 90, shares: 20, username: "aditya_jain", description:"Exploring the mountains", timeAgo: "5h ago" },
            { image: Image3, likes: 1500, comments: 200, shares: 50, username: "aditya_jain", description:"City lights at night", timeAgo: "1d ago" },
            { image: Image1, likes: 1200, comments: 150, shares: 30, username: "aditya_jain", description:"A beautiful sunset", timeAgo: "2h ago" },
            { image: Image3, likes: 980, comments: 90, shares: 20, username: "aditya_jain", description:"Exploring the mountains", timeAgo: "5h ago" },
            { image: Image3, likes: 1500, comments: 200, shares: 50, username: "aditya_jain", description:"City lights at night", timeAgo: "1d ago" },
        ],
        youtube: [
            { image: Image1, likes: 1200, comments: 150, shares: 30, username: "aditya_jain", description:"A beautiful sunset", timeAgo: "2h ago" },
            { image: Image2, likes: 980, comments: 90, shares: 20, username: "aditya_jain", description:"Exploring the mountains", timeAgo: "5h ago" },
            { image: Image3, likes: 1500, comments: 200, shares: 50, username: "aditya_jain", description:"City lights at night", timeAgo: "1d ago" },
            { image: Image1, likes: 1200, comments: 150, shares: 30, username: "aditya_jain", description:"A beautiful sunset", timeAgo: "2h ago" },
        ],
    };

    const chunkedPosts = chunkArray(posts[platform], postCount);

    return (
        <div className="py-5 ">
            <div className="text-gray-50 text-20-semibold mb-5">Your Top Performing Posts 📢</div>
            <div className="flex gap-5 transition-all duration-500 min-h-[310px] overflow-x-auto box-border">
                {chunkedPosts[currentPage].map((post, index) => (
                    <PostCard key={index} {...post} />
                ))}
            </div>
            <div className="flex justify-center mt-4 gap-2">
                {chunkedPosts.map((_, index) => (
                    <div
                        key={index}
                        onClick={() => setCurrentPage(index)}
                        className={`w-2.5 h-2.5 rounded-full ${currentPage === index ? 'bg-gray-300 w-5' : 'bg-gray-400'}`}
                    />
                ))}
            </div>
        </div>
    );
};

export default PostCarousel;
