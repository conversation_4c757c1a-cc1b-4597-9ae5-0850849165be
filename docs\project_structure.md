# Project Structure

This document outlines the directory and file structure of the Creatorverse Frontend project.

```
E:/Project/creatorverse_frontend/
├───.gitignore
├───eslint.config.js
├───index.html
├───package.json
├───pnpm-lock.yaml
├───README.md
├───vite.config.js
├───.git/...
├───.vscode/
├───node_modules/...
├───public/
│   ├───robots.txt
│   └───vite.svg
├───src/
│   ├───index.css
│   ├───main.jsx
│   ├───app/
│   │   ├───App.css
│   │   ├───App.jsx
│   │   ├───AppInitializer.jsx
│   │   ├───router.jsx
│   │   ├───store.js
│   │   └───store/
│   │       ├───enum.js
│   │       ├───api/
│   │       │   ├───alovaInstance.js
│   │       │   ├───authApi.js
│   │       │   └───brandManagementApi.js
│   │       └───slices/
│   │           └───rolesSlice.js
│   ├───assets/
│   │   ├───brand-selection-frame.svg
│   │   ├───BrandsSection.svg
│   │   ├───briefcase.svg
│   │   ├───cash.svg
│   │   ├───creator1.svg
│   │   ├───creator2.svg
│   │   ├───creator3.svg
│   │   ├───Ellipse-1.svg
│   │   ├───Ellipse-2.svg
│   │   ├───Ellipse-3.svg
│   │   ├───Ellipse-4.svg
│   │   ├───Ellipse-5.svg
│   │   ├───Ellipse-6.svg
│   │   ├───Ellipse-7.svg
│   │   ├───Ellipse-8.svg
│   │   ├───Ellipse-9.svg
│   │   ├───Frame1.svg
│   │   ├───Frame2.svg
│   │   ├───Frame3.svg
│   │   ├───greenTick.svg
│   │   ├───react.svg
│   │   ├───rocket.svg
│   │   ├───Section.svg
│   │   ├───social-card1.png
│   │   ├───social-card2.svg
│   │   ├───Emoji/
│   │   │   └───Waving_Hand_Light_Skin_Tone.png
│   │   ├───Gif/
│   │   │   ├───invite.mp4
│   │   │   └───rocket.mp4
│   │   ├───Icon/
│   │   │   ├───ai-search.svg
│   │   │   ├───all_inclusive-blue.svg
│   │   │   ├───all_inclusive.svg
│   │   │   ├───arrow_downward.svg
│   │   │   ├───arrow_upward.svg
│   │   │   ├───arrow-down-left.svg
│   │   │   ├───arrow-left.svg
│   │   │   ├───arrow-up-right.svg
│   │   │   ├───blue-cross.svg
│   │   │   ├───bookmark_border-white.svg
│   │   │   ├───bookmark-filled.svg
│   │   │   ├───bookmark.svg
│   │   │   ├───bullhorn.svg
│   │   │   ├───calendar.svg
│   │   │   ├───chart-pie-alt.svg
│   │   │   ├───chat.svg
│   │   │   ├───check.svg
│   │   │   ├───commentDot.svg
│   │   │   ├───content_copy.svg
│   │   │   ├───down.svg
│   │   │   ├───download.svg
│   │   │   ├───earning-1.svg
│   │   │   ├───earning-2.svg
│   │   │   ├───earning-3.svg
│   │   │   ├───earth-americas.svg
│   │   │   ├───edit.svg
│   │   │   ├───error_outline.svg
│   │   │   ├───error.svg
│   │   │   ├───eye.svg
│   │   │   ├───favorite_border.svg
│   │   │   ├───filter.svg
│   │   │   ├───gem.svg
│   │   │   ├───google-icon.svg
│   │   │   ├───heart.svg
│   │   │   ├───Icon.svg
│   │   │   ├───info.svg
│   │   │   ├───instagram-blue.svg
│   │   │   ├───instagram-circle.svg
│   │   │   ├───instagram-gray.svg
│   │   │   ├───instagram-icon.png
│   │   │   ├───instagram-s-icon.png
│   │   │   ├───lightbulb.svg
│   │   │   ├───lightbulb1.svg
│   │   │   ├───location.svg
│   │   │   ├───mail-icon.svg
│   │   │   ├───megaphone-blue.svg
│   │   │   ├───notifications.svg
│   │   │   ├───onboarding1.svg
│   │   │   ├───onboarding2.svg
│   │   │   ├───onboarding3.svg
│   │   │   ├───onboarding4.svg
│   │   │   ├───paper-plane.svg
│   │   │   ├───ranking-star.svg
│   │   │   ├───resources.svg
│   │   │   ├───rocket-lunch.svg
│   │   │   ├───save-instagram.svg
│   │   │   ├───seedling.svg
│   │   │   ├───send.svg
│   │   │   ├───settings-sliders.svg
│   │   │   ├───tips_and_updates.svg
│   │   │   ├───trash.svg
│   │   │   ├───trending_up.svg
│   │   │   ├───up.svg
│   │   │   ├───userAdd.svg
│   │   │   ├───userGroup.svg
│   │   │   ├───verified.svg
│   │   │   ├───visibility.svg
│   │   │   ├───youtube-blue.svg
│   │   │   ├───youtube-circle.svg
│   │   │   ├───youtube-gray.svg
│   │   │   ├───youtube-icon.svg
│   │   │   └───Nav/
│   │   │       ├───calendar.svg
│   │   │       ├───comment.svg
│   │   │       ├───creditcard.svg
│   │   │       ├───flag.svg
│   │   │       ├───home.svg
│   │   │       ├───megaphone.svg
│   │   │       ├───profile.png
│   │   │       ├───search.svg
│   │   │       ├───settings.svg
│   │   │       └───sparkle.svg
│   │   ├───image/
│   │   │   ├───Image1.png
│   │   │   ├───Image2.png
│   │   │   └───Image3.png
│   ├───features/
│   │   ├───auth/
│   │   │   ├───component/
│   │   │   │   ├───GradientSignupCard.jsx
│   │   │   │   ├───NotificationStrip.jsx
│   │   │   │   ├───OtpVerificationDialog.jsx
│   │   │   │   ├───ProfileOnboardingFlow.jsx
│   │   │   │   ├───signUpCard.jsx
│   │   │   │   └───signUpCard2.jsx
│   │   │   ├───pages/
│   │   │   │   └───BrandAuthPage.jsx
│   │   │   │   └───GoogleAuthCallback.jsx
│   │   │   │   └───...
│   │   │   └───service/
│   │   │       └───authThunk.js
│   │   ├───brand/
│   │   │   ├───components/
│   │   │   ├───pages/
│   │   │   └───services/
│   │   └───influencer/
│   │       ├───components/
│   │       ├───pages/
│   │       └───services/
│   ├───i18n/
│   │   ├───config.js
│   │   └───language/
│   │       ├───en/
│   │       ├───fr/
│   │       └───th/
│   ├───lib/
│   │   └───utils.js
│   └───shared/
│       ├───components/
│       │   ├───AvatarStack.jsx
│   │   │   ├───CampaignPopup.jsx
│   │   │   ├───DialogBox.jsx
│   │   │   ├───FilterComponent.jsx
│   │   │   ├───Navbar.jsx
│   │   │   ├───NavElement.jsx
│   │   │   ├───PlatformToggle.jsx
│   │   │   ├───ProtectedRoute.jsx
│   │   │   ├───PublicRoute.jsx
│   │   │   ├───examples/
│   │   │   └───UI/
│       ├───hooks/
│       │   ├───useApiRequest.js
│       │   ├───useApiService.js
│       │   ├───useAuth.js
│       │   ├───useContainerWidth.js
│       │   └───useRoles.js
│       └───layout/
│           ├───AuthLayout.jsx
│           ├───BrandOnboardingLayout.jsx
│           ├───DashboardLayout.jsx
│           └───SocialSignupLayout.jsx
```

## Top-level Files and Directories

- `public/`: Contains static assets served directly by the web server.
- `src/`: Contains all the source code for the React application.
- `node_modules/`: Contains all the installed Node.js modules.
- `.git/`: Git version control system files.
- `.vscode/`: VS Code editor specific settings.
- `.gitignore`: Specifies intentionally untracked files to ignore.
- `eslint.config.js`: ESLint configuration file for code linting.
- `index.html`: The main HTML file for the application.
- `package.json`: Project metadata and dependencies.
- `pnpm-lock.yaml`: Lock file for pnpm package manager.
- `README.md`: Project README file.
- `vite.config.js`: Vite build tool configuration.

## `src/` Directory Structure

- `src/app/`: Core application setup, routing, and global state management.
- `src/assets/`: Static assets like images, icons, and fonts.
- `src/features/`: Contains feature-specific modules (e.g., authentication, brand management, influencer profiles).
- `src/i18n/`: Internationalization (i18n) configuration and language files.
- `src/lib/`: Utility functions and helper modules.
- `src/shared/`: Reusable components, hooks, and layouts shared across different features.
- `src/index.css`: Global CSS styles.
- `src/main.jsx`: Entry point of the React application.

### `src/app/`
- `App.css`: Global styles for the main App component.
- `App.jsx`: The root component of the application.
- `AppInitializer.jsx`: Component for initializing app-wide settings or data.
- `router.jsx`: Defines the application's routing using React Router.
- `store.js`: Configures the Redux store.
- `store/api/`: API service definitions using Alova.
- `store/slices/`: Redux slices for state management.

### `src/assets/`
- `Emoji/`: Emoji images.
- `Gif/`: GIF animations.
- `Icon/`: SVG icons.
- `Icon/Nav/`: Navigation specific icons.
- `image/`: General images.

### `src/features/`
- Each subdirectory within `features` represents a distinct feature module (e.g., `auth`, `brand`, `influencer`).
- Each feature module typically contains:
    - `components/`: Feature-specific reusable UI components.
    - `pages/`: React components representing different pages within the feature.
    - `service/`: API service calls and business logic related to the feature.

### `src/i18n/`
- `config.js`: i18n configuration.
- `language/`: Language-specific translation files (e.g., `en/`, `fr/`, `th/`).

### `src/lib/`
- `utils.js`: General utility functions.

### `src/shared/`
- `components/`: Generic, reusable UI components that are not tied to a specific feature.
- `hooks/`: Custom React hooks for shared logic.
- `layout/`: Layout components used to structure pages.

