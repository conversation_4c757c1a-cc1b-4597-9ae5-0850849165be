# Status 410 Suggestions Implementation

## Overview

This implementation handles API responses with status 410 (query not related to influencer search) by extracting suggestions from the response and displaying them in the SearchWithSuggestions component.

## Implementation Details

### 1. **API Response Handling (brandThunks.js)**

Updated `searchCreatorsThunk` to handle status 410:

```javascript
else if (response.status === 410) {
    // Extract suggestions from multiple possible locations
    const suggestions = response.data?.examples || 
                      response.data?.data?.examples || 
                      response.data?.tips || 
                      response.data?.data?.tips || 
                      [];
    
    return {
        message: response.data?.message || 'Query not related to influencer search',
        success: false,
        status: 410,
        suggestions: suggestions,
        data: response.data.data || null
    };
}
```

### 2. **Redux State Management (brandSlice.js)**

Added `searchSuggestions` to store suggestions:

```javascript
// Initial state
searchSuggestions: [], // Store suggestions from 410 responses

// Fulfilled case handling
if (action.payload.status === 410) {
    state.searchStatus = RequestStatus.FAILED;
    state.searchError = action.payload.message;
    state.searchSuggestions = action.payload.suggestions || [];
    state.searchResults = { profiles: [], metadata: null, lastSearchParams: action.meta.arg };
} else {
    // Successful search - clear suggestions
    state.searchSuggestions = [];
    // ... handle successful response
}
```

### 3. **SearchWithSuggestions Component Updates**

Enhanced component to accept and display external suggestions:

```javascript
const SearchWithSuggestions = ({
    // ... existing props
    externalSuggestions = [] // New prop for API suggestions
}) => {
    // Prioritize external suggestions over default ones
    const activeSuggestions = externalSuggestions.length > 0 ? externalSuggestions : suggestions;
    
    // Update suggestions when external suggestions change
    useEffect(() => {
        if (externalSuggestions.length > 0) {
            setFilteredSuggestions(externalSuggestions);
            setShowSuggestions(true);
        }
    }, [externalSuggestions]);
}
```

### 4. **Discovery Component Integration**

Connected Redux state to SearchWithSuggestions:

```javascript
// Get suggestions from Redux store
const searchSuggestions = useSelector(state => state.brand.searchSuggestions);

// Pass to SearchWithSuggestions component
<SearchWithSuggestions
    // ... existing props
    externalSuggestions={searchSuggestions}
/>

// Handle 410 status in search results
if (result.payload?.status === 410) {
    const message = result.payload?.message || 'Query not related to influencer search. Please try the suggestions below.';
    showSnackbar(message, 'info');
    setCreators([]);
    setError(null); // Don't treat as error since suggestions are available
}
```

## API Response Structure Support

The implementation supports multiple possible response structures:

```json
// Structure 1: Direct examples
{
    "data": {},
    "status": 410,
    "examples": ["suggestion1", "suggestion2", "suggestion3"]
}

// Structure 2: Nested examples
{
    "data": {
        "examples": ["suggestion1", "suggestion2", "suggestion3"]
    },
    "status": 410
}

// Structure 3: Tips instead of examples
{
    "data": {},
    "status": 410,
    "tips": ["suggestion1", "suggestion2", "suggestion3"]
}
```

## User Experience Flow

1. **User searches** with a query not related to influencer search
2. **API returns 410** with suggestions in the response
3. **Redux store** captures and stores the suggestions
4. **SearchWithSuggestions** automatically displays the API suggestions
5. **User sees** relevant suggestions instead of default ones
6. **User clicks** on a suggestion to perform a new search
7. **Successful search** clears suggestions and shows results

## Features

✅ **Automatic Suggestion Display**: Shows API suggestions immediately when 410 status is received
✅ **Priority System**: External suggestions override default suggestions
✅ **State Management**: Suggestions persist until next successful search
✅ **User Feedback**: Info message explains why suggestions are shown
✅ **Seamless Integration**: Works with existing search and speech recognition features
✅ **Error Handling**: 410 status treated as info, not error
✅ **Flexible API Support**: Handles multiple response structures

## Testing

To test the implementation:

1. Enter a search query unrelated to influencer search (e.g., "weather forecast")
2. Verify API returns status 410 with suggestions
3. Check that suggestions appear in the search component
4. Click on a suggestion to trigger a new search
5. Verify suggestions clear when successful search occurs

## Debugging

Console logs are added to track suggestion extraction:
- Check browser console for "410 Response - Extracted suggestions:" logs
- Verify Redux DevTools shows `searchSuggestions` state updates
- Monitor network tab for API response structure
