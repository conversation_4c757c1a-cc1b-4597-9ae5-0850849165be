/**
 * Saved Filters API Service - Multi-Backend Architecture
 * 
 * Handles saved filter sets and global filters using the dedicated discovery service.
 * This service provides endpoints for:
 * - Creating and managing saved filter sets
 * - Retrieving user-specific and global filter sets
 * - Filter set search and pagination
 * - Global filter management
 */

import discoveryInstance from './instances/discoveryInstance';

/**
 * Saved Filters API service
 * Contains all endpoints related to saved filter sets and global filters
 */
const savedFiltersApi = {
  
  // === SAVED FILTER SETS ENDPOINTS ===
  
  /**
   * Create a new saved filter set
   * @param {Object} filterSetData - Filter set data (name, description, channel, filters, tags)
   * @returns {Promise} Created filter set
   */
  createFilterSet: (filterSetData) => {
    return discoveryInstance.Post('/saved/filters', filterSetData);
  },

  /**
   * Retrieve saved filter sets for the authenticated user
   * @param {Object} params - Query parameters (channel, include_global, search, page, per_page)
   * @returns {Promise} Paginated filter sets
   */
  getFilterSets: (params = {}) => {
    const queryParams = new URLSearchParams();
    
    if (params.channel) queryParams.append('channel', params.channel);
    if (params.include_global !== undefined) queryParams.append('include_global', params.include_global);
    if (params.search) queryParams.append('search', params.search);
    if (params.page) queryParams.append('page', Math.max(params.page, 1));
    if (params.per_page) queryParams.append('per_page', Math.min(Math.max(params.per_page, 1), 100));
    
    const queryString = queryParams.toString();
    return discoveryInstance.Get(`/saved/filters${queryString ? `?${queryString}` : ''}`);
  },

  /**
   * Get detailed information about a specific saved filter set
   * @param {string} filterSetId - Filter set ID
   * @returns {Promise} Filter set details
   */
  getFilterSet: (filterSetId) => {
    return discoveryInstance.Get(`/saved/filters/${filterSetId}`);
  },

  /**
   * Update an existing saved filter set
   * @param {string} filterSetId - Filter set ID
   * @param {Object} updateData - Updated filter set data
   * @returns {Promise} Updated filter set
   */
  updateFilterSet: (filterSetId, updateData) => {
    return discoveryInstance.Put(`/saved/filters/${filterSetId}`, updateData);
  },

  /**
   * Delete (soft delete) a saved filter set
   * @param {string} filterSetId - Filter set ID
   * @returns {Promise} Deletion result
   */
  deleteFilterSet: (filterSetId) => {
    return discoveryInstance.Delete(`/saved/filters/${filterSetId}`);
  },

  // === GLOBAL FILTERS ENDPOINTS ===

  /**
   * Get all global filter sets with their details
   * @param {Object} params - Query parameters (channel)
   * @returns {Promise} Global filter sets
   */
  getGlobalFilters: (params = {}) => {
    const queryParams = new URLSearchParams();
    
    if (params.channel) queryParams.append('channel', params.channel);
    
    const queryString = queryParams.toString();
    console.log('Query string:', queryString);
    return discoveryInstance.Get(`/global-filters/list${queryString ? `?${queryString}` : ''}`);
  },

  // === UTILITY METHODS ===

  /**
   * Validate filter set data before creation/update
   * @param {Object} filterSetData - Filter set data to validate
   * @returns {Object} Validation result with errors if any
   */
  validateFilterSet: (filterSetData) => {
    const errors = [];
    
    if (!filterSetData.name || filterSetData.name.trim().length === 0) {
      errors.push('Filter set name is required');
    }
    
    if (filterSetData.name && filterSetData.name.length > 100) {
      errors.push('Filter set name must be 100 characters or less');
    }
    
    if (!filterSetData.channel) {
      errors.push('Channel is required');
    }
    
    if (!Array.isArray(filterSetData.filters)) {
      errors.push('Filters must be an array');
    } else if (filterSetData.filters.length === 0) {
      errors.push('At least one filter is required');
    }
    
    // Validate individual filters
    if (Array.isArray(filterSetData.filters)) {
      filterSetData.filters.forEach((filter, index) => {
        if (!filter.channel) {
          errors.push(`Filter ${index + 1}: Channel is required`);
        }
        if (!filter.filter) {
          errors.push(`Filter ${index + 1}: Filter type is required`);
        }
        if (filter.value === undefined || filter.value === null) {
          errors.push(`Filter ${index + 1}: Filter value is required`);
        }
        if (!filter.filterFor) {
          errors.push(`Filter ${index + 1}: FilterFor is required`);
        }
      });
    }
    
    if (filterSetData.tags && !Array.isArray(filterSetData.tags)) {
      errors.push('Tags must be an array');
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  },

  /**
   * Build filter set query with common parameters
   * @param {Object} params - Query parameters
   * @returns {Object} Formatted query parameters
   */
  buildFilterSetQuery: (params) => {
    const {
      channel = null,
      include_global = true,
      search = '',
      page = 1,
      per_page = 20,
      sort_by = 'created_at',
      sort_order = 'desc'
    } = params;

    return {
      channel,
      include_global,
      search: search.trim(),
      page: Math.max(page, 1),
      per_page: Math.min(Math.max(per_page, 1), 100),
      sort_by,
      sort_order
    };
  },

  /**
   * Format filter set for display
   * @param {Object} filterSet - Raw filter set data
   * @returns {Object} Formatted filter set
   */
  formatFilterSet: (filterSet) => {
    return {
      id: filterSet.id,
      name: filterSet.name,
      description: filterSet.description || '',
      channel: filterSet.channel,
      filters: filterSet.filters || [],
      tags: filterSet.tags || [],
      is_global: filterSet.is_global || false,
      created_at: filterSet.created_at,
      updated_at: filterSet.updated_at,
      filter_count: filterSet.filters ? filterSet.filters.length : 0
    };
  },

  /**
   * Search filter sets by name, description, or tags
   * @param {Array} filterSets - Array of filter sets to search
   * @param {string} searchTerm - Search term
   * @returns {Array} Filtered results
   */
  searchFilterSets: (filterSets, searchTerm) => {
    if (!searchTerm || !Array.isArray(filterSets)) return filterSets;
    
    const term = searchTerm.toLowerCase().trim();
    
    return filterSets.filter(filterSet => {
      const name = (filterSet.name || '').toLowerCase();
      const description = (filterSet.description || '').toLowerCase();
      const tags = (filterSet.tags || []).join(' ').toLowerCase();
      
      return name.includes(term) || 
             description.includes(term) || 
             tags.includes(term);
    });
  },

  /**
   * Group filter sets by channel
   * @param {Array} filterSets - Array of filter sets
   * @returns {Object} Filter sets grouped by channel
   */
  groupByChannel: (filterSets) => {
    if (!Array.isArray(filterSets)) return {};
    
    return filterSets.reduce((groups, filterSet) => {
      const channel = filterSet.channel || 'unknown';
      if (!groups[channel]) {
        groups[channel] = [];
      }
      groups[channel].push(filterSet);
      return groups;
    }, {});
  },

  /**
   * Get filter set statistics
   * @param {Array} filterSets - Array of filter sets
   * @returns {Object} Statistics
   */
  getStatistics: (filterSets) => {
    if (!Array.isArray(filterSets)) return {};
    
    const stats = {
      total: filterSets.length,
      by_channel: {},
      global_count: 0,
      user_count: 0,
      avg_filters_per_set: 0
    };
    
    let totalFilters = 0;
    
    filterSets.forEach(filterSet => {
      const channel = filterSet.channel || 'unknown';
      stats.by_channel[channel] = (stats.by_channel[channel] || 0) + 1;
      
      if (filterSet.is_global) {
        stats.global_count++;
      } else {
        stats.user_count++;
      }
      
      totalFilters += (filterSet.filters || []).length;
    });
    
    stats.avg_filters_per_set = filterSets.length > 0 ? 
      Math.round((totalFilters / filterSets.length) * 100) / 100 : 0;
    
    return stats;
  }
};

export default savedFiltersApi;
