import { useRef, useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import PerformanceMetricsGrid from "./PerformanceMetricsGrid";
import YoutubeGrayIcon from '@assets/icon/youtube-gray.svg';
import YoutubeBlueIcon from '@assets/icon/youtube-blue.svg';
import InstagramBlueIcon from '@assets/icon/instagram-blue.svg';
import InstagramGrayIcon from '@assets/icon/instagram-gray.svg';

import PostCarousel from "./PostCarousel";

const SocialPerformanceSlider = ({ initialTab = "instagram", platformConfig }) => {
    const [activeTab, setActiveTab] = useState(initialTab);
    const [underlineStyle, setUnderlineStyle] = useState({});
    const navigate = useNavigate();

    const instagramRef = useRef(null);
    const youtubeRef = useRef(null);

    useEffect(() => {
        const currentRef = activeTab === "instagram" ? instagramRef : youtubeRef;
        if (currentRef.current) {
            const { offsetLeft, offsetWidth } = currentRef.current;
            setUnderlineStyle({
                left: offsetLeft,
                width: offsetWidth,
            });
        }
    }, [activeTab]);
    return (
        <div className="w-full max-w-full  bg-gray-600 rounded-lg shadow-lg p-5 relative">
            {/* Tabs */}
            <div className="relative w-fit ">
                <div className="flex items-start h-17 ">
                    <div
                        ref={instagramRef}
                        onClick={() => setActiveTab("instagram")}
                        className={`flex h-full items-center py-5 px-4 text-16-semibold cursor-pointer
                        ${activeTab === "instagram" ? "text-brand-500" : "text-gray-400"}`}
                    >
                        <img src={activeTab === "instagram" ? InstagramBlueIcon : InstagramGrayIcon} alt="Instagram" className="pr-2" />
                        Instagram
                    </div>
                    <div
                        ref={youtubeRef}
                        onClick={() => setActiveTab("youtube")}
                        className={`flex h-full items-center py-5 px-4 text-16-semibold cursor-pointer
                        ${activeTab === "youtube" ? "text-brand-500" : "text-gray-400"}`}
                    >
                        <img src={activeTab === "youtube" ? YoutubeBlueIcon : YoutubeGrayIcon} alt="YouTube" className="pr-2" />
                        YouTube
                    </div>

                </div>

                {/* Sliding underline */}
                <div
                    className="absolute bottom-0 h-1 bg-brand-500 transition-all duration-300 ease-in-out rounded-full"
                    style={underlineStyle}
                />
            </div>

            {
                (platformConfig[activeTab]) === true ? (
                    <div className="absolute right-7 top-10">
                        <span
                            className="text-16-medium text-white cursor-pointer hover:text-brand-500 hover:underline"
                            onClick={() => navigate("/influencer/profile-analytics")}
                        >
                            View Analytics
                        </span>
                    </div>
                ) : null
            }


            {/* Slider */}
            <div className="w-full h-auto overflow-hidden">
                <div className="flex transition-transform duration-300 ease-in-out">
                    {/* Instagram View */}
                    <div className="w-full">
                        <PerformanceMetricsGrid platform={activeTab} isConfigured={platformConfig[activeTab]} />
                        <PostCarousel platform={activeTab} isConfigured={platformConfig[activeTab]} />
                    </div>


                    {/* Instagram View */}
                    {/* <div className="w-full">
                        <PerformanceMetricsGrid platform="instagram" />
                        <PostCarousel platform="instagram" />
                    </div> */}

                    {/* YouTube View */}
                    {/* <div className="w-full">
                        <PerformanceMetricsGrid platform="youtube" />
                        <PostCarousel platform="youtube" />
                    </div> */}
                </div>
            </div>
        </div>
    );
};

export default SocialPerformanceSlider;
