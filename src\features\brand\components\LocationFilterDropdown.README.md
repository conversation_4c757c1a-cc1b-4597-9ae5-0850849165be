# LocationFilterDropdown Component

A dynamic, three-phase location filter component that integrates seamlessly with the CreatorVerse brand dashboard architecture.

## Overview

The LocationFilterDropdown component provides a sophisticated location selection interface with the following key features:

- **Three-Phase Selection**: Country selection → Combined location/group selection → Granular group selection
- **Dual Selection Methods**: Individual search AND group-based selection simultaneously
- **Enhanced Data Structure**: Distinguishes between cities and states with type information
- **Granular Group Control**: Select entire groups with individual exclusions
- **State Persistence**: Remembers and restores previous selections when dropdown reopens
- **Cross-Selection Recognition**: Items selected via any method show as selected across all interfaces
- **Multi-Group Selection**: Select items from multiple groups in a single session
- **Visual Selection Indicators**: Checkmarks, highlights, selection summaries, and cumulative counts
- **Typeahead Search**: Debounced search functionality for countries and locations
- **Dynamic Group Display**: Conditional group information based on country data
- **Loading States**: Visual feedback during API requests
- **Error Handling**: Graceful error handling with user feedback
- **Responsive Design**: Consistent with existing dashboard styling

## Architecture

### Component Structure

```
LocationFilterDropdown/
├── Main Component (LocationFilterDropdown.jsx)
├── Mock API Layer (mockLocationApi)
├── Custom Hooks (useDebounce)
├── Usage Examples (LocationFilterDropdown.example.jsx)
└── Documentation (LocationFilterDropdown.README.md)
```

### Key Features

#### 1. Three-Phase Selection Process

**Phase 1: Country Selection**
- Searchable dropdown with typeahead functionality
- Displays countries with their associated groups
- **Visual Selection Indicators**: Countries with selected locations show:
  - Selection count badges (e.g., "5 selected")
  - Blue borders and highlighting
  - Selection summaries (e.g., "Mumbai, Delhi + 3 more")
  - Selection type badges (Individual/Groups/Pending)
- Debounced search (300ms delay) for optimal performance
- Loading state during API requests

**Phase 2: Combined Location Selection**
- **Dual Interface**: Search input AND group selection shown simultaneously
- **Multi-Individual Search**: Typeahead search for specific cities/states with multi-selection support
- **Persistent Dropdown**: Dropdown stays open for selecting multiple individual items
- **Auto-Clear Search**: Search input clears after each selection for easy additional searches
- **Group Selection**: Browse and select by groups below the search
- **Type Distinction**: Shows whether location is a city or state
- **Group Preview**: Expandable group sections showing contained locations
- **Pending Management**: Shows cumulative pending selections with "Apply All" functionality

**Phase 3: Granular Group Selection**
- **Detailed Control**: Select/deselect individual items within a group
- **Master Checkbox**: Single "Select All" checkbox with three states:
  - Unchecked: No items selected
  - Indeterminate: Some items selected (shows dash icon)
  - Checked: All items selected
- **Bulk Operations**: Master checkbox provides unified select/deselect all functionality
- **Exclusion Support**: Remove specific items from group selections
- **Selection Summary**: Shows count and excluded items
- **Confirmation**: Explicit confirmation before applying group selection

#### 2. Group Display Logic

The component intelligently handles group information:

- **Countries with groups**: Shows group information (e.g., "Tier 1", "Tier 2")
- **Countries without groups**: Displays cities without group labels
- **Dynamic rendering**: Group display is conditional based on data

#### 3. State Persistence

The component automatically remembers and restores previous selections when the dropdown is reopened:

**Individual Selection Persistence:**
- Navigates directly to Phase 2 (location selection) for the previously selected country
- Highlights the selected city/state with checkmark and blue background
- Updates button text to show the selected location name
- Maintains search context and visual indicators

**Group Selection Persistence:**
- Restores the selected group's state with selection summary
- Shows visual indicators (blue border, checkmark) for selected groups
- Preserves individual item selections and exclusions within groups
- Maintains group expansion states for better UX

**Technical Implementation:**
```javascript
// Automatic state restoration on dropdown open
useEffect(() => {
  if (isOpen && selectedLocations.length > 0) {
    restorePreviousSelection();
  }
}, [isOpen, selectedLocations.length, restorePreviousSelection]);

// Restoration logic handles both selection types
const restorePreviousSelection = async () => {
  const lastSelection = selectedLocations[selectedLocations.length - 1];
  // Load country and city data
  // Set appropriate phase and selection state
  // Restore visual indicators
};
```

#### 4. Cross-Selection Recognition

The component provides consistent visual feedback across all selection interfaces:

**Selection Source Indicators:**
- 🟢 **Individual**: Items selected through direct search
- 🔵 **Group**: Items selected through committed group selections
- 🟡 **Pending**: Items selected in current group session (not yet committed)

**Cross-Interface Consistency:**
```javascript
// Helper function ensures consistent selection detection
const isItemSelected = (item) => {
  // Check individual selections
  const isIndividuallySelected = selectedLocations.some(/* ... */);

  // Check active group selections (pending)
  const isActiveGroupSelected = Object.values(groupSelections).some(/* ... */);

  // Check committed group selections
  const isCommittedGroupSelected = selectedLocations.some(/* ... */);

  return {
    isSelected: isIndividuallySelected || isActiveGroupSelected || isCommittedGroupSelected,
    selectionType: /* determine source */
  };
};
```

**Visual Consistency Features:**
- Items selected via group selection appear highlighted in individual search results
- Items selected individually show as selected in group preview sections
- Selection indicators update in real-time across all interfaces
- Source badges show how each item was selected

#### 5. Country Selection Visual Indicators

Countries in Phase 1 show comprehensive visual feedback about their selection state:

**Selection Count Badges:**
```javascript
// Countries with selections show count badges
<span className="bg-blue-600 text-blue-100 px-2 py-0.5 rounded-full text-xs">
  {selectionSummary.totalLocations} selected
</span>
```

**Selection Summaries:**
- **Individual selections**: "Mumbai, Delhi + 3 more"
- **Group selections**: "Tier 1 group, Tier 2: 3 locations"
- **Mixed selections**: Shows both individual and group information
- **Pending selections**: Includes items selected but not yet committed

**Visual Styling:**
- Blue borders and background highlighting for countries with selections
- Checkmark icons for selected countries
- Selection type badges (Individual/Groups/Pending)
- Enhanced text styling for selected countries

**Implementation:**
```javascript
const getCountrySelectionSummary = (countryName) => {
  // Analyze individual selections
  const individualSelections = selectedLocations.filter(/* ... */);

  // Analyze committed group selections
  const committedGroupSelections = selectedLocations.filter(/* ... */);

  // Analyze pending group selections
  const pendingGroupSelections = /* current group state */;

  // Generate comprehensive summary
  return {
    totalLocations: /* total count */,
    summary: /* text summary */,
    hasIndividual: /* boolean */,
    hasGroups: /* boolean */,
    hasPending: /* boolean */
  };
};
```

#### 6. Master Checkbox Control

Phase 3 (Group Detail) features a unified master checkbox for efficient group selection:

**Three-State Checkbox:**
```javascript
const getMasterCheckboxState = (groupName) => {
  const groupItems = cities.filter(city => city.group === groupName);
  const selection = groupSelections[groupName] || { selectedItems: [], excludedItems: [] };
  const selectedCount = selection.selectedItems.length;
  const totalCount = groupItems.length;

  if (selectedCount === 0) {
    return 'unchecked';        // No items selected
  } else if (selectedCount === totalCount) {
    return 'checked';          // All items selected
  } else {
    return 'indeterminate';    // Some items selected
  }
};
```

**Visual States:**
- **Unchecked**: Empty checkbox - click to select all items
- **Indeterminate**: Checkbox with dash icon - click to select all remaining items
- **Checked**: Checkbox with checkmark - click to deselect all items

**User Interaction:**
```javascript
const handleMasterCheckboxToggle = (groupName) => {
  const shouldSelectAll = selectedCount < totalCount;

  setGroupSelections(prev => ({
    ...prev,
    [groupName]: {
      selectedItems: shouldSelectAll ? groupItems : [],
      excludedItems: []
    }
  }));
};
```

**Benefits:**
- **Unified Control**: Single interface for bulk selection operations
- **Visual Clarity**: Clear indication of current selection state
- **Efficient Workflow**: Quick selection of entire groups or clearing selections
- **Automatic Updates**: State reflects changes from individual item toggles

#### 7. Multi-Individual Selection Support

Phase 2 supports selecting multiple individual cities/states in a single session:

**Enhanced Individual Selection Workflow:**
```javascript
const handleIndividualSelect = (item) => {
  const locationData = {
    country: selectedCountry.name,
    city: item.name,
    type: item.type,
    group: item.group,
    fullLocation: `${item.name} (${item.type}), ${selectedCountry.name}`,
    selectionType: 'individual'
  };

  // Toggle selection in pending state
  const isAlreadySelected = pendingIndividualSelections.some(/* ... */);

  if (isAlreadySelected) {
    // Remove from pending selections
    setPendingIndividualSelections(prev => prev.filter(/* ... */));
  } else {
    // Add to pending selections
    setPendingIndividualSelections(prev => [...prev, locationData]);
  }

  // Clear search for next selection
  setCityQuery('');

  // Keep dropdown open for multi-selection
};
```

**Key Features:**
- **Persistent Dropdown**: Stays open after each individual selection
- **Toggle Selection**: Click selected items to deselect them
- **Auto-Clear Search**: Search input clears after each selection
- **Pending State**: Individual selections stored in pending state until committed
- **Visual Feedback**: Selected items show checkmarks and highlighting
- **Cross-Selection Sync**: Works with existing group selection synchronization

**User Experience:**
1. **Search**: Type to find cities/states
2. **Select**: Click items to add to pending selections
3. **Continue**: Search clears, dropdown stays open
4. **Toggle**: Click selected items to deselect
5. **Commit**: Use "Apply All Selections" to finalize

**Integration with Multi-Group Selection:**
- Pending individual selections combine with pending group selections
- Total pending count includes both individual and group selections
- Single "Apply All Selections" commits everything at once
- Cross-selection recognition works across all selection methods

#### 8. Multi-Group Selection Support

The component supports selecting items from multiple groups in a single session:

**Workflow:**
1. **Group Selection**: Select items from first group → Returns to Phase 2
2. **Additional Groups**: Select items from more groups → Each returns to Phase 2
3. **Cumulative View**: Phase 2 shows all pending selections across groups
4. **Final Commit**: "Apply All Selections" commits all pending selections at once

**Key Features:**
```javascript
// Track pending selections across multiple groups
const getTotalPendingSelections = () => {
  return Object.values(groupSelections).reduce((total, selection) => {
    return total + selection.selectedItems.length;
  }, 0);
};

// Commit all pending group selections
const handleApplyAllSelections = () => {
  Object.entries(groupSelections).forEach(([groupName, selection]) => {
    if (selection.selectedItems.length > 0) {
      const groupData = {
        country: selectedCountry.name,
        group: groupName,
        selectedItems: selection.selectedItems,
        excludedItems: selection.excludedItems,
        selectionType: 'group'
      };
      onSelect(groupData);
    }
  });
  // Close dropdown and reset
};
```

**User Experience:**
- **Pending Indicators**: Shows "X pending" count in Phase 2 header
- **Apply Button**: "Apply All Selections" button in Phase 2 and footer
- **Cumulative Counts**: Button text shows total across all selections
- **Non-Blocking**: Dropdown stays open until user explicitly applies or cancels
- **Mixed Selection**: Supports individual + multiple group selections

**Example Scenarios:**
- "2 locations from Tier 1 + 3 locations from Tier 2"
- "Individual cities + Tier 1 group + partial Tier 3 selection"
- "Multiple groups with exclusions across different tiers"

#### 7. API Integration

**Enhanced Data Structure**:

```javascript
// Countries API - Unchanged
mockLocationApi.searchCountries(query)
// Returns: [{ name: "India", groups: ["Tier 1", "Tier 2", "Tier 3"] }]

// Cities/States API - Enhanced with type field
mockLocationApi.searchCities(countryName, query)
// Returns: [
//   { name: "Mumbai", group: "Tier 1", type: "city" },
//   { name: "Maharashtra", group: "Tier 1", type: "state" }
// ]
```

**Selection Return Formats**:

```javascript
// Individual Selection
{
  country: "India",
  city: "Mumbai",
  type: "city",
  group: "Tier 1",
  fullLocation: "Mumbai (City), India",
  selectionType: "individual"
}

// Group Selection
{
  country: "India",
  group: "Tier 2",
  selectedItems: [
    { name: "Pune", group: "Tier 2", type: "city" },
    { name: "Gujarat", group: "Tier 2", type: "state" }
  ],
  excludedItems: [
    { name: "Kolkata", group: "Tier 2", type: "city" }
  ],
  selectionType: "group"
}
```

## Usage

### Basic Implementation

```jsx
import LocationFilterDropdown from '@brand/components/LocationFilterDropdown';

const MyComponent = () => {
  const [selectedLocation, setSelectedLocation] = useState(null);

  const handleLocationSelect = (locationData) => {
    console.log('Selected:', locationData);
    // locationData structure:
    // {
    //   country: "India",
    //   city: "Mumbai", 
    //   group: "Tier 1",
    //   fullLocation: "Mumbai, India"
    // }
    setSelectedLocation(locationData);
  };

  return (
    <LocationFilterDropdown
      onSelect={handleLocationSelect}
      selectedLocations={selectedLocation ? [selectedLocation] : []}
      placeholder="Select Location"
    />
  );
};
```

### Props Interface

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `onSelect` | `Function` | Required | Callback when location is selected |
| `selectedLocation` | `Object` | `null` | Currently selected location |
| `selectedLocations` | `Array` | `[]` | Array of selected locations |
| `multiSelect` | `Boolean` | `false` | Enable multiple selections |
| `placeholder` | `String` | `"Select Location"` | Button placeholder text |
| `className` | `String` | `""` | Additional CSS classes |

### Integration with Filter System

```jsx
const handleLocationFilter = (locationData) => {
  if (!locationData) {
    // Clear location filter
    setActiveFilters(prev => ({ ...prev, location: null }));
    return;
  }

  // Add to active filters
  setActiveFilters(prev => ({
    ...prev,
    location: {
      country: locationData.country,
      city: locationData.city,
      group: locationData.group
    }
  }));

  // Trigger search
  searchCreators({ query: searchQuery, filters: activeFilters });
};
```

## Technical Implementation

### Debouncing

The component uses a custom `useDebounce` hook to optimize API calls:

```javascript
const useDebounce = (value, delay) => {
  const [debouncedValue, setDebouncedValue] = useState(value);
  
  useEffect(() => {
    const handler = setTimeout(() => setDebouncedValue(value), delay);
    return () => clearTimeout(handler);
  }, [value, delay]);
  
  return debouncedValue;
};
```

### State Management

The component manages multiple state variables:

- `phase`: Current selection phase ('country' or 'city')
- `selectedCountry`: Currently selected country object
- `countryQuery`/`cityQuery`: Search input values
- `countries`/`cities`: API response data
- `isLoadingCountries`/`isLoadingCities`: Loading states
- `error`: Error state for user feedback

### Error Handling

Comprehensive error handling includes:

- API request failures
- Network timeouts
- Invalid responses
- User-friendly error messages via SnackbarContext

## Styling

The component follows the established design system:

- **Colors**: Gray-900 background, gray-800 borders, blue-500 accents
- **Typography**: Consistent with existing filter components
- **Spacing**: Tailwind CSS utility classes
- **Animations**: Smooth transitions and loading states

## Real API Integration

To integrate with real APIs, replace the mock functions:

```javascript
// Replace mockLocationApi with actual API service
const locationApi = {
  searchCountries: async (query) => {
    const response = await discoveryInstance.Get(`/locations/countries?q=${query}`);
    return response.data;
  },
  
  searchCities: async (countryName, query) => {
    const response = await discoveryInstance.Get(
      `/locations/cities?country=${countryName}&q=${query}`
    );
    return response.data;
  }
};
```

## Performance Considerations

- **Debounced Search**: 300ms delay prevents excessive API calls
- **Lazy Loading**: Countries loaded only when dropdown opens
- **Caching**: Consider implementing response caching for frequently accessed data
- **Virtualization**: For large city lists, consider implementing virtual scrolling

## Accessibility

The component includes accessibility features:

- Keyboard navigation support
- ARIA labels and roles
- Focus management
- Screen reader compatibility

## Testing

Key test scenarios:

1. Country search and selection
2. City search and selection  
3. Back navigation functionality
4. Loading states
5. Error handling
6. Debounced search behavior
7. Clear functionality

## Future Enhancements

Potential improvements:

- Multi-select functionality
- Infinite scroll for large datasets
- Geolocation-based suggestions
- Recent selections memory
- Bulk import/export capabilities
- Advanced filtering options
