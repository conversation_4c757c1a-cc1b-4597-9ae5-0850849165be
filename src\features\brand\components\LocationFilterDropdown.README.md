# LocationFilterDropdown Component

A dynamic, three-phase location filter component that integrates seamlessly with the CreatorVerse brand dashboard architecture.

## Overview

The LocationFilterDropdown component provides a sophisticated location selection interface with the following key features:

- **Three-Phase Selection**: Country selection → Combined location/group selection → Granular group selection
- **Dual Selection Methods**: Individual search AND group-based selection simultaneously
- **Enhanced Data Structure**: Distinguishes between cities and states with type information
- **Granular Group Control**: Select entire groups with individual exclusions
- **State Persistence**: Remembers and restores previous selections when dropdown reopens
- **Cross-Selection Recognition**: Items selected via any method show as selected across all interfaces
- **Visual Selection Indicators**: Checkmarks, highlights, selection summaries, and source indicators
- **Typeahead Search**: Debounced search functionality for countries and locations
- **Dynamic Group Display**: Conditional group information based on country data
- **Loading States**: Visual feedback during API requests
- **Error Handling**: Graceful error handling with user feedback
- **Responsive Design**: Consistent with existing dashboard styling

## Architecture

### Component Structure

```
LocationFilterDropdown/
├── Main Component (LocationFilterDropdown.jsx)
├── Mock API Layer (mockLocationApi)
├── Custom Hooks (useDebounce)
├── Usage Examples (LocationFilterDropdown.example.jsx)
└── Documentation (LocationFilterDropdown.README.md)
```

### Key Features

#### 1. Three-Phase Selection Process

**Phase 1: Country Selection**
- Searchable dropdown with typeahead functionality
- Displays countries with their associated groups
- Debounced search (300ms delay) for optimal performance
- Loading state during API requests

**Phase 2: Combined Location Selection**
- **Dual Interface**: Search input AND group selection shown simultaneously
- **Individual Search**: Typeahead search for specific cities/states at the top
- **Group Selection**: Browse and select by groups below the search
- **Type Distinction**: Shows whether location is a city or state
- **Group Preview**: Expandable group sections showing contained locations

**Phase 3: Granular Group Selection**
- **Detailed Control**: Select/deselect individual items within a group
- **Bulk Operations**: "Select All" and "Deselect All" for entire groups
- **Exclusion Support**: Remove specific items from group selections
- **Selection Summary**: Shows count and excluded items
- **Confirmation**: Explicit confirmation before applying group selection

#### 2. Group Display Logic

The component intelligently handles group information:

- **Countries with groups**: Shows group information (e.g., "Tier 1", "Tier 2")
- **Countries without groups**: Displays cities without group labels
- **Dynamic rendering**: Group display is conditional based on data

#### 3. State Persistence

The component automatically remembers and restores previous selections when the dropdown is reopened:

**Individual Selection Persistence:**
- Navigates directly to Phase 2 (location selection) for the previously selected country
- Highlights the selected city/state with checkmark and blue background
- Updates button text to show the selected location name
- Maintains search context and visual indicators

**Group Selection Persistence:**
- Restores the selected group's state with selection summary
- Shows visual indicators (blue border, checkmark) for selected groups
- Preserves individual item selections and exclusions within groups
- Maintains group expansion states for better UX

**Technical Implementation:**
```javascript
// Automatic state restoration on dropdown open
useEffect(() => {
  if (isOpen && selectedLocations.length > 0) {
    restorePreviousSelection();
  }
}, [isOpen, selectedLocations.length, restorePreviousSelection]);

// Restoration logic handles both selection types
const restorePreviousSelection = async () => {
  const lastSelection = selectedLocations[selectedLocations.length - 1];
  // Load country and city data
  // Set appropriate phase and selection state
  // Restore visual indicators
};
```

#### 4. Cross-Selection Recognition

The component provides consistent visual feedback across all selection interfaces:

**Selection Source Indicators:**
- 🟢 **Individual**: Items selected through direct search
- 🔵 **Group**: Items selected through committed group selections
- 🟡 **Pending**: Items selected in current group session (not yet committed)

**Cross-Interface Consistency:**
```javascript
// Helper function ensures consistent selection detection
const isItemSelected = (item) => {
  // Check individual selections
  const isIndividuallySelected = selectedLocations.some(/* ... */);

  // Check active group selections (pending)
  const isActiveGroupSelected = Object.values(groupSelections).some(/* ... */);

  // Check committed group selections
  const isCommittedGroupSelected = selectedLocations.some(/* ... */);

  return {
    isSelected: isIndividuallySelected || isActiveGroupSelected || isCommittedGroupSelected,
    selectionType: /* determine source */
  };
};
```

**Visual Consistency Features:**
- Items selected via group selection appear highlighted in individual search results
- Items selected individually show as selected in group preview sections
- Selection indicators update in real-time across all interfaces
- Source badges show how each item was selected

#### 5. API Integration

**Enhanced Data Structure**:

```javascript
// Countries API - Unchanged
mockLocationApi.searchCountries(query)
// Returns: [{ name: "India", groups: ["Tier 1", "Tier 2", "Tier 3"] }]

// Cities/States API - Enhanced with type field
mockLocationApi.searchCities(countryName, query)
// Returns: [
//   { name: "Mumbai", group: "Tier 1", type: "city" },
//   { name: "Maharashtra", group: "Tier 1", type: "state" }
// ]
```

**Selection Return Formats**:

```javascript
// Individual Selection
{
  country: "India",
  city: "Mumbai",
  type: "city",
  group: "Tier 1",
  fullLocation: "Mumbai (City), India",
  selectionType: "individual"
}

// Group Selection
{
  country: "India",
  group: "Tier 2",
  selectedItems: [
    { name: "Pune", group: "Tier 2", type: "city" },
    { name: "Gujarat", group: "Tier 2", type: "state" }
  ],
  excludedItems: [
    { name: "Kolkata", group: "Tier 2", type: "city" }
  ],
  selectionType: "group"
}
```

## Usage

### Basic Implementation

```jsx
import LocationFilterDropdown from '@brand/components/LocationFilterDropdown';

const MyComponent = () => {
  const [selectedLocation, setSelectedLocation] = useState(null);

  const handleLocationSelect = (locationData) => {
    console.log('Selected:', locationData);
    // locationData structure:
    // {
    //   country: "India",
    //   city: "Mumbai", 
    //   group: "Tier 1",
    //   fullLocation: "Mumbai, India"
    // }
    setSelectedLocation(locationData);
  };

  return (
    <LocationFilterDropdown
      onSelect={handleLocationSelect}
      selectedLocations={selectedLocation ? [selectedLocation] : []}
      placeholder="Select Location"
    />
  );
};
```

### Props Interface

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `onSelect` | `Function` | Required | Callback when location is selected |
| `selectedLocation` | `Object` | `null` | Currently selected location |
| `selectedLocations` | `Array` | `[]` | Array of selected locations |
| `multiSelect` | `Boolean` | `false` | Enable multiple selections |
| `placeholder` | `String` | `"Select Location"` | Button placeholder text |
| `className` | `String` | `""` | Additional CSS classes |

### Integration with Filter System

```jsx
const handleLocationFilter = (locationData) => {
  if (!locationData) {
    // Clear location filter
    setActiveFilters(prev => ({ ...prev, location: null }));
    return;
  }

  // Add to active filters
  setActiveFilters(prev => ({
    ...prev,
    location: {
      country: locationData.country,
      city: locationData.city,
      group: locationData.group
    }
  }));

  // Trigger search
  searchCreators({ query: searchQuery, filters: activeFilters });
};
```

## Technical Implementation

### Debouncing

The component uses a custom `useDebounce` hook to optimize API calls:

```javascript
const useDebounce = (value, delay) => {
  const [debouncedValue, setDebouncedValue] = useState(value);
  
  useEffect(() => {
    const handler = setTimeout(() => setDebouncedValue(value), delay);
    return () => clearTimeout(handler);
  }, [value, delay]);
  
  return debouncedValue;
};
```

### State Management

The component manages multiple state variables:

- `phase`: Current selection phase ('country' or 'city')
- `selectedCountry`: Currently selected country object
- `countryQuery`/`cityQuery`: Search input values
- `countries`/`cities`: API response data
- `isLoadingCountries`/`isLoadingCities`: Loading states
- `error`: Error state for user feedback

### Error Handling

Comprehensive error handling includes:

- API request failures
- Network timeouts
- Invalid responses
- User-friendly error messages via SnackbarContext

## Styling

The component follows the established design system:

- **Colors**: Gray-900 background, gray-800 borders, blue-500 accents
- **Typography**: Consistent with existing filter components
- **Spacing**: Tailwind CSS utility classes
- **Animations**: Smooth transitions and loading states

## Real API Integration

To integrate with real APIs, replace the mock functions:

```javascript
// Replace mockLocationApi with actual API service
const locationApi = {
  searchCountries: async (query) => {
    const response = await discoveryInstance.Get(`/locations/countries?q=${query}`);
    return response.data;
  },
  
  searchCities: async (countryName, query) => {
    const response = await discoveryInstance.Get(
      `/locations/cities?country=${countryName}&q=${query}`
    );
    return response.data;
  }
};
```

## Performance Considerations

- **Debounced Search**: 300ms delay prevents excessive API calls
- **Lazy Loading**: Countries loaded only when dropdown opens
- **Caching**: Consider implementing response caching for frequently accessed data
- **Virtualization**: For large city lists, consider implementing virtual scrolling

## Accessibility

The component includes accessibility features:

- Keyboard navigation support
- ARIA labels and roles
- Focus management
- Screen reader compatibility

## Testing

Key test scenarios:

1. Country search and selection
2. City search and selection  
3. Back navigation functionality
4. Loading states
5. Error handling
6. Debounced search behavior
7. Clear functionality

## Future Enhancements

Potential improvements:

- Multi-select functionality
- Infinite scroll for large datasets
- Geolocation-based suggestions
- Recent selections memory
- Bulk import/export capabilities
- Advanced filtering options
