# Multi-Backend API Architecture

This document describes the multi-backend API architecture implemented in the Creatorverse Frontend application. The system is designed to handle authentication and discovery/analytics through separate backend services for improved scalability and maintainability.

## Architecture Overview

The application now uses a **multi-backend architecture** with two primary services:

1. **Authentication Service** - Handles user authentication, registration, and authorization
2. **Discovery & Analytics Service** - Handles creator discovery, profile analytics, and filter management

## Service Endpoints

### Authentication Service
- **Development**: `https://a6a2-103-19-196-138.ngrok-free.app`
- **Staging**: `https://auth-staging.creatorverse.com`
- **Production**: `https://auth.creatorverse.com`

### Discovery & Analytics Service
- **Development**: `http://localhost:8000`
- **Staging**: `https://discovery-staging.creatorverse.com`
- **Production**: `https://discovery.creatorverse.com`

## API Services Structure

### Authentication API (`authApi.js`)

**Service**: Authentication Service
**Base URL**: Configured per environment
**Authentication**: Required for protected endpoints

#### Influencer Authentication
- `POST /auth/influencer/login` - Influencer login
- `POST /auth/influencer/register` - Influencer registration
- `POST /auth/influencer/verify-otp` - Verify OTP for registration
- `POST /auth/influencer/login/verify-otp` - Verify OTP for login
- `POST /auth/influencer/resend-otp` - Resend OTP

#### Brand Authentication
- `POST /auth/brand/login` - Brand login
- `POST /auth/brand/register` - Brand registration
- `POST /auth/brand/verify-otp` - Verify OTP for brand registration
- `POST /auth/brand/login/verify-otp` - Verify OTP for brand login
- `POST /auth/brand/resend-otp` - Resend OTP for brand

#### General Authentication
- `POST /auth/logout` - User logout (requires session_id parameter)
- `GET /auth/me` - Get current user info
- `POST /auth/refresh` - Refresh access token
- `GET /auth/validate` - Validate current token

#### OAuth
- `POST /oauth/initiate` - Initiate OAuth flow

#### Common
- `GET /common/master/roles` - Get available roles

### Brand Management API (`brandManagementApi.js`)

**Service**: Authentication Service
**Base URL**: Configured per environment
**Authentication**: Required

- `POST /brands/{brandId}/join` - Request brand access
- `POST /brands` - Create a new brand
- `GET /auth/brand/userinfo` - Get user brand info
- `GET /youtube/channels` - Get YouTube channels

### Discovery API (`discoveryApi.js`)

**Service**: Discovery & Analytics Service
**Base URL**: Configured per environment
**Authentication**: Required

#### Search & Discovery
- `POST /discovery/search` - Search for creators with filters
- `GET /discovery/filters` - Get filter catalog (deprecated)

#### Filter Management
- `GET /filters/metadata` - Get complete filter metadata
- `POST /filters/transform` - Transform filters to provider format
- `DELETE /filters/cache` - Invalidate filter cache

#### Cache Management
- `GET /discovery/cache/stats` - Get cache performance statistics
- `POST /discovery/cache/cleanup` - Clean up expired cache entries

#### Health Checks
- `GET /health` - Basic health check
- `GET /health/detailed` - Detailed health check
- `GET /health/metrics` - Service metrics

### Analytics API (`analyticsApi.js`)

**Service**: Discovery & Analytics Service
**Base URL**: Configured per environment
**Authentication**: Required

#### Profile Analytics
- `GET /analytics/profiles/{profileId}` - Get basic profile data
- `GET /analytics/profiles/{profileId}/detailed` - Get detailed analytics
- `POST /analytics/profiles/{profileId}/fetch-analytics` - Trigger analytics fetch
- `GET /analytics/profiles/{profileId}/status` - Check analytics status
- `GET /analytics/profiles` - List profiles with pagination

#### Dashboard KPIs
- `GET /analytics/dashboard/kpi/{profileId}` - Get dashboard KPIs
- `GET /analytics/dashboard/top-posts/{profileId}` - Get top posts
- `GET /analytics/dashboard/monthly-impressions/{profileId}` - Monthly impressions
- `GET /analytics/dashboard/monthly-engagement-rate/{profileId}` - Monthly engagement
- `GET /analytics/dashboard/monthly-likes-by-type/{profileId}` - Monthly likes by type
- `GET /analytics/dashboard/content-distribution/{profileId}` - Content distribution

### Saved Filters API (`savedFiltersApi.js`)

**Service**: Discovery & Analytics Service
**Base URL**: Configured per environment
**Authentication**: Required

#### Saved Filter Sets
- `POST /saved/filters` - Create saved filter set
- `GET /saved/filters` - Get saved filter sets
- `GET /saved/filters/{filterSetId}` - Get specific filter set
- `PUT /saved/filters/{filterSetId}` - Update filter set
- `DELETE /saved/filters/{filterSetId}` - Delete filter set

#### Global Filters
- `GET /global-filters/list` - Get global filter sets

| Endpoint | Method | Description |
|---|---|---|
| `/v1/auth/influencer/login` | `POST` | Logs in an influencer. |
| `/v1/auth/influencer/register` | `POST` | Registers a new influencer. |
| `/v1/oauth/initiate` | `POST` | Initiates OAuth process. |
| `/v1/common/master/roles` | `GET` | Retrieves available user roles. |
| `/v1/auth/influencer/verify-otp` | `POST` | Verifies OTP for influencer registration. |
| `/v1/auth/influencer/login/verify-otp` | `POST` | Verifies OTP for influencer login. |
| `/v1/auth/influencer/resend-otp` | `POST` | Resends OTP for influencer. |
| `/v1/auth/logout` | `POST` | Logs out the current user (requires session_id parameter). |
| `/v1/auth/me` | `GET` | Retrieves information about the current authenticated user. |
| `/v1/auth/brand/login` | `POST` | Logs in a brand user. |
| `/v1/auth/brand/register` | `POST` | Registers a new brand user. |
| `/v1/auth/brand/verify-otp` | `POST` | Verifies OTP for brand registration. |
| `/v1/auth/brand/login/verify-otp` | `POST` | Verifies OTP for brand login. |
| `/v1/auth/brand/resend-otp` | `POST` | Resends OTP for brand. |
| `/v1/oauth/callback` | `POST` | Handles OAuth callback and exchanges code for token. |

## Brand Management API (`brandManagementApi.js`)

| Endpoint | Method | Description |
|---|---|---|
| `/v1/brands/{brandId}/join` | `POST` | Requests access to a specific brand. |
| `/v1/brands` | `POST` | Creates a new brand. |

