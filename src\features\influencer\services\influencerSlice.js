import { createSlice } from '@reduxjs/toolkit';
import {
    getInfluencerInfoThunk,
    getYoutubeChannelsThunk,
    selectYoutubeChannelsThunk,
    getBasicProfileThunk,
    getDetailedProfileThunk,
    triggerAnalyticsFetchThunk,
    getAnalyticsStatusThunk,
    getDashboardKPIsThunk,
    getTopPostsThunk,
    getMonthlyImpressionsThunk,
    getMonthlyEngagementThunk,
    getMonthlyLikesByTypeThunk,
    getContentDistributionThunk,
    listProfilesThunk,
} from './influencerThunks';
import {
    requestUserCreatorInfoThunk
} from '@auth/service/authThunks'
import { RequestStatus } from '../../../app/store/enum';

const getParsedItem = (key, fallback) => {
    try {
        const value = localStorage.getItem(key);
        if (!value || value === 'undefined') return fallback;
        return JSON.parse(value);
    } catch {
        return fallback;
    }
};

const socialProfiles = getParsedItem('socialProfiles', []);


/**
 * Influencer slice initial state
 */
const initialState = {
    // Profile management state
    socialProfiles: getParsedItem('socialProfiles', []),
    selectedProfile: socialProfiles.length > 0 ? socialProfiles[0] : null,
    youtubeChannels: [],
    selectedChannels: [],

    // Analytics state
    profiles: [],
    currentProfile: null,
    basicProfileData: {},
    detailedProfileData: {},
    analyticsStatus: {},

    // Dashboard data
    dashboardKPIs: {},
    topPosts: {},
    monthlyImpressions: {},
    monthlyEngagement: {},
    monthlyLikesByType: {},
    contentDistribution: {},

    // Request status tracking
    status: RequestStatus.IDLE,
    youtubeStatus: RequestStatus.IDLE,
    profileStatus: RequestStatus.IDLE,
    // analyticsStatus: RequestStatus.IDLE,
    dashboardStatus: RequestStatus.IDLE,

    // Error handling
    error: null,
    youtubeError: null,
    profileError: null,
    analyticsError: null,
    dashboardError: null,

    // UI state
    selectedProfileId: null,
    dashboardView: 'overview' // 'overview', 'analytics', 'content'
};

/**
 * Influencer slice with reducers and extra reducers for thunks
 */
const influencerSlice = createSlice({
    name: 'influencer',
    initialState,
    reducers: {
        // Synchronous actions for UI state management
        setSelectedProfile(state, action) {
            state.selectedProfileId = action.payload;
            state.currentProfile = state.profiles.find(p => p.profile_id === action.payload) || null;
        },

        setDashboardView(state, action) {
            state.dashboardView = action.payload;
        },

        clearProfileData(state) {
            state.basicProfileData = {};
            state.detailedProfileData = {};
            state.analyticsStatus = {};
            state.profileStatus = RequestStatus.IDLE;
            state.profileError = null;
        },

        clearDashboardData(state) {
            state.dashboardKPIs = {};
            state.topPosts = {};
            state.monthlyImpressions = {};
            state.monthlyEngagement = {};
            state.monthlyLikesByType = {};
            state.contentDistribution = {};
            state.dashboardStatus = RequestStatus.IDLE;
            state.dashboardError = null;
        },

        clearErrors(state) {
            state.error = null;
            state.youtubeError = null;
            state.profileError = null;
            state.analyticsError = null;
            state.dashboardError = null;
        },

        clearInfluencerError(state) {
            state.error = null;
        },

        clearYoutubeError(state) {
            state.youtubeError = null;
        },

        clearProfileError(state) {
            state.profileError = null;
        },

        clearAnalyticsError(state) {
            state.analyticsError = null;
        },

        clearDashboardError(state) {
            state.dashboardError = null;
        },

        clearAllInfluencerState(state) {
            // Reset to initial state values
            state.socialProfiles = [];
            state.youtubeChannels = [];
            state.selectedChannels = [];
            state.profiles = [];
            state.currentProfile = null;
            state.basicProfileData = {};
            state.detailedProfileData = {};
            state.analyticsStatus = {};
            state.dashboardKPIs = {};
            state.topPosts = {};
            state.monthlyImpressions = {};
            state.monthlyEngagement = {};
            state.monthlyLikesByType = {};
            state.contentDistribution = {};
            state.selectedProfileId = null;
            state.dashboardView = 'overview';

            // Reset status
            state.status = RequestStatus.IDLE;
            state.youtubeStatus = RequestStatus.IDLE;
            state.profileStatus = RequestStatus.IDLE;
            state.analyticsStatus = RequestStatus.IDLE;
            state.dashboardStatus = RequestStatus.IDLE;

            // Clear errors
            state.error = null;
            state.youtubeError = null;
            state.profileError = null;
            state.analyticsError = null;
            state.dashboardError = null;
        }
    },
    extraReducers: (builder) => {
        builder
            // Get influencer info thunk cases
            .addCase(getInfluencerInfoThunk.pending, (state) => {
                state.status = RequestStatus.LOADING;
                state.error = null;
            })
            .addCase(getInfluencerInfoThunk.fulfilled, (state, action) => {
                state.status = RequestStatus.SUCCEEDED;
                if (action.payload.data) {
                    state.socialProfiles = action.payload.data.social_profiles || [];
                    state.selectedProfile = action.payload.data.social_profiles[0] || null;
                }
            })
            .addCase(requestUserCreatorInfoThunk.fulfilled, (state, action) => {
                state.socialProfiles = action.payload.data.social_profiles;
            })
            .addCase(getInfluencerInfoThunk.rejected, (state, action) => {
                state.status = RequestStatus.FAILED;
                state.error = action.payload?.message || 'Failed to retrieve influencer info';
            })

            // Get YouTube channels thunk cases
            .addCase(getYoutubeChannelsThunk.pending, (state) => {
                state.youtubeStatus = RequestStatus.LOADING;
                state.youtubeError = null;
            })
            .addCase(getYoutubeChannelsThunk.fulfilled, (state, action) => {
                state.youtubeStatus = RequestStatus.SUCCEEDED;
                if (action.payload.data) {
                    state.youtubeChannels = action.payload.data;
                }
            })
            .addCase(getYoutubeChannelsThunk.rejected, (state, action) => {
                state.youtubeStatus = RequestStatus.FAILED;
                state.youtubeError = action.payload?.message || 'Failed to retrieve YouTube channels';
            })

            // Select YouTube channels thunk cases
            .addCase(selectYoutubeChannelsThunk.pending, (state) => {
                state.youtubeStatus = RequestStatus.LOADING;
                state.youtubeError = null;
            })
            .addCase(selectYoutubeChannelsThunk.fulfilled, (state, action) => {
                state.youtubeStatus = RequestStatus.SUCCEEDED;
                if (action.payload.data) {
                    state.selectedChannels = action.meta.arg;
                }
            })
            .addCase(selectYoutubeChannelsThunk.rejected, (state, action) => {
                state.youtubeStatus = RequestStatus.FAILED;
                state.youtubeError = action.payload?.message || 'Failed to select YouTube channels';
            })

            // Get basic profile thunk cases
            .addCase(getBasicProfileThunk.pending, (state) => {
                state.profileStatus = RequestStatus.LOADING;
                state.profileError = null;
            })
            .addCase(getBasicProfileThunk.fulfilled, (state, action) => {
                state.profileStatus = RequestStatus.SUCCEEDED;
                if (action.payload.data && action.meta.arg) {
                    state.basicProfileData[action.meta.arg] = action.payload.data;
                }
            })
            .addCase(getBasicProfileThunk.rejected, (state, action) => {
                state.profileStatus = RequestStatus.FAILED;
                state.profileError = action.payload?.message || 'Failed to retrieve basic profile data';
            })

            // Get detailed profile thunk cases
            .addCase(getDetailedProfileThunk.pending, (state) => {
                state.profileStatus = RequestStatus.LOADING;
                state.profileError = null;
            })
            .addCase(getDetailedProfileThunk.fulfilled, (state, action) => {
                state.profileStatus = RequestStatus.SUCCEEDED;
                if (action.payload.data && action.payload.profileId) {
                    state.detailedProfileData[action.payload.profileId] = action.payload.data;
                }
            })
            .addCase(getDetailedProfileThunk.rejected, (state, action) => {
                state.profileStatus = RequestStatus.FAILED;
                state.profileError = action.payload?.message || 'Failed to retrieve detailed profile analytics';
            })

            // Trigger analytics fetch thunk cases
            .addCase(triggerAnalyticsFetchThunk.pending, (state) => {
                state.analyticsStatus = RequestStatus.LOADING;
                state.analyticsError = null;
            })
            .addCase(triggerAnalyticsFetchThunk.fulfilled, (state) => {
                state.analyticsStatus = RequestStatus.SUCCEEDED;
            })
            .addCase(triggerAnalyticsFetchThunk.rejected, (state, action) => {
                state.analyticsStatus = RequestStatus.FAILED;
                state.analyticsError = action.payload?.message || 'Failed to trigger analytics fetch';
            })

            // Get analytics status thunk cases
            .addCase(getAnalyticsStatusThunk.pending, (state) => {
                state.analyticsStatus = RequestStatus.LOADING;
                state.analyticsError = null;
            })
            .addCase(getAnalyticsStatusThunk.fulfilled, (state, action) => {
                state.analyticsStatus = RequestStatus.SUCCEEDED;
                if (action.payload.data && action.payload.profileId) {
                    state.analyticsStatus[action.payload.profileId] = action.payload.data;
                }
            })
            .addCase(getAnalyticsStatusThunk.rejected, (state, action) => {
                state.analyticsStatus = RequestStatus.FAILED;
                state.analyticsError = action.payload?.message || 'Failed to retrieve analytics status';
            })

            // List profiles thunk cases
            .addCase(listProfilesThunk.pending, (state) => {
                state.profileStatus = RequestStatus.LOADING;
                state.profileError = null;
            })
            .addCase(listProfilesThunk.fulfilled, (state, action) => {
                state.profileStatus = RequestStatus.SUCCEEDED;
                if (action.payload.data) {
                    state.profiles = action.payload.data.profiles || [];
                }
            })
            .addCase(listProfilesThunk.rejected, (state, action) => {
                state.profileStatus = RequestStatus.FAILED;
                state.profileError = action.payload?.message || 'Failed to retrieve profiles list';
            })

            // Dashboard KPIs thunk cases
            .addCase(getDashboardKPIsThunk.pending, (state) => {
                state.dashboardStatus = RequestStatus.LOADING;
                state.dashboardError = null;
            })
            .addCase(getDashboardKPIsThunk.fulfilled, (state, action) => {
                state.dashboardStatus = RequestStatus.SUCCEEDED;
                if (action.payload.data && action.payload.profileId) {
                    state.dashboardKPIs[action.payload.profileId] = action.payload.data;
                }
            })
            .addCase(getDashboardKPIsThunk.rejected, (state, action) => {
                state.dashboardStatus = RequestStatus.FAILED;
                state.dashboardError = action.payload?.message || 'Failed to retrieve dashboard KPIs';
            })

            // Top posts thunk cases
            .addCase(getTopPostsThunk.pending, (state) => {
                state.dashboardStatus = RequestStatus.LOADING;
                state.dashboardError = null;
            })
            .addCase(getTopPostsThunk.fulfilled, (state, action) => {
                state.dashboardStatus = RequestStatus.SUCCEEDED;
                if (action.payload.data && action.payload.profileId) {
                    state.topPosts[action.payload.profileId] = action.payload.data;
                }
            })
            .addCase(getTopPostsThunk.rejected, (state, action) => {
                state.dashboardStatus = RequestStatus.FAILED;
                state.dashboardError = action.payload?.message || 'Failed to retrieve top posts';
            })

            // Monthly impressions thunk cases
            .addCase(getMonthlyImpressionsThunk.pending, (state) => {
                state.dashboardStatus = RequestStatus.LOADING;
                state.dashboardError = null;
            })
            .addCase(getMonthlyImpressionsThunk.fulfilled, (state, action) => {
                state.dashboardStatus = RequestStatus.SUCCEEDED;
                if (action.payload.data && action.payload.profileId) {
                    state.monthlyImpressions[action.payload.profileId] = action.payload.data;
                }
            })
            .addCase(getMonthlyImpressionsThunk.rejected, (state, action) => {
                state.dashboardStatus = RequestStatus.FAILED;
                state.dashboardError = action.payload?.message || 'Failed to retrieve monthly impressions';
            })

            // Monthly engagement thunk cases
            .addCase(getMonthlyEngagementThunk.pending, (state) => {
                state.dashboardStatus = RequestStatus.LOADING;
                state.dashboardError = null;
            })
            .addCase(getMonthlyEngagementThunk.fulfilled, (state, action) => {
                state.dashboardStatus = RequestStatus.SUCCEEDED;
                if (action.payload.data && action.payload.profileId) {
                    state.monthlyEngagement[action.payload.profileId] = action.payload.data;
                }
            })
            .addCase(getMonthlyEngagementThunk.rejected, (state, action) => {
                state.dashboardStatus = RequestStatus.FAILED;
                state.dashboardError = action.payload?.message || 'Failed to retrieve monthly engagement rate';
            })

            // Monthly likes by type thunk cases
            .addCase(getMonthlyLikesByTypeThunk.pending, (state) => {
                state.dashboardStatus = RequestStatus.LOADING;
                state.dashboardError = null;
            })
            .addCase(getMonthlyLikesByTypeThunk.fulfilled, (state, action) => {
                state.dashboardStatus = RequestStatus.SUCCEEDED;
                if (action.payload.data && action.payload.profileId) {
                    state.monthlyLikesByType[action.payload.profileId] = action.payload.data;
                }
            })
            .addCase(getMonthlyLikesByTypeThunk.rejected, (state, action) => {
                state.dashboardStatus = RequestStatus.FAILED;
                state.dashboardError = action.payload?.message || 'Failed to retrieve monthly likes by type';
            })

            // Content distribution thunk cases
            .addCase(getContentDistributionThunk.pending, (state) => {
                state.dashboardStatus = RequestStatus.LOADING;
                state.dashboardError = null;
            })
            .addCase(getContentDistributionThunk.fulfilled, (state, action) => {
                state.dashboardStatus = RequestStatus.SUCCEEDED;
                if (action.payload.data && action.payload.profileId) {
                    state.contentDistribution[action.payload.profileId] = action.payload.data;
                }
            })
            .addCase(getContentDistributionThunk.rejected, (state, action) => {
                state.dashboardStatus = RequestStatus.FAILED;
                state.dashboardError = action.payload?.message || 'Failed to retrieve content distribution';
            });
    },
});

// Export synchronous actions
export const {
    setSelectedProfile,
    setDashboardView,
    clearProfileData,
    clearDashboardData,
    clearErrors,
    clearInfluencerError,
    clearYoutubeError,
    clearProfileError,
    clearAnalyticsError,
    clearDashboardError,
    clearAllInfluencerState
} = influencerSlice.actions;

// Export thunks
export {
    getInfluencerInfoThunk,
    getYoutubeChannelsThunk,
    selectYoutubeChannelsThunk,
    getBasicProfileThunk,
    getDetailedProfileThunk,
    triggerAnalyticsFetchThunk,
    getAnalyticsStatusThunk,
    getDashboardKPIsThunk,
    getTopPostsThunk,
    getMonthlyImpressionsThunk,
    getMonthlyEngagementThunk,
    getMonthlyLikesByTypeThunk,
    getContentDistributionThunk,
    listProfilesThunk
};

// Export the reducer
export default influencerSlice.reducer;
