import React from 'react';
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

/**
 * Toast notification container component.
 * Place this component once in your app layout or main component
 */
export const ToastNotificationContainer = () => {
  return (
    <ToastContainer
      position="bottom-center"
      autoClose={2000}
      hideProgressBar={false}
      newestOnTop
      closeOnClick
      rtl={false}
      pauseOnFocusLoss
      draggable
      pauseOnHover
      theme="colored"
    />
  );
};

/**
 * Toast notification service with different notification types
 */
export const Toast = {
  /**
   * Show a success toast notification
   * @param {string} message - The message to show
   * @param {object} options - Optional toast configuration overrides
   */
  success: (message, options = {}) => {
    toast.success(message, {
      icon: '✅',
      ...options
    });
  },

  /**
   * Show an error toast notification
   * @param {string} message - The message to show
   * @param {object} options - Optional toast configuration overrides
   */
  error: (message, options = {}) => {
    toast.error(message, {
      icon: '❌',
      ...options
    });
  },

  /**
   * Show a warning toast notification
   * @param {string} message - The message to show
   * @param {object} options - Optional toast configuration overrides
   */
  warning: (message, options = {}) => {
    toast.warning(message, {
      icon: '⚠️',
      ...options
    });
  },

  /**
   * Show an info toast notification
   * @param {string} message - The message to show
   * @param {object} options - Optional toast configuration overrides
   */
  info: (message, options = {}) => {
    toast.info(message, {
      icon: 'ℹ️',
      ...options
    });
  },

  /**
   * Show a loading toast notification
   * @param {string} message - The message to show
   * @param {object} options - Optional toast configuration overrides
   * @returns {string} Toast ID that can be used to update the toast later
   */
  loading: (message = 'Loading...', options = {}) => {
    return toast.loading(message, {
      ...options
    });
  },

  /**
   * Update an existing toast notification
   * @param {string} toastId - ID of the toast to update
   * @param {object} options - New options for the toast
   * @param {string} type - New toast type
   */
  update: (toastId, { render, type, ...options }) => {
    toast.update(toastId, {
      render,
      type,
      isLoading: false,
      ...options
    });
  },

  /**
   * Dismiss all toast notifications
   */
  dismissAll: () => {
    toast.dismiss();
  }
};
