// Auth API service - Updated for Multi-Backend Architecture
import authInstance from './instances/authInstance';

/**
 * Authentication API service - Updated for Multi-Backend Architecture
 * Contains all endpoints related to user authentication and authorization
 * Now uses dedicated auth service instance for better separation of concerns
 */
const authApi = {
  // Login endpoint
  login: (credentials) => {
    return authInstance.Post('/auth/influencer/login', credentials);
  },

  // Register endpoint for email
  register: (userData) => {
    return authInstance.Post('/auth/influencer/register', userData);
  },

  // Get available roles
  getRoles: () => {
    return authInstance.Get('/common/master/roles');
  },

  // Verify OTP endpoint
  verifyOtp: (data) => {
    return authInstance.Post('/auth/influencer/verify-otp', data);
  },

  loginVerifyOtp: (data) => {
    return authInstance.Post('/auth/influencer/login/verify-otp', data);
  },

  // Resend OTP endpoint
  resendOtp: (data) => {
    return authInstance.Post('/auth/influencer/resend-otp', data);
  },

  // Logout endpoint
  logout: (sessionData) => {
    return authInstance.Post('/auth/logout', sessionData);
  },

  // Get current user info
  getCurrentUser: () => {
    return authInstance.Get('/auth/me');
  },



  //OAuth related endpoints
  //OauthLogin endpoint
  initiateOAuth: (data) => {
    return authInstance.Post('/oauth/initiate', data);
  },

  // Brand related endpoints
  // Brand Login endpoint
  brandLogin: (credentials) => {
    return authInstance.Post('/auth/brand/login', credentials);
  },

  // Brand Register
  brandRegister: (data) => {
    return authInstance.Post('/auth/brand/register', data);
  },

  // Verify OTP endpoint
  brandVerifyOtp: (data) => {
    return authInstance.Post('/auth/brand/verify-otp', data);
  },

  brandLoginVerifyOtp: (data) => {
    return authInstance.Post('/auth/brand/login/verify-otp', data);
  },

  // Resend OTP endpoint
  brandResendOtp: (data) => {
    return authInstance.Post('/auth/brand/resend-otp', data);
  },

};

export default authApi;