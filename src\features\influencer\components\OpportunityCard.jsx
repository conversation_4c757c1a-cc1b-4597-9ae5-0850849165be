import React, { useState } from "react";
import BookmarkIcon from "@assets/icon/bookmark-filled.svg";
import BookmarkOutlineIcon from "@assets/icon/bookmark.svg";

// Utility to format time ago
function getTimeAgo(dateString) {
    const now = new Date();
    const date = new Date(dateString);
    const diff = Math.floor((now - date) / 1000); // in seconds
    if (isNaN(diff)) return "";
    if (diff < 60) return `${diff} min ago`;
    if (diff < 3600) return `${Math.floor(diff / 60)} min ago`;
    if (diff < 86400) return `${Math.floor(diff / 3600)} hr ago`;
    if (diff < 604800) return `${Math.floor(diff / 86400)} day${Math.floor(diff / 86400) > 1 ? 's' : ''} ago`;
    if (diff < 2592000) return `${Math.floor(diff / 604800)} week${Math.floor(diff / 604800) > 1 ? 's' : ''} ago`;
    if (diff < 31536000) return `${Math.floor(diff / 2592000)} month${Math.floor(diff / 2592000) > 1 ? 's' : ''} ago`;
    return `${Math.floor(diff / 31536000)} year${Math.floor(diff / 31536000) > 1 ? 's' : ''} ago`;
}

const OpportunityCard = ({
    brand,
    logo,
    timeAgo,
    title,
    description,
    deliverables,
    budget,
    deadline,
    isSaved: initialSaved,
    platform,
    requirements,
    postDate,
    backgroundColor = "bg-white",
}) => {
    const [hovered, setHovered] = useState(false);
    const [isSaved, setIsSaved] = useState(initialSaved);
    const [bookmarkHover, setBookmarkHover] = useState(false);

    // Use postDate if provided, otherwise fallback to timeAgo
    const displayTime = postDate ? getTimeAgo(postDate) : timeAgo;
    const getMatchScoreEmoji = (matchScore) => {
        if (matchScore < 30) return "🧩";
        if (matchScore < 60) return "💫";
        return "🚀";
    };
    const matchScore = (requirements.filter((r) => r.isChecked).length / requirements.length) * 100;

    return (
        <div className={`flex flex-col flex-shrink-0 h-full`}>
            {/* Match Score */}
            <span className="text-12-semibold mb-[5px]" role="img" aria-label="target"> {getMatchScoreEmoji(matchScore)} {matchScore}% Match Score </span>

            <div
                className={`flex flex-col relative justify-between rounded-[20px] border-1 border-gray-800 p-2 transition-all duration-500 overflow-hidden shadow-md group h-[428px]`}
                onMouseEnter={() => setHovered(true)}
                onMouseLeave={() => setHovered(false)}
            >

                <div className={`flex flex-col ${backgroundColor} rounded-[12px] p-3.5 gap-3`}>
                    <div className={`flex justify-between items-center gap-1`}>
                        <span className="text-gray-900 text-12-regular">{displayTime}</span>
                        <div
                            className={`rounded-full transition-all duration-200 ${bookmarkHover ? "bg-primary/10" : "bg-transparent"}`}
                            onClick={() => setIsSaved(!isSaved)}
                            onMouseEnter={() => setBookmarkHover(true)}
                            onMouseLeave={() => setBookmarkHover(false)}
                        >
                            <img src={isSaved ? BookmarkIcon : BookmarkOutlineIcon} className="w-10" alt="bookmark" />
                        </div>
                    </div>

                    {/* Card Content */}
                    <div className="flex flex-col gap-2">
                        <div className="flex items-center gap-3 mb-2">
                            <img src={logo} alt={brand} className="w-[30px] rounded-full" />
                            <span className="font-bold text-18-medium text-black">{brand}</span>
                        </div>
                        <div className="text-18-medium text-black">{title}</div>
                        <div className="text-gray-500 text-14-regular mb-2">{description}</div>
                    </div>

                    {/* Deliverables */}
                    <div
                        className={`
                                    overflow-hidden
                                    flex flex-wrap gap-2 mb-2
                                    ${hovered ? "animate-collapse" : "animate-expand"}
                                `}
                    >
                        {deliverables.map((d, i) => (
                            <span
                                key={i}
                                className="text-gray-500 rounded-full px-3 py-1 h-6 text-14-regular border-1 border-gray-200"
                            >
                                {d}
                            </span>
                        ))}
                    </div>


                </div>

                {/* View details button */}

                <div className="relative mb-15  gap-3 min-h-[138px]">
                    {/* Always render both blocks for smooth transitions */}

                    {/* Non-hovered view */}
                    <div
                        className={`absolute top-3.5 inset-0 transition-opacity duration-500 ease-in-out ${hovered ? "opacity-0 pointer-events-none" : "opacity-100"} `}
                    >
                        <div className="flex flex-row justify-between items-start text-14-regular text-white">
                            <div className="flex flex-col gap-1">
                                <span>💸 Budget: {budget}</span>
                                <span>⏰ Deadline: {deadline}</span>
                            </div>

                            <div className="flex mb-2 p-2">
                                {platform.map((img, idx) => (
                                    <img
                                        key={idx}
                                        src={img.src}
                                        alt={img.alt || ""}
                                        className={`h-7 ${img.className || ""}`}
                                    />
                                ))}
                            </div>
                        </div>
                    </div>

                    {/* Hovered view */}
                    <div
                        className={`absolute top-3.5 inset-0 transition-opacity duration-500 ease-in-out ${hovered ? "opacity-100" : "opacity-0 pointer-events-none"} `}
                    >
                        <div className="flex flex-col gap-1 text-14-regular text-white">
                            <span className="mb-2">
                                🎖️ {requirements.filter((r) => r.isChecked).length} out of {requirements.length} Criteria Matched
                            </span>

                            {requirements.map((item) => (
                                <span key={item.label}>
                                    {item.isChecked ? `✅ ${item.label}` : `❌ ${item.label}`}
                                </span>
                            ))}
                        </div>
                    </div>
                </div>
                {/* View Details button (always visible) */}
                <div className="absolute bottom-3.5 right-3.5 flex justify-end">
                    <button className="bg-brand-500 text-white text-16-semibold px-3 py-2 h-11 rounded-lg transition-all duration-200 hover:bg-brand-600">
                        View Details
                    </button>
                </div>

            </div>
        </div>
    );
};

export default OpportunityCard;