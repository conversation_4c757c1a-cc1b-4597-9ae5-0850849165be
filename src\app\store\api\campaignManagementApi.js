/**
 * Campaign Management API Service - Multi-Backend Architecture
 *
 * Handles campaign management functionality using a dedicated campaign service.
 * This service provides endpoints for:
 * - Campaign CRUD operations
 * - Campaign creator management
 * - Content approval workflows
 * - Campaign analytics and metrics
 * - Campaign collaboration features
 */

import campaignInstance from './instances/campaignInstance';

/**
 * Campaign Management API service
 * Contains all endpoints related to campaign management
 */
const campaignManagementApi = {
  
  // === CAMPAIGN CRUD OPERATIONS ===
  
  /**
   * Get all campaigns for the current brand
   * @param {Object} params - Query parameters (search, filters, pagination)
   * @returns {Promise} List of campaigns
   */
  getCampaigns: (params = {}) => {
    const queryString = new URLSearchParams(params).toString();
    const url = queryString ? `/campaigns?${queryString}` : '/campaigns';
    return campaignInstance.Get(url);
  },

  /**
   * Get campaign details by ID
   * @param {string|number} campaignId - Campaign ID
   * @returns {Promise} Campaign details
   */
  getCampaignById: (campaignId) => {
    return campaignInstance.Get(`/campaigns/${campaignId}`);
  },

  /**
   * Create a new campaign
   * @param {Object} campaignData - Campaign data
   * @returns {Promise} Created campaign
   */
  createCampaign: (campaignData) => {
    return campaignInstance.Post('/campaigns', campaignData);
  },

  /**
   * Update an existing campaign
   * @param {string|number} campaignId - Campaign ID
   * @param {Object} campaignData - Updated campaign data
   * @returns {Promise} Updated campaign
   */
  updateCampaign: (campaignId, campaignData) => {
    return campaignInstance.Put(`/campaigns/${campaignId}`, campaignData);
  },

  /**
   * Delete a campaign
   * @param {string|number} campaignId - Campaign ID
   * @returns {Promise} Deletion result
   */
  deleteCampaign: (campaignId) => {
    return campaignInstance.Delete(`/campaigns/${campaignId}`);
  },

  // === CAMPAIGN CREATOR MANAGEMENT ===

  /**
   * Get all creators assigned to a campaign
   * @param {string|number} campaignId - Campaign ID
   * @returns {Promise} List of campaign creators
   */
  getCampaignCreators: (campaignId) => {
    return campaignInstance.Get(`/campaigns/${campaignId}/creators`);
  },

  /**
   * Add a creator to a campaign
   * @param {string|number} campaignId - Campaign ID
   * @param {Object} creatorData - Creator assignment data
   * @returns {Promise} Creator assignment result
   */
  addCreatorToCampaign: (campaignId, creatorData) => {
    return campaignInstance.Post(`/campaigns/${campaignId}/creators`, creatorData);
  },

  /**
   * Remove a creator from a campaign
   * @param {string|number} campaignId - Campaign ID
   * @param {string|number} creatorId - Creator ID
   * @returns {Promise} Removal result
   */
  removeCreatorFromCampaign: (campaignId, creatorId) => {
    return campaignInstance.Delete(`/campaigns/${campaignId}/creators/${creatorId}`);
  },

  /**
   * Update creator assignment in a campaign
   * @param {string|number} campaignId - Campaign ID
   * @param {string|number} creatorId - Creator ID
   * @param {Object} updateData - Update data
   * @returns {Promise} Update result
   */
  updateCampaignCreator: (campaignId, creatorId, updateData) => {
    return campaignInstance.Put(`/campaigns/${campaignId}/creators/${creatorId}`, updateData);
  },

  // === CONTENT MANAGEMENT ===

  /**
   * Get all content items for a campaign
   * @param {string|number} campaignId - Campaign ID
   * @param {Object} params - Query parameters (status, creator, etc.)
   * @returns {Promise} List of content items
   */
  getCampaignContent: (campaignId, params = {}) => {
    const queryString = new URLSearchParams(params).toString();
    const url = queryString ? `/campaigns/${campaignId}/content?${queryString}` : `/campaigns/${campaignId}/content`;
    return campaignInstance.Get(url);
  },

  /**
   * Get specific content item details
   * @param {string|number} campaignId - Campaign ID
   * @param {string|number} contentId - Content ID
   * @returns {Promise} Content item details
   */
  getContentById: (campaignId, contentId) => {
    return campaignInstance.Get(`/campaigns/${campaignId}/content/${contentId}`);
  },

  /**
   * Approve content item
   * @param {string|number} campaignId - Campaign ID
   * @param {string|number} contentId - Content ID
   * @param {Object} approvalData - Approval data (comments, etc.)
   * @returns {Promise} Approval result
   */
  approveContent: (campaignId, contentId, approvalData = {}) => {
    return campaignInstance.Post(`/campaigns/${campaignId}/content/${contentId}/approve`, approvalData);
  },

  /**
   * Reject content item
   * @param {string|number} campaignId - Campaign ID
   * @param {string|number} contentId - Content ID
   * @param {Object} rejectionData - Rejection data (reason, comments, etc.)
   * @returns {Promise} Rejection result
   */
  rejectContent: (campaignId, contentId, rejectionData) => {
    return campaignInstance.Post(`/campaigns/${campaignId}/content/${contentId}/reject`, rejectionData);
  },

  /**
   * Request content revision
   * @param {string|number} campaignId - Campaign ID
   * @param {string|number} contentId - Content ID
   * @param {Object} revisionData - Revision request data
   * @returns {Promise} Revision request result
   */
  requestContentRevision: (campaignId, contentId, revisionData) => {
    return campaignInstance.Post(`/campaigns/${campaignId}/content/${contentId}/revision`, revisionData);
  },

  // === CAMPAIGN ANALYTICS ===

  /**
   * Get campaign analytics overview
   * @param {string|number} campaignId - Campaign ID
   * @returns {Promise} Campaign analytics data
   */
  getCampaignAnalytics: (campaignId) => {
    return campaignInstance.Get(`/campaigns/${campaignId}/analytics`);
  },

  /**
   * Get campaign performance metrics
   * @param {string|number} campaignId - Campaign ID
   * @param {Object} params - Query parameters (timeRange, metrics, etc.)
   * @returns {Promise} Campaign metrics data
   */
  getCampaignMetrics: (campaignId, params = {}) => {
    const queryString = new URLSearchParams(params).toString();
    const url = queryString ? `/campaigns/${campaignId}/metrics?${queryString}` : `/campaigns/${campaignId}/metrics`;
    return campaignInstance.Get(url);
  },

  /**
   * Get campaign ROI analysis
   * @param {string|number} campaignId - Campaign ID
   * @returns {Promise} ROI analysis data
   */
  getCampaignROI: (campaignId) => {
    return campaignInstance.Get(`/campaigns/${campaignId}/roi`);
  },

  /**
   * Export campaign analytics
   * @param {string|number} campaignId - Campaign ID
   * @param {Object} exportParams - Export parameters (format, dateRange, etc.)
   * @returns {Promise} Export result
   */
  exportCampaignAnalytics: (campaignId, exportParams = {}) => {
    return campaignInstance.Post(`/campaigns/${campaignId}/analytics/export`, exportParams);
  },

  // === CAMPAIGN COLLABORATION ===

  /**
   * Get campaign team members
   * @param {string|number} campaignId - Campaign ID
   * @returns {Promise} List of team members
   */
  getCampaignTeam: (campaignId) => {
    return campaignInstance.Get(`/campaigns/${campaignId}/team`);
  },

  /**
   * Add team member to campaign
   * @param {string|number} campaignId - Campaign ID
   * @param {Object} memberData - Team member data
   * @returns {Promise} Addition result
   */
  addTeamMember: (campaignId, memberData) => {
    return campaignInstance.Post(`/campaigns/${campaignId}/team`, memberData);
  },

  /**
   * Remove team member from campaign
   * @param {string|number} campaignId - Campaign ID
   * @param {string|number} memberId - Member ID
   * @returns {Promise} Removal result
   */
  removeTeamMember: (campaignId, memberId) => {
    return campaignInstance.Delete(`/campaigns/${campaignId}/team/${memberId}`);
  },

  // === CAMPAIGN TEMPLATES ===

  /**
   * Get campaign templates
   * @param {Object} params - Query parameters (category, etc.)
   * @returns {Promise} List of campaign templates
   */
  getCampaignTemplates: (params = {}) => {
    const queryString = new URLSearchParams(params).toString();
    const url = queryString ? `/campaigns/templates?${queryString}` : '/campaigns/templates';
    return campaignInstance.Get(url);
  },

  /**
   * Create campaign from template
   * @param {string|number} templateId - Template ID
   * @param {Object} campaignData - Campaign customization data
   * @returns {Promise} Created campaign
   */
  createCampaignFromTemplate: (templateId, campaignData) => {
    return campaignInstance.Post(`/campaigns/templates/${templateId}/create`, campaignData);
  },

  // === HEALTH CHECK ===

  /**
   * Campaign service health check
   * @returns {Promise} Health status
   */
  healthCheck: () => {
    return campaignInstance.Get('/campaigns/health');
  },

};

export default campaignManagementApi;
