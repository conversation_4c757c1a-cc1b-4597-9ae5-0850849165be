import React, { useState, useEffect } from 'react'
import { useLocation } from "react-router-dom";
import { Input } from "@shared/components/UI/input";
import AvatarStack from '@shared/components/AvatarStack';
import DataTable from '../components/DataTable';
import PopupLayout from '@shared/components/UI/PopupLayout';
import CampaignListCard from '../components/CampaignListCard';
import { Outlet, useParams, useNavigate } from "react-router-dom";

import useBrandSelectors from '@brand/services/brandSelectors';
import { useDispatch } from 'react-redux';
import { useLoading } from '@shared/components/UI/LoadingContext';
import { useSnackbar } from '@shared/components/UI/SnackbarContext';
import {
    getCampaignsThunk,
} from '@brand/services/campaignThunks';


import SearchIcon from "@assets/icon/nav/search.svg";
import DownloadIcon from "@assets/icon/download.svg";
import TrashIcon from '@assets/icon/trash.svg';
import { TbLayoutGrid } from "react-icons/tb";
import { IoStar } from "react-icons/io5";
import { FaMagic, FaHammer } from 'react-icons/fa';



const sampleAvatars = {
    avatar1: "https://images.unsplash.com/photo-1494790108377-be9c29b29330?w=150&h=150&fit=crop&crop=faces",
    avatar2: "https://images.unsplash.com/photo-1570295999919-56ceb5ecca61?w=150&h=150&fit=crop&crop=faces",
    avatar3: "https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150&h=150&fit=crop&crop=faces",
    avatar4: "https://images.unsplash.com/photo-1539571696357-5a69c17a67c6?w=150&h=150&fit=crop&crop=faces",
    avatar5: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=faces"
};

const campaignList = [
    {
        id: 1,
        name: "Summer Vibes",
        createdOn: "2023-10-01",
        liveDate: "2023-10-15",
        createdBy: "John Doe",
        createdByPicture: "https://images.unsplash.com/photo-1494790108377-be9c29b29330?w=150&h=150&fit=crop&crop=faces",
        status: "Active",
        creators: [sampleAvatars.avatar1, sampleAvatars.avatar2, sampleAvatars.avatar3, sampleAvatars.avatar4],
        progress: 75
    },
    {
        id: 2,
        name: "Winter Warmers",
        createdOn: "2023-11-10",
        liveDate: "2023-11-25",
        createdBy: "Emma Watson",
        createdByPicture: "https://images.unsplash.com/photo-1502685104226-ee32379fefbe?w=150&h=150&fit=crop&crop=faces",
        status: "Planned",
        creators: [sampleAvatars.avatar5, sampleAvatars.avatar2],
        progress: 25
    },
    {
        id: 3,
        name: "Spring Bloom",
        createdOn: "2024-02-01",
        liveDate: "2024-02-15",
        createdBy: "Liam Smith",
        createdByPicture: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=faces",
        status: "Completed",
        creators: [sampleAvatars.avatar3, sampleAvatars.avatar2, sampleAvatars.avatar1],
        progress: 100
    },
    {
        id: 4,
        name: "Fall Fashion",
        createdOn: "2023-09-12",
        liveDate: "2023-09-30",
        createdBy: "Sophia Lee",
        createdByPicture: "https://images.unsplash.com/photo-1524504388940-b1c1722653e1?w=150&h=150&fit=crop&crop=faces",
        status: "Draft",
        creators: [sampleAvatars.avatar4, sampleAvatars.avatar2, sampleAvatars.avatar3],
        progress: 50
    },
    {
        id: 5,
        name: "Holiday Cheers",
        createdOn: "2023-12-05",
        liveDate: "2023-12-20",
        createdBy: "Michael Chen",
        createdByPicture: "https://images.unsplash.com/photo-1517841905240-472988babdf9?w=150&h=150&fit=crop&crop=faces",
        status: "Paused",
        creators: [sampleAvatars.avatar6, sampleAvatars.avatar1, sampleAvatars.avatar3, sampleAvatars.avatar4],
        progress: 75
    }
];



const CampaignManagement = () => {
    const dispatch = useDispatch();
    const navigate = useNavigate();
    const params = useParams();
    const location = useLocation();
    const isSubRoute = location.pathname !== "/brand/campaigns";

    // const isSubRoute = !!params.campaignId;
    const { setIsLoading } = useLoading();
    const { showSnackbar } = useSnackbar();
    const { selectedBrand } = useBrandSelectors();

    const [searchTerm, setSearchTerm] = useState("");
    const [selectedStatus, setSelectedStatus] = useState("All");
    const [campaignList, setCampaignList] = useState([]);

    const [openPopup, setOpenPopup] = useState("");
    const [step, setStep] = useState(1);

    const campaignStatus = ["All", "Active", "Planned", "Completed", "Draft"];

    useEffect(() => {
        if (isSubRoute) return;
        fetchCampaignList();
    }, [dispatch, selectedBrand, params]);

    // Filter data based on search term
    const getFilteredData = (data, searchTerm, selectedStatus) => {
        let filtered = [...data];

        // Filter by status
        if (selectedStatus && selectedStatus !== "All") {
            filtered = filtered.filter(item =>
                item.status.toLowerCase() === selectedStatus.toLowerCase()
            );
        }

        // No search term, return filtered list
        if (!searchTerm || !searchTerm.trim()) return filtered;

        const normalizedSearchTerm = searchTerm.trim().toLowerCase();

        return filtered.filter(item => {
            const isCreatorList = 'listName' in item;

            if (isCreatorList) {
                return (
                    item.listName?.toLowerCase().startsWith(normalizedSearchTerm) ||
                    item.createdBy?.toLowerCase().startsWith(normalizedSearchTerm) ||
                    item.createdOn?.toLowerCase().startsWith(normalizedSearchTerm) ||
                    item.lastUpdated?.toLowerCase().startsWith(normalizedSearchTerm)
                );
            } else {
                return (
                    item.name?.toLowerCase().startsWith(normalizedSearchTerm) ||
                    item.status?.toLowerCase().startsWith(normalizedSearchTerm) ||
                    item.campaign?.toLowerCase().startsWith(normalizedSearchTerm) ||
                    item.notes?.toLowerCase().startsWith(normalizedSearchTerm) ||
                    (item.labels && Array.isArray(item.labels) &&
                        item.labels.some(label => label.toLowerCase().startsWith(normalizedSearchTerm))) ||
                    (typeof item.channels === 'string' && item.channels.toLowerCase().startsWith(normalizedSearchTerm)) ||
                    (Array.isArray(item.channels) &&
                        (
                            (normalizedSearchTerm === 'instagram' && item.channels.length > 0) ||
                            (normalizedSearchTerm === 'youtube' && item.channels.length > 1)
                        )
                    )
                );
            }
        });
    };


    // API CALLS
    // ✅ Common function to fetch creator lists
    const fetchCampaignList = async () => {
        try {
            setIsLoading(true);
            const result = await dispatch(getCampaignsThunk({ brand_id: selectedBrand?.id || "sd" }));
            if (result.meta.requestStatus === 'fulfilled') {
                const responseData = result.payload.data;
                setCampaignList(responseData);
            } else {
                showSnackbar(result.payload.message || 'Failed to load lists.', 'error');
            }
        } catch (error) {
            console.error('Error fetching lists:', error);
            showSnackbar(error.message || 'Failed to fetch creator lists.', 'error');
        } finally {
            setIsLoading(false);
        }
    };



    return (
        <div className="h-full w-full bg-primary/70 flex flex-col gap-7.5 ">
            {/* Header */}
            <div className="flex items-center">
                <h1 className="text-30-semibold text-gray-50">Campaign Management</h1>
            </div>
            {
                isSubRoute ? (
                    <Outlet />
                ) : (
                    <div className='flex flex-col gap-7.5 '>
                        <div className='flex justify-end'>
                            <button
                                // onClick={() => navigate("/brand/campaigns/create")}
                                onClick={() => { setOpenPopup("createCampaign") }}
                                className="px-8 py-2.5 bg-brand-500 text-white rounded hover:bg-brand-600 transition"
                            >
                                + Create Campaign
                            </button>
                        </div>
                        <div className='flex flex-col gap-2.5 w-full'>
                            <div className='w-full flex gap-2.5'>
                                <div className='w-1/3 bg-gray-600 py-10 px-7.5 rounded-lg flex justify-center items-center'>
                                    Campaign Distribution
                                    (Pie Chart)
                                </div>
                                <div className='w-1/3 bg-gray-600 py-10 px-7.5 rounded-lg flex justify-center items-center'>
                                    Top performing campaigns
                                    (Leaderboard)
                                </div>
                                <div className='w-1/3 bg-gray-600 py-10 px-7.5 rounded-lg flex justify-center items-center'>
                                    Top performing Creators
                                    (Leaderboard)
                                </div>
                            </div>
                            <div className="flex justify-end">
                                <span
                                    className="text-16-semibold text-white cursor-pointer hover:text-brand-500 hover:underline"
                                // onClick={() => navigate("/influencer/profile-analytics")}
                                >
                                    View Analytics
                                </span>
                            </div>
                        </div>
                        <div className='flex items-center justify-between'>
                            <div className='flex items-center gap-4'>
                                <div className="flex items-center space-x-4">
                                    <Input
                                        type="text"
                                        value={searchTerm}
                                        onChange={(e) => setSearchTerm(e.target.value)}
                                        icon={SearchIcon}
                                        placeholder="Search lists..."
                                        className="px-3 py-1 h-10 w-94 bg-transparent text-sm placeholder-gray-400 focus:outline-none"
                                    />
                                </div>
                            </div>
                            <div className='flex items-center gap-4'>
                                {
                                    campaignStatus.map((status, index) => (
                                        <button
                                            key={index}
                                            className={`px-4 py-2.5 border border-gray-400 rounded-full flex items-center gap-1.5 text-14-medium  hover:text-gray-100 transition-colors cursor-pointer ${status === selectedStatus ? "bg-gray-500 text-white" : ""}`}
                                            onClick={() => setSelectedStatus(status)}
                                            title="Toggle Filters"
                                        >
                                            {status === "All" ? <TbLayoutGrid /> : null}
                                            {status}
                                        </button>
                                    ))
                                }
                                {/* Import List
                    <button
                        className={`relative px-4 py-2.5 bg-transparent border border-gray-400 rounded-full flex items-center gap-1.5 text-14-medium text-gray-50 hover:text-gray-300 transition-colors cursor-pointer`}
                        onClick={() => {
                            // setIsPopupOpen("import")
                            console.log('Popup clicked');
                        }}
                        title="Toggle Filters"
                    >
                        <img src={DownloadIcon} alt="Filter" className="w-5 h-5" />
                        Import
                    </button> */}
                            </div>
                        </div>
                        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-5 w-full">
                            {getFilteredData(campaignList, searchTerm, selectedStatus).map((row, index) => (
                                <CampaignListCard
                                    key={index}
                                    campaign={row}
                                    onClick={(e) => {
                                        e.stopPropagation();
                                        console.log('Row clicked:', row);
                                        setSearchTerm('');
                                        navigate(`/brand/campaigns/${row.id}`);
                                    }}
                                    onDelete={(e) => {
                                        e.stopPropagation();
                                        console.log('Delete clicked:', row); setSearchTerm('')
                                    }}
                                />
                            ))}
                        </div>
                    </div>
                )

            }


            {openPopup === "createCampaign" && (
                <PopupLayout
                    title="Create Campaign"
                    onClose={() => setOpenPopup("")}
                    isAcceptButton={false}
                    isCancelButton={false}
                    width="600px"
                >
                    <div className="text-white w-full">
                        <h2 className="text-xl font-semibold mb-2">Let’s Create Campaign</h2>
                        <p className="text-sm text-gray-400 mb-6">
                            How do you want to create this campaign?
                        </p>

                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                            {/* AI option */}
                            <div className="p-4 bg-gray-600 rounded-lg border-1 border-gray-600 text-center hover:transform-gpu transition-all">
                                <div className="flex items-center justify-center gap-2 mb-2">
                                    <span className="text-sm font-semibold">✨ Let AI help me build it</span>
                                </div>
                                <p className="text-xs text-gray-100 mb-3">
                                    Get smart suggestions and auto-filled recommendations based on your goals.
                                </p>
                                <p className="text-xs text-gray-100 mb-4">
                                    Perfect for quick campaign setup with AI-powered insights.
                                </p>
                                <div className="flex justify-center flex-wrap gap-2 text-xs mb-4">
                                    <span className="bg-light-1 text-violet px-3 py-1 rounded-full">Smart suggestions</span>
                                    <span className="bg-light-5 text-blue px-3 py-1 rounded-full">Auto-targeting</span>
                                    <span className="bg-light-8 text-green-2 px-3 py-1 rounded-full">Quick Setup</span>
                                </div>
                                <button
                                    className="w-full py-2 bg-transparent border border-gray-200 hover:scale-104 cursor-pointer hover:border-gray-200 hover:text-gray-200 text-gray-300 rounded-md text-sm font-semibold transition-all"
                                    onClick={() => { setOpenPopup("stepper") }}
                                >
                                    Use AI to build
                                </button>
                            </div>

                            {/* Manual option */}
                            <div className="p-4 bg-gray-600 rounded-lg border-1 border-gray-600 text-center">
                                <div className="flex items-center justify-center gap-2 mb-2">
                                    <span className="text-sm font-semibold">🛠️ I'll do it manually</span>
                                </div>
                                <p className="text-xs text-gray-100 mb-3">
                                    Take full control with step-by-step configuration.
                                </p>
                                <p className="text-xs text-gray-100 mb-4">
                                    Ideal for detailed campaigns with specific requirements.
                                </p>
                                <div className="flex justify-center flex-wrap gap-2 text-xs mb-4">
                                    <span className="bg-light-2 text-red-2 px-3 py-1 rounded-full">Full control</span>
                                    <span className="bg-light-8 text-green-2 px-3 py-1 rounded-full">Detailed setup</span>
                                    <span className="bg-light-5 text-blue px-3 py-1 rounded-full">Step-by-step</span>
                                </div>
                                <button
                                    className="w-full py-2 bg-transparent border border-gray-200 hover:scale-104 cursor-pointer hover:border-gray-200 hover:text-gray-200 text-gray-300 rounded-md text-sm font-semibold transition-all"
                                    onClick={() => {
                                        navigate("/brand/campaigns/create");
                                        setOpenPopup("");
                                    }}
                                >
                                    Manual Setup
                                </button>
                            </div>
                        </div>
                    </div>
                </PopupLayout>
            )}
            {openPopup === "stepper" && (
                <PopupLayout
                    title=""
                    onClose={() => setOpenPopup("")}
                    isAcceptButton={false}
                    isCancelButton={false}
                    width="1200px"
                >
                    {step === 1 && <CreateCampaignStepper onNext={() => setStep(2)} />}
                    {step === 2 && (
                        <ContentScope onNext={() => setStep(3)} onBack={() => setStep(1)} />
                    )}
                    {step === 3 && <CreatorAudienceRequirements onBack={() => setStep(2)} />}
                </PopupLayout>
            )}
        </div>
    )
}

export default CampaignManagement


import { FaToggleOn } from 'react-icons/fa';
import { IoChevronForward } from 'react-icons/io5';

const CreateCampaignStepper = ({ onCancel, onNext }) => {
    const [campaignData, setCampaignData] = useState({
        name: '',
        description: '',
        liveDate: '',
        objective: '',
        closeDate: '',
        isPrivate: true,
    });

    const handleChange = (e) => {
        const { name, value } = e.target;
        setCampaignData({ ...campaignData, [name]: value });
    };

    return (
        <div className="w-full text-white">
            <h2 className="text-24-bold mb-4">Campaign Management</h2>
            <p className="text-14-medium mb-6 cursor-pointer" onClick={onCancel}>
                ← Create Campaign
            </p>

            <div className="bg-[#1C1C1E] rounded-xl p-6 space-y-6">
                {/* Step Progress */}
                <div className="flex justify-between items-center bg-[#2A2A2C] p-3 rounded-lg">
                    {["Campaign Overview", "Content Scope", "Creator & Audience Requirements", "Review & Publish"].map(
                        (step, idx) => (
                            <div key={idx} className="flex flex-col items-center w-1/4 text-center">
                                <div
                                    className={`w-6 h-6 rounded-full mb-1 flex items-center justify-center text-xs font-bold ${idx === 0 ? 'bg-white text-black' : 'bg-gray-600 text-gray-400'
                                        }`}
                                >
                                    {idx + 1}
                                </div>
                                <span className={`text-12-medium ${idx === 0 ? 'text-white' : 'text-gray-400'}`}>{step}</span>
                            </div>
                        )
                    )}
                </div>

                {/* Campaign Form */}
                <div className="space-y-4">
                    <div>
                        <label className="block text-sm mb-1">Campaign Name</label>
                        <input
                            type="text"
                            name="name"
                            value={campaignData.name}
                            onChange={handleChange}
                            placeholder="Enter Campaign Name"
                            className="w-full p-2 rounded-md bg-[#2A2A2C] border border-gray-600 text-white"
                        />
                    </div>

                    <div>
                        <label className="block text-sm mb-1">Campaign Description</label>
                        <textarea
                            name="description"
                            value={campaignData.description}
                            onChange={handleChange}
                            rows={4}
                            placeholder="Describe the tone, style, key messages..."
                            className="w-full p-2 rounded-md bg-[#2A2A2C] border border-gray-600 text-white"
                        />
                    </div>

                    <div className="flex gap-4">
                        <div className="flex-1">
                            <label className="block text-sm mb-1">Campaign Live Date</label>
                            <input
                                type="date"
                                name="liveDate"
                                value={campaignData.liveDate}
                                onChange={handleChange}
                                className="w-full p-2 rounded-md bg-[#2A2A2C] border border-gray-600 text-white"
                            />
                        </div>

                        <div className="flex-1">
                            <label className="block text-sm mb-1">Campaign Objective</label>
                            <select
                                name="objective"
                                value={campaignData.objective}
                                onChange={handleChange}
                                className="w-full p-2 rounded-md bg-[#2A2A2C] border border-gray-600 text-white"
                            >
                                <option value="">Select Campaign Type</option>
                                <option value="Brand Awareness">Brand Awareness</option>
                                <option value="Product Launch">Product Launch</option>
                                <option value="Sales Promotion">Sales Promotion</option>
                            </select>
                        </div>
                    </div>

                    <div className="flex gap-4 items-center">
                        <div className="flex-1">
                            <label className="block text-sm mb-1">Application Close Date</label>
                            <input
                                type="date"
                                name="closeDate"
                                value={campaignData.closeDate}
                                onChange={handleChange}
                                className="w-full p-2 rounded-md bg-[#2A2A2C] border border-gray-600 text-white"
                            />
                        </div>

                        <div className="flex items-center gap-2 mt-6">
                            <label className="text-sm">Keep Private</label>
                            <input
                                type="checkbox"
                                checked={campaignData.isPrivate}
                                onChange={() => setCampaignData({ ...campaignData, isPrivate: !campaignData.isPrivate })}
                            />
                        </div>
                    </div>
                </div>

                {/* Footer Buttons */}
                <div className="flex justify-end gap-4 pt-4">
                    <button
                        onClick={() => console.log("Saving draft", campaignData)}
                        className="px-5 py-2 text-sm border border-gray-500 text-white rounded hover:bg-gray-700"
                    >
                        Save as draft
                    </button>
                    <button
                        onClick={() => onNext(campaignData)}
                        className="px-6 py-2 bg-brand-500 text-white rounded hover:bg-brand-600 transition flex items-center gap-1"
                    >
                        Next <IoChevronForward />
                    </button>
                </div>
            </div>
        </div>
    );
};


import { FaMinus, FaPlus } from "react-icons/fa";
import { FaInstagram, FaYoutube } from "react-icons/fa6";

const ContentScope = ({ onCancel, onNext }) => {
    const [contentCounts, setContentCounts] = useState({
        instagram: { post: 5, reels: 0, story: 2 },
        youtube: { video: 0, shorts: 2 },
    });

    const [hashtags, setHashtags] = useState("");
    const [mentions, setMentions] = useState("");
    const [creators, setCreators] = useState(1);
    const [budget, setBudget] = useState(40000);

    const updateCount = (platform, type, delta) => {
        setContentCounts((prev) => ({
            ...prev,
            [platform]: {
                ...prev[platform],
                [type]: Math.max(0, prev[platform][type] + delta),
            },
        }));
    };

    const totalPerInfluencer = budget / Math.max(1, creators);

    return (
        <div className="bg-[#121212] text-white p-6 rounded-xl w-full max-w-5xl mx-auto shadow-md">
            <h2 className="text-lg font-semibold mb-6">Content Scope*</h2>

            <div className="flex flex-col md:flex-row gap-4 mb-6">
                {/* Instagram */}
                <div className="flex-1 border border-gray-700 rounded-lg p-4">
                    <div className="flex items-center gap-2 mb-4">
                        <FaInstagram className="text-pink-500 text-xl" />
                        <span className="font-semibold">Instagram</span>
                    </div>
                    {["post", "reels", "story"].map((type) => (
                        <div key={type} className="flex justify-between items-center mb-3">
                            <span className="capitalize">{type}</span>
                            <div className="flex items-center gap-2">
                                <button onClick={() => updateCount("instagram", type, -1)}>
                                    <FaMinus />
                                </button>
                                <span className="w-6 text-center">{contentCounts.instagram[type]}</span>
                                <button onClick={() => updateCount("instagram", type, 1)}>
                                    <FaPlus />
                                </button>
                            </div>
                        </div>
                    ))}
                </div>

                {/* YouTube */}
                <div className="flex-1 border border-gray-700 rounded-lg p-4">
                    <div className="flex items-center gap-2 mb-4">
                        <FaYoutube className="text-red-500 text-xl" />
                        <span className="font-semibold">YouTube</span>
                    </div>
                    {["video", "shorts"].map((type) => (
                        <div key={type} className="flex justify-between items-center mb-3">
                            <span className="capitalize">{type}</span>
                            <div className="flex items-center gap-2">
                                <button onClick={() => updateCount("youtube", type, -1)}>
                                    <FaMinus />
                                </button>
                                <span className="w-6 text-center">{contentCounts.youtube[type]}</span>
                                <button onClick={() => updateCount("youtube", type, 1)}>
                                    <FaPlus />
                                </button>
                            </div>
                        </div>
                    ))}
                </div>
            </div>

            {/* Content Summary */}
            <div className="flex flex-wrap gap-3 text-sm mb-6">
                <span className="bg-[#1C1C1C] px-3 py-1 rounded-full text-pink-400">
                    {contentCounts.instagram.post} Posts, {contentCounts.instagram.story} Stories
                </span>
                <span className="bg-[#1C1C1C] px-3 py-1 rounded-full text-red-400">
                    {contentCounts.youtube.shorts} Shorts
                </span>
            </div>

            {/* Hashtags & Mentions */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <input
                    type="text"
                    placeholder="Enter hashtags, separate with comma"
                    value={hashtags}
                    onChange={(e) => setHashtags(e.target.value)}
                    className="bg-[#1C1C1C] border border-gray-700 rounded-md px-4 py-2 text-sm placeholder:text-gray-500"
                />
                <input
                    type="text"
                    placeholder="Enter mentions, separate with comma"
                    value={mentions}
                    onChange={(e) => setMentions(e.target.value)}
                    className="bg-[#1C1C1C] border border-gray-700 rounded-md px-4 py-2 text-sm placeholder:text-gray-500"
                />
            </div>

            {/* Creators & Budget */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div>
                    <label className="block mb-2 text-sm font-medium">Total Creators to Onboard</label>
                    <div className="flex items-center gap-2">
                        <button onClick={() => setCreators(Math.max(1, creators - 1))}>
                            <FaMinus />
                        </button>
                        <span className="w-6 text-center">{creators}</span>
                        <button onClick={() => setCreators(creators + 1)}>
                            <FaPlus />
                        </button>
                    </div>
                </div>

                <div>
                    <label className="block mb-2 text-sm font-medium">Total Budget (₹)*</label>
                    <input
                        type="number"
                        value={budget}
                        onChange={(e) => setBudget(Number(e.target.value))}
                        className="w-full bg-[#1C1C1C] border border-gray-700 rounded-md px-4 py-2 text-sm"
                    />
                </div>
            </div>

            {/* Budget Breakdown */}
            <div className="bg-[#1C1C1C] border border-gray-700 rounded-lg p-4 mb-6">
                <p className="text-sm text-gray-400 mb-2">Budget Breakdown</p>
                <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                        <p className="text-gray-500">Total Budget</p>
                        <p className="text-white font-semibold">₹{budget.toLocaleString()}</p>
                    </div>
                    <div>
                        <p className="text-gray-500">Per Influencer</p>
                        <p className="text-white font-semibold">₹{totalPerInfluencer.toLocaleString()}</p>
                    </div>
                </div>
            </div>

            {/* Navigation Buttons */}
            <div className="flex justify-between mt-6">
                <button className="text-sm text-white underline">← Go Back</button>
                <div className="flex gap-3">
                    <button className="text-sm bg-transparent text-white border border-gray-500 px-4 py-2 rounded-md">
                        Save as draft
                    </button>
                    <button onClick={onNext} className="text-sm bg-[#00C2FF] text-black px-4 py-2 rounded-md font-semibold">
                        Next
                    </button>
                </div>
            </div>
        </div>
    );
};

import { FaChevronLeft } from 'react-icons/fa';

const requirements = {
    creator: {
        'Follower Count*': 'Select',
        'Engagement Rate*': 'Select',
        'Location*': 'Select',
        'Gender*': 'Select',
        'Age Range (optional)': 'Select',
        'Language (optional)': 'Select',
        'Category*': ['Fashion', 'Lifestyle']
    },
    audience: {
        'Gender': 'Select',
        'Age Range (optional)': 'Select',
        'Location': 'Select',
        'Interests': ['Fashion', 'Lifestyle']
    }
};

const SelectField = ({ label, isMulti = false, options = [], defaultValue }) => (
    <div className="flex flex-col gap-1">
        <label className="text-sm text-gray-300">{label}</label>
        {isMulti ? (
            <div className="flex flex-wrap gap-2 px-3 py-2 bg-[#1E1E1E] rounded-md border border-[#333]">
                {defaultValue.map((val, idx) => (
                    <div
                        key={idx}
                        className="bg-[#2A2A2A] text-sm text-white px-2 py-1 rounded-md"
                    >
                        {val}
                    </div>
                ))}
            </div>
        ) : (
            <select className="px-3 py-2 bg-[#1E1E1E] text-white rounded-md border border-[#333]">
                <option>{defaultValue || 'Select'}</option>
                {options.map((opt, i) => (
                    <option key={i}>{opt}</option>
                ))}
            </select>
        )}
    </div>
);

const CreatorAudienceRequirements = ({ onCancel, onNext }) => {
    return (
        <div className=" p-8 text-white font-medium">
            {/* Steps */}
            <div className="flex items-center justify-between mb-8">
                {['Campaign Overview', 'Content Scope', 'Creator & Audience Requirements', 'Review & Publish'].map(
                    (step, i) => (
                        <div
                            key={i}
                            className={`flex-1 flex flex-col items-center justify-center p-2 text-sm ${i === 2 ? 'text-white' : 'text-gray-500'
                                }`}
                        >
                            <div
                                className={`w-4 h-4 mb-1 rounded-full ${i === 2 ? 'bg-white' : 'bg-gray-600'
                                    }`}
                            />
                            STEP {i + 1}
                            <span className="text-xs">{step}</span>
                        </div>
                    )
                )}
            </div>

            {/* Form Card */}
            <div className="border border-[#333] rounded-xl p-6">
                <h2 className="text-lg font-semibold mb-4">Creator & Audience Requirements</h2>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* Creator Requirements */}
                    <div>
                        <h3 className="mb-2 text-sm text-gray-400">Creator Requirements</h3>
                        <div className="space-y-4 flex flex-wrap gap-x-1">
                            {Object.entries(requirements.creator).map(([label, val]) => (
                                <div className="w-48/100 ">
                                    <SelectField
                                        key={label}
                                        label={label}
                                        isMulti={Array.isArray(val)}
                                        defaultValue={val}
                                    />
                                </div>
                            ))}
                        </div>
                    </div>

                    {/* Audience Requirements */}
                    <div>
                        <h3 className="mb-2 text-sm text-gray-400">Audience Requirements</h3>
                        <div className="space-y-4 flex flex-wrap gap-x-1">
                            {Object.entries(requirements.audience).map(([label, val]) => (
                                <div className="w-48/100 ">
                                    <SelectField
                                        key={label}
                                        label={label}
                                        isMulti={Array.isArray(val)}
                                        defaultValue={val}
                                    />
                                </div>
                            ))}
                        </div>
                    </div>
                </div>
            </div>

            {/* Footer Buttons */}
            <div className="mt-8 flex items-center justify-between">
                <button className="flex items-center gap-2 text-sm text-white">
                    <FaChevronLeft className="text-xs" />
                    Go Back
                </button>

                <div className="flex gap-4">
                    <button className="px-6 py-2 rounded-lg border border-gray-600 text-white text-sm">
                        Save as draft
                    </button>
                    <button onClick={onNext} className="px-6 py-2 rounded-lg bg-[#00BFFF] text-black text-sm font-semibold">
                        Next
                    </button>
                </div>
            </div>
        </div>
    );
};
