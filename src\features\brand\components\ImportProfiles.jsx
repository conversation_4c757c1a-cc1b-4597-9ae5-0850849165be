import React, { useState, useRef, useEffect } from 'react';
import { FiArrowLeft, FiX, FiPlus } from 'react-icons/fi';
import PopupLayout from '../../../shared/components/UI/PopupLayout';
import { Input } from "@shared/components/UI/input";
import SearchIcon from "@assets/icon/nav/search.svg";


const ImportProfiles = ({ onClose, onComplete }) => {
    const [file, setFile] = useState(null);
    const [isUploaded, setIsUploaded] = useState(false);
    const [showAddToList, setShowAddToList] = useState(false);
    const [isFileUploadError, setIsFileUploadError] = useState(false);
    const [isPopupOpen, setIsPopupOpen] = useState(false);
    const [uploadResults, setUploadResults] = useState({
        added: 0,
        skipped: 0,
        skippedProfiles: []
    });
    const fileInputRef = useRef(null);
    const [creatorLists, setCreatorLists] = useState([
        { id: '1', listName: 'My Creators', isChecked: false },
        { id: '2', listName: 'Beach Vibes Initiative', isChecked: false },
        { id: '3', listName: 'Holiday Sales Blitz', isChecked: false },
        { id: '4', listName: 'Hot Picks', isChecked: false },
        { id: '5', listName: 'Buzzing', isChecked: false },
        { id: '6', listName: 'Trending', isChecked: false }
    ]);
    const [newListName, setNewListName] = useState('');
    const [isCreatingNewList, setIsCreatingNewList] = useState(false);
    const newListInputRef = useRef(null);

    // Prevent default browser behavior for file drops
    useEffect(() => {
        const preventDefaults = (e) => {
            e.preventDefault();
            e.stopPropagation();
            console.log('Prevented default browser behavior for drag/drop');
        };

        const handleDrop = (e) => {
            e.preventDefault();
            e.stopPropagation();
            console.log('Document drop event intercepted');

            if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
                const droppedFile = e.dataTransfer.files[0];
                if (droppedFile.name.toLowerCase().endsWith('.csv')) {
                    console.log('CSV file dropped:', droppedFile.name);
                    handleFileSelected(droppedFile);
                } else {
                    console.log('Non-CSV file dropped, ignoring');
                    // alert('Please upload a CSV file');
                    setIsFileUploadError(true);
                }
            }
        };

        // Add event listeners to prevent default browser behavior
        document.addEventListener('dragover', preventDefaults);
        document.addEventListener('drop', handleDrop);

        console.log('Component mounted - Added document-level drop handlers');

        return () => {
            // Clean up event listeners
            document.removeEventListener('dragover', preventDefaults);
            document.removeEventListener('drop', handleDrop);
            console.log('Component unmounted - Removed document-level handlers');
        };
    }, []);

    // Handler when a file is selected either by drop or file input
    const handleFileSelected = (selectedFile) => {
        console.log('File selected:', selectedFile.name);
        setFile(selectedFile);
    };

    // Handler for input change event
    const handleFileInputChange = (event) => {
        const selectedFile = event.target.files[0];
        console.log('File input change:', selectedFile?.name);
        if (selectedFile) {
            if (selectedFile.name.toLowerCase().endsWith('.csv')) {
                handleFileSelected(selectedFile);
            } else {
                // alert('Please upload a CSV file');
                setIsFileUploadError(true);
                event.target.value = '';
            }
        }
    };

    // Handler to open file dialog
    const handleBrowseClick = () => {
        console.log('Browse button clicked');
        if (fileInputRef.current) {
            fileInputRef.current.click();
            setIsFileUploadError(false);
        }
    };

    const handleRemoveFile = (e) => {
        if (e) {
            e.stopPropagation();
            e.preventDefault();
        }
        setFile(null);
        console.log('File removed');

        // Reset file input
        if (fileInputRef.current) {
            fileInputRef.current.value = '';
        }
    };

    const handleContinue = () => {
        if (!file) return;

        setIsUploaded(true);
        setUploadResults({
            added: 20,
            skipped: 2,
            skippedProfiles: ["https://example.com/profile1", "https://example.com/profile2"]
        });
    };

    const handleAddToList = () => {
        setShowAddToList(true);
    };

    const handleDone = () => {
        if (onComplete) {
            // Pass the results and selected lists to parent component
            onComplete({
                added: uploadResults.added,
                skipped: uploadResults.skipped,
                skippedProfiles: uploadResults.skippedProfiles,
                selectedLists: creatorLists.filter(list => list.isChecked).map(list => list.id)
            });
        }

        onClose && onClose();
    };

    const handleToggleList = (id) => {
        setCreatorLists(prevLists =>
            prevLists.map(list => ({
                ...list,
                isChecked: list.id === id
            }))
        );
    };

    const handleCreateNewList = () => {
        setIsCreatingNewList(true);
        setTimeout(() => {
            newListInputRef.current?.focus();
        }, 10);
    };

    const handleNewListKeyDown = (e) => {
        if (e.key === 'Enter' && newListName.trim()) {
            const newList = {
                id: `new-${Date.now()}`,
                listName: newListName.trim(),
                isChecked: true
            };

            setCreatorLists(prev => [
                newList, // new item at the top
                ...prev.map(item => ({ ...item, isChecked: false })) // uncheck others
            ]);

            setNewListName('');
            setIsCreatingNewList(false);
        } else if (e.key === 'Escape') {
            setIsCreatingNewList(false);
            setNewListName('');
        }
    };


    // Component for file upload UI
    const UploadView = () => (
        <>
            <div className="text-left mb-6">
                <h2 className="text-20-bold text-gray-50 mb-2">Import profiles</h2>
                <p className="text-gray-300 text-146medium">Import bulk profiles by adding a CSV file containing profile links</p>
            </div>

            <div
                className={`border-2 border-dashed rounded-[18px] p-8 text-center transition-colors hover:cursor-pointer hover:bg-brand-500/10
                    ${file ? 'border-brand-500 bg-brand-500/10' : 'border-gray-800 hover:border-brand-500'} `}
                onClick={(e) => {
                    e.stopPropagation();
                    handleBrowseClick();
                }}
                onDragOver={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    console.log('Drag over dropzone');
                }}
                onDrop={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    console.log('Drop on dropzone');

                    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
                        const droppedFile = e.dataTransfer.files[0];
                        if (droppedFile.name.toLowerCase().endsWith('.csv')) {
                            handleFileSelected(droppedFile);
                            setIsFileUploadError(false);
                        } else {
                            setIsFileUploadError(true);
                            // alert('Please upload a CSV file');
                        }
                    }
                }}
            >
                {/* Hidden file input */}
                <input
                    type="file"
                    ref={fileInputRef}
                    onChange={handleFileInputChange}
                    accept=".csv"
                    className="hidden"
                />

                {file ? (
                    <div className="flex items-center justify-between border-1 border-brand-500 overflow-hidden rounded" onClick={(e) => e.stopPropagation()}>
                        <div className="flex items-center p-3">
                            <div className="bg-brand-500/20 p-2 rounded">
                                <svg className="w-5 h-5 text-brand-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                </svg>
                            </div>
                            <span className="text-white truncate max-w-60 ml-3">{file.name}</span>
                        </div>
                        <div className='flex justify-center items-center bg-brand-500 h-15 w-15 shrink-0'
                            onClick={handleRemoveFile}
                        >
                            <button className="text-gray-100 hover:text-white">
                                <FiX size={18} />
                            </button>
                        </div>
                    </div>
                ) : (
                    <>
                        <div className="mb-4 flex justify-center">
                            <div className="p-3 bg-brand-500/20 rounded-full">
                                <svg className="w-6 h-6 text-brand-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                                </svg>
                            </div>
                        </div>
                        <p className="text-lg font-medium text-white mb-2">Click or drag files to upload</p>
                        <p className="text-sm text-gray-400 mb-4">Upload a .CSV file with profile URLs in column A</p>
                    </>
                )}
            </div>
            <div>
                {isFileUploadError && <span className="text-red-2 text-sm">* Please upload a .CSV file</span>}
            </div>

            <div className="mt-6 flex justify-end">
                <button
                    className={`px-10 py-2 rounded-md font-medium transition-colors ${file
                        ? 'bg-brand-500 text-white hover:bg-brand-600'
                        : 'bg-gray-500 text-gray-300 cursor-not-allowed'
                        }`}
                    disabled={!file}
                    onClick={handleContinue}
                >
                    Continue
                </button>
            </div>
        </>
    );

    // Component for results view
    const ResultsView = () => (
        <>
            <div className="mb-6 flex items-center">
                <button onClick={() => setIsUploaded(false)} className="text-gray-400 hover:text-white mr-3">
                    <FiArrowLeft size={20} />
                </button>
                <div>
                    <h2 className="text-20-semibold text-white">Import profiles</h2>
                </div>
            </div>
            <div className="flex items-center mb-6">
                <div className="bg-brand-500/20 rounded-md border-1 border-brand-500 text-white mr-2 flex items-center overflow-hidden">
                    <div className='px-3 py-2 flex items-center'>
                        {file?.name}
                    </div>
                    <div onClick={() => { setFile(null); setIsUploaded(false); }} className="ml-2 p-3 bg-brand-500 text-gray-100 hover:text-white hover:cursor-pointer">
                        <FiX size={16} />
                    </div>
                </div>
            </div>

            <div className="mb-6 mt-10 gap-1.5 flex flex-col">
                <h3 className="text-20-semibold font-medium text-white">Import Details</h3>
                <p className="text-gray-300 text-16-medium">Create a new list or add creators to existing one.</p>
            </div>

            <div className="space-y-4">
                <div className="flex items-center">
                    <div className="mr-3 text-green-2">
                        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                        </svg>
                    </div>
                    <div className="text-white text-18-medium"><span className='text-green-2 text-20-medium'>{uploadResults.added}</span> creators added successfully</div>
                </div>

                {uploadResults.skipped > 0 && (
                    <div className="flex items-center">
                        <div className="mr-3 text-red-2">
                            <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm-1-11a1 1 0 112 0v4a1 1 0 11-2 0V7zm1 9a1 1 0 100-2 1 1 0 000 2z" clipRule="evenodd" />
                            </svg>
                        </div>
                        <div className='flex'>
                            <div className="text-white text-18-medium"><span className='text-red-2 text-20-medium'>{uploadResults.skipped}</span> creators skipped</div>
                            <button
                                className="ml-3 text-brand-500 hover:text-brand-600 hover:cursor-pointer text-sm underline"
                                onClick={() => setIsPopupOpen(true)}
                            >
                                View skipped
                            </button>
                        </div>
                    </div>
                )}
            </div>
            <div className="mt-12 flex justify-end space-y-3">
                <div className='px-5  bg-brand-500 hover:bg-brand-600 rounded-md '>
                    <button
                        className="py-2 text-white  transition-colors font-medium"
                        onClick={handleAddToList}
                    >
                        Add to list
                    </button>
                </div>
            </div>
        </>
    );

    // Component for Add to List view
    const AddToListView = () => (
        <>
            <div className="mb-6 flex items-start">
                <button onClick={() => setShowAddToList(false)} className="text-gray-400 hover:text-white py-1.5 mr-3">
                    <FiArrowLeft size={20} />
                </button>
                <div>
                    <h2 className="text-xl font-semibold text-white">Add to list</h2>
                    <p className="text-gray-400">Create a new list or add creators to existing one.</p>
                </div>
            </div>

            <div className='flex flex-col gap-2 border border-gray-700 rounded-md p-4'>
                <div className="max-h-60 overflow-y-auto space-y-2 pr-1 scrollbar-thin scrollbar-thumb-gray-700 scrollbar-track-gray-900">
                    {creatorLists.map(list => (
                        <div
                            key={list.id}
                            className="group flex justify-between items-center p-1 px-2 hover:bg-gray-500/50 rounded-md cursor-pointer transition-colors"
                            onClick={() => handleToggleList(list.id)}
                        >
                            <span className=" text-white">{list.listName}</span>
                            <div className={`w-5 h-5 flex items-center justify-center rounded border ${list.isChecked ? 'bg-green border-green' : 'border-gray-500 group-hover:border-gray-400'}`}>
                                {list.isChecked && (
                                    <svg className="w-3.5 h-3.5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="3" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                )}
                            </div>
                        </div>
                    ))}


                </div>
                {isCreatingNewList ? (
                    // <div className="flex items-center p-2 bg-gray-900 rounded-md border-1 border-gray-200">
                    //     <input
                    //         ref={newListInputRef}
                    //         type="text"
                    //         value={newListName}
                    //         onChange={(e) => setNewListName(e.target.value)}
                    //         onKeyDown={handleNewListKeyDown}
                    //         className="w-full bg-gray-900 text-white outline-none"
                    //         placeholder="Enter list name"
                    //         autoFocus
                    //     />
                    // </div>
                    <Input
                        type="text"
                        autoFocus
                        ref={newListInputRef}
                        value={newListName}
                        onChange={(e) => setNewListName(e.target.value)}
                        onblur={() => setIsCreatingNewList(false)}
                        onKeyDown={handleNewListKeyDown}
                        placeholder="Enter List Name"
                        className="px-3 py-1 h-10 w-full bg-transparent text-sm placeholder-gray-400 focus:outline-none"
                    />
                ) : (
                    <div
                        className="flex items-center p-2 hover:bg-gray-500/50 rounded-md cursor-pointer transition-colors"
                        onClick={handleCreateNewList}
                    >
                        <span className=" text-brand-500 font-medium">Create New +</span>
                    </div>
                )}
            </div>

            <div className="mt-6 flex justify-end">
                <button
                    className="py-2 px-15 bg-brand-500 text-white rounded-md hover:bg-brand-600 transition-colors font-medium"
                    onClick={handleDone}
                >
                    Done!
                </button>
            </div>
        </>
    );

    return (
        <div className="w-full">
            {showAddToList ? <AddToListView /> : isUploaded ? <ResultsView /> : <UploadView />}
            {isPopupOpen && (
                <PopupLayout
                    onClose={() => setIsPopupOpen(false)}
                    width="440px"
                    maxWidth="90%"
                    isAcceptButton={false}
                    isCancelButton={true}
                    cancelText="Close"
                >
                    <div className="bg-gray-900 flex flex-col gap-10">
                        <div>
                            <h2 className="text-20-semibold font-semibold text-gray-50 mb-1">Skipped Creator</h2>
                            <p className="text-gray-200 text-14-medium">Invalid or unmatched profile URL(s)</p>
                        </div>
                        <div>
                            <ul className="list-disc list-inside text-gray-50">
                                {uploadResults.skippedProfiles.map((profile, index) => (
                                    <li key={index} className="mb-2">
                                        {profile}
                                    </li>
                                ))}
                            </ul>
                        </div>
                    </div>
                </PopupLayout>
            )}
        </div>
    );
};

export default ImportProfiles;
