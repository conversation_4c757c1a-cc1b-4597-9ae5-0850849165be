import { useDispatch } from 'react-redux';
import {
    getInfluencerInfoThunk,
    getYoutubeChannelsThunk,
    selectYoutubeChannelsThunk,
    getBasicProfileThunk,
    getDetailedProfileThunk,
    triggerAnalyticsFetchThunk,
    getAnalyticsStatusThunk,
    getDashboardKPIsThunk,
    getTopPostsThunk,
    getMonthlyImpressionsThunk,
    getMonthlyEngagementThunk,
    getMonthlyLikesByTypeThunk,
    getContentDistributionThunk,
    listProfilesThunk,
    setSelectedProfile,
    setDashboardView,
    clearProfileData,
    clearDashboardData,
    clearErrors,
    clearInfluencerError,
    clearYoutubeError,
    clearProfileError,
    clearAnalyticsError,
    clearDashboardError
} from './influencerSlice';

/**
 * Custom hook for using influencer-related actions in components
 * This provides a clean interface for components to interact with influencer state
 */
const useInfluencerActions = () => {
    const dispatch = useDispatch();

    return {
        // === PROFILE MANAGEMENT ACTIONS ===
        
        /**
         * Get influencer profile information
         */
        getInfluencerInfo: () => dispatch(getInfluencerInfoThunk()),
        
        /**
         * Get YouTube channels for the influencer
         */
        getYoutubeChannels: () => dispatch(getYoutubeChannelsThunk()),
        
        /**
         * Select YouTube channels
         * @param {Array} channels - Array of selected channel data
         */
        selectYoutubeChannels: (channels) => dispatch(selectYoutubeChannelsThunk(channels)),

        // === PROFILE ANALYTICS ACTIONS ===
        
        /**
         * Get basic profile data
         * @param {string} profileId - Profile ID
         */
        getBasicProfile: (profileId) => dispatch(getBasicProfileThunk(profileId)),
        
        /**
         * Get detailed profile analytics
         * @param {string} profileId - Profile ID
         * @param {Object} options - Query options (force_refresh)
         */
        getDetailedProfile: (profileId, options = {}) => 
            dispatch(getDetailedProfileThunk({ profileId, options })),
        
        /**
         * Trigger analytics fetch for a profile
         * @param {string} profileId - Profile ID
         */
        triggerAnalyticsFetch: (profileId) => dispatch(triggerAnalyticsFetchThunk(profileId)),
        
        /**
         * Get analytics status for a profile
         * @param {string} profileId - Profile ID
         */
        getAnalyticsStatus: (profileId) => dispatch(getAnalyticsStatusThunk(profileId)),
        
        /**
         * List profiles with pagination
         * @param {Object} params - Query parameters (platform, has_detailed, limit, offset)
         */
        listProfiles: (params = {}) => dispatch(listProfilesThunk(params)),

        // === DASHBOARD ANALYTICS ACTIONS ===
        
        /**
         * Get dashboard KPIs for a profile
         * @param {string} profileId - Profile ID
         */
        getDashboardKPIs: (profileId) => dispatch(getDashboardKPIsThunk(profileId)),
        
        /**
         * Get top posts for a profile
         * @param {string} profileId - Profile ID
         * @param {number} postCount - Number of top posts to retrieve (default: 5)
         */
        getTopPosts: (profileId, postCount = 5) => 
            dispatch(getTopPostsThunk({ profileId, postCount })),
        
        /**
         * Get monthly impressions data
         * @param {string} profileId - Profile ID
         * @param {number} period - Number of months (default: 12)
         */
        getMonthlyImpressions: (profileId, period = 12) => 
            dispatch(getMonthlyImpressionsThunk({ profileId, period })),
        
        /**
         * Get monthly engagement rate data
         * @param {string} profileId - Profile ID
         * @param {number} period - Number of months (default: 12)
         */
        getMonthlyEngagement: (profileId, period = 12) => 
            dispatch(getMonthlyEngagementThunk({ profileId, period })),
        
        /**
         * Get monthly likes by content type
         * @param {string} profileId - Profile ID
         * @param {number} period - Number of months (default: 12)
         */
        getMonthlyLikesByType: (profileId, period = 12) => 
            dispatch(getMonthlyLikesByTypeThunk({ profileId, period })),
        
        /**
         * Get content distribution for a profile
         * @param {string} profileId - Profile ID
         */
        getContentDistribution: (profileId) => dispatch(getContentDistributionThunk(profileId)),

        // === UI STATE MANAGEMENT ACTIONS ===
        
        /**
         * Set the currently selected profile
         * @param {string} profileId - Profile ID to select
         */
        setSelectedProfile: (profileId) => dispatch(setSelectedProfile(profileId)),
        
        /**
         * Set dashboard view mode
         * @param {string} view - Dashboard view ('overview', 'analytics', 'content')
         */
        setDashboardView: (view) => dispatch(setDashboardView(view)),
        
        /**
         * Clear profile data and reset profile state
         */
        clearProfileData: () => dispatch(clearProfileData()),
        
        /**
         * Clear dashboard data and reset dashboard state
         */
        clearDashboardData: () => dispatch(clearDashboardData()),

        // === ERROR MANAGEMENT ACTIONS ===
        
        /**
         * Clear all influencer-related errors
         */
        clearAllErrors: () => dispatch(clearErrors()),
        
        /**
         * Clear influencer management errors
         */
        clearInfluencerError: () => dispatch(clearInfluencerError()),
        
        /**
         * Clear YouTube-related errors
         */
        clearYoutubeError: () => dispatch(clearYoutubeError()),
        
        /**
         * Clear profile-related errors
         */
        clearProfileError: () => dispatch(clearProfileError()),
        
        /**
         * Clear analytics-related errors
         */
        clearAnalyticsError: () => dispatch(clearAnalyticsError()),
        
        /**
         * Clear dashboard-related errors
         */
        clearDashboardError: () => dispatch(clearDashboardError()),

        // === CONVENIENCE METHODS ===
        
        /**
         * Load complete profile analytics (basic + detailed + dashboard)
         * @param {string} profileId - Profile ID
         * @param {Object} options - Options for detailed analytics
         */
        loadCompleteProfile: async (profileId, options = {}) => {
            const actions = [
                dispatch(getBasicProfileThunk(profileId)),
                dispatch(getDetailedProfileThunk({ profileId, options })),
                dispatch(getDashboardKPIsThunk(profileId))
            ];
            
            return Promise.all(actions);
        },
        
        /**
         * Load all dashboard data for a profile
         * @param {string} profileId - Profile ID
         * @param {Object} options - Options (period, postCount)
         */
        loadDashboardData: async (profileId, options = {}) => {
            const { period = 12, postCount = 5 } = options;
            
            const actions = [
                dispatch(getDashboardKPIsThunk(profileId)),
                dispatch(getTopPostsThunk({ profileId, postCount })),
                dispatch(getMonthlyImpressionsThunk({ profileId, period })),
                dispatch(getMonthlyEngagementThunk({ profileId, period })),
                dispatch(getMonthlyLikesByTypeThunk({ profileId, period })),
                dispatch(getContentDistributionThunk(profileId))
            ];
            
            return Promise.all(actions);
        },
        
        /**
         * Refresh profile analytics
         * @param {string} profileId - Profile ID
         */
        refreshProfileAnalytics: async (profileId) => {
            await dispatch(triggerAnalyticsFetchThunk(profileId));
            // Wait a bit then fetch updated data
            setTimeout(() => {
                dispatch(getDetailedProfileThunk({ profileId, options: { force_refresh: true } }));
            }, 2000);
        }
    };
};

export default useInfluencerActions;
