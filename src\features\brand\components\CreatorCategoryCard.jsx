import React from 'react';

import AvatarStack from '@shared/components/AvatarStack';
import InstagramCircle from "@assets/icon/instagram-circle.svg";
import YoutubeCircle from "@assets/icon/youtube-circle.svg";

import Icon1 from '@assets/icon/seedling.svg';
import Icon2 from '@assets/icon/chart-pie-alt.svg';
import Icon3 from '@assets/icon/ranking-star.svg';
import Icon4 from '@assets/icon/rocket-lunch.svg';
import Icon5 from '@assets/icon/bullhorn.svg';
import Icon6 from '@assets/icon/gem.svg';
import Icon7 from '@assets/icon/earth-americas.svg';
import Icon8 from '@assets/icon/tips_and_updates.svg';

const Icons = {
    icon1: Icon1,
    icon2: Icon2,
    icon3: Icon3,
    icon4: Icon4,
    icon5: Icon5,
    icon6: Icon6,
    icon7: Icon7,
    icon8: Icon8
};

/**
 * Component that displays a single creator category card based on Figma design
 */
const CreatorCategoryCard = ({
    title,
    description,
    gradient,
    avatars = [],
    count,
    className = "",
    onClick,
    Icon,
    platform = "instagram",
    maxAvatars = 4
}) => {
    return (
        <div
            className={`rounded-xl p-4 relative overflow-hidden cursor-pointer hover:border-gray-500 transition-all ${className}`}
            onClick={onClick}
            style={{ background: gradient }}
        >
            {/* Background decorative icon - placed first in DOM so other content appears above it */}
            <div className="absolute bottom-0 right-0 w-20 h-20 opacity-80 pointer-events-none z-0">
                {Icon && <img src={Icons[`icon${Icon}`]} alt="" className="w-full h-full" />}
            </div>

            <div className='absolute top-3 right-3 '>
                <img src={platform === "instagram" ? InstagramCircle : YoutubeCircle} alt="" className="w-7 h-7" />
            </div>

            {/* Main content - all z-10 to appear above the background icon */}
            <div className="relative z-10 flex flex-col justify-between">
                <div className='h-[90px]'>
                    <div className="flex items-center justify-between mb-1.5 mr-6">
                        <h3 className="text-lg/5 font-semibold  text-gray-900 line-clamp-2 pb-0.5">{title}</h3>
                    </div>
                    {/* Description */}
                    <p className="text-14-regular text-gray-800 line-clamp-2 pb-1">{description}</p>
                </div>

                {/* Avatar Stack - now using the AvatarStack component */}
                <AvatarStack
                    avatars={avatars}
                    count={count}
                    maxAvatars={maxAvatars}
                />
            </div>
        </div>
    );
};

export default CreatorCategoryCard;