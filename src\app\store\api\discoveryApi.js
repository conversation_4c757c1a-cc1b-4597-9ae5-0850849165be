/**
 * Discovery API Service - Multi-Backend Architecture
 * 
 * Handles creator discovery and search functionality using the dedicated discovery service.
 * This service provides endpoints for:
 * - Creator search with filters
 * - Filter metadata management
 * - Cache management
 * - Search result optimization
 */

import discoveryInstance from './instances/discoveryInstance';

/**
 * Discovery API service
 * Contains all endpoints related to creator discovery and search
 */
const discoveryApi = {
  
  // === DISCOVERY SEARCH ENDPOINTS ===
  
  /**
   * Search for creators with filter-based discovery
   * @param {Object} searchParams - Search parameters including query, filters, pagination
   * @returns {Promise} Search results with creator profiles
   */
  searchCreators: (searchParams) => {
    return discoveryInstance.Post('/discovery/search', searchParams);
  },

  /**
   * Get filter catalog for frontend display (deprecated - use getFilterMetadata instead)
   * @param {string} channel - Platform channel (instagram, youtube, tiktok)
   * @returns {Promise} Filter catalog
   */
  getFilters: (channel) => {
    const params = channel ? `?channel=${channel}` : '';
    return discoveryInstance.Get(`/discovery/filters${params}`);
  },

  // === FILTER METADATA ENDPOINTS ===

  /**
   * Get complete filter metadata for a platform channel
   * @returns {Promise} Filter metadata
   */
  getFilterMetadata: () => {
    return discoveryInstance.Get(`/filters/metadata/raw`);
  },

  /**
   * Transform CreatorVerse filter format to provider's API format
   * @param {Object} transformData - Filter transformation data
   * @returns {Promise} Transformed filter payload
   */
  transformFilters: (transformData) => {
    return discoveryInstance.Post('/filters/transform', transformData);
  },

  /**
   * Invalidate cached filter metadata
   * @param {Object} params - Cache invalidation parameters (channel, provider)
   * @returns {Promise} Cache invalidation result
   */
  invalidateFilterCache: (params = {}) => {
    const queryParams = new URLSearchParams();
    
    if (params.channel) queryParams.append('channel', params.channel);
    if (params.provider) queryParams.append('provider', params.provider);
    
    const queryString = queryParams.toString();
    return discoveryInstance.Delete(`/filters/cache${queryString ? `?${queryString}` : ''}`);
  },

  // === CACHE MANAGEMENT ENDPOINTS ===

  /**
   * Get filter cache performance statistics
   * @param {string} platform - Platform filter (optional)
   * @returns {Promise} Cache statistics
   */
  getCacheStats: (platform = null) => {
    const params = platform ? `?platform=${platform}` : '';
    return discoveryInstance.Get(`/discovery/cache/stats${params}`);
  },

  /**
   * Clean up expired cache entries
   * @returns {Promise} Cache cleanup result
   */
  cleanupCache: () => {
    return discoveryInstance.Post('/discovery/cache/cleanup');
  },

  // === HEALTH CHECK ENDPOINTS ===

  /**
   * Basic health check for discovery service
   * @returns {Promise} Health status
   */
  healthCheck: () => {
    return discoveryInstance.Get('/health');
  },

  /**
   * Detailed health check with component status
   * @returns {Promise} Detailed health status
   */
  detailedHealthCheck: () => {
    return discoveryInstance.Get('/health/detailed');
  },

  /**
   * Get basic service metrics
   * @returns {Promise} Service metrics
   */
  getMetrics: () => {
    return discoveryInstance.Get('/health/metrics');
  },

  // === UTILITY METHODS ===

  /**
   * Build search query with common parameters
   * @param {Object} params - Search parameters
   * @returns {Object} Formatted search query
   */
  buildSearchQuery: (params) => {
    const {
      searchQuery = '',
      filters = [],
      limit = 20,
      offset = 0,
      sortBy = 'relevance',
      sortOrder = 'desc'
    } = params;

    return {
      searchQuery,
      filters,
      limit: Math.min(limit, 100), // Cap at 100 results per request
      offset: Math.max(offset, 0), // Ensure non-negative offset
      sortBy,
      sortOrder
    };
  },

  /**
   * Validate filter structure before sending to API
   * @param {Array} filters - Array of filter objects
   * @returns {boolean} Whether filters are valid
   */
  validateFilters: (filters) => {
    if (!Array.isArray(filters)) return false;
    
    return filters.every(filter => 
      filter.channel && 
      filter.filter && 
      filter.value !== undefined && 
      filter.filterFor
    );
  },

  /**
   * Format filter for API consumption
   * @param {Object} filter - Raw filter object
   * @returns {Object} Formatted filter
   */
  formatFilter: (filter) => {
    return {
      channel: filter.channel,
      filter: filter.filter,
      value: filter.value,
      filterFor: filter.filterFor || 'creator'
    };
  }
};

export default discoveryApi;
