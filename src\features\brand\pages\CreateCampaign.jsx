import React, { useState } from 'react'
import { useForm, FormProvider } from 'react-hook-form';
import { useNavigate } from 'react-router-dom';
import useBrandSelectors from '@brand/services/brandSelectors';
import { useDispatch, useSelector } from 'react-redux';
import { useLoading } from '@shared/components/UI/LoadingContext';
import { useSnackbar } from '@shared/components/UI/SnackbarContext';

import { IoChevronBack } from 'react-icons/io5';

// eslint-disable-next-line no-unused-vars
import { motion } from 'framer-motion';
import { FaShieldAlt, FaRegFileAlt, FaUserFriends, FaRocket } from 'react-icons/fa';
import CampaignOverview from '../components/campaign/CampaignOverview';
import ContentScope from '../components/campaign/ContentScope';
import CreatorAudienceRequirements from '../components/campaign/CreatorAudienceRequirements';
import ReviewPublish from '../components/campaign/ReviewPublish';

const steps = [
    { title: 'Campaign Overview', icon: <i className="fi fi-rr-megaphone text-white mt-0.5"></i> },
    { title: 'Content Scope', icon: <i className="fi fi-rs-script text-white mt-0.5"></i> },
    { title: 'Creator & Audience Requirements', icon: <i className="fi fi-rr-badge-check text-white mt-0.5"></i> },
    { title: 'Review & Publish', icon: <i className="fi fi-rr-rocket-lunch text-white mt-0.5"></i> },
];

const CreateCampaign = () => {
    const navigate = useNavigate();
    const dispatch = useDispatch();
    const containerRef = React.useRef(null);

    const { setIsLoading } = useLoading();
    const { showSnackbar } = useSnackbar();
    const { selectedBrand } = useBrandSelectors();

    const [currentStep, setCurrentStep] = useState(0);


    const {
        selectedCampaign,
        detailsStatus,
        detailsError,
        activeTab
    } = useSelector(state => state.campaign);

    React.useEffect(() => {
        if (currentStep === 3 && containerRef.current) {
            containerRef.current.scrollTo({ top: 0, behavior: 'smooth' });
            // OR if the whole window needs to scroll: window.scrollTo({ top: 0, behavior: 'smooth' });
        }
    }, [currentStep]);



    const handleBackClick = () => {
        navigate('/brand/campaigns');
    };

    // Form

    const methods = useForm({ mode: 'onBlur' }); // react-hook-form
    const { handleSubmit, trigger } = methods;

    const onSubmit = (data) => {
        console.log("Final Submission Data", data);
        // navigate('/brand/campaigns');
        // dispatch(saveCampaign(data)) or API call here
    };

    const handleNext = async () => {
        const isStepValid = await trigger(); // validates current step fields
        if (isStepValid) setCurrentStep((prev) => prev + 1);
    };

    const handlePrevious = () => {
        setCurrentStep((prev) => prev - 1);
    };

    return (
        <div
            ref={containerRef}
            className='h-full w-full bg-primary/70 flex flex-col gap-5 overflow-y-auto scrollbar-hidden'
        >
            <div className='flex items-center'>
                <button onClick={handleBackClick} className="mr-2 text-lg text-gray-300 hover:text-white transition cursor-pointer">
                    <IoChevronBack className='h-6 w-6' />
                </button>
                <h1 className="text-24-semibold text-gray-50 inline">Create Campaign</h1>
            </div>
            <div className='flex flex-col gap-5 px-7.5 py-10 rounded-3xl bg-gray-900'>
                <div className='flex justify-between items-end'>
                    <span className='text-20-semibold text-gray-50'>Manual Campaign Setup</span>
                    <div className='flex justify-end'>
                        <button
                            // onClick={() => navigate("/brand/campaigns/create")}
                            className="px-8 py-1.5 bg-brand-500 text-white rounded hover:bg-brand-600 transition"
                        >
                            + Create Campaign
                        </button>
                    </div>
                </div>

                <div className="flex justify-between bg-light-5 py-7.5 px-10 rounded-xl w-full mx-auto">
                    {steps.map((step, index) => {
                        const isCompleted = index < currentStep;
                        const isActive = index === currentStep;

                        return (
                            <motion.div
                                key={index}
                                initial={false}
                                animate={{
                                    z: isActive ? 20 : 0, // Elevate active step
                                    scale: isActive ? 1.1 : 1,
                                }}
                                transition={{ type: 'spring', stiffness: 300, damping: 20 }}
                                className="flex flex-col items-center "
                            >
                                <div className="flex items-center gap-2 px-10">
                                    <motion.div
                                        whileHover={isCompleted ? { scale: 1.2, rotate: 3 } : {}}
                                        whileTap={{ scale: 0.95 }}
                                        className={`text-xl p-2 text-amber-50 w-[36px] h-[36px] flex items-center justify-center ${isCompleted ? 'cursor-pointer' : ''} `}
                                        animate={{
                                            backgroundColor: isCompleted
                                                ? 'var(--color-green-2) ' // green-400
                                                : isActive
                                                    ? 'var(--color-blue)' // blue-500
                                                    : '#4F98FA99', // blue-200
                                            color: 'var(--color-gray-900)',
                                            scale: isActive ? 1.05 : 1,
                                        }}
                                        style={{
                                            borderRadius: '8px',
                                            background: isCompleted
                                                ? 'var(--Dark-Colours-Green2, #12C9AC)'
                                                : isActive
                                                    ? 'var(--color-blue)'
                                                    : 'rgba(79, 152, 250, 0.60)',
                                            boxShadow: isCompleted
                                                ? '0px 4px 28px 0px rgba(18, 201, 172, 0.50)'
                                                : '0px 0px 8px 0px rgba(79, 152, 250, 0.50)',
                                            color: 'white',
                                        }}
                                        onClick={isCompleted ? () => setCurrentStep(index) : undefined}
                                    >
                                        {step.icon}
                                    </motion.div>
                                    <div>
                                        <div className="text-10-medium uppercase tracking-wider text-primary">Step {index + 1}</div>
                                        <div className="text-16-regular text-center text-primary">{step.title}</div>
                                    </div>
                                </div>
                            </motion.div>
                        );
                    })}
                </div>

                <FormProvider {...methods}>
                    <form onSubmit={handleSubmit(onSubmit)} className='flex flex-col gap-5'>
                        {currentStep === 0 && <CampaignOverview stepConfig={steps[currentStep]} />}
                        {currentStep === 1 && <ContentScope stepConfig={steps[currentStep]} />}
                        {currentStep === 2 && <CreatorAudienceRequirements stepConfig={steps[currentStep]} />}
                        {currentStep === 3 && (
                            <div className='flex flex-col gap-5 pointer-events-none'>
                                <CampaignOverview stepConfig={steps[0]} />
                                <ContentScope stepConfig={steps[1]} />
                                <CreatorAudienceRequirements stepConfig={steps[2]} />
                                <div className={`flex py-7.5 px-10 gap-5 bg-teal rounded-[20px] `}>
                                    <div className='flex flex-col gap-1'>
                                        <div className="text-white text-18-medium flex gap-2">
                                            <i class="fi fi-rr-rocket-lunch text-[14px] mt-0.5"></i>
                                            Ready to Launch?
                                        </div>
                                        <span className='text-gray-50 text-14-regular'>Your campaign will be published and visible to creators on the platform.</span>
                                    </div>
                                </div>
                            </div>
                        )}
                        {/* Navigation Buttons */}
                        <div>
                            <div className="flex justify-between">
                                {currentStep > 0 && (
                                    <button onClick={handlePrevious} className="flex items-center text-16-semibold text-gray-200 gap-1.5 cursor-pointer hover:scale-101 transition-all">
                                        <i className="fi fi-br-arrow-small-left"></i>
                                        <span className='hover:underline hover:text-gray-200 whitespace-nowrap'>Go Back</span>
                                    </button>
                                )}
                                <div className="flex gap-3 w-full justify-end">
                                    <button
                                        // onClick={() => handleRejectContent(selectedContent.id)}
                                        className="w-fit px-4 py-1.5 bg-transparent border border-gray-500 rounded-lg text-gray-100 hover:bg-gray-800 cursor-pointer flex gap-2 items-center"
                                    >
                                        Save as draft
                                    </button>
                                    {/* 
                                    <button
                                        // onClick={() => navigate("/brand/campaigns/create")}
                                        className="px-8 py-1.5 bg-brand-500 text-white rounded-md hover:bg-brand-600 transition hover:cursor-pointer"
                                    >
                                        Next
                                    </button> 
                                    */}
                                    <button
                                        type={currentStep === steps.length - 1 ? 'submit' : 'button'}
                                        onClick={currentStep === steps.length - 1 ? undefined : handleNext}
                                        className="px-8 py-1.5 bg-brand-500 text-white rounded-md hover:bg-brand-600 transition hover:cursor-pointer flex gap-2 items-center"
                                    >
                                        {currentStep === steps.length - 1 ? 'Publish Campaign' : 'Next'}
                                        {currentStep === steps.length - 1 && (
                                            <i class="fi fi-rr-rocket-lunch text-[14px] mt-1"></i>
                                        )}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </FormProvider >
            </div >
        </div >
    )
}

export default CreateCampaign
