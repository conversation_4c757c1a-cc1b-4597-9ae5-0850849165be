# Route Protection Troubleshooting Guide

## Common Issues and Solutions

### 1. Maximum Update Depth Exceeded Error

**Error Message:**
```
Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render.
```

**Cause:**
This error occurs when the route protection components cause infinite re-renders, usually due to:
- Utility functions creating new objects on every render
- useSelector returning new objects instead of primitive values
- Circular redirects between routes

**Solution:**
✅ **Fixed in the updated implementation:**
- Removed utility function calls that create new objects
- Used direct primitive value extraction from useSelector
- Added useMemo to prevent unnecessary recalculations
- Simplified redirect logic to avoid circular redirects

### 2. User Type Not Detected Correctly

**Symptoms:**
- Users are redirected to wrong dashboards
- Route protection doesn't work as expected
- User type shows as null or undefined

**Debug Steps:**
1. **Check Auth State Data:**
   ```javascript
   // Add this to your component to debug
   const authState = useSelector(state => state.auth);
   console.log('Auth State:', authState);
   ```

2. **Verify User Data:**
   - **Brand Users** should have: `allocatedBrands` or `organizationBrands` with data
   - **Influencer Users** should have: `socialProfiles` with data

3. **Check localStorage:**
   ```javascript
   console.log('Allocated Brands:', localStorage.getItem('allocatedBrands'));
   console.log('Organization Brands:', localStorage.getItem('organizationBrands'));
   console.log('Social Profiles:', localStorage.getItem('socialProfiles'));
   ```

**Common Fixes:**
- Ensure login process properly sets user data
- Verify API responses include the correct user type data
- Check that localStorage is being populated correctly

### 3. Infinite Redirect Loops

**Symptoms:**
- Browser keeps redirecting between pages
- URL keeps changing rapidly
- Console shows multiple navigation attempts

**Debug Steps:**
1. **Check for Circular Logic:**
   - Brand user accessing brand routes but being redirected to influencer
   - Influencer user accessing influencer routes but being redirected to brand

2. **Verify Route Configuration:**
   ```javascript
   // Make sure userType prop matches the route
   <ProtectedRoute userType="brand">  // ✅ Correct
   <ProtectedRoute userType="influencer">  // ✅ Correct
   ```

3. **Check User Type Detection:**
   ```javascript
   const isBrandUser = (allocatedBrands && allocatedBrands.length > 0) || 
                      (organizationBrands && organizationBrands.length > 0);
   const isInfluencerUser = socialProfiles && socialProfiles.length > 0;
   ```

### 4. Authentication State Issues

**Symptoms:**
- User appears authenticated but gets redirected to login
- Token exists but user data is missing
- Inconsistent authentication state

**Debug Steps:**
1. **Check Authentication Requirements:**
   ```javascript
   const { isAuthenticated, user, token } = useSelector(state => state.auth);
   console.log('Is Authenticated:', isAuthenticated);
   console.log('Has Token:', !!token);
   console.log('Has User:', !!user);
   ```

2. **Verify Token Validity:**
   - Check if token is expired
   - Verify token format
   - Ensure token is being sent with API requests

**Common Fixes:**
- Implement token refresh logic
- Add proper error handling for expired tokens
- Ensure logout clears all authentication data

### 5. Route Protection Not Working

**Symptoms:**
- Unauthenticated users can access protected routes
- Wrong user types can access restricted routes
- Public routes don't redirect authenticated users

**Debug Steps:**
1. **Verify Route Wrapping:**
   ```javascript
   // ✅ Correct - Protected route
   <Route path="/brand/dashboard" element={
     <ProtectedRoute userType="brand">
       <BrandDashboard />
     </ProtectedRoute>
   } />

   // ✅ Correct - Public route
   <Route path="/brand/signin" element={
     <PublicRoute userType="brand">
       <BrandAuthPage />
     </PublicRoute>
   } />
   ```

2. **Check Component Import:**
   ```javascript
   import ProtectedRoute from '../shared/components/ProtectedRoute';
   import PublicRoute from '../shared/components/PublicRoute';
   ```

## Debug Tools

### 1. Route Protection Debug Component

Add this component to any page to see real-time route protection status:

```javascript
import RouteProtectionDebug from '../shared/components/debug/RouteProtectionDebug';

// Add to your component
<RouteProtectionDebug />
```

### 2. Console Debugging

Add these console logs to debug specific issues:

```javascript
// In ProtectedRoute component
console.log('ProtectedRoute - Auth State:', { isAuthenticated, userType, allocatedBrands, organizationBrands, socialProfiles });

// In PublicRoute component
console.log('PublicRoute - Redirect Logic:', { isAuthenticated, userType, redirectTo });

// In useUserType hook
console.log('useUserType - Calculated:', { userType, isBrand, isInfluencer });
```

### 3. Browser DevTools

1. **Redux DevTools:** Monitor auth state changes
2. **Network Tab:** Check API responses for user data
3. **Application Tab:** Verify localStorage data
4. **Console:** Look for error messages and warnings

## Testing Scenarios

### Manual Testing Checklist

#### Authentication Flow
- [ ] Unauthenticated user accessing protected route → Redirected to login
- [ ] Authenticated user accessing public route → Redirected to dashboard
- [ ] Successful login → Redirected to intended destination
- [ ] Logout → Redirected to login page

#### User Type Flow
- [ ] Brand user accessing brand routes → Allowed
- [ ] Brand user accessing influencer routes → Redirected to brand dashboard
- [ ] Influencer user accessing influencer routes → Allowed
- [ ] Influencer user accessing brand routes → Redirected to influencer dashboard

#### Edge Cases
- [ ] User with no type-specific data → Handled gracefully
- [ ] Corrupted localStorage data → Handled gracefully
- [ ] Network failures during auth check → Handled gracefully
- [ ] Rapid navigation between routes → No infinite loops

## Performance Optimization

### 1. Memoization
The updated components use `useMemo` to prevent unnecessary recalculations:

```javascript
const userTypeInfo = useMemo(() => {
  // User type calculation
}, [isAuthenticated, allocatedBrands, organizationBrands, socialProfiles]);
```

### 2. Selective useSelector
Extract only needed values from Redux state:

```javascript
// ✅ Good - Only extract needed values
const { isAuthenticated, allocatedBrands, organizationBrands, socialProfiles } = useSelector(state => state.auth);

// ❌ Avoid - Extracting entire object
const authState = useSelector(state => state.auth);
```

### 3. Avoid Function Calls in Render
Move utility function calls outside of render or memoize them:

```javascript
// ✅ Good - Direct calculation
const isBrandUser = (allocatedBrands && allocatedBrands.length > 0);

// ❌ Avoid - Function call in render
const isBrandUser = isBrandUser(authState);
```

## Best Practices

1. **Always specify userType** for type-specific routes
2. **Use debug components** during development
3. **Test all authentication scenarios** thoroughly
4. **Monitor performance** with React DevTools
5. **Handle edge cases** gracefully
6. **Keep route logic simple** and predictable

## Getting Help

If you're still experiencing issues:

1. **Enable debug mode** with RouteProtectionDebug component
2. **Check console logs** for error messages
3. **Verify auth state data** in Redux DevTools
4. **Test with different user types** and authentication states
5. **Review the implementation** against this troubleshooting guide
