import React from "react";
import PropTypes from "prop-types";
import clsx from "clsx";
import { GrHomeRounded } from "react-icons/gr";
import { FiSearch, FiFlag } from "react-icons/fi";
import { FaRegComment } from "react-icons/fa6";
import { LuCalendarRange } from "react-icons/lu";
import { MdOutlineCampaign } from "react-icons/md";
import { HiOutlineSpeakerphone } from "react-icons/hi";
import { TbCreditCard } from "react-icons/tb";
import { TbSettings } from "react-icons/tb";
import { LuSparkle } from "react-icons/lu";
import { FaRegCircleUser } from "react-icons/fa6";
import { IoWalletOutline, IoSettingsOutline } from "react-icons/io5";



const getIcon = (icon) => {
  switch (icon) {
    case "home":
      return <GrHomeRounded className="w-5 h-5" />
    case "search":
      return <FiSearch className="w-5 h-5" />;
    case "flag":
      return <FiFlag className="w-5 h-5" />;
    case "comment":
      return <FaRegComment className="w-5 h-5" />;
    case "calendar":
      return <LuCalendarRange className="w-5 h-5" />;
    case "megaphone":
      return <HiOutlineSpeakerphone className="w-5 h-5" />;
    case "creditcard":
      return <IoWalletOutline className="w-5 h-5" />;
    case "settings":
      return <IoSettingsOutline className="w-5 h-5" />;
    case "sparkle":
      return <LuSparkle className="w-5 h-5" />;
    case "profile":
      return "https://www.gravatar.com/avatar/2c7d99fe281ecd3bcd65ab915bac6dd5?s=250";
    default:
      return null;
  }
}

const NavElement = ({ icon, label, active, onClick, isProfile = false }) => (
  <li
    onClick={onClick}
    className={clsx(
      "flex items-center gap-3 px-3 py-2 rounded-md cursor-pointer transition-colors duration-200",
      active ? "bg-cyan-500 text-white scale-[1.02] transform-gpu" : "text-gray-400 hover:bg-primary hover:text-white "
    )}
  >
    {/* {icon && <img src={icon} alt={label} className="w-5 h-5" />} */}
    {isProfile ? (
      <img
        src={icon}
        alt={label}
        className="w-6 h-6 rounded-full object-cover"
        onError={(e) => {
          e.target.onerror = null;
          e.target.src = getIcon("profile");
        }}
      />
    ) : (
      <div className="flex items-center w-6">{icon && (getIcon(icon))}</div>
    )}

    {/* Smooth slide + fade animation on sidebar hover */}
    <span
      className={clsx(
        "whitespace-nowrap overflow-hidden max-w-0 opacity-0 translate-x-[-4px]",
        "group-hover:max-w-[200px] group-hover:opacity-100 group-hover:translate-x-0",
        "transition-all duration-300"
      )}
    >
      {label}
    </span>
  </li>
);


NavElement.propTypes = {
  icon: PropTypes.string,
  label: PropTypes.string.isRequired,
  active: PropTypes.bool,
  onClick: PropTypes.func,
};

NavElement.defaultProps = {
  icon: null,
  active: false,
  onClick: () => { },
};

export default NavElement;
