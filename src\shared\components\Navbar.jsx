import React, { useState } from "react";
import { Link, useLocation, useNavigate } from "react-router-dom";
import profile from "@assets/icon/nav/profile.png";
import NavElement from "./NavElement";
import useAuthSelectors from "@auth/service/authSelectors";
import { TbLogout } from "react-icons/tb";
import useAuthActions from "../../features/auth/service/authActions";
import { useLoading } from '@shared/components/UI/LoadingContext';



const Navbar = ({ type }) => {
    const { user } = useAuthSelectors();
    const location = useLocation();
    const navigate = useNavigate();
    const { logout } = useAuthActions();
    const { setIsLoading } = useLoading();




    const profileImage = user?.profile_image || user?.avatar || profile;

    const navItems = type === "influencer"
        ? [
            { icon: "home", label: "Overview", path: "/influencer/dashboard" },
            { icon: "search", label: "Opportunities", path: "/influencer/opportunities" },
            { icon: "flag", label: "My Content", path: "/influencer/my-content" },
            { icon: "comment", label: "My Conversations", path: "/influencer/conversations" },
            { icon: "calendar", label: "Content Calendar", path: "/influencer/calendar" },
            { icon: "megaphone", label: "My Campaigns", path: "/influencer/campaigns" },
            { icon: "creditcard", label: "Earnings & Payouts", path: "/influencer/payouts" },
            { icon: "settings", label: "Setting", path: "/influencer/settings" },
        ]
        : [
            { icon: "home", label: "Overview", path: "/brand/dashboard" },
            { icon: "search", label: "Discover", path: "/brand/discovery" },
            { icon: "sparkle", label: "Manage Creators", path: "/brand/manage-creator" },
            { icon: "megaphone", label: "My Campaigns", path: "/brand/campaigns" },
            { icon: "flag", label: "My Content", path: "/brand/my-content" },
            { icon: "calendar", label: "Content Calendar", path: "/brand/calendar" },
            { icon: "comment", label: "My Conversations", path: "/brand/conversations" },
            { icon: "creditcard", label: "Payments & Contracts", path: "/brand/payments" },
            { icon: "settings", label: "Setting", path: "/brand/settings" },
        ];

    const [width, setWidth] = useState("w-20");

    const handleLogout = async () => {
        try {
            setIsLoading(true);
            await logout();
            setIsLoading(false);
            // Redirect to appropriate login page based on user type
            const loginPath = type === 'brand' ? '/brand/signin' : '/influencer/login';
            navigate(loginPath, { replace: true });
        } catch (error) {
            // Even if logout fails, redirect to login for security
            console.error('Logout error:', error);
            const loginPath = type === 'brand' ? '/brand/signin' : '/influencer/login';
            navigate(loginPath, { replace: true });
        }
    };

    return (
        <nav className={`group absolute h-full ${width} transition-all duration-300 bg-black text-white z-99 `}
            onMouseEnter={() => setWidth("w-65")}
            onMouseLeave={() => setWidth("w-20")}
        >
            <ul className="flex flex-col space-y-3 px-4 py-5">
                {navItems.map((item, idx) => (
                    <Link to={item.path} key={idx}>
                        <NavElement
                            icon={item.icon}
                            label={item.label}
                            active={
                                location.pathname.includes(item.path) ||
                                (location.pathname === '/' && idx === 0)
                            }
                        // active={activeIndex === idx}
                        />
                    </Link>
                ))}
            </ul>

            {/* Profile Icon */}
            {/* <div className="absolute bottom-4 left-3 right-4">
                <Link to="/profile">
                    <NavElement
                        icon={profileImage}
                        label={user?.name || user?.username || "Profile"}
                        active={location.pathname === "/profile"}
                        onClick={() => setActiveIndex(-1)}
                        isProfile={true}
                    />
                </Link>
            </div> */}
            <div className="absolute flex justify-between bottom-4 left-3 right-4">
                <Link to="/profile">
                    <div
                        className={
                            [
                                "flex items-center gap-3 px-2 min-w-10 py-2 rounded-md cursor-pointer transition-colors duration-200",
                                "text-gray-400 hover:bg-primary hover:text-white "
                            ].join(" ")
                        }
                    >
                        <img
                            src={profileImage}
                            alt={user?.name || user?.username || "Profile"}
                            className="w-6 h-6 rounded-full object-cover"
                            onError={(e) => {
                                e.target.onerror = null;
                                e.target.src = profile;
                            }}
                        />
                        {/* Smooth slide + fade animation on sidebar hover */}
                        <span
                            className={
                                [
                                    "whitespace-nowrap overflow-hidden max-w-0 opacity-0 translate-x-[-4px]",
                                    "group-hover:max-w-[200px] group-hover:opacity-100 group-hover:translate-x-0",
                                    "transition-all duration-300"
                                ].join(" ")
                            }
                        >
                            {user?.name || user?.username || "Profile"}
                        </span>
                    </div>
                </Link>
                <div
                    className="w-10 h-10 flex items-center justify-center text-gray-400 hover:bg-primary hover:text-white cursor-pointer rounded-xl"
                    onClick={handleLogout}
                >
                    <TbLogout className=" w-6 h-6 opacity-0 group-hover:opacity-100 transition-all duration-300" />
                </div>
            </div>
        </nav >
    );
};

export default Navbar;
