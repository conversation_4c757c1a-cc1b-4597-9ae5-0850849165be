import React from 'react';

/**
 * Component for displaying a stack of avatars with a count badge
 * 
 * @param {Object[]} avatars - Array of avatar image URLs
 * @param {number} count - Number to display in the count badge
 * @param {number} maxAvatars - Maximum number of avatars to display before showing count (default: 4)
 * @param {string} className - Additional CSS classes
 */
const AvatarStack = ({ avatars = [], count = 0, maxAvatars = 4, className = "" }) => {
  // Calculate how many avatars to show
  const visibleAvatars = avatars.slice(0, maxAvatars);

  const formatNumber = (num) => {
    if (num >= 1_000_000) {
      return (num / 1_000_000).toFixed(num % 1_000_000 === 0 ? 0 : 1) + 'M';
    } else if (num >= 1_000) {
      return (num / 1_000).toFixed(num % 1_000 === 0 ? 0 : 1) + 'K';
    }
    return num.toString();
  };

  return (
    <div className={`flex items-center ${className}`}>
      <div className="flex -space-x-2">
        {visibleAvatars.map((avatar, i) => (
          <img
            key={i}
            src={avatar}
            alt="Creator"
            className="w-8 h-8 rounded-full border-2 border-white object-cover"
          />
        ))}
      </div>
      {count > 0 && (
        <div className="flex justify-center items-center -ml-2 min-w-8 h-8 text-12-medium text-[#7F56D9] bg-[#F9F5FF] border-2 border-white p-1 px-2 rounded-full">
          +{formatNumber(count)}
        </div>
      )}
    </div>
  );
};

export default AvatarStack;