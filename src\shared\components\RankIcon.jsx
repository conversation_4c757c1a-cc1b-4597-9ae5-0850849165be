import React, { useMemo } from 'react';
import PropTypes from 'prop-types';

const RankIcon = ({
  percentage = 0,
  type = 'star',
  size = 24,
  fillColor = '#FFD700',
  fillDirection = 'bt', // 'bt', 'tb', 'lr', 'rl'
  className = ''
}) => {
  const validPercentage = Math.max(0, Math.min(100, percentage));

  // Unique ID to prevent conflicts
  const gradientId = useMemo(
    () => `grad-${type}-${Math.random().toString(36).substring(2, 9)}`,
    [type]
  );

  const strokeColor = validPercentage > 0 ? fillColor : '#71717A';

  // Gradient direction mapping
  const directionMap = {
    bt: { x1: '0%', y1: '100%', x2: '0%', y2: '0%' },   // bottom → top
    tb: { x1: '0%', y1: '0%', x2: '0%', y2: '100%' },   // top → bottom
    lr: { x1: '0%', y1: '0%', x2: '100%', y2: '0%' },   // left → right
    rl: { x1: '100%', y1: '0%', x2: '0%', y2: '0%' }    // right → left
  };

  const { x1, y1, x2, y2 } = directionMap[fillDirection] || directionMap.bt;

  // For directions where fill grows upward or rightward
  const fillFirst = ['tb', 'rl'].includes(fillDirection);

  const fillOffset = `${validPercentage}%`;
  const transparentOffset = `${validPercentage}%`;

  const paths = {
    star: "M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z",
    bolt: "M13 10V3L4 14H11V21L20 10H13Z"
  };

  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={`transition-colors duration-200 ${className}`}
    >
      <defs>
        <linearGradient id={gradientId} x1={x1} y1={y1} x2={x2} y2={y2}>
          {fillFirst ? (
            <>
              <stop offset={fillOffset} stopColor={fillColor} />
              <stop offset={fillOffset} stopColor="transparent" />
            </>
          ) : (
            <>
              <stop offset={transparentOffset} stopColor={fillColor} />
              <stop offset={transparentOffset} stopColor="transparent" />
            </>
          )}
        </linearGradient>
      </defs>

      <path
        d={paths[type]}
        stroke={strokeColor}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
        fill={`url(#${gradientId})`}
      />
    </svg>
  );
};

RankIcon.propTypes = {
  percentage: PropTypes.number,
  type: PropTypes.oneOf(['star', 'bolt']),
  size: PropTypes.number,
  fillColor: PropTypes.string,
  fillDirection: PropTypes.oneOf(['bt', 'tb', 'lr', 'rl']),
  className: PropTypes.string
};

export default RankIcon;
