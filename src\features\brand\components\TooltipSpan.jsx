import React, { useState } from 'react';
import infoIcon from '@assets/icon/info.svg';

const TooltipSpan = ({ text, tooltipText, position = 'top', iconVisible = true, spanClass="" }) => {
  const [isTooltipVisible, setIsTooltipVisible] = useState(false);

  // Determine tooltip position classes
  const tooltipPositionClass =
    position === 'top'
      ? 'bottom-full -translate-y-2'
      : 'top-full translate-y-2';

  return (
    <div className="relative inline-flex items-center">
      {iconVisible === true ?
        <span className={spanClass}>{text}</span>
        :
        <span className={`cursor-pointer ${spanClass}`}
          onMouseEnter={() => setIsTooltipVisible(true)}
          onMouseLeave={() => setIsTooltipVisible(false)}
          tabIndex={0}
        >
          {text}
        </span>


      }
      <div
        className="relative ml-1"
        onMouseEnter={() => setIsTooltipVisible(true)}
        onMouseLeave={() => setIsTooltipVisible(false)}
        tabIndex={0}
      >
        {iconVisible &&
          <img src={infoIcon} className="w-4 h-4 cursor-pointer" alt="Info" />
        }
        {isTooltipVisible && (
          <div
            className={`absolute z-50 items-center transform -translate-x-1/2 px-3 py-2 bg-gray-500 text-gray-50 text-12-regular  rounded shadow-lg  w-max ${tooltipPositionClass}`}
          >
            {tooltipText}
          </div>
        )}
      </div>
    </div>
  );
};

export default TooltipSpan;
