# CreatorVerse Discovery & Profile Analytics API Documentation

## Overview

The CreatorVerse API provides a comprehensive system for creator discovery, profile analytics, and filter management. The API follows RESTful principles and uses JWT authentication for secure access.

## Base URL

```
http://localhost:8000/v1
```

## Authentication

All endpoints (except health checks) require JWT authentication. Include the JWT token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

### Test Token
For testing purposes, a 7-day valid JWT token can be created using:
```bash
python3 create_test_token.py
```

Test user details:
- Email: <EMAIL>
- User ID: 31c72ea2-f334-4307-929e-e5d8dbfd2b66
- Roles: admin
- Permissions: profile.analytics.read, profile.analytics.detailed, analytics.all

## Response Format

All API responses follow the StandardResponse format:

```json
{
  "success": true,
  "message": "Success message",
  "data": {
    // Response data
  }
}
```

### Error Response Format

```json
{
  "success": false,
  "message": "Error description",
  "data": {
    "error_code": "ERROR_CODE",
    // Additional error details
  }
}
```

## API Endpoints

### 1. Health Check Endpoints

#### GET /v1/health/
Basic health check endpoint to verify service availability.

**Request:**
```http
GET /v1/health/
```

**Response:**
```json
{
  "success": true,
  "message": "Service is healthy",
  "data": {
    "status": "healthy",
    "service": "CreatorVerse Profile Analytics",
    "environment": "development"
  }
}
```

#### GET /v1/health/detailed
Detailed health check with component status.

**Request:**
```http
GET /v1/health/detailed
```

**Response:**
```json
{
  "success": true,
  "message": "All systems operational",
  "data": {
    "status": "healthy",
    "service": "CreatorVerse Profile Analytics",
    "environment": "development",
    "components": {
      "database": true,
      "redis": true,
      "overall": true
    }
  }
}
```

#### GET /v1/health/metrics
Get basic service metrics.

**Request:**
```http
GET /v1/health/metrics
```

**Response:**
```json
{
  "success": true,
  "message": "Metrics retrieved successfully",
  "data": {
    "service_metrics": {
      "total_requests": 1234,
      "cache_hits": 890,
      "external_api_calls": 344
    },
    "service": "CreatorVerse Profile Analytics",
    "environment": "development"
  }
}
```

### 2. Filter Metadata Endpoints

#### GET /v1/filters/metadata
Get complete filter metadata for a platform channel based on the active provider.

**Request:**
```http
GET /v1/filters/metadata?channel=instagram&option_for=creator&include_validation_rules=false
```

**Query Parameters:**
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| channel | string | Yes | Platform channel (instagram, youtube, tiktok) |
| option_for | string | No | Filter target: creator, audience, or None for both |
| include_validation_rules | boolean | No | Include provider-specific validation rules |

**Response:**
```json
{
  "success": true,
  "message": "Filter metadata retrieved successfully",
  "data": {
    "channel": "instagram",
    "provider": {
      "name": "phyllo",
      "version": "1.0"
    },
    "filters": [
      {
        "name": "gender",
        "type": "radio-button",
        "icon": "gender-icon",
        "options": [
          {
            "label": "Male",
            "value": "male"
          },
          {
            "label": "Female", 
            "value": "female"
          }
        ]
      }
    ],
    "validation_rules": {}
  }
}
```

#### POST /v1/filters/transform
Transform CreatorVerse filter format to active provider's API format.

**Request:**
```http
POST /v1/filters/transform
Content-Type: application/json

{
  "channel": "instagram",
  "filters": [
    {
      "filter": "gender",
      "value": "male",
      "filterFor": "creator"
    },
    {
      "filter": "location",
      "value": ["mumbai", "delhi"],
      "filterFor": "creator"
    }
  ]
}
```

**Response:**
```json
{
  "success": true,
  "message": "Filters transformed successfully",
  "data": {
    "provider_payload": {
      "creator_gender": "MALE",
      "creator_locations": ["loc_123", "loc_456"]
    },
    "transformation_report": {
      "total_filters": 2,
      "transformed": 2,
      "skipped": 0,
      "provider": "phyllo"
    }
  }
}
```

#### DELETE /v1/filters/cache
Invalidate cached filter metadata.

**Request:**
```http
DELETE /v1/filters/cache?channel=instagram&provider=phyllo
```

**Query Parameters:**
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| channel | string | No | Specific channel to invalidate |
| provider | string | No | Specific provider to invalidate |

### 3. Discovery Endpoints

#### POST /v1/discovery/search
Search for creators with filter-based discovery and intelligent caching.

**Request:**
```http
POST /v1/discovery/search
Content-Type: application/json

{
  "searchQuery": "fashion blogger",
  "filters": [
    {
      "channel": "instagram",
      "filter": "gender",
      "value": "female",
      "filterFor": "creator"
    },
    {
      "channel": "instagram",
      "filter": "follower_count",
      "value": {
        "min": 10000,
        "max": 1000000
      },
      "filterFor": "creator"
    }
  ],
  "limit": 20,
  "offset": 0
}
```

**Response:**
```json
{
  "success": true,
  "message": "Creator discovery results",
  "data": {
    "profiles": [
      {
        "external_id": "123456789",
        "platform_username": "fashionista",
        "profile_id": "550e8400-e29b-41d4-a716-************",
        "url": "https://instagram.com/fashionista",
        "image_url": "https://example.com/profile.jpg",
        "full_name": "Fashion Blogger",
        "follower_count": 50000,
        "engagement_rate": 3.5,
        "is_verified": true
      }
    ],
    "metadata": {
      "total_count": 150,
      "offset": 0,
      "limit": 20,
      "cache_hit": false,
      "execution_time_ms": 245,
      "filter_hash": "abc123def456"
    }
  }
}
```

#### GET /v1/discovery/cache/stats
Get filter cache performance statistics.

**Request:**
```http
GET /v1/discovery/cache/stats?platform=instagram
```

**Query Parameters:**
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| platform | string | No | Platform filter |

#### POST /v1/discovery/cache/cleanup
Clean up expired cache entries.

**Request:**
```http
POST /v1/discovery/cache/cleanup
```

#### GET /v1/discovery/filters
Get filter catalog for frontend display (deprecated - use /v1/filters/metadata instead).

**Request:**
```http
GET /v1/discovery/filters?channel=instagram
```

### 4. Profile Analytics Endpoints

#### GET /v1/analytics/profiles/{profile_id}
Get basic profile data (Phase 1 only).

**Request:**
```http
GET /v1/analytics/profiles/550e8400-e29b-41d4-a716-************
```

**Response:**
```json
{
  "success": true,
  "message": "Basic profile data retrieved",
  "data": {
    "profile_id": "550e8400-e29b-41d4-a716-************",
    "platform_username": "fashionista",
    "full_name": "Fashion Blogger",
    "follower_count": 50000,
    "engagement_rate": 3.5,
    "is_verified": true,
    "platform": "instagram",
    "created_at": "2024-01-15T10:30:00Z"
  }
}
```

#### GET /v1/analytics/profiles/{profile_id}/detailed
Get detailed analytics (triggers Phase 2 if needed).

**Request:**
```http
GET /v1/analytics/profiles/550e8400-e29b-41d4-a716-************/detailed?force_refresh=false
```

**Query Parameters:**
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| force_refresh | boolean | No | Force refresh detailed analytics even if cached |

**Response:**
```json
{
  "success": true,
  "message": "Detailed profile analytics retrieved",
  "data": {
    "profile": {
      // Basic profile data
    },
    "audience_demographics": {
      "gender_distribution": {
        "male": 35.5,
        "female": 64.5
      },
      "age_distribution": {
        "13-17": 15.2,
        "18-24": 45.3,
        "25-34": 25.1,
        "35-44": 10.2,
        "45+": 4.2
      },
      "location_distribution": [
        {
          "country": "India",
          "percentage": 45.5,
          "cities": [
            {"name": "Mumbai", "percentage": 12.3},
            {"name": "Delhi", "percentage": 10.1}
          ]
        }
      ]
    },
    "audience_psychographics": {
      "interests": ["Fashion", "Beauty", "Lifestyle"],
      "brand_affinities": ["Nike", "Zara", "H&M"]
    },
    "content_analytics": {
      "top_posts": [],
      "sponsored_performance": {},
      "engagement_patterns": {}
    },
    "reputation_history": []
  }
}
```

#### POST /v1/analytics/profiles/{profile_id}/fetch-analytics
Manually trigger detailed analytics fetch.

**Request:**
```http
POST /v1/analytics/profiles/550e8400-e29b-41d4-a716-************/fetch-analytics
```

#### GET /v1/analytics/profiles/{profile_id}/status
Check analytics fetch status for a profile.

**Request:**
```http
GET /v1/analytics/profiles/550e8400-e29b-41d4-a716-************/status
```

#### GET /v1/analytics/dashboard/kpi/{profile_id}
Get dashboard KPI metrics for a specific profile.

**Request:**
```http
GET /v1/analytics/dashboard/kpi/550e8400-e29b-41d4-a716-************
```

**Response:**
```json
{
  "success": true,
  "message": "Dashboard KPI data retrieved successfully",
  "data": {
    "followers": 50000,
    "audience_credibility_score": 0.85,
    "engagement_rate": 3.5,
    "average_reach": 25000,
    "average_impression": 100000,
    "average_likes": 1750,
    "average_comments": 150,
    "average_views": 30000
  }
}
```

#### GET /v1/analytics/dashboard/top-posts/{profile_id}
Get top performing posts for a specific profile.

**Request:**
```http
GET /v1/analytics/dashboard/top-posts/550e8400-e29b-41d4-a716-************?post_count=5
```

**Query Parameters:**
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| post_count | integer | No | Number of top posts to return (1-20, default: 5) |

#### GET /v1/analytics/dashboard/monthly-impressions/{profile_id}
Get monthly impressions data with 3 series breakdown.

**Request:**
```http
GET /v1/analytics/dashboard/monthly-impressions/550e8400-e29b-41d4-a716-************?period=12
```

**Query Parameters:**
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| period | integer | No | Number of months to return (1-24, default: 12) |

#### GET /v1/analytics/dashboard/monthly-engagement-rate/{profile_id}
Get monthly engagement rate data with 3 series breakdown.

**Request:**
```http
GET /v1/analytics/dashboard/monthly-engagement-rate/550e8400-e29b-41d4-a716-************?period=12
```

#### GET /v1/analytics/dashboard/monthly-likes-by-type/{profile_id}
Get monthly likes breakdown by content type.

**Request:**
```http
GET /v1/analytics/dashboard/monthly-likes-by-type/550e8400-e29b-41d4-a716-************?period=12
```

#### GET /v1/analytics/dashboard/content-distribution/{profile_id}
Get content type distribution for a profile.

**Request:**
```http
GET /v1/analytics/dashboard/content-distribution/550e8400-e29b-41d4-a716-************
```

#### GET /v1/analytics/profiles
List profiles with basic information and pagination.

**Request:**
```http
GET /v1/analytics/profiles?platform=instagram&has_detailed=true&limit=20&offset=0
```

**Query Parameters:**
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| platform | string | No | Filter by platform (instagram, youtube, tiktok) |
| has_detailed | boolean | No | Filter by detailed analytics availability |
| limit | integer | No | Number of profiles to return (1-100, default: 20) |
| offset | integer | No | Number of profiles to skip |

### 5. Saved Filter Sets Endpoints

#### POST /v1/saved/filters
Create a new saved filter set.

**Request:**
```http
POST /v1/saved/filters
Content-Type: application/json

{
  "name": "High Engagement Beauty Creators",
  "description": "Beauty influencers with high engagement rates",
  "channel": "instagram",
  "filters": [
    {
      "channel": "instagram",
      "filter": "categories",
      "value": ["beauty"],
      "filterFor": "creator"
    },
    {
      "channel": "instagram",
      "filter": "engagement_rate",
      "value": {"min": 5.0, "max": 10.0},
      "filterFor": "creator"
    }
  ],
  "tags": ["beauty", "high-engagement"]
}
```

#### GET /v1/saved/filters
Retrieve saved filter sets for the authenticated user.

**Request:**
```http
GET /v1/saved/filters?channel=instagram&include_global=true&search=beauty&page=1&per_page=20
```

**Query Parameters:**
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| channel | string | No | Filter by platform channel |
| include_global | boolean | No | Include global filter sets (default: true) |
| search | string | No | Search in name, description, or tags |
| page | integer | No | Page number (default: 1) |
| per_page | integer | No | Items per page (1-100, default: 20) |

#### GET /v1/saved/filters/{filter_set_id}
Get detailed information about a specific saved filter set.

**Request:**
```http
GET /v1/saved/filters/550e8400-e29b-41d4-a716-************
```

#### PUT /v1/saved/filters/{filter_set_id}
Update an existing saved filter set.

**Request:**
```http
PUT /v1/saved/filters/550e8400-e29b-41d4-a716-************
Content-Type: application/json

{
  "name": "Updated Filter Set Name",
  "description": "Updated description",
  "tags": ["beauty", "high-engagement", "verified"]
}
```

#### DELETE /v1/saved/filters/{filter_set_id}
Delete (soft delete) a saved filter set.

**Request:**
```http
DELETE /v1/saved/filters/550e8400-e29b-41d4-a716-************
```

### 6. Global Filters Endpoints

#### GET /v1/global-filters/list
Get all global filter sets with their details.

**Request:**
```http
GET /v1/global-filters/list?channel=instagram
```

**Query Parameters:**
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| channel | string | No | Filter by platform channel |

**Response:**
```json
{
  "success": true,
  "message": "Global filters retrieved successfully",
  "data": {
    "filters": [
      {
        "id": "550e8400-e29b-41d4-a716-************",
        "name": "Default Instagram Filters",
        "channel": "instagram",
        "is_default": true,
        "filters": []
      }
    ]
  }
}
```

## Error Codes

| Code | Description |
|------|-------------|
| 400 | Bad Request - Invalid parameters |
| 401 | Unauthorized - Invalid or missing JWT token |
| 403 | Forbidden - Insufficient permissions |
| 404 | Not Found - Resource not found |
| 500 | Internal Server Error |

## Rate Limiting

The API implements rate limiting to ensure fair usage:
- 1000 requests per hour per user
- 100 requests per minute per user

## Caching

The API uses intelligent caching:
- Filter results: 24 hour TTL
- Profile data: 1 hour TTL
- External API responses: 30 minutes TTL

## Provider Independence

The system is designed to be provider-independent:
- Frontend is unaware of backend providers
- Filters are automatically mapped to provider formats
- Data is stored in normalized format
- Provider switching requires no frontend changes

## Two-Phase Data Collection

The API implements a two-phase approach:

**Phase 1 (Discovery):**
- Triggered by filter search
- Returns basic profile data
- Fast response times
- Lower API costs

**Phase 2 (Detailed Analytics):**
- Triggered on-demand when viewing specific profiles
- Returns comprehensive analytics data
- May involve external API calls
- Cached for performance