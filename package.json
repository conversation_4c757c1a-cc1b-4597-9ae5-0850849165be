{"name": "creator-verse", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@flaticon/flaticon-uicons": "^3.3.1", "@radix-ui/react-slot": "^1.2.3", "@reduxjs/toolkit": "^2.8.2", "@tailwindcss/vite": "^4.1.7", "alova": "^3.3.2", "antd": "^5.26.2", "antd-style": "^3.7.1", "authority": "0.0.1-beta-1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dayjs": "^1.11.13", "framer-motion": "^12.16.0", "highcharts": "^12.2.0", "highcharts-react-official": "^3.2.2", "i18next": "^25.2.0", "prop-types": "^15.8.1", "react": "^19.1.0", "react-autocomplete": "^1.8.1", "react-datepicker": "^8.4.0", "react-dom": "^19.1.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.60.0", "react-i18next": "^15.5.1", "react-icons": "^5.5.0", "react-redux": "^9.2.0", "react-router-dom": "^7.6.0", "react-slot": "^0.1.2", "react-speech-recognition": "^4.0.1", "react-spinners": "^0.17.0", "react-toastify": "^11.0.5", "tailwind-merge": "^3.3.0", "tailwindcss": "^4.1.7"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "tailwind-scrollbar": "^4.0.2", "vite": "^6.3.5", "vite-plugin-compression2": "^1.4.0"}}