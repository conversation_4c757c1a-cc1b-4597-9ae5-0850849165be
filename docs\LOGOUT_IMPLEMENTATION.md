# Logout Functionality Implementation

## Overview

This document describes the complete logout functionality implementation that follows the existing service architecture patterns and provides comprehensive state clearing and user redirection.

## Implementation Details

### 1. API Integration

**File**: `src/app/store/api/authApi.js`
- Updated logout endpoint to accept `session_id` parameter
- Endpoint: `POST http://************:8008/v1/auth/logout`
- Payload: `{ session_id: "string" }`

```javascript
// Updated API method
logout: (sessionData) => {
  return authInstance.Post('/auth/logout', sessionData);
}
```

```javascript
// Updated API method
logout: (sessionData) => {
  return authInstance.Post('/auth/logout', sessionData);
}
```

### 2. Enhanced Logout Thunk

**File**: `src/features/auth/service/authThunks.js`
- Enhanced `logoutThunk` to handle complete logout flow
- Calls logout API with session_id parameter
- Clears all localStorage data including:
  - `auth_token`
  - `refresh_token`
  - `user`
  - `allocatedBrands` / `organisationBrands`
  - `organizationId` / `organisationId`
  - `socialProfiles`
  - `session_id`
- Clears all sessionStorage data
- Dispatches clear actions for brand and influencer slices
- Handles both success and failure scenarios gracefully

```javascript
export const logoutThunk = createAsyncThunk(
  'auth/logout',
  async (_, { rejectWithValue, dispatch }) => {
    try {
      // Get session_id from localStorage
      const sessionId = localStorage.getItem('session_id') || 'default_session';

      // Call logout API with session_id
      await authApi.logout({ session_id: sessionId }).send();

      // Clear all localStorage data
      const localStorageKeys = [
        'auth_token', 'refresh_token', 'user',
        'allocatedBrands', 'organizationBrands', 'organisationBrands',
        'organizationId', 'organisationId', 'socialProfiles', 'session_id'
      ];
      localStorageKeys.forEach(key => localStorage.removeItem(key));

      // Clear all sessionStorage
      sessionStorage.clear();

      // Dispatch clear actions for other slices
      const { clearAllBrandState } = await import('../../brand/services/brandSlice');
      const { clearAllInfluencerState } = await import('../../influencer/services/influencerSlice');

      dispatch(clearAllBrandState());
      dispatch(clearAllInfluencerState());

      return { message: 'Logged out successfully', success: true };
    } catch (error) {
      // Even on API failure, clear local data for security
      // ... (error handling with local data clearing)
    }
  }
);
```

### 3. State Management Updates

#### Auth Slice (`src/features/auth/service/authSlice.js`)
- Added pending, fulfilled, and rejected cases for logout
- Resets all auth state on logout (even on failure for security)
- Proper error handling and status management

#### Brand Slice (`src/features/brand/services/brandSlice.js`)
- Added `clearAllBrandState` action
- Resets all brand-related state to initial values
- Clears all status and error states

#### Influencer Slice (`src/features/influencer/services/influencerSlice.js`)
- Added `clearAllInfluencerState` action
- Resets all influencer-related state to initial values
- Clears all status and error states

### 4. Selector Enhancements

**File**: `src/features/auth/service/authSelectors.js`
- Added loading state selectors (`isLoading`, `isIdle`, `isSucceeded`, `isFailed`)
- Provides UI feedback during logout process

### 5. UI Integration

**File**: `src/shared/layout/DashboardLayout.jsx`
- Integrated logout functionality with existing logout button
- Added loading state indication during logout
- Proper error handling and user feedback
- Smart redirection based on user type:
  - Brand users → `/brand/login`
  - Influencer users → `/influencer/login`

## Usage

### In Components

```javascript
import useAuthActions from '../../features/auth/service/authActions';
import useAuthSelectors from '../../features/auth/service/authSelectors';

const MyComponent = () => {
    const { logout } = useAuthActions();
    const { isLoading } = useAuthSelectors();

    const handleLogout = async () => {
        try {
            await logout();
            // Handle success
        } catch (error) {
            // Handle error
        }
    };

    return (
        <button 
            onClick={handleLogout} 
            disabled={isLoading}
        >
            {isLoading ? 'Logging out...' : 'Logout'}
        </button>
    );
};
```

### Logout Flow

1. User clicks logout button
2. `handleLogout` function is called
3. `logoutThunk` is dispatched
4. API call is made with session_id
5. All localStorage and sessionStorage data is cleared
6. Brand and influencer state is cleared via dispatched actions
7. Auth state is reset
8. User is redirected to appropriate login page
9. Loading states and error handling provide user feedback

## Security Features

- **Graceful Degradation**: Even if API call fails, local data is still cleared
- **Complete State Reset**: All application state is cleared on logout
- **Secure Redirection**: Users are always redirected to login after logout
- **Session Management**: Proper session_id handling for backend tracking

## Error Handling

- API failures don't prevent local data clearing
- Comprehensive error logging for debugging
- User-friendly error messages
- Fallback redirection ensures users can't remain in authenticated state

## Testing Considerations

To test the logout functionality:

1. **Successful Logout**: Verify API call, state clearing, and redirection
2. **Failed API Logout**: Ensure local data is still cleared and user redirected
3. **Loading States**: Verify UI shows loading indication during logout
4. **State Persistence**: Confirm no sensitive data remains after logout
5. **Cross-Slice Clearing**: Verify brand and influencer state is properly cleared

## Maintenance Notes

- The implementation follows existing service architecture patterns
- All new actions are properly exported in index files
- Error handling is consistent with existing thunks
- Loading states follow the same RequestStatus enum pattern
- The logout flow is extensible for future requirements

## Dependencies

- Redux Toolkit for state management
- React Router for navigation
- Existing service architecture patterns
- Multi-tenant backend API structure

This implementation provides a robust, secure, and user-friendly logout experience that maintains consistency with the existing codebase architecture.
