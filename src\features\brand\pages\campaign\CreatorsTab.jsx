import React, { useEffect, useState } from 'react';
import { useSnackbar } from '@shared/components/UI/SnackbarContext';
import { useParams } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { getCampaignCreatorsThunk } from '../../services/campaignThunks';
import { RequestStatus } from '../../../../app/store/enum';


import DataTable from '../../components/DataTable';
import RankIcon from '@shared/components/RankIcon';
import ContentCopy from "@assets/icon/content_copy.svg";
import AvatarStack from '@shared/components/AvatarStack';
import LoadingState from '../../components/LoadingState';
import ErrorState from '../../components/ErrorState';
import { Input } from "@shared/components/UI/input";
import Filter from '@brand/components/FilterFixed';
import { selectAllChannelsWithIcons } from '@/app/store/slices/systemSlice';


import VerifiedIcon from '@assets/icon/blue_verified.svg';
import SettingSliderIcon from '@assets/icon/settings-sliders.svg'
import SearchIcon from "@assets/icon/nav/search.svg";
import { TbLayoutGrid } from "react-icons/tb";
import { Table, Tooltip } from 'antd';
import { createStyles } from 'antd-style';
const useStyle = createStyles(({ css, token }) => {
    const { antCls } = token;

    return {
        customTable: css`
            .${antCls}-table-container .${antCls}-table-content,
            .${antCls}-table-container .${antCls}-table-body {
                scrollbar-width: thin;
                scrollbar-color: #eaeaea transparent;
                scrollbar-gutter: stable;
                overflow-x: auto;
            }

            .${antCls}-table-content::-webkit-scrollbar,
            .${antCls}-table-body::-webkit-scrollbar {
                height: 6px;
            }

            .${antCls}-table-content::-webkit-scrollbar-thumb,
            .${antCls}-table-body::-webkit-scrollbar-thumb {
                background: #eaeaea;
                border-radius: 3px;
            }
        `,
    };
});




const searchCreatorsData = [
    {
        id: 1,
        avatar: "https://images.unsplash.com/photo-1580489944761-15a19d654956?w=150&h=150&fit=crop&crop=faces",
        name: "Ananya Pandey",
        userName: "ananya_pandey",
        status: "shortlisted",
        paymentStatus: "paid",
        impactScore: 20,
        contentDueDate: "2024-07-15",
        followers: 12000,
        engagementRate: 3,
        addedBy: "John Doe",
        links: "https://www.ananypandey.com",
        deliverables: {
            instagram: { post: 1, reels: 1, story: 1 },
            youtube: { video: 1, shorts: 1 },
        },
    },
    {
        id: 2,
        avatar: "https://images.unsplash.com/photo-1502767089025-6572583495b9?w=150&h=150&fit=crop&crop=faces",
        name: "Rohit Sharma",
        userName: "rohit_s",
        status: "outreached",
        paymentStatus: "pending",
        impactScore: 35,
        contentDueDate: "2024-07-20",
        followers: 25000,
        engagementRate: 4.5,
        addedBy: "Alice Smith",
        links: "https://www.rohitsharma.com",
        deliverables: {
            instagram: { post: 2, reels: 1 },
            youtube: { video: 1 },
        },
    },
    {
        id: 3,
        avatar: "https://images.unsplash.com/photo-1517841905240-472988babdf9?w=150&h=150&fit=crop&crop=faces",
        name: "Megha Singh",
        userName: "megha.singh",
        status: "negotiation",
        paymentStatus: "paid",
        impactScore: 18,
        contentDueDate: "2024-07-22",
        followers: 9800,
        engagementRate: 2.8,
        addedBy: "David Liu",
        links: "https://www.meghasingh.in",
        deliverables: {
            instagram: { story: 2 },
        },
    },
    {
        id: 4,
        avatar: "https://images.unsplash.com/photo-1603415526960-f9e124d91445?w=150&h=150&fit=crop&crop=faces",
        name: "Arjun Mehta",
        userName: "arjun_mehta",
        status: "onboarded",
        paymentStatus: "paid",
        impactScore: 28,
        contentDueDate: "2024-07-18",
        followers: 15000,
        engagementRate: 3.6,
        addedBy: "Sana Khan",
        links: "https://www.arjunmehta.co",
        deliverables: {
            instagram: { post: 1 },
            youtube: { shorts: 2 },
        },
    },
    {
        id: 5,
        avatar: "https://images.unsplash.com/photo-1531891437562-5c9e1ed401c7?w=150&h=150&fit=crop&crop=faces",
        name: "Neha Dhawan",
        userName: "neha_dh",
        status: "in_progress",
        paymentStatus: "paid",
        impactScore: 22,
        contentDueDate: "2024-07-25",
        followers: 21000,
        engagementRate: 3.9,
        addedBy: "Michael Ray",
        links: "https://www.nehadhawan.org",
        deliverables: {
            instagram: { reels: 2, story: 1 },
        },
    },
    {
        id: 6,
        avatar: "https://images.unsplash.com/photo-1529626455594-4ff0802cfb7e?w=150&h=150&fit=crop&crop=faces",
        name: "Vikram Kapoor",
        userName: "vik_kapoor",
        status: "rejected",
        paymentStatus: "paid",
        impactScore: 30,
        contentDueDate: "2024-07-21",
        followers: 18000,
        engagementRate: 4.1,
        addedBy: "John Doe",
        links: "https://www.vikramkapoor.com",
        deliverables: {
            youtube: { video: 1, shorts: 1 },
        },
    },
    {
        id: 7,
        avatar: "https://images.unsplash.com/photo-1494790108377-be9c29b29330?w=150&h=150&fit=crop&crop=faces",
        name: "Priya Verma",
        userName: "priyav",
        status: "negotiation",
        paymentStatus: "pending",
        impactScore: 27,
        contentDueDate: "2024-07-28",
        followers: 30000,
        engagementRate: 5.2,
        addedBy: "Alice Smith",
        links: "https://www.priyaverma.in",
        deliverables: {
            instagram: { post: 1, reels: 1 },
            youtube: { video: 1 },
        },
    },
    {
        id: 8,
        avatar: "https://images.unsplash.com/photo-1517363898873-fb96f06bfa7b?w=150&h=150&fit=crop&crop=faces",
        name: "Kunal Joshi",
        userName: "kunal.j",
        status: "shortlisted",
        paymentStatus: "pending",
        impactScore: 24,
        contentDueDate: "2024-07-19",
        followers: 17000,
        engagementRate: 3.3,
        addedBy: "Sana Khan",
        links: "https://www.kunaljoshi.net",
        deliverables: {
            instagram: { story: 2 },
        },
    },
    {
        id: 9,
        avatar: "https://images.unsplash.com/photo-1552058544-f2b08422138a?w=150&h=150&fit=crop&crop=faces",
        name: "Ritika Sharma",
        userName: "ritika_s",
        status: "onboarded",
        paymentStatus: "paid",
        impactScore: 32,
        contentDueDate: "2024-07-17",
        followers: 26000,
        engagementRate: 4.7,
        addedBy: "David Liu",
        links: "https://www.ritikasharma.org",
        deliverables: {
            instagram: { reels: 2 },
            youtube: { shorts: 1 },
        },
    },
    {
        id: 10,
        avatar: "https://images.unsplash.com/photo-1552374196-c4e7ffc6e126?w=150&h=150&fit=crop&crop=faces",
        name: "Kabir Anand",
        userName: "kabir.anand",
        status: "completed",
        paymentStatus: "cancelled",
        impactScore: 79,
        contentDueDate: "2024-07-23",
        followers: 11000,
        engagementRate: 2.9,
        addedBy: "Michael Ray",
        links: "https://www.kabiranand.dev",
        deliverables: {
            instagram: { post: 1 },
            youtube: { video: 1 },
        },
    },
];


const CreatorsTab = () => {
    const { campaignId } = useParams();
    const { showSnackbar } = useSnackbar();
    const { styles } = useStyle(); // ✅ Access the styles here

    const channels = useSelector((state) => selectAllChannelsWithIcons(state));
    console.log('Channels:', channels);

    const dispatch = useDispatch();

    const creatorCampaignStatus = ["All", "Shortlisted", "Outreached", "Negotiation", "Onboarded", "In_Progress", "Rejected", "Completed"];


    const [sortConfig, setSortConfig] = useState({ key: '', direction: 'asc' });
    const [searchTerm, setSearchTerm] = useState("")
    const [savedFilters, setSavedFilters] = useState([])
    const [showFilter, setShowFilter] = useState(false);
    const [selectedStatus, setSelectedStatus] = useState("All");
    const [campaignCreators, setCampaignCreators] = useState(searchCreatorsData);



    const {
        selectedCampaign,
        // campaignCreators,
        detailsStatus,
        detailsError
    } = useSelector(state => state.campaign);

    useEffect(() => {
        async function fetchData() {
            if (campaignId) {
                const result = await dispatch(getCampaignCreatorsThunk(campaignId));
                if (result.meta.requestStatus === 'fulfilled') {
                    const responseData = result.payload.data;

                    setCampaignCreators(responseData);
                    console.log('Creator List:', responseData);
                } else {
                    showSnackbar(result.payload.message || 'Failed to load lists.', 'error');
                }
            }
        }
        if (selectedCampaign?.id != campaignId) {
            fetchData();
        }
    }, [dispatch, campaignId, selectedCampaign?.id, showSnackbar]);

    // Table columns configuration

    // const campaignCreatorColumns = [
    //     {
    //         key: 'name',
    //         header: 'Name',
    //         width: '120px',
    //         render: (row) => (
    //             <div className="flex items-center gap-3">
    //                 <img src={row.avatar} className="w-10 h-10 rounded-full" alt={row.name} />
    //                 <div className="flex flex-col">
    //                     <div className="flex items-center gap-1">
    //                         <span className="font-semibold text-white">{row.name}</span>
    //                         {row.verified && <img src={VerifiedIcon} className="h-3 w-3" alt="Verified" />}
    //                     </div>
    //                 </div>
    //             </div>
    //         )
    //     },
    //     {
    //         key: 'status',
    //         header: 'Status',
    //         width: '70px',
    //         render: (row) => (renderStatus(row.status))
    //     },
    //     {
    //         key: 'deliverables',
    //         header: 'Deliverables',
    //         width: '200px',
    //         render: (row) => {
    //             const deliverables = row.deliverables || {};

    //             const platformIcons = {
    //                 instagram: 'fi fi-brands-instagram text-gray-200',
    //                 youtube: 'fi fi-brands-youtube text-gray-200',
    //             };

    //             const formatDeliverableText = (items) => {
    //                 return Object.entries(items)
    //                     .map(([type, count]) => {
    //                         const label =
    //                             type.toLowerCase() === 'story'
    //                                 ? 'Stories'
    //                                 : type.toLowerCase() === 'reel'
    //                                     ? 'Reel'
    //                                     : `${type.charAt(0).toUpperCase() + type.slice(1)}`;
    //                         return `${count} ${label}`;
    //                     })
    //                     .join(', ');
    //             };

    //             if (Object.keys(deliverables).length === 0) {
    //                 return <span className="text-gray-400 text-sm">No deliverables</span>;
    //             }

    //             return (
    //                 <div className="flex flex-col justify-center gap-2">
    //                     {Object.entries(deliverables).map(([platform, items]) => (
    //                         <div
    //                             key={platform}
    //                             className="flex items-center gap-2 w-fit px-3 py-1 rounded-full border border-gray-500 bg-transparent text-gray-200 text-14-medium"
    //                         >
    //                             {/* <i className={`${platformIcons[platform]} text-base mt-1`} /> */}
    //                             {channels.find(channel => channel.name.toLowerCase() === platform)?.icon.url ? (
    //                                 <img src={channels.find(channel => channel.name.toLowerCase() === platform)?.icon.url} className="h-4 w-4" alt={platform} />
    //                             ) : (
    //                                 <i className={`${platformIcons[platform]} text-base mt-1`} />
    //                             )}
    //                             <span >{formatDeliverableText(items)}</span>
    //                         </div>
    //                     ))}
    //                 </div>

    //             );
    //         },
    //     },
    //     {
    //         key: 'impactScore',
    //         header: 'Impact Score',
    //         sortable: true,
    //         width: '60px',
    //         render: (row) => {
    //             // API response might not have creatorverseScore, use a default or calculate from available data
    //             const score = row.impactScore || Math.floor(Math.random() * 100); // Temporary fallback
    //             return renderCreatorverseScore(score);
    //         },
    //     },
    //     {
    //         key: 'audience',
    //         header: 'Audience',
    //         sortable: true,
    //         width: '50px',
    //         render: (row) => <span className="text-14-medium text-white">{row.followers ? formatNumber(row.followers) : 'N/A'}</span>,
    //     },
    //     {
    //         key: 'engagementRate',
    //         header: 'Eng. Rate',
    //         sortable: true,
    //         width: '80px',
    //         tooltip: 'Avg engagement (likes, comments, shares) divided by audience count',
    //         tooltipPosition: 'top',
    //         render: (row) => {
    //             const formattedRate = Number(row.engagementRate).toFixed(2); // Ensures 2 decimal places
    //             return <span className={`text-14-medium text-white`}>{formattedRate} %</span>;
    //         },
    //     },
    //     {
    //         key: 'contentDueDate',
    //         header: 'Content Due Date',
    //         sortable: true,
    //         width: '120px',
    //         render: (row) => {
    //             return <span className={`text-14-medium text-white`}>{formatDateToReadable(row.contentDueDate)}</span>;
    //         },
    //     },
    //     {
    //         key: 'paymentStatus',
    //         header: 'Payment Status',
    //         width: '80px',
    //         render: (row) => (renderPaymentStatus(row.paymentStatus))
    //     },
    //     {
    //         key: 'addedBy',
    //         header: 'Added By',
    //         width: '100px',
    //         sortable: true,
    //         render: (row) => (
    //             <div className='flex items-center gap-2'>
    //                 <span className="text-14-medium text-white">{row.addedBy}</span>
    //             </div>
    //         )
    //     },
    //     {
    //         key: 'links',
    //         header: 'Links',
    //         width: '100px',
    //         render: (row) => (
    //             <div className="flex items-center w-fit">
    //                 <span className="text-gray-50 w-30 line-clamp-2 truncate" title={row.links}>{row.links}</span>
    //                 <img
    //                     src={ContentCopy}
    //                     alt="Copy icon"
    //                     className="ml-3 cursor-pointer"
    //                     onClick={() => {
    //                         if (navigator.clipboard) {
    //                             navigator.clipboard.writeText(row.links);
    //                             showSnackbar('Link copied to clipboard!', 'info', 1000);
    //                         } else {
    //                             const textarea = document.createElement('textarea');
    //                             textarea.value = row.links;
    //                             document.body.appendChild(textarea);
    //                             textarea.select();
    //                             document.execCommand('copy');
    //                             document.body.removeChild(textarea);
    //                         }
    //                     }}
    //                 />
    //             </div>
    //         )
    //     }
    // ]

    const columns = [
        {
            title: 'Name',
            dataIndex: 'name',
            key: 'name',
            fixed: 'left',
            width: 180,
            render: (text, row) => (
                <div className="flex items-center gap-3">
                    <img src={row.avatar} className="w-10 h-10 rounded-full" alt={row.name} />
                    <div className="flex flex-col">
                        <div className="flex items-center gap-1">
                            <span className="font-semibold text-white">{row.name}</span>
                            {row.verified && <img src={VerifiedIcon} className="h-3 w-3" alt="Verified" />}
                        </div>
                    </div>
                </div>
            ),
        },
        {
            title: 'Status',
            dataIndex: 'status',
            key: 'status',
            width: 130,
            render: (status, row) => (renderStatus(row.status))
        },
        {
            title: 'Deliverables',
            dataIndex: 'deliverables',
            key: 'deliverables',
            width: 210,
            render: (deliverable, row) => {
                const deliverables = row.deliverables || {};

                const platformIcons = {
                    instagram: 'fi fi-brands-instagram text-gray-200',
                    youtube: 'fi fi-brands-youtube text-gray-200',
                };

                const formatDeliverableText = (items) => {
                    return Object.entries(items)
                        .map(([type, count]) => {
                            const label =
                                type.toLowerCase() === 'story'
                                    ? 'Stories'
                                    : type.toLowerCase() === 'reel'
                                        ? 'Reel'
                                        : `${type.charAt(0).toUpperCase() + type.slice(1)}`;
                            return `${count} ${label}`;
                        })
                        .join(', ');
                };

                if (Object.keys(deliverables).length === 0) {
                    return <span className="text-gray-400 text-sm">No deliverables</span>;
                }

                return (
                    <div className="flex flex-col justify-center gap-2">
                        {Object.entries(deliverables).map(([platform, items]) => (
                            <div
                                key={platform}
                                className="flex items-center gap-2 w-fit px-3 py-1 rounded-full border border-gray-500 bg-transparent text-gray-200 text-14-medium"
                            >
                                {/* <i className={`${platformIcons[platform]} text-base mt-1`} /> */}
                                {channels.find(channel => channel.name.toLowerCase() === platform)?.icon.url ? (
                                    <img src={channels.find(channel => channel.name.toLowerCase() === platform)?.icon.url} className="h-4 w-4" alt={platform} />
                                ) : (
                                    <i className={`${platformIcons[platform]} text-base mt-1`} />
                                )}
                                <span >{formatDeliverableText(items)}</span>
                            </div>
                        ))}
                    </div>

                );
            },
        },
        {
            title: (
                <Tooltip title="Creator's impact score based on engagement" color='var(--color-gray-800)'>
                    Impact Score
                </Tooltip>
            ),
            dataIndex: 'impactScore',
            key: 'impactScore',
            width: 150,
            render: (row) => {
                // API response might not have creatorverseScore, use a default or calculate from available data
                const score = row.impactScore || Math.floor(Math.random() * 100); // Temporary fallback
                return renderCreatorverseScore(score);
            },
        },
        {
            title: 'Audience',
            dataIndex: 'audience',
            key: 'audience',
            width: 140,
            render: (text, row) => <span className="text-14-medium text-white">{row.followers ? formatNumber(row.followers) : 'N/A'}</span>,
        },
        {
            title: (
                <Tooltip title="Avg engagement (likes, comments, shares) divided by audience count" color='var(--color-gray-800)'>
                    Eng. Score
                </Tooltip>
            ),
            dataIndex: 'engagementRate',
            key: 'engagementRate',
            width: 120,
            render: (engagement, row) => {
                const formattedRate = Number(row.engagementRate).toFixed(2); // Ensures 2 decimal places
                return <span className={`text-14-medium text-white`}>{formattedRate} %</span>;
            },
        },
        {
            title: 'Content Due Date',
            dataIndex: 'contentDueDate',
            key: 'contentDueDate',
            width: 180,
            render: (text, row) => {
                return <span className={`text-14-medium text-white`}>{formatDateToReadable(row.contentDueDate)}</span>;
            }
        },
        {
            title: 'Payment Status',
            dataIndex: 'paymentStatus',
            key: 'paymentStatus',
            width: 150,
            render: (text, row) => (renderPaymentStatus(row.paymentStatus))
        },
        {
            title: 'Added By',
            dataIndex: 'addedBy',
            key: 'addedBy',
            width: 150,
            render: (text, row) => (
                <div className="flex items-center gap-2">
                    <span className="text-14-medium text-white">{row.addedBy}</span>
                </div>
            )
        },
        {
            title: 'Links',
            dataIndex: 'links',
            key: 'links',
            width: 150,
            render: (links, row) => (
                <div className="flex items-center w-fit">
                    <span className="text-gray-50 w-30 line-clamp-2 truncate" title={row.links}>{row.links}</span>
                    <img
                        src={ContentCopy}
                        alt="Copy icon"
                        className="ml-3 cursor-pointer"
                        onClick={() => {
                            if (navigator.clipboard) {
                                navigator.clipboard.writeText(row.links);
                                showSnackbar('Link copied to clipboard!', 'info', 1000);
                            } else {
                                const textarea = document.createElement('textarea');
                                textarea.value = row.links;
                                document.body.appendChild(textarea);
                                textarea.select();
                                document.execCommand('copy');
                                document.body.removeChild(textarea);
                            }
                        }}
                    />
                </div>
            )
        }
    ];

    // Filter options array - keeping the existing array as is
    const filterOptions = [
        {
            optionName: "Status",
            optionFor: "creator",
            channel: "instagram",
            filters: [
                {
                    name: "Status",
                    type: "checkbox",
                    icon: "gender-icon",
                    minmax: false,
                    enterValue: false,
                    placeholder: "Select Status", //only needed for enterValue
                    options: [
                        { label: "Shortlisted", value: "shortlisted", description: "" },
                        { label: "In Progress", value: "in_progress", description: "" },
                        { label: "Negotiation", value: "negotiation", description: "" },
                        { label: "Outreached", value: "outreached", description: "" },
                        { label: "Onboarded", value: "onboarded", description: "" },
                        { label: "Rejected", value: "rejected", description: "" },
                    ]
                },
            ]
        },
        {
            optionName: "Channels",
            optionFor: "creator",
            channel: "instagram",
            filters: [
                {
                    name: "channel",
                    type: "checkbox",
                    minmax: false,
                    icon: "follower-icon",
                    enterValue: false,
                    placeholder: "Select Channel", //only needed for enterValue
                    options: [
                        { label: "Instagram", value: "instagram", description: "Instagram" },
                        { label: "YouTube", value: "youtube", description: "YouTube" },
                    ]
                }
            ]
        },
        {
            optionName: "Audience",
            optionFor: "creator",
            channel: "instagram",
            filters: [
                {
                    name: "audience",
                    type: "checkbox",
                    minmax: true,
                    icon: "age-icon",
                    enterValue: false,
                    placeholder: "Select Age", //only needed for enterValue
                    options: [
                        { label: "Nano", value: "1000-10000", description: "1k-10k" },
                        { label: "Micro", value: "10001-50000", description: "10k-50k" },
                        { label: "Mid", value: "50001-500000", description: "50k-500k" },
                        { label: "Macro", value: "500001-1000000", description: "500k-1M" },
                        { label: "Mega", value: "1000001+", description: "1M+" }
                    ]
                },
            ]
        },
        {
            optionName: "Engagement Rate",
            optionFor: "creator",
            channel: "instagram",
            filters: [
                {
                    name: "Engagement Rate",
                    type: "checkbox",
                    minmax: false,
                    icon: "engagement-icon",
                    enterValue: false,
                    placeholder: "Select Engagement Rate", //only needed for enterValue
                    options: [
                        { label: "Very High", value: "10%+", description: "10%+" },
                        { label: "High", value: "5%-10%", description: "5%-10%" },
                        { label: "Medium", value: "2%-5%", description: "2%-5%" },
                        { label: "Low", value: "<2%", description: "<2%" },
                    ]
                },
            ]
        },
        {
            optionName: "Campaigns",
            optionFor: "creator",
            channel: "instagram",
            filters: [
                {
                    name: "Campaigns",
                    type: "checkbox",
                    searchBox: true,
                    minmax: false,
                    icon: "engagement-icon",
                    enterValue: false,
                    placeholder: "Select Campaigns",
                    options: [
                        { label: "Summer Surge", value: "Summer Surge", description: "" },
                        { label: "Engagement Explosion", value: "Engagement Explosion", description: "" },
                        { label: "Precision Targeting", value: "Precision Targeting", description: "" },
                        { label: "Brand Spotlight", value: "Brand Spotlight", description: "" },
                    ]
                },
            ]
        },
    ];

    const formatNumber = (num) => {
        if (num >= 1_000_000) {
            return (num / 1_000_000).toFixed(num % 1_000_000 === 0 ? 0 : 1) + 'M';
        } else if (num >= 1_000) {
            return (num / 1_000).toFixed(num % 1_000 === 0 ? 0 : 1) + 'K';
        }
        return num.toString();
    };

    const handleAddCreator = () => {
        console.log('Add creator to campaign');
        // TODO: Implement add creator functionality
    };

    if (detailsStatus === RequestStatus.LOADING) {
        return <LoadingState message="Loading campaign creators..." />;
    }

    if (detailsStatus === RequestStatus.FAILED) {
        return <ErrorState message={detailsError} />;
    }

    const renderCreatorverseScore = (score) => {
        const getColor = () => {
            if (score >= 80) return 'text-green-2';
            if (score >= 50) return 'text-sky-blue';
            if (score >= 20) return 'text-orange';
            if (score >= 0) return 'text-red-2';
            return 'bg-red-2';
        };
        return (
            <div className={`flex items-center justify-center rounded-md py-1 w-9 gap-1 ${getColor()}`}>
                <span className="text-14-semibold">{score}</span>
                <RankIcon type="bolt" percentage={score} fillDirection="bt" fillColor="var(--color-yellow)" />

                {/* <img src={getIcon()} alt="" /> */}
            </div>
        );
    };

    const renderPaymentStatus = (paymentStatus) => {
        const getColor = () => {
            if (paymentStatus === "pending") return 'bg-orange-1 text-white border-orange';
            if (paymentStatus === "paid") return 'bg-green-1 text-white border-green-2';
            if (paymentStatus === "cancelled") return 'bg-red-1 text-white border-red-2';
            if (paymentStatus === "") return 'bg-gray-400 text-white';
        };

        const getIconColor = () => {
            if (paymentStatus === "pending") return 'text-orange fi-rr-pending text-md ';
            if (paymentStatus === "paid") return 'text-green-2 fi-br-check text-sm ';
            if (paymentStatus === "cancelled") return 'text-red-2 fi-sr-cross-small text-md ';
            if (paymentStatus === "") return 'text-gray-400 text-white';
        };



        const formatPaymentStatusLabel = (paymentStatus) => {
            return paymentStatus
                .replace(/_/g, ' ')
                .replace(/\w\S*/g, (txt) => txt.charAt(0).toUpperCase() + txt.slice(1).toLowerCase());
        };

        return (
            <div className={`flex items-center gap-2 rounded-full px-3 py-0.5 w-fit border-1 ${getColor()}`}>
                <i className={`fi mt-1 ${getIconColor()}`}></i>
                <span className="text-14-regular">{formatPaymentStatusLabel(paymentStatus)}</span>
            </div>
        );
    };

    const renderStatus = (status) => {
        const getColor = () => {
            if (status === "shortlisted") return 'bg-teal text-white';
            if (status === "in_progress") return 'bg-yellow text-primary';
            if (status === "negotiation") return 'bg-orange text-white';
            if (status === "outreached") return 'bg-gray-400 text-white';
            if (status === "onboarded") return 'bg-sky-blue text-primary';
            if (status === "rejected") return 'bg-red-2 text-white';
            if (status === "no_status") return 'bg-transparent text-white';
            if (status === "completed") return 'bg-green-2 text-white';
            if (status === "") return 'bg-gray-400 text-white';
        };

        const formatStatusLabel = (status) => {
            return status
                .replace(/_/g, ' ')
                .replace(/\w\S*/g, (txt) => txt.charAt(0).toUpperCase() + txt.slice(1).toLowerCase());
        };

        return (
            <span className={`flex items-center gap-2 rounded-full text-14-medium py-1 px-3 w-fit ${getColor()}`}>{formatStatusLabel(status)}</span>
        );
    };

    function formatDateToReadable(dateString) {
        if (!dateString) return '';

        const date = new Date(dateString);

        if (isNaN(date)) return ''; // handle invalid date

        return date.toLocaleDateString('en-GB', {
            day: '2-digit',
            month: 'long',
            year: 'numeric',
        });
    }

    // Enhanced sort logic
    const getSortedData = (data, sortConfig) => {
        if (!sortConfig?.key || !sortConfig?.key.trim() || !data || data.length === 0) return data;

        console.log("Sorting by:", sortConfig.key, "Direction:", sortConfig.direction);

        return [...data].sort((a, b) => {
            let aVal = a[sortConfig.key];
            let bVal = b[sortConfig.key];

            // Handle undefined or null values
            if (aVal === undefined || aVal === null) return sortConfig.direction === 'asc' ? -1 : 1;
            if (bVal === undefined || bVal === null) return sortConfig.direction === 'asc' ? 1 : -1;

            // Special handling for specific fields
            if (sortConfig.key === 'followers' || sortConfig.key === 'audience') {
                // Convert from formatted strings (e.g., "120K") to numbers if needed
                aVal = typeof aVal === 'string' ? parseFloat(aVal.replace(/[^0-9.]/g, '')) : aVal;
                bVal = typeof bVal === 'string' ? parseFloat(bVal.replace(/[^0-9.]/g, '')) : bVal;
            } else if (sortConfig.key === 'engagementRate') {
                // Convert from percentage strings if needed
                aVal = typeof aVal === 'string' ? parseFloat(aVal.replace('%', '')) : aVal;
                bVal = typeof bVal === 'string' ? parseFloat(bVal.replace('%', '')) : bVal;
            }

            // String comparison
            if (typeof aVal === 'string') {
                return sortConfig.direction === 'asc'
                    ? aVal.localeCompare(bVal)
                    : bVal.localeCompare(aVal);
            }

            // Number comparison
            return sortConfig.direction === 'asc' ? aVal - bVal : bVal - aVal;
        });
    };

    // Filter data based on search term
    const getFilteredData = (data, searchTerm, selectedStatus) => {
        let filtered = [...data];

        // Filter by status
        if (selectedStatus && selectedStatus !== "All") {
            filtered = filtered.filter(item =>
                item.status.toLowerCase() === selectedStatus.toLowerCase()
            );
        }

        // No search term, return filtered list
        if (!searchTerm || !searchTerm.trim()) return filtered;

        const normalizedSearchTerm = searchTerm.trim().toLowerCase();

        return filtered.filter(item => {
            const isCreatorList = 'listName' in item;

            if (isCreatorList) {
                return (
                    item.listName?.toLowerCase().startsWith(normalizedSearchTerm) ||
                    item.createdBy?.toLowerCase().startsWith(normalizedSearchTerm) ||
                    item.createdOn?.toLowerCase().startsWith(normalizedSearchTerm) ||
                    item.lastUpdated?.toLowerCase().startsWith(normalizedSearchTerm)
                );
            } else {
                return (
                    item.name?.toLowerCase().startsWith(normalizedSearchTerm) ||
                    item.status?.toLowerCase().startsWith(normalizedSearchTerm) ||
                    item.campaign?.toLowerCase().startsWith(normalizedSearchTerm) ||
                    item.notes?.toLowerCase().startsWith(normalizedSearchTerm) ||
                    (item.labels && Array.isArray(item.labels) &&
                        item.labels.some(label => label.toLowerCase().startsWith(normalizedSearchTerm))) ||
                    (typeof item.channels === 'string' && item.channels.toLowerCase().startsWith(normalizedSearchTerm)) ||
                    (Array.isArray(item.channels) &&
                        (
                            (normalizedSearchTerm === 'instagram' && item.channels.length > 0) ||
                            (normalizedSearchTerm === 'youtube' && item.channels.length > 1)
                        )
                    )
                );
            }
        });
    };

    return (
        <div className="h-full w-full flex flex-col gap-5 ">
            {/* Header */}
            {/* <div className="hidden items-center justify-between">
                <div className='flex flex-col gap-2'>
                    <h2 className="text-20-semibold text-gray-50">Campaign Creators</h2>
                    <p className="text-14-regular text-gray-300">
                        Manage creators assigned to this campaign
                    </p>
                </div>
                <button
                    onClick={handleAddCreator}
                    className="px-4 py-2 bg-brand-500 text-white rounded hover:bg-brand-600 transition"
                >
                    + Add Creator
                </button>
            </div> */}
            <div className='w-full flex justify-between items-center gap-5'>
                <div className="bg-gray-600 rounded-xl p-5 flex flex-col justify-between w-full shadow-sm min-w-[260px]">
                    {/* Icon and Label */}
                    <div className="flex gap-2 items-center">
                        <div className="bg-brand-200 bg-opacity-10 rounded-lg h-7.5 w-7.5 flex items-center justify-center pt-0.75 shadow-brand-200">
                            <i className="fi fi-rs-diamond text-brand-500"></i>
                        </div>
                        <span className="text-16-medium text-gray-50 text-left">Creators Onboarded / Required</span>
                    </div>

                    {/* Number */}
                    <div className="text-right ml-4">
                        <span className="text-28-semibold text-sky-blue mr-0.5">12</span>
                        <span className="text-22-semibold text-gray-300 opacity-40">/15</span>
                    </div>
                </div>
                <div className="bg-gray-600 rounded-xl p-5 flex flex-col justify-between w-full shadow-sm min-w-[260px]">
                    {/* Icon and Label */}
                    <div className="flex gap-2 items-center">
                        <div className="bg-brand-200 bg-opacity-10 rounded-lg h-7.5 w-7.5 flex items-center justify-center pt-0.75 shadow-brand-200">
                            <i className="fi fi-rs-diamond text-brand-500"></i>
                        </div>
                        <span className="text-16-medium text-gray-50 text-left">Content Posted / Total Content</span>
                    </div>

                    {/* Number */}
                    <div className="text-right ml-4">
                        <span className="text-28-semibold text-sky-blue mr-0.5">12</span>
                        <span className="text-22-semibold text-gray-300 opacity-40">/15</span>
                    </div>
                </div>
                <div className="bg-gray-600 rounded-xl p-5 flex flex-col justify-between w-full shadow-sm min-w-[260px]">
                    {/* Icon and Label */}
                    <div className="flex gap-2 items-center">
                        <div className="bg-brand-200 bg-opacity-10 rounded-lg h-7.5 w-7.5 flex items-center justify-center pt-0.75 shadow-brand-200">
                            <i className="fi fi-rs-diamond text-brand-500"></i>
                        </div>
                        <span className="text-16-medium text-gray-50 text-left">Spend / Budget</span>
                    </div>

                    {/* Number */}
                    <div className="text-right ml-4">
                        <span className="text-28-semibold text-sky-blue mr-0.5">12</span>
                        <span className="text-22-semibold text-gray-300 opacity-40">/15</span>
                    </div>
                </div>
            </div>
            <div className='flex items-center gap-4'>
                {/* Search Box */}
                <div className="flex items-center space-x-4 ml-0.5">
                    <Input
                        type="text"
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        icon={SearchIcon}
                        placeholder="Search by name, channel....."
                        className="px-3 py-1 h-10 w-100 bg-transparent text-sm placeholder-gray-400 focus:outline-none"
                    />
                </div>
                {/* Filter Buttons */}
                <button
                    className={`relative px-4 py-2.5 bg-transparent border border-gray-500 rounded-lg flex items-center gap-1.5 text-sm text-gray-50 hover:text-gray-300 transition-colors cursor-pointer`}
                    onClick={() => {
                        setShowFilter(!showFilter);
                        console.log('Filter button clicked');
                    }}
                    title="Toggle Filters"
                >
                    <img src={SettingSliderIcon} alt="Filter" className="w-3.5 h-3.5" />
                    Filters
                    {savedFilters.length > 0 && (
                        <span className="ml-2 bg-brand-200 text-brand-600 text-xs rounded-full w-5 h-5 flex items-center justify-center">
                            {savedFilters.length}
                        </span>
                    )}

                    {showFilter ? <Filter
                        filterOptions={filterOptions}
                        hidetab={true}
                        savedFilters={savedFilters}
                        onClose={() => setShowFilter(false)}
                        onApplyFilters={(filters) => {
                            setSavedFilters(filters);
                            console.log(filters);
                            // Process your filters here
                        }}
                    /> : null}

                </button>
            </div>
            <div className='flex items-center gap-4'>
                {
                    creatorCampaignStatus.map((status, index) => (
                        <button
                            key={index}
                            className={`px-4 py-2.5 border border-gray-400 rounded-full flex items-center gap-1.5 text-14-medium  hover:text-gray-100 transition-colors cursor-pointer ${status === selectedStatus ? "bg-gray-500 text-white" : ""}`}
                            onClick={() => setSelectedStatus(status)}
                            title={status}
                        >
                            {status === "All" ? <TbLayoutGrid /> : null}
                            {status}
                        </button>
                    ))
                }
            </div>

            {/* Creators Table */}
            <div className="flex-1 h-full">
                {campaignCreators && campaignCreators.length > 0 ? (
                    //Creator Table
                    // <DataTable
                    //     data={campaignCreators}
                    //     columns={campaignCreatorColumns}
                    //     sortConfig={sortConfig}
                    //     onSort={handleSort}
                    //     selectable={true}
                    //     enableHorizontalScroll={campaignCreatorColumns.length > 7}
                    // />
                    <div className="w-full border-none rounded-2xl overflow-hidden pr-1">
                        <Table
                            columns={columns}
                            dataSource={getSortedData(getFilteredData(campaignCreators, searchTerm, selectedStatus), sortConfig)}
                            scroll={{ x: 1200 }}
                            pagination={false}
                            bordered
                        />
                    </div>

                ) : (
                    <div className="flex flex-col items-center justify-center h-64 bg-gray-600 rounded-lg">
                        <div className="text-16-medium text-gray-300 mb-2">No creators assigned</div>
                        <div className="text-14-regular text-gray-400 mb-4">
                            Start by adding creators to this campaign
                        </div>
                        <button
                            onClick={handleAddCreator}
                            className="px-4 py-2 bg-brand-500 text-white rounded hover:bg-brand-600 transition"
                        >
                            Add First Creator
                        </button>
                    </div>
                )}
            </div>
        </div>
    );
};

export default CreatorsTab;
