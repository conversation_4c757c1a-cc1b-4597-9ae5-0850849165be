import React from 'react';
import {
    TbLayoutGrid,        // All
    TbCheck,             // Approved
    TbFileUpload,        // Submitted
    TbX,                 // Rejected
    TbClock,             // Scheduled
    TbSend,              // Posted
    TbFileText           // Draft
} from "react-icons/tb"; // or from other icon sets if preferred
import { FaInstagram } from "react-icons/fa";


const statusColors = {
    Approved: "bg-green-100 text-green-700",
    Submitted: "bg-purple-100 text-purple-700",
    Scheduled: "bg-orange-100 text-orange-700",
    Posted: "bg-blue-100 text-blue-700",
    Reject: "bg-red-100 text-red-700",
    Draft: "bg-gray-100 text-gray-700",
};

const statusIcons = {
    Approved: <TbCheck />,
    Submitted: <TbFileUpload />,
    Scheduled: <TbClock />,
    Posted: <TbSend />,
    Reject: <TbX />,
    Draft: <TbFileText />,
};

const ContentCard = ({ card }) => {
    return (
        <div className="rounded-xl border border-gray-400 p-1 bg-gray-900 text-white flex flex-col">
            <div className={`w-full px-2 py-1 rounded-tl-lg rounded-tr-lg text-14-medium flex justify-center items-center gap-1 ${statusColors[card.status]}`}>
                {statusIcons[card.status]}

                {card.status}
            </div>
            <div className="flex flex-col p-5 gap-3.5">
                <p className="text-12-regular text-gray-200">Published on
                    <span className='text-gray-100'>
                        {card.date}
                    </span>
                </p>
                <div className="flex items-center gap-3">
                    <img src={card.brandAvatar} className="w-10 h-10 rounded-full" alt={card.brandAvatar} />
                    <div className="flex flex-col">
                        <div className="flex flex-col items-start ">
                            <span className="text-16-medium text-white">{card.brandName}</span>
                            <p className='text-12-regular text-gray-200'>{card.campaignName}</p>
                        </div>
                    </div>
                </div>
                <div className="h-[100px] w-full bg-gray-700 rounded-md mb-3"></div>

                <p className="text-14-regular text-gray-50 mb-2">{card.campaignDesctiption}</p>
                <div className='flex gap-2'>
                    <div className="text-gray-300 rounded-lg px-3 py-1 flex gap-1 h-6 text-14-regular border-1 border-gray-400">
                        <FaInstagram />
                        <span>{card.type}</span>
                    </div>
                </div>
                <div className='flex flex-col gap-2 border-b-1 pb-2 border-gray-500 h-12'>
                    <span className='text-14-regular text-gray-300'>Remark</span>
                    <span className='text-14-regular text-gray-50'> {card.remarks}</span>
                </div>
                <div className="flex justify-between items-center mt-4 gap-2">
                    {card.showEdit && (
                        <button className=" w-full px-4 py-2 bg-transparent border border-gray-500 rounded-lg text-gray-100 hover:bg-gray-700">
                            Edit
                        </button>
                    )}
                    {card.showAction && (
                        <button className="w-full px-4 py-2 bg-brand-500 rounded-lg text-white hover:bg-brand-600 transition">
                            {card.buttonText}
                        </button>
                    )}
                </div>
                <div className='flex justify-end'>
                    <p className='text-12-regular text-gray-200'>Organisation Name: {card.organisationName}</p>
                </div>
            </div>

        </div>
    );
};

export default ContentCard;
