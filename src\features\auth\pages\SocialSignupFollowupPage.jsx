import React, { useState } from "react";
import { useParams } from "react-router-dom";
import { Link } from "react-router-dom";
import SocialButton from "@shared/components/UI/button";
import { useNavigate } from "react-router-dom";
import instagramIcon from "@assets/icon/instagram-icon.png";
import youtubeIcon from "@assets/icon/youtube-icon.svg";
import ArrowLeft from "@assets/icon/arrow-left.svg";
import Bulb from "@assets/icon/lightbulb.svg";
import WarningMessage from "@shared/components/DialogBox";
import SocialSignupLayout from "@shared/layout/SocialSignupLayout";
import { IoChevronBack } from "react-icons/io5";



const SocialSignupFollowupPage = () => {
    const navigate = useNavigate();
    const { platform } = useParams(); // ✅ get 'platform' from route

    const [showWarning, setShowWarning] = useState(false);


    const isInstagramLinked = platform === "instagram";
    const nextPlatform = isInstagramLinked ? "YouTube" : "Instagram";
    const nextChannel = isInstagramLinked ? youtubeIcon : instagramIcon;
    const currentChannel = isInstagramLinked ? instagramIcon : youtubeIcon;

    const handleBack = () => {
        navigate(-1);
    };

    const handleSocialButtonClick = () => {
        // Show the warning dialog
        setShowWarning(true);
    };

    return (
        <SocialSignupLayout>
            <div className="flex flex-col items-center justify-center bg-primary text-white px-4 gap-10">
                <div className="max-w-md w-full text-left space-y-8 relative">

                    {/* Back Button */}
                    <button
                        onClick={handleBack}
                        className="flex justify-center items-center bg-gray-500 rounded-full w-10 h-10 text-gray-300 hover:text-gray-200 hover:bg-gray-400 transition-colors cursor-pointer"
                    >
                        <IoChevronBack />
                    </button>

                    <div className="flex flex-col text-left justify-center space-y-6 w-full">
                        {/* Status */}
                        <div className="text-18-regular  text-gray-200 mt-6 h-5 flex items-center">
                            You're good to go with{" "}
                            <span className="text-gray-200 inline-flex items-center ml-2">
                                <img src={currentChannel} alt={nextPlatform} className="w-5 h-5 mr-2 " />
                                {isInstagramLinked ? "Instagram" : "YouTube"}
                            </span>
                            !
                        </div>

                        {/* Main heading */}
                        <div className="text-30-semibold  text-gray-50 w-full">
                            One down, one to go! <br />
                            Want to link{" "}
                            <span className={isInstagramLinked ? "text-[#FA4B60]" : "text-[#A8EAFE]"}>
                                {nextPlatform}
                            </span>{" "}
                            too?
                        </div>

                        {/* Supporting text */}
                        <p className="text-sm text-gray-400 text-16-regular mr-5">
                            Creators with linked socials are <span className="text-20-medium text-gray-100">3x</span> more likely to get brand deals.
                        </p>

                        {/* Buttons */}
                        <div className="space-y-4">
                            <div className="flex justify-center gap-4">
                                <button
                                    onClick={() => navigate('/profile-onboarding')}
                                    className="border border-gray-600 rounded-lg px-6 py-2 text-sm hover:bg-gray-800"
                                >
                                    Later
                                </button>

                                <SocialButton
                                    icon={<img src={nextChannel} alt={nextPlatform} className="w-5 h-5" />}
                                    label={nextPlatform}
                                    variant="default"
                                    className={`w-full px-6 text-16-semibold hover:bg-white text-gray-900 ${nextPlatform === "Instagram" ? "bg-[#f3efff]" : "bg-light-6"}`}
                                    onClick={handleSocialButtonClick}

                                />
                            </div>

                            <p className="text-xs text-right text-gray-50 -mt-1 text-14-semibold">
                                You're <span className="text-[#E8FD95] ">50% closer</span>  to unlocking brand offers 🚀
                            </p>
                        </div>
                    </div>

                    {/* Highlight box */}
                    <div className="flex flex-row justify-center w-full h-[85px] gap-3 py-4 px-5 bg-blue-100 text-blue-900 border-2 rounded-lg mt-20">
                        <div className=" h-[25px] flex items-start justify-end -mr-2">
                            <img src={Bulb} alt="" />
                        </div>
                        <div className="text-left flex flex-col gap-1">
                            <div className="text-14-semibold block">Double The Spotlight, Double The Opportunities!</div>
                            <div className="text-12-regular ">Linking both your <span className="text-12-semibold text-[#696EFF]">Instagram and Youtube</span> boosts your chances of landing a brand deal.</div>
                        </div>
                    </div>

                </div>
                {/* Conditional WarningMessage */}
                {showWarning && (
                    <WarningMessage
                        dialogType="warning"
                        onTryAnother={() => {
                            setShowWarning(false);
                            // Trigger logic to try again (could redirect or re-open OAuth)
                        }}
                        onLater={() => {
                            setShowWarning(false);
                        }}
                    />
                )}
            </div>
        </SocialSignupLayout>
    );
};

export default SocialSignupFollowupPage;
