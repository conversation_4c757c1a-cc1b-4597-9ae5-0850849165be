import envConfig from './config/environmentConfig';
import authApi from './authApi';
import brandManagementApi from './brandManagementApi';
import discoveryApi from './discoveryApi';
import savedFiltersApi from './savedFiltersApi';
import analyticsApi from './analyticsApi';
import authService, { AUTH_EVENTS } from './services/authService';
import errorHandler, { ERROR_TYPES } from './services/errorHandler';
import healthMonitor, { HEALTH_STATUS } from './services/healthMonitor';
import apiConfig, { SERVICE_TYPES, ENVIRONMENTS } from './config/apiConfig';
/**
 * Multi-Backend API Index
 * 
 * Central export point for all API services, configurations, and utilities.
 * This file provides a clean interface for consuming the multi-backend architecture
 * throughout the application.
 */

// === CORE INSTANCES ===
export { default as authInstance } from './instances/authInstance';
export { default as discoveryInstance } from './instances/discoveryInstance';
export { default as campaignInstance } from './instances/campaignInstance';

// === API SERVICES ===
export { default as authApi } from './authApi';
export { default as brandManagementApi } from './brandManagementApi';
export { default as discoveryApi } from './discoveryApi';
export { default as analyticsApi } from './analyticsApi';
export { default as savedFiltersApi } from './savedFiltersApi';
export { default as campaignManagementApi } from './campaignManagementApi';

// === CONFIGURATION ===
export { default as apiConfig, SERVICE_TYPES, ENVIRONMENTS } from './config/apiConfig';
export { default as envConfig } from './config/environmentConfig';

// === SERVICES ===
export { default as authService, AUTH_EVENTS, TOKEN_KEYS } from './services/authService';
export { default as errorHandler, ERROR_TYPES, CIRCUIT_STATES } from './services/errorHandler';
export { default as healthMonitor, HEALTH_STATUS, METRICS } from './services/healthMonitor';

// === LEGACY SUPPORT ===
// Keep the old alovaInstance for backward compatibility during migration
export { default as alovaInstance } from './alovaInstance';

/**
 * API Service Factory
 * Provides a convenient way to access all API services
 */
export const apiServices = {
  // Authentication services
  auth: authApi,
  brandManagement: brandManagementApi,
  
  // Discovery and analytics services
  discovery: discoveryApi,
  analytics: analyticsApi,
  savedFilters: savedFiltersApi,
};

/**
 * Service Management Utilities
 */
export const serviceUtils = {
  // Authentication utilities
  auth: authService,
  
  // Error handling
  errorHandler,
  
  // Health monitoring
  healthMonitor,
  
  // Configuration
  config: envConfig,
};

/**
 * Multi-Backend API Manager
 * High-level interface for managing the multi-backend architecture
 */
export class MultiBackendApiManager {
  constructor() {
    this.isInitialized = false;
    this.services = apiServices;
    this.utils = serviceUtils;
  }

  /**
   * Initialize the multi-backend API system
   * @param {Object} options - Initialization options
   */
  async initialize(options = {}) {
    if (this.isInitialized) {
      console.warn('[API MANAGER] Already initialized');
      return;
    }

    try {
      console.log('[API MANAGER] Initializing multi-backend API system...');

      // Validate configuration
      const configValidation = envConfig.validateConfig();
      if (!configValidation.isValid) {
        throw new Error(`Configuration validation failed: ${configValidation.errors.join(', ')}`);
      }

      // Start health monitoring if enabled
      if (envConfig.isFeatureEnabled('enableHealthMonitoring')) {
        healthMonitor.startMonitoring();
      }

      // Perform initial health checks
      await this.performInitialHealthChecks();

      // Set up error handling
      this.setupErrorHandling();

      // Set up authentication event listeners
      this.setupAuthEventListeners();

      this.isInitialized = true;
      console.log('[API MANAGER] Multi-backend API system initialized successfully');

      return {
        success: true,
        services: Object.keys(this.services),
        environment: envConfig.getEnvironment(),
        features: envConfig.get('features', {})
      };
    } catch (error) {
      console.error('[API MANAGER] Initialization failed:', error);
      throw error;
    }
  }

  /**
   * Perform initial health checks
   */
  async performInitialHealthChecks() {
    console.log('[API MANAGER] Performing initial health checks...');
    
    try {
      await healthMonitor.checkAllServices();
      const overallHealth = healthMonitor.getOverallHealth();
      
      if (overallHealth.status === HEALTH_STATUS.UNHEALTHY) {
        console.warn('[API MANAGER] Some services are unhealthy:', overallHealth.services);
      }
      
      return overallHealth;
    } catch (error) {
      console.error('[API MANAGER] Health check failed:', error);
      // Don't throw - allow app to continue with degraded functionality
    }
  }

  /**
   * Set up global error handling
   */
  setupErrorHandling() {
    // Set up global error handlers for unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      console.error('[API MANAGER] Unhandled promise rejection:', event.reason);
      
      // Check if it's an API error
      if (event.reason && event.reason.service) {
        errorHandler.handleError(event.reason, event.reason.context);
      }
    });
  }

  /**
   * Set up authentication event listeners
   */
  setupAuthEventListeners() {
    // Listen for token refresh events
    authService.on(AUTH_EVENTS.TOKEN_REFRESHED, (data) => {
      console.log('[API MANAGER] Token refreshed successfully');
    });

    // Listen for authentication errors
    authService.on(AUTH_EVENTS.TOKEN_EXPIRED, (data) => {
      console.warn('[API MANAGER] Token expired:', data);
    });

    // Listen for logout events
    authService.on(AUTH_EVENTS.LOGOUT, () => {
      console.log('[API MANAGER] User logged out');
      // Reset any cached data or state
    });
  }

  /**
   * Get system status
   * @returns {Object} System status information
   */
  getSystemStatus() {
    return {
      initialized: this.isInitialized,
      environment: envConfig.getEnvironment(),
      health: healthMonitor.getOverallHealth(),
      metrics: healthMonitor.getAllServicesMetrics(),
      circuitBreakers: errorHandler.getServiceHealth(),
      authentication: authService.getAuthStatus(),
      configuration: envConfig.getConfigSummary()
    };
  }

  /**
   * Shutdown the API system
   */
  shutdown() {
    console.log('[API MANAGER] Shutting down multi-backend API system...');
    
    // Stop health monitoring
    healthMonitor.stopMonitoring();
    
    // Clear any intervals or timeouts
    // Reset state
    this.isInitialized = false;
    
    console.log('[API MANAGER] Shutdown complete');
  }

  /**
   * Get service by name
   * @param {string} serviceName - Service name
   * @returns {Object} Service API
   */
  getService(serviceName) {
    return this.services[serviceName];
  }

  /**
   * Get utility by name
   * @param {string} utilName - Utility name
   * @returns {Object} Utility service
   */
  getUtil(utilName) {
    return this.utils[utilName];
  }

  /**
   * Check if service is available
   * @param {string} serviceName - Service name
   * @returns {boolean} Whether service is available
   */
  isServiceAvailable(serviceName) {
    // Map service names to service types
    const serviceTypeMap = {
      auth: SERVICE_TYPES.AUTH,
      brandManagement: SERVICE_TYPES.AUTH,
      discovery: SERVICE_TYPES.DISCOVERY,
      analytics: SERVICE_TYPES.DISCOVERY,
      savedFilters: SERVICE_TYPES.DISCOVERY
    };
    
    const serviceType = serviceTypeMap[serviceName];
    return serviceType ? healthMonitor.isServiceAvailable(serviceType) : false;
  }

  /**
   * Get available services
   * @returns {Array} List of available service names
   */
  getAvailableServices() {
    return Object.keys(this.services).filter(service => 
      this.isServiceAvailable(service)
    );
  }
}

/**
 * Create and export singleton API manager instance
 */
export const apiManager = new MultiBackendApiManager();

/**
 * Convenience function to initialize the API system
 * @param {Object} options - Initialization options
 * @returns {Promise} Initialization result
 */
export const initializeApiSystem = (options = {}) => {
  return apiManager.initialize(options);
};

/**
 * Default export for backward compatibility
 */
export default {
  // Services
  ...apiServices,
  
  // Utilities
  ...serviceUtils,
  
  // Manager
  apiManager,
  
  // Configuration
  config: apiConfig,
  envConfig,
  
  // Constants
  SERVICE_TYPES,
  ENVIRONMENTS,
  AUTH_EVENTS,
  ERROR_TYPES,
  HEALTH_STATUS
};
