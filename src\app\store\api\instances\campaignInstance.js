/**
 * Campaign Management Service Alova Instance
 * 
 * This instance handles all campaign management-related API calls including:
 * - Campaign CRUD operations
 * - Creator assignment and management
 * - Content approval workflows
 * - Campaign analytics and metrics
 * - Campaign collaboration features
 */

import { createAlova } from 'alova';
import FetchAdapter from 'alova/fetch';
import ReactHook from 'alova/react';
import { apiConfig, SERVICE_TYPES } from '../config/apiConfig';

/**
 * Token management utilities (shared with auth service)
 */
const getAuthToken = () => localStorage.getItem('auth_token');
const getRefreshToken = () => localStorage.getItem('refresh_token');
const clearTokens = () => {
  localStorage.removeItem('auth_token');
  localStorage.removeItem('refresh_token');
  localStorage.removeItem('user');
  localStorage.removeItem('allocatedBrands');
  localStorage.removeItem('organizationBrands');
  localStorage.removeItem('organisationId');
};

/**
 * Redirect to appropriate login page based on context
 */
const redirectToLogin = (requestUrl = '') => {
  const isBrand = requestUrl.includes('/brand');
  if (isBrand) {
    window.location.href = '/brand/signin';
  } else {
    window.location.href = '/influencer/login';
  }
};

/**
 * Token refresh via auth service
 * Campaign service uses auth service for token refresh
 */
const refreshTokenViaAuthService = async (method) => {
  const refreshToken = getRefreshToken();
  
  if (!refreshToken) {
    console.error('[CAMPAIGN SERVICE] No refresh token available');
    clearTokens();
    redirectToLogin(method.url);
    throw new Error('No refresh token available');
  }

  try {
    console.log('[CAMPAIGN SERVICE] Attempting token refresh via auth service...');
    
    const refreshResponse = await fetch(`${apiConfig.authServiceURL}/auth/refresh-token`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...(apiConfig.environment === 'development' && {
          'ngrok-skip-browser-warning': 'true'
        })
      },
      body: JSON.stringify({ refresh_token: refreshToken }),
    });

    const refreshData = await refreshResponse.json();

    console.log('Refresh response:', refreshResponse);

    if (refreshResponse.ok && refreshData.data?.access_token) {
      const { access_token, refresh_token } = refreshData.data;
      
      // Update localStorage
      localStorage.setItem('auth_token', access_token);
      if (refresh_token) {
        localStorage.setItem('refresh_token', refresh_token);
      }
      
      // Update the original request with new token
      method.config.headers['Authorization'] = `Bearer ${access_token}`;
      
      // Retry the original request
      return await method.send();
    } else {
      throw new Error(refreshData.message || 'Token refresh failed');
    }
  } catch (error) {
    console.error('[CAMPAIGN SERVICE] Token refresh error:', error);
    clearTokens();
    redirectToLogin(method.url);
    throw error;
  }
};

/**
 * Create Campaign Management Service Alova Instance
 */
const campaignInstance = createAlova({
  baseURL: apiConfig.campaignServiceURL,
  requestAdapter: FetchAdapter(),
  statesHook: ReactHook,

  // Default request options
  defaultOptions: {
    headers: {
      ...apiConfig.common.headers,
      ...(apiConfig.environment === 'development' && {
        'ngrok-skip-browser-warning': 'true'
      })
    },
  },
  withCredentials: apiConfig.common.withCredentials,

  // Global response handling
  responded: {
    // Transform successful responses
    onSuccess: async (response, method) => {
      console.log(`[CAMPAIGN SERVICE] API response received:`, {
        url: method.url,
        status: response.status,
        timestamp: new Date().toISOString()
      });

      const json = await response.json();

      // Handle 401 Unauthorized - attempt token refresh via auth service
      if (response.status === 401) {
        console.warn('[CAMPAIGN SERVICE] Access token expired. Attempting token refresh via auth service...');
        
        try {
          return await refreshTokenViaAuthService(method);
        } catch (refreshError) {
          console.error('[CAMPAIGN SERVICE] Token refresh failed:', refreshError);
          // Let the error fall through to be handled by the component
        }
      }

      return {
        data: json,
        status: response.status,
        service: SERVICE_TYPES.CAMPAIGN
      };
    },

    // Handle errors
    onError: (error, method) => {
      console.error(`[CAMPAIGN SERVICE] API error:`, {
        url: method.url,
        error: error.message,
        timestamp: new Date().toISOString()
      });
      throw error;
    }
  },

  // Add authorization headers for all requests
  beforeRequest(method) {
    console.log(`[CAMPAIGN SERVICE] Preparing request:`, {
      url: method.url,
      method: method.type,
      timestamp: new Date().toISOString()
    });

    // All campaign service endpoints require authentication
    const token = getAuthToken();
    if (token) {
      method.config.headers['Authorization'] = `Bearer ${token}`;
    } else {
      console.warn('[CAMPAIGN SERVICE] No auth token available for request');
      // Don't throw error here, let the server respond with 401
    }

    // Add development headers
    if (apiConfig.environment === 'development') {
      method.config.headers['ngrok-skip-browser-warning'] = 'true';
    }
  }
});

/**
 * Campaign instance utility functions
 */
export const campaignUtils = {
  getAuthToken,
  getRefreshToken,
  clearTokens,
  redirectToLogin,
  refreshTokenViaAuthService,
  
  // Service health check
  checkHealth: () => apiConfig.checkServiceHealth(SERVICE_TYPES.CAMPAIGN),
  
  // Get service configuration
  getConfig: () => apiConfig.getServiceConfig(SERVICE_TYPES.CAMPAIGN),
  
  // Campaign-specific utilities
  validateCampaignData: (campaignData) => {
    const required = ['name', 'objective', 'liveDate'];
    const missing = required.filter(field => !campaignData[field]);
    
    if (missing.length > 0) {
      throw new Error(`Missing required fields: ${missing.join(', ')}`);
    }
    
    return true;
  },
  
  // Format campaign dates
  formatCampaignDates: (campaign) => {
    const formatDate = (dateString) => {
      if (!dateString) return null;
      return new Date(dateString).toISOString().split('T')[0];
    };
    
    return {
      ...campaign,
      liveDate: formatDate(campaign.liveDate),
      closeDate: formatDate(campaign.closeDate),
      createdOn: formatDate(campaign.createdOn)
    };
  },
  
  // Calculate campaign progress
  calculateProgress: (campaign) => {
    if (!campaign.liveDate || !campaign.closeDate) return 0;
    
    const now = new Date();
    const start = new Date(campaign.liveDate);
    const end = new Date(campaign.closeDate);
    
    if (now < start) return 0;
    if (now > end) return 100;
    
    const total = end - start;
    const elapsed = now - start;
    
    return Math.round((elapsed / total) * 100);
  },
  
  // Get campaign status color
  getStatusColor: (status) => {
    const colors = {
      'Active': 'bg-green-500/20 text-green-400',
      'Planned': 'bg-blue-500/20 text-blue-400',
      'Completed': 'bg-gray-500/20 text-gray-400',
      'Paused': 'bg-yellow-500/20 text-yellow-400',
      'Cancelled': 'bg-red-500/20 text-red-400'
    };
    return colors[status] || 'bg-gray-500/20 text-gray-400';
  }
};

export default campaignInstance;
