import { useRef, useEffect, useState } from "react";
import { Outlet, useNavigate, useLocation } from 'react-router-dom';
import { Select, Space } from 'antd';

import {
    TbLayoutGrid,        // All
    TbCheck,             // Approved
    TbFileUpload,        // Submitted
    TbX,                 // Rejected
    TbClock,             // Scheduled
    TbSend,              // Posted
    TbFileText           // Draft
} from "react-icons/tb"; // or from other icon sets if preferred
import ContentCard from "../components/ContentCard";


const campaignStatus = ["All", "Submitted", "Approved", "Rejected", "Scheduled", "Posted", "Draft"];

const statusIcons = {
    All: <TbLayoutGrid />,
    Submitted: <TbFileUpload />,
    Approved: <TbCheck />,
    Rejected: <TbX />,
    Scheduled: <TbClock />,
    Posted: <TbSend />,
    Draft: <TbFileText />,
};

const statusColors = {
    Approved: "bg-green-100 text-green-700",
    Submitted: "bg-purple-100 text-purple-700",
    Scheduled: "bg-orange-100 text-orange-700",
    Posted: "bg-blue-100 text-blue-700",
    Reject: "bg-red-100 text-red-700",
    Draft: "bg-gray-100 text-gray-700",
};

const cardData = [
    {
        id: 1,
        brandName: "Google",
        organisationName: "Google",
        campaignName: "Summer Streaming Extravaganza",
        brandAvatar: "https://upload.wikimedia.org/wikipedia/commons/thumb/c/c1/Google_%22G%22_logo.svg/800px-Google_%22G%22_logo.svg.png",
        status: "Approved",
        campaignDesctiption: "Summer Streaming Extravaganza is a campaign by Google to promote its streaming services during the summer season.",
        date: "23 April 2024 | 4:20 pm",
        type: "Post",
        remarks: "Almost there! Just a few tweaks needed",
        buttonText: "Publish",
        showEdit: true,
        showAction: true
    },
    {
        id: 2,
        brandName: "Netflix",
        organisationName: "Netflix",
        campaignName: "Summer Promotional Campaign",
        brandAvatar: "https://upload.wikimedia.org/wikipedia/commons/0/08/Netflix_2015_logo.svg",
        status: "Submitted",
        campaignDesctiption: "A seasonal push to promote original content and new series.",
        date: "24 April 2024 | 11:00 am",
        type: "Shorts",
        remarks: "",
        buttonText: "Publish",
        showEdit: true
    },
    {
        id: 3,
        brandName: "Meta",
        organisationName: "Meta",
        campaignName: "Creator Boost",
        brandAvatar: "https://upload.wikimedia.org/wikipedia/commons/0/05/Facebook_Logo_%282019%29.png",
        status: "Approved",
        campaignDesctiption: "Empowering creators with more tools to grow on the platform.",
        date: "25 April 2024 | 2:30 pm",
        type: "Story",
        remarks: "All good to go!",
        buttonText: "Publish",
        showEdit: true,
        showAction: true
    },
    {
        id: 4,
        brandName: "Spotify",
        organisationName: "Spotify",
        campaignName: "Vibe Fest 2024",
        brandAvatar: "https://upload.wikimedia.org/wikipedia/commons/1/19/Spotify_logo_without_text.svg",
        status: "Scheduled",
        campaignDesctiption: "Promoting summer playlists and exclusive artist sessions.",
        date: "26 April 2024 | 5:00 pm",
        type: "Video",
        remarks: "Scheduled for weekend release.",
        buttonText: "Publish Now",
        showAction: true
    },
    {
        id: 5,
        brandName: "Apple",
        organisationName: "Apple",
        campaignName: "Creativity Unleashed",
        brandAvatar: "https://upload.wikimedia.org/wikipedia/commons/f/fa/Apple_logo_black.svg",
        status: "Posted",
        campaignDesctiption: "Showcasing creative apps and iPad accessories for creators.",
        date: "22 April 2024 | 9:45 am",
        type: "Reel",
        remarks: "Published successfully.",
        buttonText: "Delete",
        showEdit: true
    },
    {
        id: 6,
        brandName: "Adobe",
        organisationName: "Adobe",
        campaignName: "Creative Cloud Mastery",
        brandAvatar: "https://upload.wikimedia.org/wikipedia/commons/4/4f/Adobe_Corporate_Logo.png",
        status: "Approved",
        campaignDesctiption: "Promoting tutorials and tools from the Creative Cloud suite.",
        date: "21 April 2024 | 3:00 pm",
        type: "Post, Story",
        remarks: "Great work! Ready to go live.",
        buttonText: "Publish",
        showEdit: true,
        showAction: true
    },
    {
        id: 7,
        brandName: "Twitter",
        organisationName: "Twitter",
        campaignName: "Tech Talk Threads",
        brandAvatar: "https://upload.wikimedia.org/wikipedia/commons/5/51/Twitter_logo_new.svg",
        status: "Reject",
        campaignDesctiption: "Daily discussion threads on trending tech topics.",
        date: "20 April 2024 | 1:00 pm",
        type: "Post, Video",
        remarks: "Please update the content tone and re-submit.",
        buttonText: "Publish",
        showEdit: true
    },
    {
        id: 8,
        brandName: "Amazon",
        organisationName: "Amazon",
        campaignName: "Global Explorer",
        brandAvatar: "https://upload.wikimedia.org/wikipedia/commons/a/a9/Amazon_logo.svg",
        status: "Draft",
        campaignDesctiption: "Showcasing curated global products for summer travel.",
        date: "19 April 2024 | 10:00 am",
        type: "Story, Post, Video",
        remarks: "-",
        buttonText: "Edit",
        showEdit: false,
        showAction: true
    }
];

const campaignData = {
    LocobuzzIndia: ['SummerIndiaCampaign', 'HoliCampaign', 'SummerCampaign'],
    LocobuzzThai: ['HoliIndiaCampaign', 'SummerCampaign', 'HoliCampaign'],
};
const brandData = ['LocobuzzIndia', 'LocobuzzThai'];


const MyContent = () => {
    const navigate = useNavigate();
    const location = useLocation();

    // Check if we are on the "create" nested route
    const isCreateRoute = location.pathname.endsWith('/create-post');

    const [activeTab, setActiveTab] = useState("content");
    const [underlineStyle, setUnderlineStyle] = useState({});
    const [selectedStatus, setSelectedStatus] = useState("All");
    const [selectedBrand, setSelectedBrand] = useState("All");
    const [selectedCampaign, setSelectedCampaign] = useState("All");

    const campaignStatus = ["All", "Submitted", "Approved", "Rejected", "Scheduled", "Posted", "Draft"];

    const brandOptions = ["All", ...new Set(cardData.map(c => c.brandName))];

    const campaignOptions = ["All", ...new Set(
        cardData
            .filter(c => selectedBrand === "All" || c.brandName === selectedBrand)
            .map(c => c.campaignName)
    )];

    const filteredCards = cardData.filter(card => {
        const statusMatch = selectedStatus === "All" || card.status === selectedStatus;
        const brandMatch = selectedBrand === "All" || card.brandName === selectedBrand;
        const campaignMatch = selectedCampaign === "All" || card.campaignName === selectedCampaign;
        return statusMatch && brandMatch && campaignMatch;
    });

    const contentRef = useRef(null);
    const calenderRef = useRef(null);

    const [brands, setBrands] = useState(campaignData[brandData[0]]);
    const [campaigns, setCampaigns] = useState(campaignData[brandData[0]][0]);
    const handleBrandChange = value => {
        setBrands(campaignData[value]);
        setCampaigns(campaignData[value][0]);
    };
    const onCmapaignChange = value => {
        setCampaigns(value);
    };

    useEffect(() => {
        const currentRef = activeTab === "content" ? contentRef : calenderRef;
        if (currentRef.current) {
            const { offsetLeft, offsetWidth } = currentRef.current;
            setUnderlineStyle({
                left: offsetLeft,
                width: offsetWidth,
            });
        }
    }, [activeTab]);

    return (
        <div className="h-full w-full bg-primary/70 flex flex-col gap-5">
            {/* Header */}
            <div className="flex flex-col items-start justify-center gap-2.5">
                <h1 className="text-30-semibold text-gray-50">Campaign Management</h1>
                <p className='text-12-medium text-gray-200'>🎬 Lights, camera, content! Your brand collabs live here</p>
            </div>

            <Outlet />

            {/* Show this section only when not on /create */}
            {!isCreateRoute && (
                <div className='flex flex-col gap-7.5 rounded-2xl bg-gray-600 p-5'>
                    <div className="flex justify-between items-center w-full">
                        <div className="relative w-fit ">
                            <div className="flex items-start h-12 ">
                                <div
                                    ref={contentRef}
                                    onClick={() => setActiveTab("content")}
                                    className={`flex h-full items-center py-5 px-4 text-16-semibold cursor-pointer
                                                ${activeTab === "content" ? "text-brand-500" : "text-gray-400"}`}
                                >
                                    {/* <img src={activeTab === "instagram" ? InstagramBlueIcon : InstagramGrayIcon} alt="Instagram" className="pr-2" /> */}
                                    Content
                                </div>
                                <div
                                    ref={calenderRef}
                                    onClick={() => setActiveTab("calender")}
                                    className={`flex h-full items-center py-5 px-4 text-16-semibold cursor-pointer
                                                ${activeTab === "calender" ? "text-brand-500" : "text-gray-400"}`}
                                >
                                    {/* <img src={activeTab === "youtube" ? YoutubeBlueIcon : YoutubeGrayIcon} alt="YouTube" className="pr-2" /> */}
                                    Calender
                                </div>

                            </div>

                            {/* Sliding underline */}
                            <div
                                className="absolute bottom-0 h-0.5 bg-brand-500 transition-all duration-300 ease-in-out rounded-full"
                                style={underlineStyle}
                            />
                        </div>
                        <button
                            onClick={() => navigate('create-post')}
                            className="px-8 py-2.5 bg-brand-500 text-white rounded hover:bg-brand-600 transition"
                        >
                            + Create New Post
                        </button>
                    </div>
                    {/* Content */}
                    <div className="hidden flex flex-col gap-7.5">
                        <div className="flex justify-between">
                            <div>
                                <Space wrap>
                                    <div className="flex flex-col gap-0.5">
                                        <p className="text-14-medium text-gray-50">Brand</p>
                                        <div className="overflow-hidden rounded-md border-1 border-gray-800 hover:border-gray-400">
                                            <Select
                                                defaultValue={brandData[0]}
                                                style={{ width: 150 }}
                                                options={brands.map(brand => ({ label: brand, value: brand }))}
                                                onChange={handleBrandChange}
                                                dropdownStyle={{
                                                    backgroundColor: 'var(--color-gray-400)',
                                                    color: '#ffffff',
                                                }}
                                            />
                                        </div>
                                    </div>
                                    <div className="flex flex-col gap-0.5">
                                        <p className="text-14-medium text-gray-50">Campaign</p>
                                        <div className="overflow-hidden rounded-md border-1 border-gray-800 hover:border-gray-400">
                                            <Select
                                                style={{ width: 200 }}
                                                value={campaigns}
                                                options={brands.map(campaign => ({ label: campaign, value: campaign }))}
                                                onChange={onCmapaignChange}
                                                className="border-gray-600 bg-gray-900"
                                                dropdownStyle={{
                                                    backgroundColor: 'var(--color-gray-400)',
                                                    color: '#ffffff',
                                                }}
                                            />
                                        </div>
                                    </div>
                                </Space>
                            </div>
                            <div className="flex items-end">
                                {/* Import List */}
                                <button
                                    className={`relative px-4 py-2.5 bg-transparent border border-gray-400 rounded-full flex items-center gap-1.5 text-14-medium text-gray-50 hover:text-gray-300 transition-colors cursor-pointer`}
                                    onClick={() => {
                                        // setIsPopupOpen("import")
                                        console.log('Popup clicked');
                                    }}
                                    title="Toggle Filters"
                                >
                                    Import
                                </button>
                            </div>
                        </div>
                        <div className="flex gap-4 mb-4">
                            <div className="w-fit">
                                <p className="text-14-medium text-gray-50">Brand</p>
                                <div className="relative">
                                    <select
                                        value={selectedBrand}
                                        onChange={(e) => {
                                            setSelectedBrand(e.target.value);
                                            setSelectedCampaign("All");
                                        }}
                                        className="appearance-none p-2 pr-10 text-gray-400 bg-gray-900 rounded-md focus:border-gray-400 w-50 border border-gray-800 hover:border-gray-400"
                                    >
                                        {brandOptions.map((brand, idx) => (
                                            <option key={idx} value={brand}>{brand}</option>
                                        ))}
                                    </select>

                                    {/* Custom dropdown arrow */}
                                    <div className="pointer-events-none absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400">
                                        ▼
                                    </div>
                                </div>
                            </div>
                            <div className="w-fit">
                                <p className="text-14-medium text-gray-50">Campaign</p>
                                <div className="relative">
                                    <select
                                        value={selectedCampaign}
                                        onChange={(e) => setSelectedCampaign(e.target.value)}
                                        className="appearance-none p-2 text-gray-400 bg-gray-900 rounded-md focus:border-gray-400 w-80 border-1 border-gray-800 hover:border-gray-400"
                                    >
                                        {campaignOptions.map((campaign, idx) => (
                                            <option key={idx} value={campaign}>{campaign}</option>
                                        ))}
                                    </select>
                                    <div className="pointer-events-none absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400">
                                        ▼
                                    </div>
                                </div>
                            </div>


                        </div>
                        <div className="flex items-center gap-3">
                            {campaignStatus.map((status, index) => (
                                <button
                                    key={index}
                                    className={`px-4 py-2.5 border border-gray-400 rounded-full flex items-center gap-1.5 text-14-medium hover:text-gray-100 transition-colors cursor-pointer ${status === selectedStatus ? "bg-gray-500 text-white" : ""
                                        }`}
                                    onClick={() => setSelectedStatus(status)}
                                    title={status}
                                >
                                    {statusIcons[status]}
                                    {status}
                                </button>
                            ))}
                        </div>
                        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                            {filteredCards.map(card => (
                                <ContentCard key={card.id} card={card} />
                            ))}
                        </div>
                    </div>
                    {/* Calender */}
                    <div>
                        <p>Calender</p>
                    </div>
                </div>
            )}
        </div>
    );
};

export default MyContent;
