import React, { useState } from 'react';
import Loader, { <PERSON><PERSON><PERSON>oader, OverlayLoader, PageLoader } from '../UI/Loader';
import { Toast } from '../UI/Toast';
import useApiRequest from '../../hooks/useApiRequest';

/**
 * Example component demonstrating how to use Toast, Loader, and API request hooks
 */
const ApiIntegrationExample = () => {
  const { executeRequest, isLoading, error } = useApiRequest();
  const [showOverlay, setShowOverlay] = useState(false);
  
  // Example API function (simulation)
  const simulateApiCall = (shouldSucceed = true, delay = 2000) => {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        if (shouldSucceed) {
          resolve({ success: true, data: { message: 'API call completed successfully!' } });
        } else {
          reject(new Error('API call failed!'));
        }
      }, delay);
    });
  };
  
  // Handle successful API call with toast
  const handleSuccessToast = async () => {
    try {
      await executeRequest(
        () => simulateApiCall(true),
        {
          loadingMessage: 'Processing your request...',
          successMessage: 'Your request was processed successfully!',
          showLoadingToast: false,
          showSuccessToast: true,
          
        }
      );
    } catch (error) {
      console.error('Error:', error);
    }
  };
  
  // Handle error API call with toast
  const handleErrorToast = async () => {
    try {
      await executeRequest(
        () => simulateApiCall(false),
        {
          loadingMessage: 'Processing your request...',
          errorMessage: 'Failed to process your request',
          showLoadingToast: true,
          showErrorToast: true
        }
      );
    } catch (error) {
      console.error('Error was handled by toast' + error);
    }
  };
  
  // Show overlay loader for 3 seconds
  const handleOverlayLoader = () => {
    setShowOverlay(true);
    setTimeout(() => {
      setShowOverlay(false);
    }, 3000);
  };
  
  // Show different toast types
  const showToastTypes = () => {
    Toast.success('This is a success message!');
    setTimeout(() => Toast.error('This is an error message!'), 1000);
    setTimeout(() => Toast.warning('This is a warning message!'), 2000);
    setTimeout(() => Toast.info('This is an info message!'), 3000);
  };
  
  return (
    <div className="p-8 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">API Integration Components</h1>
      
      {/* Toast Examples */}
      <section className="mb-8 p-6 bg-gray-50 rounded-lg">
        <h2 className="text-xl font-semibold mb-4">Toast Notifications</h2>
        <div className="flex flex-wrap gap-3">
          <button
            onClick={showToastTypes}
            className="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700"
          >
            Show All Toast Types
          </button>
          
          <button
            onClick={handleSuccessToast}
            className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
          >
            Success API Call
          </button>
          
          <button
            onClick={handleErrorToast}
            className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
          >
            Error API Call
          </button>
        </div>
      </section>
      
      {/* Loader Examples */}
      <section className="mb-8 p-6 bg-gray-50 rounded-lg">
        <h2 className="text-xl font-semibold mb-4">Loaders</h2>
        
        <div className="grid grid-cols-2 md:grid-cols-3 gap-6 mb-6">
          <div className="p-4 border rounded flex flex-col items-center">
            <h3 className="font-medium mb-3">Clip Loader</h3>
            <Loader variant="clip" />
          </div>
          
          <div className="p-4 border rounded flex flex-col items-center">
            <h3 className="font-medium mb-3">Pulse Loader</h3>
            <Loader variant="pulse" />
          </div>
          
          <div className="p-4 border rounded flex flex-col items-center">
            <h3 className="font-medium mb-3">Beat Loader</h3>
            <Loader variant="beat" />
          </div>
        </div>
        
        <div className="flex flex-wrap gap-3">
          <button
            onClick={handleOverlayLoader}
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            Show Overlay Loader
          </button>
          
          <button
            className="px-4 py-2 bg-indigo-600 text-white rounded hover:bg-indigo-700 inline-flex items-center"
            disabled={isLoading}
          >
            Button with Loader
            {isLoading && <ButtonLoader />}
          </button>
        </div>
      </section>
      
      {/* Show the overlay loader when showOverlay is true */}
      {showOverlay && <OverlayLoader text="Loading..." className="bg-transparent" />}
      
      {/* API request status */}
      {error && (
        <div className="p-4 mb-4 bg-red-100 border border-red-200 rounded text-red-700">
          <p className="font-medium">Error:</p>
          <p>{error.message}</p>
        </div>
      )}
    </div>
  );
};

export default ApiIntegrationExample;
