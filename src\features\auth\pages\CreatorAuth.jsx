import { useParams } from 'react-router-dom';
import './LoginPage.css';
import AuthLayout from '../../../shared/layout/AuthLayout';
import React, { useState, useEffect } from 'react';
import { Link, useNavigate, useLocation } from "react-router-dom";

import { registerThunk, loginThunk, initiateOAuthThunk, selectYoutubeChannelsThunk, getYoutubeChannelsThunk } from '../service/authThunks';
import { useDispatch } from 'react-redux';
import { useSnackbar } from '@shared/components/UI/SnackbarContext';
import PopupLayout from '@shared/components/UI/PopupLayout';
import ProfileSelector from "../component/ProfileSelector";



// UI Components
import { Input } from '@shared/components/UI/input';
import SocialButton from "@shared/components/UI/button";
import GradientSignupCard from "@auth/component/GradientSignupCard";
import OtpVerificationDialog from '../component/OtpVerificationDialog';

// API and State
import { UserRole, RegisterSource } from '../../../app/store/enum';
import { Toast } from '../../../shared/components/UI/Toast';
import useRoles from '../../../shared/hooks/useRoles';

// Assets
import googleIcon from '@assets/icon/google-icon.svg';
import instagramIcon from "@assets/icon/instagram-s-icon.png";
import youtubeIcon from "@assets/icon/youtube-icon.svg";
import handwave from '@assets/emoji/Waving_Hand_Light_Skin_Tone.png';
import cash from '@assets/cash.svg';
import briefcase from '@assets/briefcase.svg';
import rocket from '@assets/rocket.svg';
import mailIcon from "@assets/icon/mail-icon.svg";

const CreatorAuth = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { showSnackbar } = useSnackbar();

  const location = useLocation();

  useEffect(() => {
    const searchParams = new URLSearchParams(location.search);
    const errorParam = searchParams.get('error');
    const cancelParam = searchParams.get('cancelled');

    if (errorParam) {
      if (cancelParam) {
        setIsPopupOpen("cancel")
      } else {
        setIsPopupOpen("error");
      }

      // Remove query params from URL
      const newUrl = window.location.pathname;
      window.history.replaceState({}, document.title, newUrl);
    }
  }, [location.search]);

  useEffect(() => {
    const searchParams = new URLSearchParams(location.search);
    if (searchParams.size > 0) {
      const accessToken = searchParams.get('accessToken');
      const refreshToken = searchParams.get('refreshToken');

      if(accessToken && refreshToken){
        localStorage.setItem('auth_token', accessToken);
        localStorage.setItem('refresh_token', refreshToken);
      }

      if (accessToken) {
        getAllProfileChannels(accessToken);
      }

    }
  }, [location.pathname, location.search]);

  const { authType } = useParams(); // "signin" or "signup"
  const isSignup = authType === "signup";

  const [email, setEmail] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [isPending, setIsPending] = useState(false);
  const [showOtpDialog, setShowOtpDialog] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [isPopupOpen, setIsPopupOpen] = useState("");
  const [selectedProfiles, setSelectedProfiles] = useState([]);
  const [selectedChannel, setSelectedChannel] = useState("");

  const [profiles, setProfiles] = useState([]);
  const [channels, setChannels] = useState([]);
  const [touched, setTouched] = useState(false);

  const { getRoleIdByName } = useRoles();

  // Simple email validation
  const isEmailValid = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email) && email.includes(".com");

  const publicEmailDomains = [
    "gmail.com",
    "yahoo.com",
    "hotmail.com",
    "outlook.com",
    "aol.com",
    "icloud.com",
    "mail.com"
  ];


  const emailParts = email.split("@");
  const domain = emailParts.length > 1 ? emailParts[1].toLowerCase() : "";

  // Get the influencer role ID
  const influencerRoleId = getRoleIdByName('influencer');

  useEffect(() => {
    console.log("Platform ", selectedChannel);
  }, [selectedChannel]);

  const handleOAuthInitiate = async (provider) => {
    try {
      setIsLoading(true);

      // showSnackbar("Hi my name is ankit")

      // Map provider names to their OAuth provider values
      const providerMap = {
        google: 'youtube',
        instagram: 'instagram',
        youtube: 'youtube'
      };

      // Make sure we have a valid role ID
      if (!influencerRoleId) {
        Toast.error('(R) Internal Server Error');
        setIsLoading(false);
        return;
      }

      // Call the OAuth initiate endpoint
      const response = await dispatch(initiateOAuthThunk({
        provider: providerMap[provider],
        role_uuid: influencerRoleId,
        redirect_path: `/influencer/${authType}`,
        error_path: `/influencer/${authType}`
      }));

      var responseData = response.payload.data;

      console.log("Email signup response:", responseData);

      console.log(`${provider} OAuth initiation response:`, response);

      // Handle the OAuth response - typically redirects to the provider's auth page
      if (responseData?.auth_url) {
        window.location.href = responseData.auth_url;
      } else {
        showSnackbar(`Failed to initiate ${provider} authentication`, 'error');
        // Toast.error(`Failed to initiate ${provider} authentication`);
        setIsLoading(false);
      }
    } catch (error) {
      console.error(`${provider} OAuth initiation error:`, error);
      showSnackbar(`Failed to initiate ${provider} authentication: ${error.message || 'Unknown error'}`, 'error');
      setIsLoading(false);
    }
  };

  const handleAuthSuccess = (type) => {
    if (type === 'signup') {
      Toast.success("OTP sent successfully!");
      // Show OTP verification dialog instead of navigating away
      setShowOtpDialog(true);
    } else {
      Toast.success("OTP sent successfully!");

      setShowOtpDialog(true);
    }
    setIsLoading(false);
  };


  const handleOtpVerified = (responseData) => {
    // After OTP verification is complete
    setShowOtpDialog(false);

    // The user is now authenticated, navigate to the appropriate page
    if (isSignup) {
      Toast.success("Registration successful! Welcome aboard!");
      setTimeout(() => navigate("/socialsignup"), 0);
    } else {
      if (responseData.social_profiles.length === 0) {
        setTimeout(() => navigate("/socialsignup"), 0);
      } else {
        Toast.success("Login successful! Welcome back!");
        setTimeout(() => navigate("/influencer/dashboard"), 0);
      }
    }

  };

  const handleUpdateEmail = () => {
    // Close the OTP dialog and let user update email
    setShowOtpDialog(false);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    setIsPending(true);
    try {
      setIsLoading(true);

      if (!influencerRoleId) {
        Toast.error('(R) Internal Server Error');
        setIsLoading(false);
        setIsPending(false);
        return;
      }

      if (isSignup) {
        // Call the email registration endpoint
        const response = await dispatch(registerThunk({
          email: email,
          role_uuid: influencerRoleId,
          register_source: RegisterSource.EMAIL_OTP
        }));

        var responseData = response.payload;

        console.log("Email signup response:", responseData);

        if (responseData.success) {
          handleAuthSuccess('signup');
        } else {
          if (responseData.message.includes("already registered")) {
            // Toast.error('User already registered. Please sign in.');
            setErrorMessage('User already registered. Please sign in.');
          } else {
            // Toast.error(responseData.message || 'Failed to send OTP. Please try again.');
            setErrorMessage(responseData.message || 'Failed to send OTP. Please try again.');
          }
        }
      }
      else {
        // Call the email registration endpoint
        const response = await dispatch(loginThunk({
          email: email,
          role_uuid: influencerRoleId,
          register_source: RegisterSource.EMAIL_OTP
        }));

        var loginResponse = response.payload;

        if (loginResponse.success) {
          handleAuthSuccess('login');
        } else {
          if (loginResponse.message.includes("already registered")) {
            // Toast.error('User already registered. Please sign in.');
            setErrorMessage('User already registered. Please sign in.');
          } else {
            // Toast.error(loginResponse.message || 'Failed to send OTP. Please try again.');
            setErrorMessage(loginResponse.message || 'Failed to send OTP. Please try again.');
          }
        }
      }
    } catch (err) {
      console.error(isSignup ? "Signup error:" : "Sign in error:", err);
      Toast.error('Server error. Try again after some time.');
    } finally {
      setIsPending(false);
      setIsLoading(false);
    }
  };

  const handleProfileChange = (profiles) => {
    console.log("Selected profiles Ankit:", profiles);
    setSelectedProfiles(profiles);
  };

  const getAllProfileChannels = async () => {
    try {
      setIsLoading(true);

      // Call the OAuth initiate endpoint
      const response = await dispatch(getYoutubeChannelsThunk());

      var responseData = response.payload;

      // showSnackbar("Hi my name is ankit")
      if (responseData.success && responseData.data !== null) {
        setIsLoading(false);

        setChannels(responseData.data.channels);

        const transformed = responseData.data.channels.map((profile) => ({
          id: profile.channel_id,
          name: profile.title,
          profile: profile.custom_url,
          avatar: profile.thumbnail_url
        }));
        if (transformed.length > 0) {
          setProfiles(transformed);
          setIsPopupOpen("profileSelector");
        }
        else {
          showSnackbar("No channels found", 'info');
        }
      }
      else {
        showSnackbar(responseData.message, 'error');
        setIsLoading(false);
      }
    } catch (error) {
      console.error(`Failed to get youtube channels:`, error);
      showSnackbar(`Failed to get youtube channels: ${error.message || 'Unknown error'}`, 'error');
      setIsLoading(false);
    }
  };

  const handleAddProfiles = async (selectedProfiles, channel) => {

    if (selectedProfiles.length === 0) {
      showSnackbar("Please select at least one profile", 'error');
      return;
    }

    try {
      setIsLoading(true);
      const selectedProfileChannels = channels.filter(channel =>
        Array.isArray(selectedProfiles) &&
        selectedProfiles.some(selectedProfile => selectedProfile.id === channel.channel_id)
      );

      // Call the OAuth initiate endpoint
      if (selectedProfileChannels.length > 0) {
        const response = await dispatch(selectYoutubeChannelsThunk(selectedProfileChannels));

        var responseData = response.payload;
        console.log("Added Youtube Channel response:", response.payload);

        // showSnackbar("Hi my name is ankit")
        if (responseData.success && responseData.data !== null) {
          console.log("Selected Youtube Channel response:", responseData.data);
          showSnackbar("Profile added successfully!", 'success');
          setIsPopupOpen("");

          if (channel === "instagram") {
            navigate('/social-signup-followup/instagram');
          } else {
            navigate('/social-signup-followup/youtube');
          }
        }
        else {
          showSnackbar(responseData.message, 'error');
        }
      }

    } catch (error) {
      console.error(`Failed to add youtube channels:`, error);
      showSnackbar(`Failed to add youtube channels: ${error.message || 'Unknown error'}`, 'error');
      setIsLoading(false);
    } finally {
      setIsLoading(false);
      setSelectedChannel("");
    }
  };

  return (
    <AuthLayout>
      {/* OTP Verification Dialog */}
      <OtpVerificationDialog
        email={email}
        type={isSignup ? 'signup' : 'login'}
        isOpen={showOtpDialog}
        onClose={() => setShowOtpDialog(false)}
        onVerified={handleOtpVerified}
        onUpdateEmail={handleUpdateEmail}
      />
      {
        isPopupOpen === "error" && (
          <PopupLayout
            title="Invite Creators"
            className="bg-[#292929]"
            onClose={() => setIsPopupOpen("")}
            isAcceptButton={false}
            isCancelButton={false}
            acceptText="Done"
          >
            <div className="flex flex-col gap-7">
              <span className='text-24-semibold text-gray-50'>⚠️ Invalid Email</span>
              <div className='flex flex-col gap-12 mb-5'>
                <div className='flex flex-col gap-2'>
                  <span className='text-18-medium text-gray-300'>Please use your personal email to {authType === 'signup' ? 'sign up' : 'sign in'} for Creatorverse.</span>
                  {/* <span className='text-14-semibold text-gray-300'>We'll keep you posted when they start responding.</span> */}
                </div>
              </div>
            </div>
          </PopupLayout>
        )
      }
      {
        isPopupOpen === "cancel" && (
          <PopupLayout
            title="Invite Creators"
            className="bg-[#292929]"
            onClose={() => setIsPopupOpen("")}
            isAcceptButton={false}
            isCancelButton={false}
            acceptText="Done"
          >
            <div className="flex flex-col gap-7">
              <span className='text-24-semibold text-gray-50'>⚠️ Alert</span>
              <div className='flex flex-col gap-12 mb-5'>
                <div className='flex flex-col gap-2'>
                  <span className='text-18-medium text-gray-300'>Operation Cancelled.</span>
                  {/* <span className='text-14-semibold text-gray-300'>We'll keep you posted when they start responding.</span> */}
                </div>
              </div>
            </div>
          </PopupLayout>
        )
      }
      {
        isPopupOpen === "profileSelector" && (
          <PopupLayout
            title="Invite Creators"
            className="bg-[#292929]"
            onClose={() => setIsPopupOpen("")}
            isAcceptButton={false}
            isCancelButton={false}
            acceptText="Add"
            width='450px'
          >
            <div className="flex flex-col gap-2">
              <span className='text-24-semibold text-gray-50'>Select Profile to Add</span>
              <div className='flex flex-col gap-12'>
                <ProfileSelector onChange={handleProfileChange} profiles={profiles} />
              </div>
              <div className="flex justify-end">
                <button
                  onClick={() => handleAddProfiles(selectedProfiles)}
                  className="px-15 py-2.5 bg-brand-500 text-white rounded hover:bg-brand-600 transition"
                >
                  Add
                </button>
              </div>
            </div>
          </PopupLayout>
        )
      }

      {/* <div className="flex flex-col w-full lg:max-w-md md:max-w-md sm:max-w-sm max-h-[80vh] transition-all duration-500"> */}
      <div className="flex flex-col w-full md:max-w-md max-h-[90vh]  px-4 md:px-0">
        <div className='flex flex-col space-y-5'>
          <div className="text-left">
            <h1 className="text-36-semibold mb-1 whitespace-nowrap ">
              {isSignup ? "Hey there, " : "Welcome back, "}
              <span className="creator-gradient-text">Creator!</span>
              <div className="bg-[#D6F7EE] w-10 h-10 rounded-full p-1.5 ml-2 inline-flex items-center justify-center align-middle">
                <img src={handwave} alt="Waving Hand" className="w-6 h-6" />
              </div>
            </h1>
            <p className="text-gray-300 text-18-regular">Land collabs. Get paid. Let's make waves.</p>
          </div>

          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="space-y-2">
              <div className="flex flex-col gap-1.5">
                {/* Label */}
                <label className="text-14-medium text-foreground">Email</label>

                {/* Input Wrapper */}
                <div
                  className={`flex items-center h-[44px] w-full rounded-lg px-3 py-2 bg-background border border-input focus-within:ring-ring focus-within:ring-[0.5px] focus-within:ring-offset-1 transition-all duration-200 hover:shadow-[0px_0px_0px_3px_rgba(0,200,255,0.14)]`}
                >
                  {/* Optional icon */}
                  <img
                    src={mailIcon}
                    alt="icon"
                    className="mr-2 h-4 w-4 opacity-70"
                  />

                  {/* Input field */}
                  <input
                    type="email"
                    onChange={(e) => {
                      setEmail(e.target.value);
                      setTouched(false); // Reset error while editing
                    }}
                    placeholder="<EMAIL>"
                    required
                    onBlur={() => setTouched(true)} // Mark as touched on blur
                    className="w-full bg-transparent text-sm outline-none placeholder:text-muted-foreground focus:bg-transparent"
                  />
                </div>

                {/* Error Message */}
                {touched && (() => {
                  let message = "";
                  if (errorMessage) {
                    message = errorMessage;
                  } else if (!email) {
                    message = "Email is required";
                  } else if (!isEmailValid) {
                    message = "Please enter a valid email address";
                  } else if (!publicEmailDomains.includes(domain)) {
                    message = "Please use a personal email";
                  }
                  return message ? <p className="text-sm text-red-2 -mt-1">{message}</p> : null;
                })()}
              </div>
            </div>

            <button
              type="submit"
              className={`w-full h-[44px] px-[18px] py-[10px] rounded-[8px] transition duration-300 text-16-regular 
                                ${isEmailValid && !isLoading && !isPending && publicEmailDomains.includes(domain)
                  ? "bg-brand-500 hover:bg-brand-600 text-white cursor-pointer"
                  : "bg-gray-500 text-gray-300"
                }`}
              disabled={!isEmailValid || isLoading || isPending || !publicEmailDomains.includes(domain)}
            >
              {isSignup ? "Create Account" : "Sign in"}
            </button>

            <div className="relative flex items-center justify-center mt-5 mb-10">
              <hr className="w-3/5 border-gray-700" />
              <div className="px-4 text-16-regular text-gray-200 bg-primary absolute">Or Continue With</div>
            </div>
            <div className="flex flex-row gap-4">
              <SocialButton
                onClick={() => handleOAuthInitiate('google')}
                icon={<img src={googleIcon} alt="Google" className="w-5 h-5" />}
                variant="default"
                className="w-full px-6 text-16-semibold text-gray-900 bg-white"
                disabled={isLoading || isPending}
              >
                Google
              </SocialButton>
              <SocialButton
                onClick={() => handleOAuthInitiate('instagram')}
                icon={<img src={instagramIcon} alt="Instagram" className="w-5 h-5" />}
                variant="default"
                className="w-full px-6 text-16-semibold text-gray-900 bg-[#f3efff]"
                disabled={isLoading || isPending}
              >
                Instagram
              </SocialButton>
              <SocialButton
                onClick={() => handleOAuthInitiate('youtube')}
                icon={<img src={youtubeIcon} alt="YouTube" className="w-5.5 h-5.5" />}
                variant="default"
                className="w-full px-6 text-16-semibold text-gray-900 bg-light-6"
                disabled={isLoading || isPending}
              >
                YouTube
              </SocialButton>
            </div>

          </form>

          <div className="text-center text-18-regular text-gray-200">
            <p>
              {isSignup
                ? <>Already part of the fam? <Link to="/influencer/signin" onClick={() => setErrorMessage("")} className="text-[rgba(71,200,236,1)] hover:underline">Sign in</Link></>
                : <>New here? <Link to="/influencer/signup" onClick={() => setErrorMessage("")} className="text-[rgba(71,200,236,1)] hover:underline">Create Account</Link></>
              }
            </p>
          </div>
        </div>

        <div className="mt-5 flex">
          <GradientSignupCard
            to="/brand/signup"
            label="Business"
            description="Build real campaigns. Drive real results"
            images={[
              { src: cash, className: "bg-blue-300 transition-all duration-500 ease-in-out ml-0 group-hover:ml-0" },
              { src: briefcase, className: "bg-green-300 transition-all duration-500 ease-in-out -ml-2 group-hover:-ml-1" },
              { src: rocket, className: "bg-yellow-300 transition-all duration-500 ease-in-out -ml-2 group-hover:-ml-1" }
            ]}
            className="mt-10"
          />
        </div>
      </div>
    </AuthLayout>
  );
};

export default CreatorAuth;
