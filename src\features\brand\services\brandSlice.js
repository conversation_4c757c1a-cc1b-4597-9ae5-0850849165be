import { createSlice } from '@reduxjs/toolkit';
import {
    getBrandInfoThunk,
    createBrandThunk,
    requestBrandAccessThunk,
    searchCreatorsThunk,
    getFilterMetadataThunk,
    transformFiltersThunk,
    invalidateFilterCacheThunk,
    getCacheStatsThunk,
    cleanupCacheThunk,
    getSavedFiltersThunk,
    getSavedFilterSetThunk,
    createSavedFilterThunk,
    updateSavedFilterThunk,
    deleteSavedFilterThunk,
    getGlobalFiltersThunk,
    getCreatorListsThunk,
    createCreatorListThunk,
    addMemberToListThunk,
    getListMembersThunk
} from './brandThunks';
import { requestUserBrandInfoThunk, brandVerifyOtpThunk } from '@auth/service/authThunks';
import { RequestStatus } from '../../../app/store/enum';

const getParsedItem = (key, fallback) => {
    try {
        const value = localStorage.getItem(key);
        if (!value || value === 'undefined') return fallback;
        return JSON.parse(value);
    } catch {
        return fallback;
    }
};

const allocatedBrands = getParsedItem('allocatedBrands', []);

/**
 * Brand slice initial state
 */
const initialState = {
    // Brand management state
    allocatedBrands: getParsedItem('allocatedBrands', []),
    organizationBrands: getParsedItem('organizationBrands', []),
    organizationId: getParsedItem('organizationId', null),

    // Creator discovery state
    searchResults: {
        profiles: [],
        metadata: null,
        lastSearchParams: null
    },
    searchSuggestions: [], // Store suggestions from 410 responses

    // Filter management state
    filterMetadata: {},
    savedFilters: [],
    creatorLists: [],
    globalFilters: getParsedItem('globalFilters', []),
    currentFilterSet: [],
    transformedFilters: null,
    cacheStats: null,

    // Request status tracking
    status: RequestStatus.IDLE,
    searchStatus: RequestStatus.IDLE,
    filterStatus: RequestStatus.IDLE,
    savedFilterStatus: RequestStatus.IDLE,
    cacheStatus: RequestStatus.IDLE,

    // Error handling
    error: null,
    searchError: null,
    filterError: null,
    savedFilterError: null,
    cacheError: null,

    // UI state
    selectedBrand: getParsedItem('selectedBrand', null) || (allocatedBrands.length > 0 ? allocatedBrands[0] : null),
    // selectedBrand: null,
    activeFilters: [],
    searchQuery: ''
};

/**
 * Brand slice with reducers and extra reducers for thunks
 */
const brandSlice = createSlice({
    name: 'brand',
    initialState,
    reducers: {
        // Synchronous actions for UI state management
        setSelectedBrand(state, action) {
            state.selectedBrand = action.payload;
            localStorage.setItem('selectedBrand', JSON.stringify(action.payload));
        },

        setActiveFilters(state, action) {
            state.activeFilters = action.payload;
        },

        setSearchQuery(state, action) {
            state.searchQuery = action.payload;
        },

        clearSearchResults(state) {
            state.searchResults = {
                profiles: [],
                metadata: null,
                lastSearchParams: null
            };
            state.searchStatus = RequestStatus.IDLE;
            state.searchError = null;
        },

        clearErrors(state) {
            state.error = null;
            state.searchError = null;
            state.filterError = null;
            state.savedFilterError = null;
            state.cacheError = null;
        },

        clearBrandError(state) {
            state.error = null;
        },

        clearSearchError(state) {
            state.searchError = null;
        },

        clearFilterError(state) {
            state.filterError = null;
        },

        clearSavedFilterError(state) {
            state.savedFilterError = null;
        },

        clearCacheError(state) {
            state.cacheError = null;
        },

        setCurrentFilterSet(state, action) {
            state.currentFilterSet = action.payload;
        },

        clearTransformedFilters(state) {
            state.transformedFilters = null;
        },

        clearAllBrandState(state) {
            // Reset to initial state values
            state.allocatedBrands = [];
            state.organizationBrands = [];
            state.organizationId = null;
            state.searchResults = {
                profiles: [],
                metadata: null,
                lastSearchParams: null
            };
            state.filterMetadata = {};
            state.creatorLists = [];
            state.savedFilters = [];
            state.globalFilters = [];
            state.currentFilterSet = [];
            state.transformedFilters = null;
            state.cacheStats = null;
            state.selectedBrand = null;
            state.activeFilters = [];
            state.searchQuery = '';

            // Reset status
            state.status = RequestStatus.IDLE;
            state.searchStatus = RequestStatus.IDLE;
            state.filterStatus = RequestStatus.IDLE;
            state.savedFilterStatus = RequestStatus.IDLE;
            state.cacheStatus = RequestStatus.IDLE;

            // Clear errors
            state.error = null;
            state.searchError = null;
            state.filterError = null;
            state.savedFilterError = null;
            state.cacheError = null;
        }
    },
    extraReducers: (builder) => {
        builder
            .addCase(brandVerifyOtpThunk.fulfilled, (state, action) => {
                state.status = RequestStatus.SUCCEEDED;
                state.allocatedBrands = action.payload.data.allocated_brands;
                state.organizationBrands = action.payload.data.organization_brands;
            })
            .addCase(requestUserBrandInfoThunk.fulfilled, (state, action) => {
                state.status = RequestStatus.SUCCEEDED;
                state.allocatedBrands = action.payload.data.allocated_brands;
                state.organizationBrands = action.payload.data.organization_brands;
            })
            // Get brand info thunk cases
            .addCase(getBrandInfoThunk.pending, (state) => {
                state.status = RequestStatus.LOADING;
                state.error = null;
            })
            .addCase(getBrandInfoThunk.fulfilled, (state, action) => {
                state.status = RequestStatus.SUCCEEDED;
                if (action.payload.data) {
                    state.allocatedBrands = action.payload.data.allocated_brands || [];
                    state.organizationBrands = action.payload.data.organization_brands || [];
                    state.organizationId = action.payload.data.organization_id || null;
                }
            })
            .addCase(getBrandInfoThunk.rejected, (state, action) => {
                state.status = RequestStatus.FAILED;
                state.error = action.payload?.message || 'Failed to retrieve brand info';
            })

            // Create brand thunk cases
            .addCase(createBrandThunk.pending, (state) => {
                state.status = RequestStatus.LOADING;
                state.error = null;
            })
            .addCase(createBrandThunk.fulfilled, (state, action) => {
                state.status = RequestStatus.SUCCEEDED;
                if (action.payload.data) {
                    state.allocatedBrands.push(action.payload.data);
                }
            })
            .addCase(createBrandThunk.rejected, (state, action) => {
                state.status = RequestStatus.FAILED;
                state.error = action.payload?.message || 'Failed to create brand';
            })

            // Request brand access thunk cases
            .addCase(requestBrandAccessThunk.pending, (state) => {
                state.status = RequestStatus.LOADING;
                state.error = null;
            })
            .addCase(requestBrandAccessThunk.fulfilled, (state) => {
                state.status = RequestStatus.SUCCEEDED;
            })
            .addCase(requestBrandAccessThunk.rejected, (state, action) => {
                state.status = RequestStatus.FAILED;
                state.error = action.payload?.message || 'Failed to request brand access';
            })

            // Search creators thunk cases
            .addCase(searchCreatorsThunk.pending, (state) => {
                state.searchStatus = RequestStatus.LOADING;
                state.searchError = null;
            })
            .addCase(searchCreatorsThunk.fulfilled, (state, action) => {
                // Handle both successful search (200) and suggestions (410)
                if (action.payload.status === 410) {
                    // Handle 410 status - query not related to influencer search
                    state.searchStatus = RequestStatus.FAILED;
                    state.searchError = action.payload.message;
                    state.searchSuggestions = action.payload.suggestions || [];
                    state.searchResults = {
                        profiles: [],
                        metadata: null,
                        lastSearchParams: action.meta.arg
                    };
                } else {
                    // Handle successful search (200)
                    state.searchStatus = RequestStatus.SUCCEEDED;
                    state.searchSuggestions = []; // Clear suggestions on successful search
                    if (action.payload.data) {
                        state.searchResults = {
                            profiles: action.payload.data.profiles || [],
                            metadata: action.payload.data.metadata || null,
                            lastSearchParams: action.meta.arg
                        };
                    }
                }
            })
            .addCase(searchCreatorsThunk.rejected, (state, action) => {
                state.searchStatus = RequestStatus.FAILED;
                state.searchError = action.payload?.message || 'Creator search failed';
            })

            // Get filter metadata thunk cases
            .addCase(getFilterMetadataThunk.pending, (state) => {
                state.filterStatus = RequestStatus.LOADING;
                state.filterError = null;
            })
            .addCase(getFilterMetadataThunk.fulfilled, (state, action) => {
                state.filterStatus = RequestStatus.SUCCEEDED;
                state.filterMetadata = action.payload.data;
            })
            .addCase(getFilterMetadataThunk.rejected, (state, action) => {
                state.filterStatus = RequestStatus.FAILED;
                state.filterError = action.payload?.message || 'Failed to retrieve filter metadata';
            })

            // Get saved filters thunk cases
            .addCase(getSavedFiltersThunk.pending, (state) => {
                state.savedFilterStatus = RequestStatus.LOADING;
                state.savedFilterError = null;
            })
            .addCase(getSavedFiltersThunk.fulfilled, (state, action) => {
                state.savedFilterStatus = RequestStatus.SUCCEEDED;
                if (action.payload.data) {
                    state.savedFilters = action.payload.data.filters || [];
                }
            })
            .addCase(getSavedFiltersThunk.rejected, (state, action) => {
                state.savedFilterStatus = RequestStatus.FAILED;
                state.savedFilterError = action.payload?.message || 'Failed to retrieve saved filters';
            })

            // Create saved filter thunk cases
            .addCase(createSavedFilterThunk.pending, (state) => {
                state.savedFilterStatus = RequestStatus.LOADING;
                state.savedFilterError = null;
            })
            .addCase(createSavedFilterThunk.fulfilled, (state, action) => {
                state.savedFilterStatus = RequestStatus.SUCCEEDED;
                if (action.payload.data) {
                    state.savedFilters.push(action.payload.data);
                }
            })
            .addCase(createSavedFilterThunk.rejected, (state, action) => {
                state.savedFilterStatus = RequestStatus.FAILED;
                state.savedFilterError = action.payload?.message || 'Failed to save filter set';
            })

            // Update saved filter thunk cases
            .addCase(updateSavedFilterThunk.pending, (state) => {
                state.savedFilterStatus = RequestStatus.LOADING;
                state.savedFilterError = null;
            })
            .addCase(updateSavedFilterThunk.fulfilled, (state, action) => {
                state.savedFilterStatus = RequestStatus.SUCCEEDED;
                if (action.payload.data && action.payload.filterSetId) {
                    const index = state.savedFilters.findIndex(filter => filter.id === action.payload.filterSetId);
                    if (index !== -1) {
                        state.savedFilters[index] = action.payload.data;
                    }
                }
            })
            .addCase(updateSavedFilterThunk.rejected, (state, action) => {
                state.savedFilterStatus = RequestStatus.FAILED;
                state.savedFilterError = action.payload?.message || 'Failed to update filter set';
            })

            // Delete saved filter thunk cases
            .addCase(deleteSavedFilterThunk.pending, (state) => {
                state.savedFilterStatus = RequestStatus.LOADING;
                state.savedFilterError = null;
            })
            .addCase(deleteSavedFilterThunk.fulfilled, (state, action) => {
                state.savedFilterStatus = RequestStatus.SUCCEEDED;
                if (action.payload.filterSetId) {
                    state.savedFilters = state.savedFilters.filter(filter => filter.id !== action.payload.filterSetId);
                }
            })
            .addCase(deleteSavedFilterThunk.rejected, (state, action) => {
                state.savedFilterStatus = RequestStatus.FAILED;
                state.savedFilterError = action.payload?.message || 'Failed to delete filter set';
            })

            // Transform filters thunk cases
            .addCase(transformFiltersThunk.pending, (state) => {
                state.filterStatus = RequestStatus.LOADING;
                state.filterError = null;
            })
            .addCase(transformFiltersThunk.fulfilled, (state, action) => {
                state.filterStatus = RequestStatus.SUCCEEDED;
                if (action.payload.data) {
                    state.transformedFilters = action.payload.data;
                }
            })
            .addCase(transformFiltersThunk.rejected, (state, action) => {
                state.filterStatus = RequestStatus.FAILED;
                state.filterError = action.payload?.message || 'Failed to transform filters';
            })

            // Invalidate filter cache thunk cases
            .addCase(invalidateFilterCacheThunk.pending, (state) => {
                state.cacheStatus = RequestStatus.LOADING;
                state.cacheError = null;
            })
            .addCase(invalidateFilterCacheThunk.fulfilled, (state) => {
                state.cacheStatus = RequestStatus.SUCCEEDED;
            })
            .addCase(invalidateFilterCacheThunk.rejected, (state, action) => {
                state.cacheStatus = RequestStatus.FAILED;
                state.cacheError = action.payload?.message || 'Failed to invalidate filter cache';
            })

            // Get cache stats thunk cases
            .addCase(getCacheStatsThunk.pending, (state) => {
                state.cacheStatus = RequestStatus.LOADING;
                state.cacheError = null;
            })
            .addCase(getCacheStatsThunk.fulfilled, (state, action) => {
                state.cacheStatus = RequestStatus.SUCCEEDED;
                if (action.payload.data) {
                    state.cacheStats = action.payload.data;
                }
            })
            .addCase(getCacheStatsThunk.rejected, (state, action) => {
                state.cacheStatus = RequestStatus.FAILED;
                state.cacheError = action.payload?.message || 'Failed to retrieve cache statistics';
            })

            // Cleanup cache thunk cases
            .addCase(cleanupCacheThunk.pending, (state) => {
                state.cacheStatus = RequestStatus.LOADING;
                state.cacheError = null;
            })
            .addCase(cleanupCacheThunk.fulfilled, (state) => {
                state.cacheStatus = RequestStatus.SUCCEEDED;
            })
            .addCase(cleanupCacheThunk.rejected, (state, action) => {
                state.cacheStatus = RequestStatus.FAILED;
                state.cacheError = action.payload?.message || 'Failed to cleanup cache';
            })

            // Get saved filter set thunk cases
            .addCase(getSavedFilterSetThunk.pending, (state) => {
                state.savedFilterStatus = RequestStatus.LOADING;
                state.savedFilterError = null;
            })
            .addCase(getSavedFilterSetThunk.fulfilled, (state, action) => {
                state.savedFilterStatus = RequestStatus.SUCCEEDED;
                if (action.payload.data) {
                    state.currentFilterSet = action.payload.data;
                }
            })
            .addCase(getSavedFilterSetThunk.rejected, (state, action) => {
                state.savedFilterStatus = RequestStatus.FAILED;
                state.savedFilterError = action.payload?.message || 'Failed to retrieve filter set';
            })

            // Get global filters thunk cases
            .addCase(getGlobalFiltersThunk.pending, (state) => {
                state.savedFilterStatus = RequestStatus.LOADING;
                state.savedFilterError = null;
            })
            .addCase(getGlobalFiltersThunk.fulfilled, (state, action) => {
                state.savedFilterStatus = RequestStatus.SUCCEEDED;
                if (action.payload.data) {
                    state.globalFilters = action.payload.data || [];
                }
            })
            .addCase(getGlobalFiltersThunk.rejected, (state, action) => {
                state.savedFilterStatus = RequestStatus.FAILED;
                state.savedFilterError = action.payload?.message || 'Failed to retrieve global filters';
            })

            .addCase(getCreatorListsThunk.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getCreatorListsThunk.fulfilled, (state, action) => {
                state.loading = false;
                state.creatorLists = action.payload.data.lists || [];
                state.totalLists = action.payload.data.lists.length || 0;
                state.successMessage = action.payload.message;
            })
            .addCase(getCreatorListsThunk.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload.message;
            })

            // Create Creator List
            .addCase(createCreatorListThunk.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(createCreatorListThunk.fulfilled, (state, action) => {
                state.loading = false;
                state.creatorLists.unshift(action.payload.data); // optional: add to start of list
                state.successMessage = action.payload.message;
            })
            .addCase(createCreatorListThunk.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload.message;
            })

            // Add Member to List
            .addCase(addMemberToListThunk.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(addMemberToListThunk.fulfilled, (state, action) => {
                state.loading = false;
                // state.listMembers.push(action.payload.data); // append new member
                state.successMessage = action.payload.message;
            })
            .addCase(addMemberToListThunk.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload.message;
            })

            // Get List Members
            .addCase(getListMembersThunk.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getListMembersThunk.fulfilled, (state, action) => {
                state.loading = false;
                state.listMembers = action.payload.data.members || [];
                state.totalMembers = action.payload.data.total || 0;
                state.successMessage = action.payload.message;
            })
            .addCase(getListMembersThunk.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload.message;
            })
    },
});

// Export synchronous actions
export const {
    setSelectedBrand,
    setActiveFilters,
    setSearchQuery,
    clearSearchResults,
    clearErrors,
    clearBrandError,
    clearSearchError,
    clearFilterError,
    clearSavedFilterError,
    clearCacheError,
    setCurrentFilterSet,
    clearTransformedFilters,
    clearAllBrandState
} = brandSlice.actions;

// Export thunks
export {
    getBrandInfoThunk,
    createBrandThunk,
    requestBrandAccessThunk,
    searchCreatorsThunk,
    getFilterMetadataThunk,
    transformFiltersThunk,
    invalidateFilterCacheThunk,
    getCacheStatsThunk,
    cleanupCacheThunk,
    getSavedFiltersThunk,
    getSavedFilterSetThunk,
    createSavedFilterThunk,
    updateSavedFilterThunk,
    deleteSavedFilterThunk,
    getGlobalFiltersThunk
};

// Export the reducer
export default brandSlice.reducer;
