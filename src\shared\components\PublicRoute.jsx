import { useSelector } from 'react-redux';
import { Navigate, useLocation } from 'react-router-dom';

/**
 * PublicRoute component that redirects authenticated users away from auth pages
 * Ensures authenticated users are redirected to their appropriate dashboard
 */
const PublicRoute = ({ children, userType }) => {
  const {
    isAuthenticated,
    allocatedBrands,
    organizationBrands,
    socialProfiles,
    // user,
  } = useSelector(state => state.auth);
  const location = useLocation();

  // Define paths where redirection should be skipped even if authenticated
  const exceptionPrefixes = [
    '/socialsignup',
    '/brand/brand-selection',
    '/social-signup-followup',
    '/profile-onboarding',
  ];

  // If not authenticated, allow access
  if (!isAuthenticated) return children;

  // Allow access to exception routes even if authenticated
  const currentPath = location.pathname;
  const isExceptionRoute = exceptionPrefixes.some(prefix => currentPath.startsWith(prefix));
if (isExceptionRoute) {
  return children;
}

  // Handle post-login redirection if a "from" path is provided
  const from = location.state?.from?.pathname;
  if (from && from !== currentPath) {
    return <Navigate to={from} replace />;
  }

  // Determine redirect destination
  let redirectTo;

  const isBrandUser =
    (allocatedBrands && allocatedBrands.length > 0) ||
    (organizationBrands && organizationBrands.length > 0);
  const isInfluencerUser =
    (socialProfiles && socialProfiles.length > 0)
  // ||
  // user?.email?.includes('gmail');

  if (isBrandUser) {
    redirectTo = '/brand/dashboard';
  } else if (isInfluencerUser) {
    redirectTo = '/influencer/dashboard';
  }
  // else {
  //   redirectTo = currentPath.includes('/brand')
  //     ? '/brand/dashboard'
  //     : '/influencer/dashboard';
  // }
  // }

  return <Navigate to={redirectTo} replace />;
};

export default PublicRoute;
