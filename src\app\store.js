import { configureStore } from '@reduxjs/toolkit';
import authReducer from '../features/auth/service/authSlice';
import brandReducer from '../features/brand/services/brandSlice';
import influencerReducer from '../features/influencer/services/influencerSlice';
import campaignReducer from '../features/brand/services/campaignSlice';
import systemReducer from './store/slices/systemSlice';

/**
 * Configure Redux store with middleware and dev tools
 */
const store = configureStore({
  reducer: {
    system: systemReducer,
    auth: authReducer,
    brand: brandReducer,
    influencer: influencerReducer,
    campaign: campaignReducer,
    // Add additional reducers here as your app grows
  },
  middleware: (getDefaultMiddleware) => 
    getDefaultMiddleware({
      serializableCheck: {
        // Ignore specific paths for non-serializable data if needed
        ignoredActions: ['auth/setCredentials'],
        ignoredPaths: ['auth.token'],
      },
    }),
  devTools: import.meta.env.MODE !== 'production', 
});

export default store;
