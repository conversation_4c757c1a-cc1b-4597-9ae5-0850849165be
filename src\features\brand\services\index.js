/**
 * Brand Services Index
 * 
 * Central export point for all brand-related services, actions, and selectors.
 * This provides a clean interface for consuming brand functionality throughout the application.
 */

// === CORE SERVICES ===
export { default as brandReducer } from './brandSlice';
export { default as useBrandActions } from './brandActions';
export { default as useBrandSelectors } from './brandSelectors';

// === THUNKS ===
export {
    getBrandInfoThunk,
    createBrandThunk,
    requestBrandAccessThunk,
    searchCreatorsThunk,
    getFilterMetadataThunk,
    transformFiltersThunk,
    invalidateFilterCacheThunk,
    getCacheStatsThunk,
    cleanupCacheThunk,
    getSavedFiltersThunk,
    getSavedFilterSetThunk,
    createSavedFilterThunk,
    updateSavedFilterThunk,
    deleteSavedFilterThunk,
    getGlobalFiltersThunk
} from './brandThunks';

// === SLICE ACTIONS ===
export {
    setSelectedBrand,
    setActiveFilters,
    setSearchQuery,
    clearSearchResults,
    clearErrors,
    clearBrandError,
    clearSearchError,
    clearFilterError,
    clearSavedFilterError,
    clearCacheError,
    setCurrentFilterSet,
    clearTransformedFilters,
    clearAllBrandState
} from './brandSlice';

// === STANDALONE SELECTORS ===
export {
    selectAllocatedBrands,
    selectOrganizationBrands,
    selectSearchResults,
    selectFilterMetadata,
    selectSavedFilters,
    selectGlobalFilters,
    selectCurrentFilterSet,
    selectTransformedFilters,
    selectCacheStats,
    selectCacheStatus,
    selectBrandStatus,
    selectSearchStatus,
    selectIsBrandLoading
} from './brandSelectors';

/**
 * Brand Service Factory
 * Provides a convenient way to access all brand services
 */
export const brandServices = {
    // Hooks
    useBrandActions,
    useBrandSelectors,
    
    // Thunks
    getBrandInfo: getBrandInfoThunk,
    createBrand: createBrandThunk,
    requestBrandAccess: requestBrandAccessThunk,
    searchCreators: searchCreatorsThunk,
    getFilterMetadata: getFilterMetadataThunk,
    getSavedFilters: getSavedFiltersThunk,
    createSavedFilter: createSavedFilterThunk,
    updateSavedFilter: updateSavedFilterThunk,
    deleteSavedFilter: deleteSavedFilterThunk,
};

/**
 * Default export for backward compatibility
 */
export default {
    // Services
    ...brandServices,
    
    // Reducer
    brandReducer,
};
