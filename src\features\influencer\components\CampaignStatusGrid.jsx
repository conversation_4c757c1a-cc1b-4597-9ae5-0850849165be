import React from 'react';
import { TbFileText, Tb<PERSON><PERSON>, Tb<PERSON><PERSON>, Tb<PERSON><PERSON><PERSON>, TbX, TbFileUpload } from "react-icons/tb";

const cardData = [
  {
    id: 1,
    brand: "StreamFlix",
    title: "Summer Streaming Extravaganza",
    status: "Approved",
    date: "23 April 2024 | 4:20 pm",
    type: "Post",
    remarks: "Almost there! Just a few tweaks needed",
    buttonText: "Publish",
    showEdit: true
  },
  {
    id: 2,
    brand: "StreamFlix",
    title: "Summer Streaming Extravaganza",
    status: "Submitted",
    date: "23 April 2024 | 4:20 pm",
    type: "Shorts",
    remarks: "",
    buttonText: "Publish",
    showEdit: true
  },
  {
    id: 3,
    brand: "StreamFlix",
    title: "Summer Streaming Extravaganza",
    status: "Approved",
    date: "23 April 2024 | 4:20 pm",
    type: "Post",
    remarks: "All clear! Feel free to publish.",
    buttonText: "Publish",
    showEdit: true
  },
  {
    id: 4,
    brand: "StreamFlix",
    title: "Summer Streaming Extravaganza",
    status: "Scheduled",
    date: "23 April 2024 | 4:20 pm",
    type: "Video",
    remarks: "Fantastic! You're all set to publish.",
    buttonText: "Publish Now",
    showEdit: false
  },
  {
    id: 5,
    brand: "Netflix",
    title: "Summer Promotional Campaign",
    status: "Posted",
    date: "23 April 2024 | 4:20 pm",
    type: "Reel",
    remarks: "Looks good! Go ahead and publish.",
    buttonText: "Delete",
    showEdit: false
  },
  {
    id: 6,
    brand: "Netflix",
    title: "Summer Promotional Campaign",
    status: "Approved",
    date: "23 April 2024 | 4:20 pm",
    type: "Story, Post, Reel, Shorts, Video",
    remarks: "Great job! It’s ready for publishing.",
    buttonText: "Publish",
    showEdit: true
  },
  {
    id: 7,
    brand: "Netflix",
    title: "Summer Promotional Campaign",
    status: "Reject",
    date: "23 April 2024 | 4:20 pm",
    type: "Story, Post, Reel, Shorts, Video",
    remarks: "Not quite ready yet. Please review.",
    buttonText: "Publish",
    showEdit: true
  },
  {
    id: 8,
    brand: "Netflix",
    title: "Summer Promotional Campaign",
    status: "Draft",
    date: "23 April 2024 | 4:20 pm",
    type: "Story, Post, Reel, Shorts, Video",
    remarks: "",
    buttonText: "Edit",
    showEdit: false
  }
];

const statusColors = {
  Approved: "bg-green-100 text-green-700",
  Submitted: "bg-purple-100 text-purple-700",
  Scheduled: "bg-orange-100 text-orange-700",
  Posted: "bg-blue-100 text-blue-700",
  Reject: "bg-red-100 text-red-700",
  Draft: "bg-gray-100 text-gray-700",
};

const CampaignStatusGrid = () => {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
      {cardData.map((card) => (
        <div key={card.id} className="rounded-xl border border-gray-600 p-4 bg-gray-800 text-white flex flex-col justify-between">
          <div>
            <div className={`w-fit px-2 py-1 rounded-full text-12-medium ${statusColors[card.status]}`}>
              {card.status}
            </div>
            <p className="text-12-medium text-gray-400 mt-1">Published on {card.date}</p>
            <h3 className="mt-2 text-16-bold">{card.brand}</h3>
            <p className="text-12-medium text-gray-400 mb-3">{card.title}</p>

            <div className="h-[100px] w-full bg-gray-700 rounded-md mb-3"></div>

            <p className="text-14-medium text-gray-100 mb-2">{card.type}</p>

            <p className="text-12-regular text-gray-300 min-h-[48px]">{card.remarks || "-"}</p>
          </div>

          <div className="flex justify-between items-center mt-4 gap-2">
            {card.showEdit && (
              <button className="px-4 py-2 bg-transparent border border-gray-500 rounded-lg text-gray-100 hover:bg-gray-700">
                Edit
              </button>
            )}
            <button className="px-4 py-2 bg-brand-500 rounded-lg text-white hover:bg-brand-600 transition">
              {card.buttonText}
            </button>
          </div>
        </div>
      ))}
    </div>
  );
};

export default CampaignStatusGrid;
