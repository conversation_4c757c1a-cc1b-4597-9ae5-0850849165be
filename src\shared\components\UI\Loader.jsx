import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>oa<PERSON>, <PERSON>Loader } from 'react-spinners';

/**
 * Loader component variants
 * @typedef {'clip' | 'pulse' | 'beat' | 'overlay'} LoaderVariant
 */

/**
 * Universal loader component that can be used in various contexts
 * @param {object} props
 * @param {LoaderVariant} [props.variant='clip'] - The loader style to use 
 * @param {string} [props.color='#4f46e5'] - CSS color of the loader
 * @param {string|number} [props.size='25'] - Size of the loader
 * @param {boolean} [props.loading=true] - Whether the loader should be visible
 * @param {string} [props.text] - Optional text to show with the loader
 * @param {string} [props.className] - Additional CSS classes
 */
const Loader = ({
  variant = 'clip',
  color = '#4f46e5',
  size = '25', 
  loading = true,
  text,
  className = '',
}) => {
  // If not loading, don't render anything
  if (!loading) return null;

  // Render spinner based on variant
  const renderSpinner = () => {
    switch (variant) {
      case 'pulse':
        return <PulseLoader color={color} size={size} />;
      case 'beat':
        return <BeatLoader color={color} size={size} />;
      case 'overlay':
        return (
          <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50">
            <div className="bg-white p-5 rounded-lg flex flex-col items-center">
              <ClipLoader color={color} size={size} />
              {text && <p className="mt-3 text-gray-700">{text}</p>}
            </div>
          </div>
        );
      case 'clip':
      default:
        return <ClipLoader color={color} size={size} />;
    }
  };

  // If it's not an overlay, render a simple container
  if (variant !== 'overlay') {
    return (
      <div className={`flex items-center justify-center ${className}`}>
        {renderSpinner()}
        {text && <span className="ml-2">{text}</span>}
      </div>
    );
  }

  // For overlay, the container is already in the renderSpinner function
  return renderSpinner();
};

/**
 * PageLoader component to use when loading an entire page
 */
export const PageLoader = () => (
  <div className="min-h-screen flex items-center justify-center">
    <Loader variant="pulse" text="Loading page..." />
  </div>
);

/**
 * ButtonLoader component to use inside buttons
 */
export const ButtonLoader = ({ color = 'white', size = 10 }) => (
  <Loader variant="beat" color={color} size={size} className="inline-flex ml-2" />
);

/**
 * OverlayLoader that covers the entire screen
 */
export const OverlayLoader = ({ text = 'Processing...' }) => (
  <Loader variant="overlay" text={text} />
);

export default Loader;
