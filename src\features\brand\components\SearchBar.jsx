import React, { useState, useRef, useEffect } from 'react';

const SearchInput = ({ onSearch, placeholder = "Search creators..." }) => {
  const [isActive, setIsActive] = useState(false);
  const [inputValue, setInputValue] = useState('');
  const inputRef = useRef(null);
  const [selections, setSelections] = useState([]);
  
  // Handle outside click to close the active input
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (inputRef.current && !inputRef.current.contains(event.target)) {
        if (inputValue.trim() !== '') {
          addSelection(inputValue);
        }
        setIsActive(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [inputValue]);

  const addSelection = (value) => {
    if (value.trim() !== '' && !selections.includes(value)) {
      setSelections([...selections, value]);
      setInputValue('');
      if (onSearch) {
        onSearch([...selections, value]);
      }
    }
  };

  const removeSelection = (value) => {
    const newSelections = selections.filter(item => item !== value);
    setSelections(newSelections);
    if (onSearch) {
      onSearch(newSelections);
    }
  };

  const handleKeyDown = (e) => {
    if (e.key === 'Enter' && inputValue.trim() !== '') {
      addSelection(inputValue);
    } else if (e.key === 'Backspace' && inputValue === '' && selections.length > 0) {
      const lastSelection = selections[selections.length - 1];
      removeSelection(lastSelection);
    }
  };

  return (
    <div className="w-full relative">
      <div 
        className={`flex flex-wrap items-center gap-2 p-2 rounded-full border transition-all ${
          isActive ? 'bg-gray-100 border-gray-300' : 'bg-transparent border-transparent'
        }`}
        onClick={() => {
          setIsActive(true);
          inputRef.current.focus();
        }}
        ref={inputRef}
      >
        {/* Selected pills */}
        {selections.map((item, index) => (
          <div 
            key={index} 
            className="px-3 py-1 bg-gray-800 text-white rounded-full flex items-center text-sm"
          >
            <span>{item}</span>
            <button 
              className="ml-1 text-white hover:text-gray-300" 
              onClick={(e) => {
                e.stopPropagation();
                removeSelection(item);
              }}
            >
              &times;
            </button>
          </div>
        ))}
        
        {/* Input field */}
        <input
          ref={inputRef}
          type="text"
          className="flex-1 bg-transparent outline-none min-w-[80px]"
          placeholder={selections.length === 0 ? placeholder : ''}
          value={inputValue}
          onChange={(e) => setInputValue(e.target.value)}
          onKeyDown={handleKeyDown}
          onFocus={() => setIsActive(true)}
        />
      </div>
      
      {/* Suggestions can be added here */}
      {isActive && (
        <div className="absolute top-full left-0 right-0 mt-1 bg-white rounded-md shadow-lg z-10 max-h-60 overflow-y-auto">
          {/* Example preset options that match the Figma design */}
          <div className="p-2">
            <div className="flex flex-wrap gap-2">
              <button 
                className="px-3 py-1 bg-gray-800 text-white rounded-full text-sm"
                onClick={() => addSelection("Recent Travel Vlogs")}
              >
                Recent Travel Vlogs
              </button>
              <button 
                className="px-3 py-1 bg-gray-800 text-white rounded-full text-sm"
                onClick={() => addSelection("Gaming Creators Open to Collabs")}
              >
                Gaming Creators Open to Collabs
              </button>
              <button 
                className="px-3 py-1 bg-gray-800 text-white rounded-full text-sm"
                onClick={() => addSelection("Gadget & App Reviewers")}
              >
                Gadget & App Reviewers
              </button>
              <button 
                className="px-3 py-1 bg-gray-800 text-white rounded-full text-sm"
                onClick={() => addSelection("Beauty Tutorial Channels")}
              >
                Beauty Tutorial Channels
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

// Usage example
export const SearchBar = () => {
  const handleSearch = (selections) => {
    console.log("Selected filters:", selections);
    // Process search/filter based on selections
  };

  return (
    <div className="max-w-3xl mx-auto p-4">
      <SearchInput 
        onSearch={handleSearch}
        placeholder="Search by category, name or interest..."
      />
    </div>
  );
};

export default SearchBar;