# Deployment

This document provides general guidelines for deploying the Creatorverse Frontend application, which is built with Vite and React.

## Building for Production

Before deployment, you need to create a production-ready build of your application. This process optimizes and minifies your code for better performance.

To build the application, run the following command:

```bash
pnpm build
```

This command will generate a `dist/` directory in your project root. This directory contains all the static assets (HTML, CSS, JavaScript, images, etc.) that need to be served by a web server.

## Serving the Application

The `dist/` directory can be served by any static file server. Here are some common options:

### 1. Static Web Servers (e.g., Nginx, Apache)

You can configure a web server like Nginx or Apache to serve the contents of the `dist/` directory. For example, with Nginx, you would configure a server block to point its `root` directive to the `dist/` directory.

```nginx
server {
    listen 80;
    server_name yourdomain.com;

    root /path/to/your/project/dist;
    index index.html;

    location / {
        try_files $uri $uri/ /index.html;
    }
}
```

### 2. Node.js Static Server (e.g., `serve` package)

For simple deployments or testing, you can use a Node.js package like `serve` to quickly serve your `dist/` directory.

First, install `serve` globally (or as a dev dependency):

```bash
pnpm install -g serve
# or
pnpm add -D serve
```

Then, run it from your project root:

```bash
serve -s dist
```

This will serve your application on `http://localhost:3000` (or another available port).

### 3. Cloud Hosting Platforms

Many cloud hosting platforms are well-suited for deploying single-page applications built with Vite. Popular choices include:

- **Netlify**: Connects directly to your Git repository, builds your project, and deploys it automatically on every push.
- **Vercel**: Similar to Netlify, offering seamless Git integration and automatic deployments.
- **GitHub Pages**: Suitable for simple static site hosting directly from your GitHub repository.
- **AWS S3 + CloudFront**: For scalable and highly available static site hosting.
- **Firebase Hosting**: Provides fast and secure hosting for your web app.

When using these platforms, you typically configure them to run `pnpm build` as the build command and set the `dist/` directory as the publish directory.

## Environment Variables

If your application uses environment variables (e.g., `VITE_API_URL`), ensure they are correctly configured in your deployment environment. Vite exposes environment variables prefixed with `VITE_` to your client-side code. These should be set as environment variables on your hosting platform.

