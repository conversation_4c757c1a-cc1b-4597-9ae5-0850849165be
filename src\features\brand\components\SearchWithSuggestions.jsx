import React, { useState, useRef, useEffect } from "react";
// eslint-disable-next-line no-unused-vars
import { motion, AnimatePresence } from "framer-motion";
import { FaMicrophone, FaMicrophoneSlash } from 'react-icons/fa';
import SpeechRecognition, { useSpeechRecognition } from 'react-speech-recognition';

import SearchIcon from '@assets/icon/ai-search.svg';

const suggestions = [
    "Recent Travel Vlogs",
    "Gaming Creators Open to Collabs",
    "Gadget & App Reviewers",
    "Beauty Tutorial Channels",
    "Fashion creators in India with 10k+ followers posting sustainable outfits"
];

const placeholders = [
    '"Fashion creators in India with 10k+ followers"',
    '"Tech influencers posting smartphone reviews"',
    '"Travel vloggers exploring South Asia"',
    '"Gaming YouTubers looking to collab"',
    '"Beauty creators sharing skincare routines"'
];

const SearchWithSuggestions = ({
    suggestionVisible = true,
    onFocus = () => { },
    onSearch = () => { },
    searchQuery: externalSearchQuery = "",
    onSearchQueryChange = () => { },
    externalSuggestions = [] // New prop for external suggestions (from API 410 responses)
}) => {
    const [searchQuery, setSearchQuery] = useState(externalSearchQuery);
    const [showSuggestions, setShowSuggestions] = useState(false);
    const [placeholderIndex, setPlaceholderIndex] = useState(0);
    const [isListening, setIsListening] = useState(false);
    const [speechError, setSpeechError] = useState('');
    const [microphonePermission, setMicrophonePermission] = useState('unknown'); // 'granted', 'denied', 'prompt', 'unknown'
    const wrapperRef = useRef(null);
    const inputRef = useRef(null);

    // Speech recognition setup
    const {
        transcript,
        listening,
        resetTranscript,
        browserSupportsSpeechRecognition
    } = useSpeechRecognition();

    // Sync with external search query
    useEffect(() => {
        setSearchQuery(externalSearchQuery);
    }, [externalSearchQuery]);

    // Handle speech recognition transcript changes
    useEffect(() => {
        if (transcript && !listening) {
            // Speech has ended, update search query
            setSearchQuery(transcript);
            onSearchQueryChange(transcript);
            // Automatically trigger search when speech ends
            if (transcript.trim()) {
                onSearch(transcript);
                setShowSuggestions(false);
            }
            // Reset transcript for next use
            resetTranscript();
        }
    }, [transcript, listening, onSearch, onSearchQueryChange, resetTranscript]);

    // Update listening state
    useEffect(() => {
        setIsListening(listening);
    }, [listening]);

    // Check microphone permissions on component mount
    useEffect(() => {
        const checkMicrophonePermission = async () => {
            if (!browserSupportsSpeechRecognition) {
                setMicrophonePermission('unsupported');
                return;
            }

            try {
                const permission = await navigator.permissions.query({ name: 'microphone' });
                setMicrophonePermission(permission.state);

                // Listen for permission changes
                permission.addEventListener('change', () => {
                    setMicrophonePermission(permission.state);
                });
            } catch (error) {
                console.error('Error checking microphone permission:', error);
                setMicrophonePermission('unknown');
            }
        };

        checkMicrophonePermission();
    }, [browserSupportsSpeechRecognition]);

    // Update suggestions when external suggestions change
    useEffect(() => {
        if (externalSuggestions.length > 0) {
            setShowSuggestions(true);
        }
    }, [externalSuggestions]);





    // Determine which suggestions to use - external suggestions take priority
    const activeSuggestions = externalSuggestions.length > 0 ? externalSuggestions : suggestions;

    const handleInputChange = (e) => {
        const value = e.target.value;
        setSearchQuery(value);
        onSearchQueryChange(value);
        setShowSuggestions(true);
    };

    const handleInputFocus = () => {
        setShowSuggestions(true);
        onFocus();
        setSpeechError("");
    };

    const handleSuggestionClick = (suggestion) => {
        setSearchQuery(suggestion);
        setShowSuggestions(false);
    };

    // Request microphone permissions
    const requestMicrophonePermission = async () => {
        try {
            // Request microphone access
            const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
            // Stop the stream immediately as we only needed permission
            stream.getTracks().forEach(track => track.stop());
            return true;
        } catch (error) {
            console.error('Microphone permission error:', error);
            if (error.name === 'NotAllowedError') {
                setSpeechError('Microphone access denied. Please click the microphone icon in your browser\'s address bar and allow microphone access.');
            } else if (error.name === 'NotFoundError') {
                setSpeechError('No microphone found. Please connect a microphone and try again.');
            } else {
                setSpeechError('Unable to access microphone. Please check your browser settings and try again.');
            }
            return false;
        }
    };

    // Handle microphone click
    const handleMicrophoneClick = async () => {
        if (!browserSupportsSpeechRecognition) {
            setSpeechError('Speech recognition is not supported in this browser. Please try typing your search instead.');
            return;
        }

        if (listening) {
            // Stop listening
            SpeechRecognition.stopListening();
            setIsListening(false);
            setSpeechError('');
            return;
        }

        // Clear any previous errors
        setSpeechError('');

        try {
            // First check if we already have permission
            const permission = await navigator.permissions.query({ name: 'microphone' });

            if (permission.state === 'denied') {
                setSpeechError('Microphone access is blocked. Please click the microphone icon in your browser\'s address bar and allow microphone access, then try again.');
                return;
            }

            // If permission is not granted yet, request it
            if (permission.state === 'prompt') {
                const hasPermission = await requestMicrophonePermission();
                if (!hasPermission) {
                    return; // Error message already set in requestMicrophonePermission
                }
            }

            // Start listening
            resetTranscript();

            SpeechRecognition.startListening({
                continuous: false,
                language: 'en-US'
            }).catch((error) => {
                console.error('Speech recognition error:', error);
                setSpeechError('Failed to start speech recognition. Please try again or type your search.');
                setIsListening(false);
            });

            setIsListening(true);

            // Auto-stop after 10 seconds to prevent hanging
            setTimeout(() => {
                if (listening) {
                    SpeechRecognition.stopListening();
                    setIsListening(false);
                }
            }, 10000);

        } catch (error) {
            console.error('Permission check error:', error);
            setSpeechError('Unable to access microphone. Please check your browser settings and try again.');
        }
    };

    // Close on click outside
    useEffect(() => {
        const handleClickOutside = (e) => {
            if (wrapperRef.current && !wrapperRef.current.contains(e.target)) {
                setShowSuggestions(false);
            }
        };
        document.addEventListener("mousedown", handleClickOutside);
        return () => document.removeEventListener("mousedown", handleClickOutside);
    }, []);

    // Cycle placeholder every 4s
    useEffect(() => {
        const interval = setInterval(() => {
            setPlaceholderIndex((prev) => (prev + 1) % placeholders.length);
        }, 4000);
        return () => clearInterval(interval);
    }, []);

    return (
        <div
            ref={wrapperRef}
            className="relative max-w-3xl mx-auto w-full transition-transform"
        >
            <div
                className={`rounded-lg p-0.5 transition-all duration-30 ${showSuggestions || document.activeElement === inputRef.current
                    ? "border-[#47c8ec] shadow-[0_0_9px_rgba(72,200,236,0.8)]"
                    : "border-gray-400"
                    }`}
                style={{
                    borderWidth: "2px"
                }}
            >
                <div className="relative">
                    <img
                        src={SearchIcon}
                        alt="Search"
                        className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5"
                    />

                    {/* Animated Placeholder */}
                    {!searchQuery && (
                        <div>
                            <span className="absolute left-10 top-1/2 transform -translate-y-1/2 text-gray-400 pointer-events-none h-6">
                                Search for
                            </span>
                            <div className="absolute left-29 top-1/2 transform -translate-y-1/2 text-gray-400 pointer-events-none h-6 overflow-hidden w-[90%]">
                                <AnimatePresence mode="wait">
                                    <motion.div
                                        key={placeholders[placeholderIndex]}
                                        initial={{ y: 20, opacity: 0 }}
                                        animate={{ y: 0, opacity: 1 }}
                                        exit={{ y: -20, opacity: 0 }}
                                        transition={{ duration: 0.4 }}
                                        className="absolute"
                                    >
                                        {placeholders[placeholderIndex]}
                                    </motion.div>
                                </AnimatePresence>
                            </div>
                        </div>
                    )}

                    <input
                        ref={inputRef}
                        type="text"
                        value={searchQuery}
                        onChange={handleInputChange}
                        onFocus={handleInputFocus}
                        onBlur={() => setShowSuggestions(false)}
                        onKeyDown={(e) => {
                            if (e.key === 'Enter') {
                                e.preventDefault(); // Optional: prevent form submission
                                onSearch(searchQuery);
                                setShowSuggestions(false);
                            }
                        }}
                        className="w-full bg-gray-900 rounded-md py-3 pl-10 pr-12 text-white focus:outline-none"
                        placeholder=""
                    />

                    {/* Microphone Icon */}
                    <div
                        className={`absolute right-3 top-1/2 transform -translate-y-1/2 transition-all duration-200 ${microphonePermission === 'unsupported'
                                ? 'text-gray-600 cursor-not-allowed'
                                : microphonePermission === 'denied'
                                    ? 'text-red-2 cursor-pointer hover:text-red-300'
                                    : listening
                                        ? 'text-red-2 animate-pulse cursor-pointer'
                                        : isListening
                                            ? 'text-blue-500 cursor-pointer'
                                            : 'text-gray-400 hover:text-white cursor-pointer'
                            }`}
                        onClick={microphonePermission === 'unsupported' ? undefined : handleMicrophoneClick}
                        title={
                            microphonePermission === 'unsupported'
                                ? 'Speech recognition not supported in this browser'
                                : microphonePermission === 'denied'
                                    ? 'Microphone access denied. Click to request permission.'
                                    : listening
                                        ? 'Stop listening'
                                        : microphonePermission === 'prompt'
                                            ? 'Click to allow microphone access and start voice search'
                                            : 'Start voice search'
                        }
                    >
                        {listening ||  microphonePermission === 'denied' ? (
                            <FaMicrophoneSlash className="w-5 h-5" />
                        ) : (
                            <FaMicrophone className="w-5 h-5" />
                        )}
                    </div>
                </div>

                {/* Microphone Permission Blocked */}
                {microphonePermission === 'denieds' && !speechError && !listening && (
                    <div className="mt-2 p-3 bg-orange-500/20 border border-orange-500/50 rounded-md">
                        <div className="text-orange-400 text-sm">
                            <p className="flex items-center gap-2 font-semibold mb-2">
                                <FaMicrophone className="text-orange-400" />
                                Microphone Access Blocked
                            </p>
                            <div className="text-orange-300 text-xs space-y-1 mb-3">
                                <p>🔧 To enable voice search:</p>
                                <p>1. Click the 🔒 or 🎤 icon in your address bar</p>
                                <p>2. Change "Microphone" to "Allow"</p>
                                <p>3. Click the refresh button below</p>
                            </div>
                            <button
                                onClick={() => window.location.reload()}
                                className="px-3 py-1 bg-orange-500 hover:bg-orange-600 text-white text-xs rounded transition-colors duration-200"
                            >
                                🔄 Refresh Page
                            </button>
                        </div>
                    </div>
                )}

                {/* Microphone Permission Prompt */}
                {microphonePermission === 'prompt' && !speechError && !listening && (
                    <div className="mt-2 p-3 bg-blue-500/20 border border-blue-500/50 rounded-md">
                        <p className="text-blue-400 text-sm flex items-center gap-2">
                            <FaMicrophone className="text-blue-400" />
                            Click the microphone icon to enable voice search
                        </p>
                    </div>
                )}

                {/* Speech Recognition Error */}
                {speechError && (
                    <div className="mt-2 p-3 bg-red-500/20 border border-red-500/50 rounded-md">
                        <p className="text-red-400 text-sm">{speechError}</p>
                        {microphonePermission === 'denied' && (
                            <div className="text-red-300 text-xs mt-2 space-y-1">
                                <p className="font-semibold">🔧 How to enable microphone:</p>
                                <p>1. Click the 🔒 or 🎤 icon in your browser's address bar</p>
                                <p>2. Change "Microphone" from "Block" to "Allow"</p>
                                <p>3. Refresh this page</p>
                                <p className="text-yellow-300 mt-1">💡 Or try: Browser Settings → Privacy → Site Settings → Microphone</p>
                            </div>
                        )}
                    </div>
                )}

                {/* Speech Recognition Status */}
                {listening && (
                    <div className="mt-2 p-3 bg-blue-500/20 border border-blue-500/50 rounded-md">
                        <p className="text-blue-400 text-sm flex items-center gap-2">
                            <FaMicrophone className="animate-pulse" />
                            Listening... Speak now
                        </p>
                    </div>
                )}

                {/* Suggestions */}
                {suggestionVisible && (
                    <AnimatePresence>
                        {showSuggestions && activeSuggestions.length > 0 && (
                            <motion.div
                                key="suggestion-container"
                                initial={{ height: 0, opacity: 0 }}
                                animate={{ height: "auto", opacity: 1 }}
                                exit={{ height: 0, opacity: 0 }}
                                transition={{ duration: 0.3 }}
                                className="overflow-hidden"
                            >
                                <div className="flex gap-2 px-2 py-2 bg-[#47C8EC]/15 border-t-2 border-[#47C8EC]/15 overflow-y-auto scrollbar-none">
                                    {activeSuggestions.map((suggestion, index) => (
                                        <button
                                            key={index}
                                            onMouseDown={(e) => {
                                                e.preventDefault(); // optional, prevents focus loss
                                                handleSuggestionClick(suggestion);
                                            }}

                                            className="cursor-pointer text-sm h-10 whitespace-nowrap text-gray-200 bg-transparent border border-gray-400 px-3 py-1.5 rounded-full hover:bg-gray-200/15 transition"
                                        >
                                            {suggestion}
                                        </button>
                                    ))}
                                </div>
                            </motion.div>
                        )}
                    </AnimatePresence>
                )}
            </div>
        </div>
    );

};

export default SearchWithSuggestions;
