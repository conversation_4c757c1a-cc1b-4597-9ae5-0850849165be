import React, { useState, useRef, useEffect } from 'react';
import { FaChevronDown, FaChevronUp, FaCheck } from 'react-icons/fa';
import { useSelector } from 'react-redux';
import { selectAllChannelsWithIcons } from '@/app/store/slices/systemSlice';

export default function PlatformSelector({ value = 'instagram', onChange, className }) {
    const channels = useSelector((state) => selectAllChannelsWithIcons(state));
    const [platforms, setPlatforms] = useState([]);
    const [isOpen, setIsOpen] = useState(false);
    const ref = useRef(null);

    useEffect(() => {
        if (channels && channels.length) {
            console.log('Channels updated:', channels);
            const mapped = channels.map((ch) => ({
                id: ch.name.toLowerCase(),
                label: ch.name,
                icon: ch.icon.url,
                color: 'text-white',
            }));
            setPlatforms(mapped);
        }
    }, [channels]);

    useEffect(() => {
        const handleClickOutside = (e) => {
            if (ref.current && !ref.current.contains(e.target)) {
                setIsOpen(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => document.removeEventListener('mousedown', handleClickOutside);
    }, []);

    const current = platforms.find((p) => p.id === value) || null;

    return (
        <div className={`relative cursor-pointer ${className}`} ref={ref}>
            {/* Trigger */}
            <button
                className="flex h-full items-center gap-2 px-4 py-2 rounded-lg border border-gray-600"
                onClick={() => setIsOpen(!isOpen)}
            >
                {current ? (
                    <>
                        <img src={current.icon} alt={current.label} className="w-7 h-7" />
                        {isOpen ? (
                            <FaChevronUp className="text-white text-xs" />
                        ) : (
                            <FaChevronDown className="text-white text-xs" />
                        )}
                    </>
                ) : (
                    null
                )}
            </button>

            {/* Dropdown */}
            {isOpen && (
                <div className="absolute mt-2 bg-[#1E1E1E] rounded-lg shadow-lg py-1 z-50 w-[180px] border border-gray-700 p-1">
                    {platforms.map((platform, idx) => (
                        <div key={platform.id} className="p-1">
                            <button
                                className={`rounded-md hover:scale-103 w-full flex items-center justify-between px-2 py-1 hover:bg-gray-900 transition-colors ${value === platform.id ? 'bg-[#2C2C2C]' : ''}`}
                                onClick={() => {
                                    onChange(platform.id);
                                    setIsOpen(false);
                                }}
                            >
                                <div className="flex items-center gap-3">
                                    <img src={platform.icon} alt={platform.label} className="w-6 h-6" />
                                    <span className="text-white font-medium">{platform.label}</span>
                                </div>
                                {value === platform.id && <FaCheck className="text-green-500" />}
                            </button>
                            {idx < platforms.length - 1 && <div className="border-t border-gray-700 mx-2 mt-2" />}
                        </div>
                    ))}
                </div>
            )}
        </div>
    );
}
