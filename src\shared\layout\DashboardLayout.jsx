import React, {useState} from "react";
import { Outlet, useNavigate } from "react-router-dom";
import Navbar from "@shared/components/Navbar";
import search from "@assets/icon/nav/search.svg";
import logo from "@assets/social-card2.svg";
import notificationBell from "@assets/icon/notifications.svg";
import { Input } from "../components/UI/input";
import useAuthActions from "../../features/auth/service/authActions";
import BrandSelector from "../components/BrandSelector";

const Header = ({ onLogout, isLoggingOut, type}) => (
    <header className="flex items-center justify-between h-20 px-6 py-4 bg-black text-white border-gray-800 ">
        <div className="flex items-center space-x-3">
            <img src={logo} alt="Logo" className="h-8 w-8 object-contain bg-white rounded-xl" />
            <h2 className="text-xl font-semibold">Creatorverse</h2>
        </div>
        <div className="flex items-center space-x-4">
            <img src={notificationBell} alt="Notifications" className="h-6 w-6 object-contain" />
            <Input
                type="text"
                icon={search}
                placeholder="Search"
                className="hidden px-3 py-1 bg-black text-sm placeholder-gray-400 focus:outline-none"
            />
            <button
                onClick={onLogout}
                disabled={isLoggingOut}
                className="hidden px-8 py-2 bg-brand-500 text-white rounded hover:bg-brand-600 transition disabled:opacity-50 disabled:cursor-not-allowed"
            >
                {isLoggingOut ? 'Logging out...' : 'Logout'}
            </button>
            {type === 'brand' && (
                <BrandSelector />
            )}
        </div>
    </header>
);

const DashboardLayout = ({ type }) => {
    const navigate = useNavigate();
    const { logout } = useAuthActions();
    // const { isLoading } = useAuthSelectors();
    const [isLoading, setIsLoading] = useState(false);

    const handleLogout = async () => {
        try {
            setIsLoading(true);
            await logout();
            setIsLoading(false);
            // Redirect to appropriate login page based on user type
            const loginPath = type === 'brand' ? '/brand/signin' : '/influencer/login';
            navigate(loginPath, { replace: true });
        } catch (error) {
            // Even if logout fails, redirect to login for security
            console.error('Logout error:', error);
            const loginPath = type === 'brand' ? '/brand/signin' : '/influencer/login';
            navigate(loginPath, { replace: true });
        }
    };

    return (
        <div className="bg-bg flex flex-col h-screen box-border min-h-[630px]  overflow-y-auto">
            <Header onLogout={handleLogout} isLoggingOut={isLoading} type={type} />
            <div className="flex-grow min-h-0 grid grid-rows-1 grid-cols-[5rem_1fr] h-full">
                {/* Sidebar with fixed width */}
                <div className="relative w-20 h-full col-start-1 col-end-2 row-start-1 row-end-2">
                    <Navbar type={type} />
                </div>

                {/* Main content takes the rest */}
                <main className="flex justify-center h-full col-start-2 col-end-3 row-start-1 row-end-2 p-6 overflow-x-auto overflow-y-auto bg-primary text-white rounded-tl-lg rounded-bl-lg shadow-inner ">
                    <Outlet />
                </main>
            </div>
        </div>
    );
};

export default DashboardLayout;
