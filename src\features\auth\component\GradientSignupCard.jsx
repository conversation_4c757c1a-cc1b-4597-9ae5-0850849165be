import React from "react";
import { Link } from "react-router-dom";

const GradientSignupCard = ({
  to,
  label = "Business",
  description = "",
  images = [],
  className = "",
  ...props
}) => {
  return (
    <Link
      to={to}
      className={`${
        label === "Creator"
          ? "creator-gradient-border shadow-creator-glow"
          : "business-gradient-border shadow-glow"
      } block py-5 md:px-10 md:p-5 rounded-xl text-left w-full bg-gray-900   ${className}`}
      {...props}
    >
      <div className="flex justify-between w-full group px-2 ">
        <div className="flex flex-col w-3/5  transition-transform duration-500 ease-in-out group-hover:-translate-x-2">
          <p className="text-white text-lg font-semibold leading-tight whitespace-nowrap">
            Join as a{" "}
            <span
              className={`${
                label === "Creator" ? "text-[#FF7850]" : "text-[#48C9EC]"
              }`}
            >
              {label}!

            </span>
          </p>
          <p className="text-gray-400 text-12-regular mr-5 mt-1">
            {description}
          </p>
        </div>
        <div className="flex  items-center w-2/5 mr-2 md:mr-0 md:ml-4 group">
          {images.map((img, idx) => (
            <img
              key={idx}
              src={img.src}
              alt={img.alt || ""}
              className={`w-12 h-12 rounded-full ${img.className || ""}`}
            />
          ))}
        </div>
      </div>
    </Link>
  );
};

export default GradientSignupCard;