import React from 'react';
import {
    TbLayoutGrid,        // All
    TbCheck,             // Approved
    TbFileUpload,        // Submitted
    TbX,                 // Rejected
    TbClock,             // Scheduled
    TbSend,              // Posted
    TbFileText           // Draft
} from "react-icons/tb"; // or from other icon sets if preferred
import { FaInstagram } from "react-icons/fa";
import { useDispatch, useSelector } from 'react-redux';
import { selectAllChannelsWithIcons } from '@/app/store/slices/systemSlice';



const statusColors = {
    approved: "bg-green-100 text-green-700",
    pending: "bg-purple-100 text-purple-700",
    posted: "bg-blue-100 text-blue-700",
    rejected: "bg-red-100 text-red-700",
    // Draft: "bg-gray-100 text-gray-700",
};

const statusIcons = {
    approved: <TbCheck />,
    pending: <TbFileUpload />,
    // posted: <TbClock />,
    posted: <TbSend />,
    rejected: <TbX />,
    // Draft: <TbFileText />,
};

function getTimeAgo(dateString) {
    const now = new Date();
    const date = new Date(dateString);
    const diff = Math.floor((now - date) / 1000); // in seconds
    if (isNaN(diff)) return "";
    if (diff < 60) return `${diff} min ago`;
    if (diff < 3600) return `${Math.floor(diff / 60)} min ago`;
    if (diff < 86400) return `${Math.floor(diff / 3600)} hr ago`;
    if (diff < 604800) return `${Math.floor(diff / 86400)} day${Math.floor(diff / 86400) > 1 ? 's' : ''} ago`;
    if (diff < 2592000) return `${Math.floor(diff / 604800)} week${Math.floor(diff / 604800) > 1 ? 's' : ''} ago`;
    if (diff < 31536000) return `${Math.floor(diff / 2592000)} month${Math.floor(diff / 2592000) > 1 ? 's' : ''} ago`;
    return `${Math.floor(diff / 31536000)} year${Math.floor(diff / 31536000) > 1 ? 's' : ''} ago`;
}

const ContentCard = ({ card, onPreview }) => {

    const channels = useSelector((state) => selectAllChannelsWithIcons(state));

    return (
        <div className="rounded-xl border border-gray-400 bg-gray-900 text-white flex flex-col">
            <div className={`w-full px-2 py-2 capitalize rounded-tl-lg rounded-tr-lg text-14-medium flex justify-center items-center gap-1 ${statusColors[card.status]}`}>
                {statusIcons[card.status]}

                {card.status}
            </div>
            <div className="flex flex-col p-3 gap-5">
                <div className="flex items-center gap-2">
                    <img src={card.profileImage} className="w-7.5 h-7.5 rounded-full" alt="Profile Image" />
                    <div className="flex flex-col">
                        <span className="text-16-medium text-white">{card.userName}</span>
                    </div>
                </div>
                <div className="bg-gray-700 rounded-md h-[250px]"></div>
                <p className="text-14-regular text-gray-50 line-clamp-2 py-1">{card.caption}</p>
                <div className='flex justify-between gap-2'>
                    <div className="text-gray-300 rounded-full px-2 py-1 flex gap-1 text-14-regular border-1 border-gray-400">
                        {channels.find(channel => channel.name.toLowerCase() === card.platform)?.icon.url ? (
                            <img src={channels.find(channel => channel.name.toLowerCase() === card.platform)?.icon.url} className="h-4 w-4" alt={card.platform} />
                        ) : null
                        }
                        <span>{card.type}</span>
                    </div>
                    <div className="text-gray-300 px-3 py-1 flex h-6 text-12-regular">
                        {card.submittedDate ? (
                            card.approvedDate ? (
                                <span>Approved On: {getTimeAgo(card.approvedDate)}</span>
                            ) : card.rejectedDate ? (
                                <span>Sent for rework: {getTimeAgo(card.rejectedDate)}</span>
                            ) : (
                                <span>Submitted: {getTimeAgo(card.submittedDate)}</span>
                            )
                        ) : null}
                    </div>

                </div>
                <button
                    onClick={() => onPreview?.(card)} // Call preview function with card data
                    className="w-full px-4 py-2 bg-transparent border border-gray-500 rounded-lg text-gray-100 hover:bg-gray-800 cursor-pointer"
                >
                    Preview
                </button>
            </div>

        </div>
    );
};

export default ContentCard;
