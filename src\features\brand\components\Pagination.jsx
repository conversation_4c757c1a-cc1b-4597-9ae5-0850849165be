import React from 'react'

// Import icons
import LeftIcon from '@assets/icon/arrow-left.svg'

const Pagination = ({ currentPage, totalPages, onPageChange }) => {
    const handlePrevious = () => {
        if (currentPage > 1) {
            onPageChange(currentPage - 1)
        }
    }

    const handleNext = () => {
        if (currentPage < totalPages) {
            onPageChange(currentPage + 1)
        }
    }

    const handlePageClick = (page) => {
        onPageChange(page)
    }

    const renderPageNumbers = () => {
        const pages = []
        const maxVisiblePages = 7 // To match the design
        
        let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2))
        let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1)
        
        // Adjust start page if we're near the end
        if (endPage - startPage < maxVisiblePages - 1) {
            startPage = Math.max(1, endPage - maxVisiblePages + 1)
        }

        // Add first page and ellipsis if needed
        if (startPage > 1) {
            pages.push(
                <button
                    key={1}
                    onClick={() => handlePageClick(1)}
                    className="flex items-center justify-center w-8 h-8 text-sm text-white bg-gray-800 hover:bg-gray-700 rounded transition-colors"
                >
                    1
                </button>
            )
            if (startPage > 2) {
                pages.push(
                    <span key="ellipsis1" className="px-1 py-1 text-gray-500">
                        ...
                    </span>
                )
            }
        }        
        // Add inner page numbers
        for (let i = startPage; i <= endPage; i++) {
            pages.push(
                <button
                    key={i}
                    onClick={() => handlePageClick(i)}
                    className={`flex items-center justify-center w-8 h-8 text-sm ${
                        i === currentPage
                            ? 'bg-sky-blue text-gray-900'
                            : 'text-white bg-gray-800 hover:bg-gray-700'
                    } rounded transition-colors`}
                >
                    {i}
                </button>
            )
        }
        
        // Add last page and ellipsis if needed
        if (endPage < totalPages) {
            if (endPage < totalPages - 1) {
                pages.push(
                    <span key="ellipsis2" className="px-1 py-1 text-gray-500">
                        ...
                    </span>
                )
            }
            pages.push(
                <button
                    key={totalPages}
                    onClick={() => handlePageClick(totalPages)}
                    className="flex items-center justify-center w-8 h-8 text-sm text-white bg-gray-800 hover:bg-gray-700 rounded transition-colors"
                >
                    {totalPages}
                </button>
            )
        }
        
        return pages
    }
    
    if (totalPages <= 1) {
        return null;
    }   
    return (
        <div className="flex items-center justify-center gap-1">
            {/* Previous Button */}
            <button
                onClick={handlePrevious}
                disabled={currentPage === 1}
                className={`flex items-center justify-center w-8 h-8 rounded ${
                    currentPage === 1 
                        ? 'text-gray-500 bg-gray-800 cursor-not-allowed' 
                        : 'text-white bg-gray-800 hover:bg-gray-700'
                } transition-colors`}
            >
                <img src={LeftIcon} alt="Previous" className="w-3.5 h-3.5" />
            </button>
            
            {/* Page Numbers */}
            <div className="flex items-center gap-1">
                {renderPageNumbers()}
            </div>
            
            {/* Next Button */}
            <button
                onClick={handleNext}
                disabled={currentPage === totalPages}
                className={`flex items-center justify-center w-8 h-8 rounded transform rotate-180 ${
                    currentPage === totalPages 
                        ? 'text-gray-500 bg-gray-800 cursor-not-allowed' 
                        : 'text-white bg-gray-800 hover:bg-gray-700'
                } transition-colors`}
            >
                <img src={LeftIcon} alt="Next" className="w-3.5 h-3.5" />
            </button>
        </div>
    )
}

export default Pagination
