import { useSelector } from 'react-redux';
import { RequestStatus } from '../../../app/store/enum';

/**
 * Custom hook for accessing influencer-related state in components
 * This provides a clean interface for components to read influencer state
 */
const useInfluencerSelectors = () => {
    // Get the influencer state slice from the store
    const influencerState = useSelector((state) => state.influencer);

    return {
        // === PROFILE MANAGEMENT DATA ===
        
        /**
         * Social profiles and YouTube data
         */
        socialProfiles: influencerState.socialProfiles,
        selectedProfile: influencerState.selectedProfile,
        youtubeChannels: influencerState.youtubeChannels,
        selectedChannels: influencerState.selectedChannels,
        
        /**
         * Profile list and current selection
         */
        profiles: influencerState.profiles,
        currentProfile: influencerState.currentProfile,
        selectedProfileId: influencerState.selectedProfileId,

        // === ANALYTICS DATA ===
        
        /**
         * Profile analytics data
         */
        basicProfileData: influencerState.basicProfileData,
        detailedProfileData: influencerState.detailedProfileData,
        analyticsStatus: influencerState.analyticsStatus,
        
        /**
         * Get basic profile data for a specific profile
         * @param {string} profileId - Profile ID
         */
        getBasicProfileData: (profileId) => influencerState.basicProfileData[profileId] || null,
        
        /**
         * Get detailed profile data for a specific profile
         * @param {string} profileId - Profile ID
         */
        getDetailedProfileData: (profileId) => influencerState.detailedProfileData[profileId] || null,
        
        /**
         * Get analytics status for a specific profile
         * @param {string} profileId - Profile ID
         */
        getAnalyticsStatusForProfile: (profileId) => influencerState.analyticsStatus[profileId] || null,

        // === DASHBOARD DATA ===
        
        /**
         * Dashboard analytics data
         */
        dashboardKPIs: influencerState.dashboardKPIs,
        topPosts: influencerState.topPosts,
        monthlyImpressions: influencerState.monthlyImpressions,
        monthlyEngagement: influencerState.monthlyEngagement,
        monthlyLikesByType: influencerState.monthlyLikesByType,
        contentDistribution: influencerState.contentDistribution,
        
        /**
         * Get dashboard KPIs for a specific profile
         * @param {string} profileId - Profile ID
         */
        getDashboardKPIs: (profileId) => influencerState.dashboardKPIs[profileId] || null,
        
        /**
         * Get top posts for a specific profile
         * @param {string} profileId - Profile ID
         */
        getTopPosts: (profileId) => influencerState.topPosts[profileId] || null,
        
        /**
         * Get monthly impressions for a specific profile
         * @param {string} profileId - Profile ID
         */
        getMonthlyImpressions: (profileId) => influencerState.monthlyImpressions[profileId] || null,
        
        /**
         * Get monthly engagement for a specific profile
         * @param {string} profileId - Profile ID
         */
        getMonthlyEngagement: (profileId) => influencerState.monthlyEngagement[profileId] || null,
        
        /**
         * Get monthly likes by type for a specific profile
         * @param {string} profileId - Profile ID
         */
        getMonthlyLikesByType: (profileId) => influencerState.monthlyLikesByType[profileId] || null,
        
        /**
         * Get content distribution for a specific profile
         * @param {string} profileId - Profile ID
         */
        getContentDistribution: (profileId) => influencerState.contentDistribution[profileId] || null,

        // === UI STATE ===
        
        /**
         * Dashboard view state
         */
        dashboardView: influencerState.dashboardView,

        // === REQUEST STATUS ===
        
        /**
         * General influencer operations status
         */
        status: influencerState.status,
        isLoading: influencerState.status === RequestStatus.LOADING,
        isSucceeded: influencerState.status === RequestStatus.SUCCEEDED,
        isFailed: influencerState.status === RequestStatus.FAILED,
        isIdle: influencerState.status === RequestStatus.IDLE,
        
        /**
         * YouTube operations status
         */
        youtubeStatus: influencerState.youtubeStatus,
        isYoutubeLoading: influencerState.youtubeStatus === RequestStatus.LOADING,
        isYoutubeSucceeded: influencerState.youtubeStatus === RequestStatus.SUCCEEDED,
        isYoutubeFailed: influencerState.youtubeStatus === RequestStatus.FAILED,
        isYoutubeIdle: influencerState.youtubeStatus === RequestStatus.IDLE,
        
        /**
         * Profile operations status
         */
        profileStatus: influencerState.profileStatus,
        isProfileLoading: influencerState.profileStatus === RequestStatus.LOADING,
        isProfileSucceeded: influencerState.profileStatus === RequestStatus.SUCCEEDED,
        isProfileFailed: influencerState.profileStatus === RequestStatus.FAILED,
        isProfileIdle: influencerState.profileStatus === RequestStatus.IDLE,
        
        /**
         * Analytics operations status
         */
        analyticsStatus: influencerState.analyticsStatus,
        isAnalyticsLoading: influencerState.analyticsStatus === RequestStatus.LOADING,
        isAnalyticsSucceeded: influencerState.analyticsStatus === RequestStatus.SUCCEEDED,
        isAnalyticsFailed: influencerState.analyticsStatus === RequestStatus.FAILED,
        isAnalyticsIdle: influencerState.analyticsStatus === RequestStatus.IDLE,
        
        /**
         * Dashboard operations status
         */
        dashboardStatus: influencerState.dashboardStatus,
        isDashboardLoading: influencerState.dashboardStatus === RequestStatus.LOADING,
        isDashboardSucceeded: influencerState.dashboardStatus === RequestStatus.SUCCEEDED,
        isDashboardFailed: influencerState.dashboardStatus === RequestStatus.FAILED,
        isDashboardIdle: influencerState.dashboardStatus === RequestStatus.IDLE,

        // === ERROR INFORMATION ===
        
        /**
         * Error states
         */
        error: influencerState.error,
        youtubeError: influencerState.youtubeError,
        profileError: influencerState.profileError,
        analyticsError: influencerState.analyticsError,
        dashboardError: influencerState.dashboardError,
        
        /**
         * Error existence checks
         */
        hasError: !!influencerState.error,
        hasYoutubeError: !!influencerState.youtubeError,
        hasProfileError: !!influencerState.profileError,
        hasAnalyticsError: !!influencerState.analyticsError,
        hasDashboardError: !!influencerState.dashboardError,
        hasAnyError: !!(
            influencerState.error || 
            influencerState.youtubeError || 
            influencerState.profileError || 
            influencerState.analyticsError || 
            influencerState.dashboardError
        ),

        // === COMPUTED VALUES ===
        
        /**
         * Check if user has social profiles
         */
        hasSocialProfiles: influencerState.socialProfiles.length > 0,
        
        /**
         * Check if user has YouTube channels
         */
        hasYoutubeChannels: influencerState.youtubeChannels.length > 0,
        
        /**
         * Check if user has selected YouTube channels
         */
        hasSelectedChannels: influencerState.selectedChannels.length > 0,
        
        /**
         * Check if user has profiles
         */
        hasProfiles: influencerState.profiles.length > 0,
        
        /**
         * Check if a profile is selected
         */
        hasSelectedProfile: !!influencerState.selectedProfileId,
        
        /**
         * Check if profile has basic data
         * @param {string} profileId - Profile ID
         */
        hasBasicProfileData: (profileId) => !!influencerState.basicProfileData[profileId],
        
        /**
         * Check if profile has detailed data
         * @param {string} profileId - Profile ID
         */
        hasDetailedProfileData: (profileId) => !!influencerState.detailedProfileData[profileId],
        
        /**
         * Check if profile has dashboard data
         * @param {string} profileId - Profile ID
         */
        hasDashboardData: (profileId) => !!influencerState.dashboardKPIs[profileId],
        
        /**
         * Check if any operation is currently loading
         */
        isAnyLoading: [
            influencerState.status,
            influencerState.youtubeStatus,
            influencerState.profileStatus,
            influencerState.analyticsStatus,
            influencerState.dashboardStatus
        ].includes(RequestStatus.LOADING),
        
        /**
         * Get profile count
         */
        profileCount: influencerState.profiles.length,
        
        /**
         * Get social profile count
         */
        socialProfileCount: influencerState.socialProfiles.length,
        
        /**
         * Get YouTube channel count
         */
        youtubeChannelCount: influencerState.youtubeChannels.length,
    };
};

// === STANDALONE SELECTORS ===
// These can be used directly with useSelector if needed

/**
 * Select social profiles
 */
export const selectSocialProfiles = (state) => state.influencer.socialProfiles;

/**
 * Select YouTube channels
 */
export const selectYoutubeChannels = (state) => state.influencer.youtubeChannels;

/**
 * Select profiles list
 */
export const selectProfiles = (state) => state.influencer.profiles;

/**
 * Select current profile
 */
export const selectCurrentProfile = (state) => state.influencer.currentProfile;

/**
 * Select basic profile data
 */
export const selectBasicProfileData = (state) => state.influencer.basicProfileData;

/**
 * Select detailed profile data
 */
export const selectDetailedProfileData = (state) => state.influencer.detailedProfileData;

/**
 * Select dashboard KPIs
 */
export const selectDashboardKPIs = (state) => state.influencer.dashboardKPIs;

/**
 * Select influencer status
 */
export const selectInfluencerStatus = (state) => state.influencer.status;

/**
 * Select if any influencer operation is loading
 */
export const selectIsInfluencerLoading = (state) => [
    state.influencer.status,
    state.influencer.youtubeStatus,
    state.influencer.profileStatus,
    state.influencer.analyticsStatus,
    state.influencer.dashboardStatus
].includes(RequestStatus.LOADING);

export default useInfluencerSelectors;
