import { createSlice } from '@reduxjs/toolkit';
import {
    getCampaignsThunk,
    getCampaignByIdThunk,
    createCampaignThunk,
    updateCampaignThunk,
    deleteCampaignThunk,
    getCampaignCreatorsThunk,
    addCreatorToCampaignThunk,
    removeCreatorFromCampaignThunk,
    getCampaignContentThunk,
    approveContentThunk,
    rejectContentThunk,
    getCampaignAnalyticsThunk,
    getCampaignMetricsThunk
} from './campaignThunks';
import { RequestStatus } from '../../../app/store/enum';

const getParsedItem = (key, fallback) => {
    try {
        const value = localStorage.getItem(key);
        if (!value || value === 'undefined') return fallback;
        return JSON.parse(value);
    } catch {
        return fallback;
    }
};

/**
 * Campaign slice initial state
 */
const initialState = {
    // Campaign List Management
    campaigns: [],
    listStatus: RequestStatus.IDLE,
    listError: null,
    searchQuery: '',
    statusFilter: 'All',
    
    // Campaign Details
    selectedCampaign: null,
    campaignCreators: [],
    detailsStatus: RequestStatus.IDLE,
    detailsError: null,
    
    // Content Management
    contentItems: [],
    pendingApprovals: [],
    contentStatus: RequestStatus.IDLE,
    contentError: null,
    
    // Conversation Management (for future implementation)
    conversations: [],
    activeConversation: null,
    messages: [],
    conversationStatus: RequestStatus.IDLE,
    conversationError: null,
    typingUsers: [],
    
    // Analytics
    campaignMetrics: null,
    performanceData: [],
    analyticsStatus: RequestStatus.IDLE,
    analyticsError: null,
    
    // UI State
    activeTab: 'creators',
    isCreatingCampaign: false,
    selectedCreators: [],
    
    // General status and error tracking
    status: RequestStatus.IDLE,
    error: null,
};

/**
 * Campaign slice with reducers and extra reducers for thunks
 */
const campaignSlice = createSlice({
    name: 'campaign',
    initialState,
    reducers: {
        // Synchronous actions for UI state management
        setActiveTab(state, action) {
            state.activeTab = action.payload;
        },

        setSearchQuery(state, action) {
            state.searchQuery = action.payload;
        },

        setStatusFilter(state, action) {
            state.statusFilter = action.payload;
        },

        setSelectedCampaign(state, action) {
            state.selectedCampaign = action.payload;
        },

        setIsCreatingCampaign(state, action) {
            state.isCreatingCampaign = action.payload;
        },

        setSelectedCreators(state, action) {
            state.selectedCreators = action.payload;
        },

        addSelectedCreator(state, action) {
            if (!state.selectedCreators.find(creator => creator.id === action.payload.id)) {
                state.selectedCreators.push(action.payload);
            }
        },

        removeSelectedCreator(state, action) {
            state.selectedCreators = state.selectedCreators.filter(
                creator => creator.id !== action.payload
            );
        },

        clearSelectedCreators(state) {
            state.selectedCreators = [];
        },

        clearCampaignErrors(state) {
            state.error = null;
            state.listError = null;
            state.detailsError = null;
            state.contentError = null;
            state.conversationError = null;
            state.analyticsError = null;
        },

        clearListError(state) {
            state.listError = null;
        },

        clearDetailsError(state) {
            state.detailsError = null;
        },

        clearContentError(state) {
            state.contentError = null;
        },

        clearAnalyticsError(state) {
            state.analyticsError = null;
        },

        clearAllCampaignState(state) {
            return initialState;
        },

        // Content management actions
        updateContentStatus(state, action) {
            const { contentId, status } = action.payload;
            const contentIndex = state.contentItems.findIndex(item => item.id === contentId);
            if (contentIndex !== -1) {
                state.contentItems[contentIndex].status = status;
            }
        },

        // Real-time conversation actions (for future implementation)
        addMessage(state, action) {
            state.messages.push(action.payload);
        },

        setTypingUsers(state, action) {
            state.typingUsers = action.payload;
        },

        setActiveConversation(state, action) {
            state.activeConversation = action.payload;
        },
    },
    extraReducers: (builder) => {
        builder
            // Get campaigns thunk cases
            .addCase(getCampaignsThunk.pending, (state) => {
                state.listStatus = RequestStatus.LOADING;
                state.listError = null;
            })
            .addCase(getCampaignsThunk.fulfilled, (state, action) => {
                state.listStatus = RequestStatus.SUCCEEDED;
                state.campaigns = action.payload.data || [];
            })
            .addCase(getCampaignsThunk.rejected, (state, action) => {
                state.listStatus = RequestStatus.FAILED;
                state.listError = action.payload || 'Failed to fetch campaigns';
            })

            // Get campaign by ID thunk cases
            .addCase(getCampaignByIdThunk.pending, (state) => {
                state.detailsStatus = RequestStatus.LOADING;
                state.detailsError = null;
            })
            .addCase(getCampaignByIdThunk.fulfilled, (state, action) => {
                state.detailsStatus = RequestStatus.SUCCEEDED;
                state.selectedCampaign = action.payload.data;
            })
            .addCase(getCampaignByIdThunk.rejected, (state, action) => {
                state.detailsStatus = RequestStatus.FAILED;
                state.detailsError = action.payload || 'Failed to fetch campaign details';
            })

            // Create campaign thunk cases
            .addCase(createCampaignThunk.pending, (state) => {
                state.status = RequestStatus.LOADING;
                state.error = null;
            })
            .addCase(createCampaignThunk.fulfilled, (state, action) => {
                state.status = RequestStatus.SUCCEEDED;
                state.campaigns.push(action.payload.data);
                state.isCreatingCampaign = false;
            })
            .addCase(createCampaignThunk.rejected, (state, action) => {
                state.status = RequestStatus.FAILED;
                state.error = action.payload || 'Failed to create campaign';
            })

            // Update campaign thunk cases
            .addCase(updateCampaignThunk.pending, (state) => {
                state.status = RequestStatus.LOADING;
                state.error = null;
            })
            .addCase(updateCampaignThunk.fulfilled, (state, action) => {
                state.status = RequestStatus.SUCCEEDED;
                const updatedCampaign = action.payload.data;
                const index = state.campaigns.findIndex(campaign => campaign.id === updatedCampaign.id);
                if (index !== -1) {
                    state.campaigns[index] = updatedCampaign;
                }
                if (state.selectedCampaign?.id === updatedCampaign.id) {
                    state.selectedCampaign = updatedCampaign;
                }
            })
            .addCase(updateCampaignThunk.rejected, (state, action) => {
                state.status = RequestStatus.FAILED;
                state.error = action.payload || 'Failed to update campaign';
            })

            // Delete campaign thunk cases
            .addCase(deleteCampaignThunk.pending, (state) => {
                state.status = RequestStatus.LOADING;
                state.error = null;
            })
            .addCase(deleteCampaignThunk.fulfilled, (state, action) => {
                state.status = RequestStatus.SUCCEEDED;
                const deletedId = action.meta.arg; // The campaign ID passed to the thunk
                state.campaigns = state.campaigns.filter(campaign => campaign.id !== deletedId);
                if (state.selectedCampaign?.id === deletedId) {
                    state.selectedCampaign = null;
                }
            })
            .addCase(deleteCampaignThunk.rejected, (state, action) => {
                state.status = RequestStatus.FAILED;
                state.error = action.payload || 'Failed to delete campaign';
            })

            // Get campaign creators thunk cases
            .addCase(getCampaignCreatorsThunk.pending, (state) => {
                state.detailsStatus = RequestStatus.LOADING;
                state.detailsError = null; 
            })
            .addCase(getCampaignCreatorsThunk.fulfilled, (state, action) => {
                state.detailsStatus = RequestStatus.SUCCEEDED;
                state.campaignCreators = action.payload.data || [];
            })
            .addCase(getCampaignCreatorsThunk.rejected, (state, action) => {
                state.detailsStatus = RequestStatus.FAILED;
                state.detailsError = action.payload || 'Failed to fetch campaign creators';
            })

            // Add creator to campaign thunk cases
            .addCase(addCreatorToCampaignThunk.pending, (state) => {
                state.status = RequestStatus.LOADING;
                state.error = null;
            })
            .addCase(addCreatorToCampaignThunk.fulfilled, (state, action) => {
                state.status = RequestStatus.SUCCEEDED;
                state.campaignCreators.push(action.payload.data);
            })
            .addCase(addCreatorToCampaignThunk.rejected, (state, action) => {
                state.status = RequestStatus.FAILED;
                state.error = action.payload || 'Failed to add creator to campaign';
            })

            // Remove creator from campaign thunk cases
            .addCase(removeCreatorFromCampaignThunk.pending, (state) => {
                state.status = RequestStatus.LOADING;
                state.error = null;
            })
            .addCase(removeCreatorFromCampaignThunk.fulfilled, (state, action) => {
                state.status = RequestStatus.SUCCEEDED;
                const removedCreatorId = action.meta.arg.creatorId;
                state.campaignCreators = state.campaignCreators.filter(
                    creator => creator.id !== removedCreatorId
                );
            })
            .addCase(removeCreatorFromCampaignThunk.rejected, (state, action) => {
                state.status = RequestStatus.FAILED;
                state.error = action.payload || 'Failed to remove creator from campaign';
            })

            // Get campaign content thunk cases
            .addCase(getCampaignContentThunk.pending, (state) => {
                state.contentStatus = RequestStatus.LOADING;
                state.contentError = null;
            })
            .addCase(getCampaignContentThunk.fulfilled, (state, action) => {
                state.contentStatus = RequestStatus.SUCCEEDED;
                state.contentItems = action.payload.data || [];
                state.pendingApprovals = action.payload.data?.filter(item => item.status === 'pending') || [];
            })
            .addCase(getCampaignContentThunk.rejected, (state, action) => {
                state.contentStatus = RequestStatus.FAILED;
                state.contentError = action.payload || 'Failed to fetch campaign content';
            })

            // Approve content thunk cases
            .addCase(approveContentThunk.pending, (state) => {
                state.contentStatus = RequestStatus.LOADING;
                state.contentError = null;
            })
            .addCase(approveContentThunk.fulfilled, (state, action) => {
                state.contentStatus = RequestStatus.SUCCEEDED;
                const approvedContentId = action.meta.arg.contentId;
                const contentIndex = state.contentItems.findIndex(item => item.id === approvedContentId);
                if (contentIndex !== -1) {
                    state.contentItems[contentIndex].status = 'approved';
                }
                state.pendingApprovals = state.pendingApprovals.filter(item => item.id !== approvedContentId);
            })
            .addCase(approveContentThunk.rejected, (state, action) => {
                state.contentStatus = RequestStatus.FAILED;
                state.contentError = action.payload || 'Failed to approve content';
            })

            // Reject content thunk cases
            .addCase(rejectContentThunk.pending, (state) => {
                state.contentStatus = RequestStatus.LOADING;
                state.contentError = null;
            })
            .addCase(rejectContentThunk.fulfilled, (state, action) => {
                state.contentStatus = RequestStatus.SUCCEEDED;
                const rejectedContentId = action.meta.arg.contentId;
                const contentIndex = state.contentItems.findIndex(item => item.id === rejectedContentId);
                if (contentIndex !== -1) {
                    state.contentItems[contentIndex].status = 'rejected';
                    state.contentItems[contentIndex].rejectionReason = action.meta.arg.reason;
                }
                state.pendingApprovals = state.pendingApprovals.filter(item => item.id !== rejectedContentId);
            })
            .addCase(rejectContentThunk.rejected, (state, action) => {
                state.contentStatus = RequestStatus.FAILED;
                state.contentError = action.payload || 'Failed to reject content';
            })

            // Get campaign analytics thunk cases
            .addCase(getCampaignAnalyticsThunk.pending, (state) => {
                state.analyticsStatus = RequestStatus.LOADING;
                state.analyticsError = null;
            })
            .addCase(getCampaignAnalyticsThunk.fulfilled, (state, action) => {
                state.analyticsStatus = RequestStatus.SUCCEEDED;
                state.campaignMetrics = action.payload.data;
            })
            .addCase(getCampaignAnalyticsThunk.rejected, (state, action) => {
                state.analyticsStatus = RequestStatus.FAILED;
                state.analyticsError = action.payload || 'Failed to fetch campaign analytics';
            })

            // Get campaign metrics thunk cases
            .addCase(getCampaignMetricsThunk.pending, (state) => {
                state.analyticsStatus = RequestStatus.LOADING;
                state.analyticsError = null;
            })
            .addCase(getCampaignMetricsThunk.fulfilled, (state, action) => {
                state.analyticsStatus = RequestStatus.SUCCEEDED;
                state.performanceData = action.payload.data || [];
            })
            .addCase(getCampaignMetricsThunk.rejected, (state, action) => {
                state.analyticsStatus = RequestStatus.FAILED;
                state.analyticsError = action.payload || 'Failed to fetch campaign metrics';
            });
    },
});

// Export synchronous actions
export const {
    setActiveTab,
    setSearchQuery,
    setStatusFilter,
    setSelectedCampaign,
    setIsCreatingCampaign,
    setSelectedCreators,
    addSelectedCreator,
    removeSelectedCreator,
    clearSelectedCreators,
    clearCampaignErrors,
    clearListError,
    clearDetailsError,
    clearContentError,
    clearAnalyticsError,
    clearAllCampaignState,
    updateContentStatus,
    addMessage,
    setTypingUsers,
    setActiveConversation,
} = campaignSlice.actions;

// Export thunks
export {
    getCampaignsThunk,
    getCampaignByIdThunk,
    createCampaignThunk,
    updateCampaignThunk,
    deleteCampaignThunk,
    getCampaignCreatorsThunk,
    addCreatorToCampaignThunk,
    removeCreatorFromCampaignThunk,
    getCampaignContentThunk,
    approveContentThunk,
    rejectContentThunk,
    getCampaignAnalyticsThunk,
    getCampaignMetricsThunk
};

// Export the reducer
export default campaignSlice.reducer;
