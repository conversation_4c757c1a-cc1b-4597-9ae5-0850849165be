/**
 * Cross-Service Authentication Manager
 * 
 * This service manages authentication tokens across multiple backend services.
 * It provides a centralized way to handle:
 * - Token storage and retrieval
 * - Token refresh across services
 * - Authentication state synchronization
 * - Cross-service token validation
 */

import { apiConfig, SERVICE_TYPES } from '../config/apiConfig';

/**
 * Token storage keys
 */
const TOKEN_KEYS = {
  ACCESS_TOKEN: 'auth_token',
  REFRESH_TOKEN: 'refresh_token',
  USER_DATA: 'user',
  ALLOCATED_BRANDS: 'allocatedBrands',
  ORGANIZATION_BRANDS: 'organizationBrands',
  ORGANIZATION_ID: 'organisationId',
  TOKEN_EXPIRY: 'token_expiry',
  LAST_REFRESH: 'last_token_refresh'
};

/**
 * Authentication events for cross-component communication
 */
const AUTH_EVENTS = {
  TOKEN_REFRESHED: 'auth:token_refreshed',
  TOKEN_EXPIRED: 'auth:token_expired',
  LOGIN_SUCCESS: 'auth:login_success',
  LOGOUT: 'auth:logout',
  SERVICE_ERROR: 'auth:service_error'
};

/**
 * Cross-Service Authentication Manager
 */
class CrossServiceAuthManager {
  constructor() {
    this.eventListeners = new Map();
    this.refreshPromise = null;
    this.isRefreshing = false;
  }

  // === TOKEN MANAGEMENT ===

  /**
   * Get access token
   * @returns {string|null} Access token
   */
  getAccessToken() {
    return localStorage.getItem(TOKEN_KEYS.ACCESS_TOKEN);
  }

  /**
   * Get refresh token
   * @returns {string|null} Refresh token
   */
  getRefreshToken() {
    return localStorage.getItem(TOKEN_KEYS.REFRESH_TOKEN);
  }

  /**
   * Set authentication tokens
   * @param {string} accessToken - Access token
   * @param {string} refreshToken - Refresh token (optional)
   * @param {number} expiresIn - Token expiry in seconds (optional)
   */
  setTokens(accessToken, refreshToken = null, expiresIn = null) {
    localStorage.setItem(TOKEN_KEYS.ACCESS_TOKEN, accessToken);
    
    if (refreshToken) {
      localStorage.setItem(TOKEN_KEYS.REFRESH_TOKEN, refreshToken);
    }
    
    if (expiresIn) {
      const expiryTime = Date.now() + (expiresIn * 1000);
      localStorage.setItem(TOKEN_KEYS.TOKEN_EXPIRY, expiryTime.toString());
    }
    
    localStorage.setItem(TOKEN_KEYS.LAST_REFRESH, Date.now().toString());
    
    this.emit(AUTH_EVENTS.TOKEN_REFRESHED, { accessToken, refreshToken });
  }

  /**
   * Clear all authentication data
   */
  clearAuth() {
    Object.values(TOKEN_KEYS).forEach(key => {
      localStorage.removeItem(key);
    });
    
    this.emit(AUTH_EVENTS.LOGOUT);
  }

  /**
   * Check if token is expired
   * @returns {boolean} Whether token is expired
   */
  isTokenExpired() {
    const expiryTime = localStorage.getItem(TOKEN_KEYS.TOKEN_EXPIRY);
    if (!expiryTime) return false;
    
    return Date.now() > parseInt(expiryTime);
  }

  /**
   * Check if token needs refresh (expires in next 5 minutes)
   * @returns {boolean} Whether token needs refresh
   */
  needsRefresh() {
    const expiryTime = localStorage.getItem(TOKEN_KEYS.TOKEN_EXPIRY);
    if (!expiryTime) return false;
    
    const fiveMinutes = 5 * 60 * 1000;
    return Date.now() > (parseInt(expiryTime) - fiveMinutes);
  }

  // === USER DATA MANAGEMENT ===

  /**
   * Set user data
   * @param {Object} userData - User data object
   */
  setUserData(userData) {
    localStorage.setItem(TOKEN_KEYS.USER_DATA, JSON.stringify(userData));
  }

  /**
   * Get user data
   * @returns {Object|null} User data
   */
  getUserData() {
    const userData = localStorage.getItem(TOKEN_KEYS.USER_DATA);
    return userData ? JSON.parse(userData) : null;
  }

  /**
   * Set brand data
   * @param {Array} allocatedBrands - Allocated brands
   * @param {Array} organizationBrands - Organization brands
   * @param {string} organizationId - Organization ID
   */
  setBrandData(allocatedBrands, organizationBrands, organizationId) {
    localStorage.setItem(TOKEN_KEYS.ALLOCATED_BRANDS, JSON.stringify(allocatedBrands));
    localStorage.setItem(TOKEN_KEYS.ORGANIZATION_BRANDS, JSON.stringify(organizationBrands));
    localStorage.setItem(TOKEN_KEYS.ORGANIZATION_ID, JSON.stringify(organizationId));
  }

  /**
   * Get brand data
   * @returns {Object} Brand data
   */
  getBrandData() {
    return {
      allocatedBrands: JSON.parse(localStorage.getItem(TOKEN_KEYS.ALLOCATED_BRANDS) || '[]'),
      organizationBrands: JSON.parse(localStorage.getItem(TOKEN_KEYS.ORGANIZATION_BRANDS) || '[]'),
      organizationId: JSON.parse(localStorage.getItem(TOKEN_KEYS.ORGANIZATION_ID) || 'null')
    };
  }

  // === TOKEN REFRESH ===

  /**
   * Refresh access token using refresh token
   * @returns {Promise<Object>} New token data
   */
  async refreshToken() {
    // Prevent multiple simultaneous refresh attempts
    if (this.isRefreshing && this.refreshPromise) {
      return this.refreshPromise;
    }

    this.isRefreshing = true;
    
    this.refreshPromise = this._performTokenRefresh();
    
    try {
      const result = await this.refreshPromise;
      this.isRefreshing = false;
      this.refreshPromise = null;
      return result;
    } catch (error) {
      this.isRefreshing = false;
      this.refreshPromise = null;
      throw error;
    }
  }

  /**
   * Perform the actual token refresh
   * @private
   * @returns {Promise<Object>} New token data
   */
  async _performTokenRefresh() {
    const refreshToken = this.getRefreshToken();
    
    if (!refreshToken) {
      throw new Error('No refresh token available');
    }

    try {
      const response = await fetch(`${apiConfig.authServiceURL}/auth/refresh`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...(apiConfig.environment === 'development' && {
            'ngrok-skip-browser-warning': 'true'
          })
        },
        body: JSON.stringify({ refresh_token: refreshToken }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Token refresh failed');
      }

      if (data.data?.access_token) {
        const { access_token, refresh_token, expires_in } = data.data;
        this.setTokens(access_token, refresh_token, expires_in);
        
        return {
          accessToken: access_token,
          refreshToken: refresh_token,
          expiresIn: expires_in
        };
      } else {
        throw new Error('Invalid refresh response');
      }
    } catch (error) {
      console.error('[AUTH SERVICE] Token refresh failed:', error);
      this.emit(AUTH_EVENTS.TOKEN_EXPIRED, { error: error.message });
      throw error;
    }
  }

  // === AUTHENTICATION VALIDATION ===

  /**
   * Validate token with auth service
   * @returns {Promise<boolean>} Whether token is valid
   */
  async validateToken() {
    const token = this.getAccessToken();
    if (!token) return false;

    try {
      const response = await fetch(`${apiConfig.authServiceURL}/auth/validate`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
          ...(apiConfig.environment === 'development' && {
            'ngrok-skip-browser-warning': 'true'
          })
        },
      });

      return response.ok;
    } catch (error) {
      console.error('[AUTH SERVICE] Token validation failed:', error);
      return false;
    }
  }

  // === REDIRECT MANAGEMENT ===

  /**
   * Redirect to appropriate login page
   * @param {string} context - Context for determining redirect (brand/influencer)
   */
  redirectToLogin(context = '') {
    this.clearAuth();
    
    const isBrand = context.includes('/brand') || context.includes('brand');
    const loginUrl = isBrand ? '/brand/signin' : '/influencer/login';
    
    window.location.href = loginUrl;
  }

  // === EVENT SYSTEM ===

  /**
   * Add event listener
   * @param {string} event - Event name
   * @param {Function} callback - Event callback
   */
  on(event, callback) {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event).push(callback);
  }

  /**
   * Remove event listener
   * @param {string} event - Event name
   * @param {Function} callback - Event callback
   */
  off(event, callback) {
    if (this.eventListeners.has(event)) {
      const listeners = this.eventListeners.get(event);
      const index = listeners.indexOf(callback);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  /**
   * Emit event
   * @param {string} event - Event name
   * @param {*} data - Event data
   */
  emit(event, data = null) {
    if (this.eventListeners.has(event)) {
      this.eventListeners.get(event).forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`[AUTH SERVICE] Event callback error for ${event}:`, error);
        }
      });
    }
  }

  // === UTILITY METHODS ===

  /**
   * Get authentication status
   * @returns {Object} Authentication status
   */
  getAuthStatus() {
    const token = this.getAccessToken();
    const user = this.getUserData();
    
    return {
      isAuthenticated: !!token && !!user,
      hasValidToken: !!token,
      isExpired: this.isTokenExpired(),
      needsRefresh: this.needsRefresh(),
      user,
      ...this.getBrandData()
    };
  }
}

// Create singleton instance
const authService = new CrossServiceAuthManager();

// Export constants and service
export { AUTH_EVENTS, TOKEN_KEYS };
export default authService;
