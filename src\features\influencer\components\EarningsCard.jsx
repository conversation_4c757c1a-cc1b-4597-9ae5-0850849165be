import React from "react";
import Highcharts from "highcharts";
import HighchartsReact from "highcharts-react-official";
import EarningIcon1 from "@assets/icon/earning-1.svg";
import EarningIcon2 from "@assets/icon/earning-2.svg";
import EarningIcon3 from "@assets/icon/earning-3.svg";


const iconMap = {
    post: <img src={EarningIcon3} alt="Star Badge" className="object-contain -my-4 opacity-20" />,
    campaign: <img src={EarningIcon2} alt="Chart" className="object-contain -mb-3 opacity-20" />,
    performance: <img src={EarningIcon1} alt="Performance Rocket" className="object-contain -my-4 opacity-20" />
};

const EarningsCard = ({ earningsData }) => {
    const chartOptions = {
        chart: {
            type: "pie",
            backgroundColor: "transparent",
            height: 250
        },
        title: {
            text: ""
        },
        plotOptions: {
            pie: {
                innerSize: "45%",
                borderWidth: 3,
                borderColor: "#303239",
                showInLegend: false,
                dataLabels: {
                    enabled: false,
                    format: "{point.name}: ₹{point.y}",
                    style: {
                        fontWeight: "bold",
                        color: "#000000"
                    }
                },
                colors: ["#12C9AC", "#E8FD95"]
            }
        },
        tooltip: {
            pointFormat: '<b>₹{point.y}</b>'
        },
        series: [
            {
                name: "Payments",
                data: [
                    { name: "Rupees Received", y: earningsData.pieChart.received },
                    { name: "Upcoming Payments", y: earningsData.pieChart.upcoming }
                ]
            }
        ],
        credits: { enabled: false },
        legend: { enabled: true }
    };

    return (
        <div className="flex flex-col bg-gray-600 rounded-xl space-y-5 p-5 w-full">
            <div className="flex justify-between items-center ">
                <div className="space-y-1">
                    <p className="text-20-semibold text-gray-50">Total Expected Earnings</p>
                    <p className="text-16-regular text-gray-200">₹{earningsData.totalExpected.toLocaleString()}</p>
                </div>
                <div className="flex items-center text-16-semibold text-gray-50">
                    View Earnings
                </div>
            </div>
            <div className="flex justify-between items-start w-full px-5 pb-5">
                {/* Left section */}
                <div className="flex space-y-4 w-1/2 h-full ">
                    <div className="flex flex-col justify-center space-y-2.5 w-full py-10 pr-15">
                        {earningsData.breakdown.map((item, idx) => (
                            <div
                                key={idx}
                                className="flex justify-between items-center bg-[#26272B] rounded-lg px-4 py-3 border border-[#3A3B40]"
                            >
                                <div className="space-y-1">
                                    <p className="text-sm text-white/60">{item.label}</p>
                                    <p className="text-lg text-[#72DDF7]">₹{item.amount.toLocaleString()}</p>
                                </div>
                                {iconMap[item.iconKey]}
                            </div>
                        ))}
                    </div>
                </div>

                {/* Right section */}
                <div className="flex flex-col items-center w-1/2 px-5 py-2.5 text-white">
                    <div className="w-full">
                        <HighchartsReact highcharts={Highcharts} options={chartOptions} />
                    </div>
                    <div className="flex justify-around w-full mt-4">
                        <div className="flex items-center space-x-2">
                            <span className="w-3 h-3 bg-green-2 rounded-full"></span>
                            <div>
                                <p className="text-20-regular text-gray-200">Payment Received</p>
                                <p className="text-20-regular text-gray-50">₹{earningsData.pieChart.received.toLocaleString()}</p>
                            </div>
                        </div>
                        <div className="flex items-center space-x-2">
                            <span className="w-3 h-3 bg-yellow rounded-full"></span>
                            <div>
                                <p className="text-20-regular text-gray-200">Upcoming Payments</p>
                                <p className="text-20-regular text-gray-50">₹{earningsData.pieChart.upcoming.toLocaleString()}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default EarningsCard;
